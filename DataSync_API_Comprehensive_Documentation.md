# Comprehensive DataSync API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Common Response Format](#common-response-format)
4. [Pagination](#pagination)
5. [Error Handling](#error-handling)
6. [User Service Endpoints](#user-service-endpoints)
7. [Product Service Endpoints](#product-service-endpoints)
8. [Integration Notes](#integration-notes)

## Overview

The DataSync API provides endpoints for synchronizing data between the server and client applications. The API is divided into two main services:

- **User Service** (`/dataSync`): Handles user, tenant, and location-related data synchronization
- **Product Service** (`/productService/dataSync`): Handles product, order, and deal-related data synchronization

Both services support efficient data synchronization using cursor-based pagination with `lastSyncedAt` timestamps.

## Authentication

All endpoints require the following headers:

| Header          | Type   | Required | Description                                               |
| --------------- | ------ | -------- | --------------------------------------------------------- |
| `Authorization` | String | Yes      | Bearer token for authentication                           |
| `devicetoken`   | String | Yes      | Device identifier token                                   |
| `userroleid`    | String | Yes      | MongoDB ObjectId of the user role for permission checking |

### Example Headers

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
devicetoken: device_abc123
userroleid: 648d99c0cd63200012aab771
```

## Common Response Format

### Success Response

All endpoints return responses in the following format:

```json
{
  "status": "success",
  "message": null,
  "data": {
    // Response data here
  }
}
```

For paginated endpoints:

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 150,  // Total number of records matching the filter
    "list": [...]  // Array of records for current page
  }
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Error description",
  "data": null
}
```

### Common HTTP Status Codes

- **200 OK** - Request successful
- **400 Bad Request** - Invalid request parameters
- **401 Unauthorized** - Missing or invalid authentication
- **403 Forbidden** - Insufficient permissions
- **422 Unprocessable Entity** - Validation error
- **500 Internal Server Error** - Server error

## Pagination

The DataSync API uses cursor-based pagination for efficient data synchronization.

### Pagination Parameters

| Parameter      | Type    | Required | Description                                                |
| -------------- | ------- | -------- | ---------------------------------------------------------- |
| `perPage`      | Integer | Yes      | Number of records per page (min: 1, default: 50)           |
| `lastSyncedAt` | String  | No       | ISO date string from the last record's `updated_at` field  |
| `cursor`       | String  | No       | ID from the last record (MongoDB ObjectId or composite ID) |

### Pagination Flow

1. **Initial Request**: Omit `lastSyncedAt` and `cursor` parameters
2. **Subsequent Requests**: Use values from the last record in the previous response
3. **End of Data**: When `count` equals 0 or `list` is empty

### Example Pagination Flow

```bash
# First request
GET /dataSync/userRoleSettings?tenantId=1131&perPage=30

# Response contains last record:
# { "_id": "1131_6621333850e6570012327222", "updated_at": "2025-06-17T12:27:54.926Z" }

# Second request using pagination
GET /dataSync/userRoleSettings?tenantId=1131&perPage=30&lastSyncedAt=2025-06-17T12:27:54.926Z&cursor=1131_6621333850e6570012327222
```

### Pagination Logic

- Records are sorted by `updated_at` (ascending) and then by `_id` (ascending)
- The query includes records where:
  - `updated_at > lastSyncedAt` OR
  - `updated_at = lastSyncedAt AND _id > cursor`

## Error Handling

### Validation Errors (422)

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": [
    {
      "field": "tenantId",
      "message": "Please provide valid 'tenantId'"
    }
  ]
}
```

### Permission Errors (403)

```json
{
  "status": "error",
  "message": "Insufficient permissions",
  "data": null
}
```

### Common Validation Rules

1. **tenantId**: Integer, minimum value 1000
2. **MongoDB ObjectIds**: Must be valid 24-character hex strings
3. **ISO Date Strings**: Must be valid ISO 8601 format
4. **Arrays**: Must have at least 1 element where specified

## User Service Endpoints

Base URL: `/dataSync`

### 1. Get Tenant App Settings

**Endpoint:** `GET /tenantAppSetting`

**Description:** Retrieves application settings configuration for a specific tenant. This endpoint returns a single configuration object (not paginated).

**Permissions Required:**

- Orders: VIEW
- Inventory: VIEW
- Products: VIEW
- Customers: VIEW

#### Request Parameters

| Parameter  | Type    | Required | Description        | Validation                    |
| ---------- | ------- | -------- | ------------------ | ----------------------------- |
| `tenantId` | Integer | Yes      | Tenant identifier  | Minimum value: 1000           |
| `type`     | String  | Yes      | Configuration type | Must be exactly "APP_SETTING" |

#### Example Request

```bash
GET /dataSync/tenantAppSetting?tenantId=1131&type=APP_SETTING
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "tenant_id": 1131,
    "quantity_label": 1,
    "consider_new_item": 1,
    "price_change": true,
    "hide_out_of_stock_product": false,
    "reduce_inventory": true,
    "customer_app_access": true,
    "catalog_mode": false,
    "customer_auto_catalog_mode": {
      "enabled": false,
      "duration": 3
    },
    "preferred_language": "en",
    "decimal_points": 2,
    "payment_voucher_whatsapp_notification": true,
    "created_by": "507f1f77bcf86cd799439012",
    "updated_by": "507f1f77bcf86cd799439012",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Response Fields

| Field                                   | Type     | Required | Description                                | Possible Values            |
| --------------------------------------- | -------- | -------- | ------------------------------------------ | -------------------------- |
| `_id`                                   | ObjectId | Yes      | Unique identifier                          | MongoDB ObjectId           |
| `tenant_id`                             | Number   | Yes      | Tenant identifier                          | >= 1000                    |
| `quantity_label`                        | Number   | Yes      | Quantity label configuration               | Integer                    |
| `consider_new_item`                     | Number   | Yes      | New item consideration setting             | Integer                    |
| `price_change`                          | Boolean  | Yes      | Whether price changes are allowed          | true/false                 |
| `hide_out_of_stock_product`             | Boolean  | Yes      | Whether to hide out-of-stock products      | true/false                 |
| `reduce_inventory`                      | Boolean  | Yes      | Whether to reduce inventory on order       | true/false                 |
| `customer_app_access`                   | Boolean  | Yes      | Whether customer app access is enabled     | true/false                 |
| `catalog_mode`                          | Boolean  | Yes      | Whether catalog mode is enabled            | true/false                 |
| `customer_auto_catalog_mode`            | Object   | Yes      | Auto catalog mode configuration            | Object                     |
| `customer_auto_catalog_mode.enabled`    | Boolean  | Yes      | Whether auto catalog mode is enabled       | true/false                 |
| `customer_auto_catalog_mode.duration`   | Number   | Yes      | Duration in months                         | Integer (default: 3)       |
| `preferred_language`                    | String   | Yes      | Preferred language code                    | Language code (e.g., "en") |
| `decimal_points`                        | Number   | Yes      | Number of decimal points for prices        | 0-4 (default: 0)           |
| `payment_voucher_whatsapp_notification` | Boolean  | No       | Whether WhatsApp notifications are enabled | true/false/null            |
| `created_by`                            | ObjectId | Yes      | User who created the setting               | MongoDB ObjectId           |
| `updated_by`                            | ObjectId | Yes      | User who last updated the setting          | MongoDB ObjectId           |
| `created_at`                            | Date     | Yes      | Creation timestamp                         | ISO 8601 date              |
| `updated_at`                            | Date     | Yes      | Last update timestamp                      | ISO 8601 date              |

### 2. Get User Role Settings

**Endpoint:** `GET /userRoleSettings`

**Description:** Retrieves user role-specific settings with pagination support. Each setting is identified by a composite ID: `{tenantId}_{userRoleId}`.

**Permissions Required:**

- Users: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation            |
| -------------- | ------- | -------- | --------------------------------------------- | --------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000   |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1      |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format |
| `cursor`       | String  | No       | Composite ID from last record                 | Non-empty string      |

#### Example Request

```bash
GET /dataSync/userRoleSettings?tenantId=1131&perPage=30&lastSyncedAt=2025-06-17T12:27:54.926Z&cursor=1131_6621333850e6570012327222
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 150,
    "list": [
      {
        "_id": "1131_6621333850e6570012327222",
        "tenant_id": 1131,
        "user_role_id": "507f1f77bcf86cd799439012",
        "default_master_price_id": "507f1f77bcf86cd799439013",
        "out_of_stock": {
          "visible": true,
          "searchable": false
        },
        "price_change": true,
        "preferred_language": "en",
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2025-06-17T12:27:54.926Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                            | Type     | Required | Description                               | Possible Values                       |
| -------------------------------- | -------- | -------- | ----------------------------------------- | ------------------------------------- |
| `count`                          | Number   | Yes      | Total number of records                   | >= 0                                  |
| `list`                           | Array    | Yes      | Array of user role settings               | Array (may be empty)                  |
| `list[]._id`                     | String   | Yes      | Composite ID: {tenantId}\_{userRoleId}    | String                                |
| `list[].tenant_id`               | Number   | Yes      | Tenant identifier                         | >= 1000                               |
| `list[].user_role_id`            | ObjectId | Yes      | Reference to user role                    | MongoDB ObjectId                      |
| `list[].default_master_price_id` | ObjectId | No       | Default master price list reference       | MongoDB ObjectId or null              |
| `list[].out_of_stock`            | Object   | No       | Out of stock configuration                | Object or null                        |
| `list[].out_of_stock.visible`    | Boolean  | Yes\*    | Whether out of stock items are visible    | true/false (\*if out_of_stock exists) |
| `list[].out_of_stock.searchable` | Boolean  | Yes\*    | Whether out of stock items are searchable | true/false (\*if out_of_stock exists) |
| `list[].price_change`            | Boolean  | No       | Whether price changes are allowed         | true/false/null                       |
| `list[].preferred_language`      | String   | No       | Preferred language code                   | Language code or null                 |
| `list[].created_at`              | Date     | Yes      | Creation timestamp                        | ISO 8601 date                         |
| `list[].updated_at`              | Date     | Yes      | Last update timestamp                     | ISO 8601 date                         |

### 3. Get User Roles

**Endpoint:** `GET /userRoles`

**Description:** Retrieves user roles filtered by specific role types with pagination support.

**Permissions Required:**

- Users: VIEW

#### Request Parameters

| Parameter       | Type    | Required | Description                                   | Validation                                |
| --------------- | ------- | -------- | --------------------------------------------- | ----------------------------------------- |
| `tenantId`      | Integer | Yes      | Tenant identifier                             | Minimum value: 1000                       |
| `perPage`       | Integer | Yes      | Number of records per page                    | Minimum value: 1                          |
| `roles`         | Array   | Yes      | Array of role names to filter                 | Min 1 role, must be valid primitive roles |
| `isInitialSync` | Boolean | No       | Whether this is initial sync                  | true/false                                |
| `lastSyncedAt`  | String  | No       | ISO date string from last record's updated_at | Valid ISO date format                     |
| `cursor`        | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId                    |

#### Valid Role Values

- `"System Owner"`
- `"Super Admin"`
- `"Account Manager"`
- `"Tenant Owner"`
- `"Admin"`
- `"Branch Manager"`
- `"Sales Person"`
- `"Supervisor"`
- `"Customer"`
- `"Warehouse Clerk"`
- `"Accountant"`
- `"Contributor"`

#### Example Request

```bash
GET /dataSync/userRoles?tenantId=1131&perPage=20&isInitialSync=true&roles[]=Sales%20Person&roles[]=Customer&lastSyncedAt=2024-07-18T07:28:56.462Z&cursor=641b093ef4fac2a95c0e77d3
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 150,
    "list": [
      {
        "_id": "64c892d6d2e6f000120b590d",
        "collection_name": "users",
        "is_deleted": false,
        "is_active": true,
        "tenant_id": 1131,
        "branch_id": "636906513b25300011587a95",
        "user_id": {
          "_id": "64c892d6d2e6f000120b5907",
          "first_name": "John",
          "last_name": "Doe",
          "email": "<EMAIL>",
          "mobile_number": *********,
          "country_code": "+56"
        },
        "role_id": "6334140732b899129ff2233b",
        "allow_price_change": true,
        "created_at": "2023-08-01T05:06:30.890Z",
        "updated_at": "2023-09-08T09:01:50.158Z"
      },
      {
        "_id": "64526f1733c103ffc35b8faa",
        "collection_name": "tenant_customers",
        "is_deleted": false,
        "is_active": true,
        "external_id": "1WTYU5112",
        "customer_name": "Test",
        "customer_legal_name": "Sheet",
        "customer_first_name": "Hin",
        "customer_last_name": "Dur",
        "customer_email": "<EMAIL>",
        "customer_app_access": false,
        "preferred_language": "en",
        "shipping_address": "",
        "shipping_mobile_number": null,
        "tenant_id": 1131,
        "user_id": {
          "_id": "64526f1633c103ffc35b8fa8",
          "country_code": "+91",
          "mobile_number": 123456789
        },
        "customer_id": "113110045",
        "role_id": "634fbeaf6d1d2d861f6e6a1f",
        "sales_person_id": "6369eafc87526f00125f5f04",
        "shipping_country_id": "62e7d842077fb60012eb35e3",
        "shipping_city_id": null,
        "shipping_region_id": null,
        "shipping_country_code": "",
        "gps_coordinates": {
          "longitude": 0,
          "latitude": 0
        },
        "price_list_id": "6374c0b1d78429fdf5777314",
        "is_verified": false,
        "created_at": "2023-05-03T14:26:31.026Z",
        "updated_at": "2024-01-02T04:52:27.376Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                              | Type     | Required | Description                             | Possible Values          |
| ---------------------------------- | -------- | -------- | --------------------------------------- | ------------------------ |
| `count`                            | Number   | Yes      | Total number of records                 | >= 0                     |
| `list`                             | Array    | Yes      | Array of user roles                     | Array (may be empty)     |
| `list[]._id`                       | ObjectId | Yes      | Unique user role identifier             | MongoDB ObjectId         |
| `list[].collection_name`           | String   | Yes      | Collection name                         | "tenant_users"           |
| `list[].customer_app_access`       | Boolean  | No       | Whether customer app access is enabled  | true/false/null          |
| `list[].customer_app_request`      | Boolean  | No       | Whether customer app request is pending | true/false/null          |
| `list[].customer_email`            | String   | No       | Customer email address                  | Email string or null     |
| `list[].customer_first_name`       | String   | No       | Customer first name                     | String or null           |
| `list[].customer_last_name`        | String   | No       | Customer last name                      | String or null           |
| `list[].customer_legal_name`       | String   | No       | Customer legal/business name            | String or null           |
| `list[].customer_name`             | String   | No       | Customer display name                   | String or null           |
| `list[].customer_id`               | String   | No       | Customer identifier                     | String or null           |
| `list[].external_id`               | String   | No       | External system identifier              | String or null           |
| `list[].branch_id`                 | ObjectId | No       | Reference to branch                     | MongoDB ObjectId or null |
| `list[].is_active`                 | Boolean  | Yes      | Whether user role is active             | true/false               |
| `list[].is_deleted`                | Boolean  | Yes      | Soft delete flag                        | true/false               |
| `list[].is_verified`               | Boolean  | No       | Whether customer is verified            | true/false/null          |
| `list[].allow_price_change`        | Boolean  | No       | Whether price changes are allowed       | true/false/null          |
| `list[].preferred_language`        | String   | No       | Preferred language code                 | Language code or null    |
| `list[].price_list_id`             | ObjectId | No       | Reference to price list                 | MongoDB ObjectId or null |
| `list[].role_id`                   | ObjectId | Yes      | Reference to role                       | MongoDB ObjectId         |
| `list[].sales_person_id`           | ObjectId | No       | Reference to sales person               | MongoDB ObjectId or null |
| `list[].tenant_id`                 | Number   | Yes      | Tenant identifier                       | >= 1000                  |
| `list[].user_id`                   | Object   | No       | User information object                 | Object or null           |
| `list[].user_id._id`               | ObjectId | Yes\*    | User identifier                         | MongoDB ObjectId         |
| `list[].user_id.first_name`        | String   | Yes\*    | User first name                         | String                   |
| `list[].user_id.last_name`         | String   | Yes\*    | User last name                          | String                   |
| `list[].user_id.email`             | String   | Yes\*    | User email address                      | Email string             |
| `list[].user_id.mobile_number`     | String   | No       | User mobile number                      | String or null           |
| `list[].user_id.country_code`      | String   | No       | User country code                       | String or null           |
| `list[].gps_coordinates`           | Object   | No       | GPS coordinates object                  | Object or null           |
| `list[].gps_coordinates.latitude`  | Number   | Yes\*    | Latitude coordinate                     | -90 to 90                |
| `list[].gps_coordinates.longitude` | Number   | Yes\*    | Longitude coordinate                    | -180 to 180              |
| `list[].shipping_address`          | String   | No       | Shipping address                        | String or null           |
| `list[].shipping_city_id`          | ObjectId | No       | Reference to shipping city              | MongoDB ObjectId or null |
| `list[].shipping_country_code`     | String   | No       | Shipping country code                   | String or null           |
| `list[].shipping_country_id`       | ObjectId | No       | Reference to shipping country           | MongoDB ObjectId or null |
| `list[].shipping_region_id`        | ObjectId | No       | Reference to shipping region            | MongoDB ObjectId or null |
| `list[].shipping_mobile_number`    | String   | No       | Shipping mobile number                  | String or null           |
| `list[].created_at`                | Date     | Yes      | Creation timestamp                      | ISO 8601 date            |
| `list[].updated_at`                | Date     | Yes      | Last update timestamp                   | ISO 8601 date            |

\*Required if parent object exists

**Note:** When `isInitialSync` is true, only active (`is_active: true`) and non-deleted (`is_deleted: false`) records are returned.

### 4. Get Regions

**Endpoint:** `GET /regions`

**Description:** Retrieves regions for a specific country with pagination support.

**Permissions Required:**

- System Regions: VIEW
- Regions: VIEW

#### Request Parameters

| Parameter       | Type    | Required | Description                                   | Validation             |
| --------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `countryId`     | String  | Yes      | MongoDB ObjectId of the country               | Valid MongoDB ObjectId |
| `perPage`       | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `isInitialSync` | Boolean | No       | Whether this is initial sync                  | true/false             |
| `lastSyncedAt`  | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`        | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /dataSync/regions?countryId=62e7d842077fb60012eb35e3&isInitialSync=true&perPage=10&lastSyncedAt=2024-02-09T08:51:02.371Z&cursor=65c5e776ab95bdbe8f8e940e
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 50,
    "list": [
      {
        "_id": "65c5e776ab95bdbe8f8e940e",
        "name": "New York",
        "secondary_language_name": "Nueva York",
        "code": "NY",
        "country_id": "62e7d842077fb60012eb35e3",
        "is_active": true,
        "is_deleted": false,
        "secondary_language_code": "NY",
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-02-09T08:51:02.371Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                            | Type     | Required | Description                    | Possible Values      |
| -------------------------------- | -------- | -------- | ------------------------------ | -------------------- |
| `count`                          | Number   | Yes      | Total number of records        | >= 0                 |
| `list`                           | Array    | Yes      | Array of regions               | Array (may be empty) |
| `list[]._id`                     | ObjectId | Yes      | Unique region identifier       | MongoDB ObjectId     |
| `list[].name`                    | String   | Yes      | Primary region name            | String               |
| `list[].secondary_language_name` | String   | Yes      | Secondary language region name | String               |
| `list[].code`                    | String   | No       | Region code                    | String or null       |
| `list[].country_id`              | ObjectId | Yes      | Reference to country           | MongoDB ObjectId     |
| `list[].is_active`               | Boolean  | Yes      | Whether region is active       | true/false           |
| `list[].is_deleted`              | Boolean  | Yes      | Soft delete flag               | true/false           |
| `list[].secondary_language_code` | String   | No       | Secondary language region code | String or null       |
| `list[].created_at`              | Date     | Yes      | Creation timestamp             | ISO 8601 date        |
| `list[].updated_at`              | Date     | Yes      | Last update timestamp          | ISO 8601 date        |

**Note:** When `isInitialSync` is true, only active (`is_active: true`) and non-deleted (`is_deleted: false`) regions are returned.

### 5. Get Cities

**Endpoint:** `POST /cities`

**Description:** Retrieves cities for specific regions with pagination support.

**Permissions Required:**

- System Cities: VIEW
- Cities: VIEW

#### Request Body Parameters

| Parameter       | Type    | Required | Description                                   | Validation                                        |
| --------------- | ------- | -------- | --------------------------------------------- | ------------------------------------------------- |
| `regionIds`     | Array   | Yes      | Array of MongoDB ObjectIds for regions        | Min 1 region, all must be valid MongoDB ObjectIds |
| `perPage`       | Integer | Yes      | Number of records per page                    | Minimum value: 1                                  |
| `isInitialSync` | Boolean | No       | Whether this is initial sync                  | true/false                                        |
| `lastSyncedAt`  | String  | No       | ISO date string from last record's updated_at | Valid ISO date format                             |
| `cursor`        | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId                            |

#### Example Request

```bash
POST /dataSync/cities
Content-Type: application/json

{
  "regionIds": ["65c5ce577f6e6ac214b1a7aa", "65c5e776ab95bdbe8f8e940e"],
  "isInitialSync": true,
  "perPage": 10
}
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 100,
    "list": [
      {
        "_id": "65c5df9d7f6e6ac214b1a7ac",
        "name": "Manhattan",
        "secondary_language_name": "Manhattan",
        "country_id": "62e7d842077fb60012eb35e3",
        "region_id": "65c5ce577f6e6ac214b1a7aa",
        "is_active": true,
        "is_deleted": false,
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-02-09T08:17:33.956Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                            | Type     | Required | Description                  | Possible Values      |
| -------------------------------- | -------- | -------- | ---------------------------- | -------------------- |
| `count`                          | Number   | Yes      | Total number of records      | >= 0                 |
| `list`                           | Array    | Yes      | Array of cities              | Array (may be empty) |
| `list[]._id`                     | ObjectId | Yes      | Unique city identifier       | MongoDB ObjectId     |
| `list[].name`                    | String   | Yes      | Primary city name            | String               |
| `list[].secondary_language_name` | String   | Yes      | Secondary language city name | String               |
| `list[].country_id`              | ObjectId | Yes      | Reference to country         | MongoDB ObjectId     |
| `list[].region_id`               | ObjectId | Yes      | Reference to region          | MongoDB ObjectId     |
| `list[].is_active`               | Boolean  | Yes      | Whether city is active       | true/false           |
| `list[].is_deleted`              | Boolean  | Yes      | Soft delete flag             | true/false           |
| `list[].created_at`              | Date     | Yes      | Creation timestamp           | ISO 8601 date        |
| `list[].updated_at`              | Date     | Yes      | Last update timestamp        | ISO 8601 date        |

**Note:** When `isInitialSync` is true, only active (`is_active: true`) and non-deleted (`is_deleted: false`) cities are returned.

## Product Service Endpoints

Base URL: `/productService/dataSync`

### 1. Get Master Data

**Endpoint:** `GET /masterData`

**Description:** Retrieves all master configuration data including tax settings, tax types, tax groups, master prices, and master units. This endpoint returns a single response object (not paginated).

**Permissions Required:**

- Settings Master Data Price List: VIEW
- Settings Master Data UOM: VIEW
- Orders: VIEW
- Products: VIEW
- Settings Configurations Tax Setting: VIEW
- Settings Configurations Tax: VIEW

#### Request Parameters

| Parameter  | Type    | Required | Description       | Validation          |
| ---------- | ------- | -------- | ----------------- | ------------------- |
| `tenantId` | Integer | Yes      | Tenant identifier | Minimum value: 1000 |

#### Example Request

```bash
GET /productService/dataSync/masterData?tenantId=1131
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "taxSetting": {
      "_id": "507f1f77bcf86cd799439011",
      "tenant_id": 1131,
      "enable_tax": true,
      "price": "INCLUSIVE",
      "default_tax": "507f1f77bcf86cd799439012",
      "universal_tax": true,
      "created_at": "2023-01-15T10:30:00.000Z",
      "updated_at": "2023-01-15T10:30:00.000Z"
    },
    "taxTypes": [
      {
        "_id": "686bb67fce9f2d859ac876ff",
        "tenant_id": 1131,
        "type": "GROUP",
        "tax_name": "GST",
        "status": true,
        "created_at": "2025-07-07T11:58:55.423Z",
        "updated_at": "2025-07-07T11:58:55.423Z"
      },
      {
        "_id": "686bb719ce9f2d859ac87708",
        "tenant_id": 1131,
        "type": "SINGLE",
        "tax_name": "VAT",
        "tax_calculation": "FLAT_VALUE",
        "tax_rate": "154",
        "status": true,
        "created_at": "2025-07-07T12:01:29.984Z",
        "updated_at": "2025-07-07T12:01:29.984Z"
      }
    ],
    "taxGroups": [
      {
        "_id": "686bb67fce9f2d859ac87701",
        "tenant_id": 1131,
        "type": "GROUP_SINGLE_VALUE",
        "group_id": "686bb67fce9f2d859ac876ff",
        "tax_name": "SGST",
        "tax_calculation": "PERCENTAGE",
        "tax_rate": "10",
        "created_at": "2025-07-07T11:58:55.472Z",
        "updated_at": "2025-07-07T11:58:55.472Z"
      },
      {
        "_id": "686bb67fce9f2d859ac87703",
        "tenant_id": 1131,
        "type": "GROUP_SINGLE_VALUE",
        "group_id": "686bb67fce9f2d859ac876ff",
        "tax_name": "CGST",
        "tax_calculation": "PERCENTAGE",
        "tax_rate": "15",
        "created_at": "2025-07-07T11:58:55.526Z",
        "updated_at": "2025-07-07T11:58:55.526Z"
      }
    ],
    "masterPrices": [
      {
        "_id": "507f1f77bcf86cd799439015",
        "tenant_id": 1131,
        "price_id": "PRICE001",
        "price_name": "Retail Price",
        "secondary_language_price_name": "खुदरा मूल्य",
        "is_deleted": false,
        "is_active": true,
        "is_default": true,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-01-15T10:30:00.000Z"
      }
    ],
    "masterUnits": [
      {
        "_id": "507f1f77bcf86cd799439016",
        "tenant_id": 1131,
        "unit_name": "Pieces",
        "secondary_language_unit_name": "टुकड़े",
        "is_deleted": false,
        "is_active": true,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-01-15T10:30:00.000Z"
      }
    ]
  }
}
```

#### Response Fields

##### Tax Setting

| Field            | Type     | Required | Description                      | Possible Values          |
| ---------------- | -------- | -------- | -------------------------------- | ------------------------ |
| `taxSetting`     | Object   | No       | Tax configuration object         | Object or null           |
| `._id`           | ObjectId | Yes\*    | Unique identifier                | MongoDB ObjectId         |
| `.tenant_id`     | Number   | Yes\*    | Tenant identifier                | >= 1000                  |
| `.enable_tax`    | Boolean  | Yes\*    | Whether tax is enabled           | true/false               |
| `.price`         | String   | Yes\*    | Price type                       | "INCLUSIVE"/"EXCLUSIVE"  |
| `.default_tax`   | ObjectId | No       | Reference to default tax         | MongoDB ObjectId or null |
| `.universal_tax` | Boolean  | Yes\*    | Whether universal tax is enabled | true/false               |
| `.created_at`    | Date     | Yes\*    | Creation timestamp               | ISO 8601 date            |
| `.updated_at`    | Date     | Yes\*    | Last update timestamp            | ISO 8601 date            |

\*Required if taxSetting exists

##### Tax Types & Tax Groups

| Field                | Type     | Required | Description                   | Possible Values                       |
| -------------------- | -------- | -------- | ----------------------------- | ------------------------------------- |
| `taxTypes`           | Array    | Yes      | Array of active tax types     | Array (may be empty)                  |
| `taxGroups`          | Array    | Yes      | Array of tax group items      | Array (may be empty)                  |
| `[].type`            | String   | Yes      | Tax type                      | "SINGLE"/"GROUP"/"GROUP_SINGLE_VALUE" |
| `[].group_id`        | ObjectId | No       | Reference to parent tax group | MongoDB ObjectId or null              |
| `[].tax_name`        | String   | Yes      | Tax name                      | String                                |
| `[].tax_calculation` | String   | Yes      | Calculation method            | "PERCENTAGE"/"AMOUNT"                 |
| `[].tax_rate`        | String   | No       | Tax rate as string            | Numeric string/null                   |
| `[].status`          | Boolean  | No       | Whether tax is active         | true/false/null                       |
| `[].order`           | Number   | No       | Display order                 | Integer/null                          |

##### Master Prices

| Field                              | Type    | Required | Description                   | Possible Values      |
| ---------------------------------- | ------- | -------- | ----------------------------- | -------------------- |
| `masterPrices`                     | Array   | Yes      | Array of master prices        | Array (may be empty) |
| `[].price_id`                      | String  | Yes      | Unique price identifier       | String               |
| `[].price_name`                    | String  | Yes      | Primary price name            | String               |
| `[].secondary_language_price_name` | String  | No       | Secondary language price name | String or null       |
| `[].is_deleted`                    | Boolean | Yes      | Soft delete flag              | true/false           |
| `[].is_active`                     | Boolean | Yes      | Whether price is active       | true/false           |
| `[].is_default`                    | Boolean | Yes      | Whether this is default price | true/false/null      |

##### Master Units

| Field                             | Type    | Required | Description                  | Possible Values      |
| --------------------------------- | ------- | -------- | ---------------------------- | -------------------- |
| `masterUnits`                     | Array   | Yes      | Array of master units        | Array (may be empty) |
| `[].unit_name`                    | String  | Yes      | Primary unit name            | String               |
| `[].secondary_language_unit_name` | String  | Yes      | Secondary language unit name | String or null       |
| `[].is_deleted`                   | Boolean | Yes      | Soft delete flag             | true/false           |
| `[].is_active`                    | Boolean | Yes      | Whether unit is active       | true/false           |

### 2. Get Category List

**Endpoint:** `GET /categories`

**Description:** Retrieves a paginated list of categories with support for initial sync filtering.

**Permissions Required:**

- Categories: VIEW
- Products: VIEW

#### Request Parameters

| Parameter       | Type    | Required | Description                                          | Validation             |
| --------------- | ------- | -------- | ---------------------------------------------------- | ---------------------- |
| `tenantId`      | Integer | Yes      | Tenant identifier                                    | Minimum value: 1000    |
| `perPage`       | Integer | Yes      | Number of records per page                           | Minimum value: 1       |
| `lastSyncedAt`  | String  | No       | ISO date string from last record's updated_at        | Valid ISO date format  |
| `cursor`        | String  | No       | MongoDB ObjectId from last record                    | Valid MongoDB ObjectId |
| `isInitialSync` | Boolean | No       | If true, only returns active, non-deleted categories | true/false             |

#### Example Request

```bash
GET /productService/dataSync/categories?tenantId=1131&perPage=10&isInitialSync=true&lastSyncedAt=2025-06-02T16:28:49.757Z&cursor=6388487c7ef5eb0012323057
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 25,
    "list": [
      {
        "_id": "6388487c7ef5eb0012323057",
        "category_name": "Electronics",
        "secondary_language_category_name": "इलेक्ट्रॉनिक्स",
        "tenant_id": 1131,
        "type": "FAMILY",
        "family_id": null,
        "parent_id": null,
        "is_active": true,
        "is_deleted": false,
        "sequence": 1,
        "image_name": "electronics.jpg",
        "product_counter": 150,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2025-06-02T16:28:49.757Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                                     | Type     | Required | Description                             | Possible Values                   |
| ----------------------------------------- | -------- | -------- | --------------------------------------- | --------------------------------- |
| `count`                                   | Number   | Yes      | Total number of records matching filter | >= 0                              |
| `list`                                    | Array    | Yes      | Array of categories                     | Array (may be empty)              |
| `list[]._id`                              | ObjectId | Yes      | Unique category identifier              | MongoDB ObjectId                  |
| `list[].category_name`                    | String   | Yes      | Primary category name                   | String                            |
| `list[].secondary_language_category_name` | String   | Yes      | Secondary language category name        | String or null                    |
| `list[].tenant_id`                        | Number   | Yes      | Tenant identifier                       | >= 1000                           |
| `list[].type`                             | String   | Yes      | Category type                           | "FAMILY"/"CATEGORY"/"SUBCATEGORY" |
| `list[].family_id`                        | ObjectId | No       | Reference to family category            | MongoDB ObjectId or null          |
| `list[].parent_id`                        | ObjectId | No       | Reference to parent category            | MongoDB ObjectId or null          |
| `list[].is_active`                        | Boolean  | Yes      | Whether category is active              | true/false                        |
| `list[].is_deleted`                       | Boolean  | Yes      | Soft delete flag                        | true/false                        |
| `list[].sequence`                         | Number   | No       | Display sequence order                  | Integer or null                   |
| `list[].image_name`                       | String   | No       | Category image filename                 | String or null                    |
| `list[].product_counter`                  | Number   | No       | Number of products in category          | >= 0 or null                      |
| `list[].created_at`                       | Date     | Yes      | Creation timestamp                      | ISO 8601 date                     |
| `list[].updated_at`                       | Date     | Yes      | Last update timestamp                   | ISO 8601 date                     |

**Note:** When `isInitialSync` is true, only active (`is_active: true`) and non-deleted (`is_deleted: false`) categories are returned.

### 3. Get Product List

**Endpoint:** `GET /products`

**Description:** Retrieves a paginated list of products with support for initial sync filtering. Returns products of all types (SINGLE, PARENT, VARIANT).

**Permissions Required:**

- Products: VIEW

#### Request Parameters

| Parameter       | Type    | Required | Description                                   | Validation             |
| --------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`      | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`       | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt`  | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`        | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |
| `isInitialSync` | Boolean | No       | If true, only returns active products         | true/false             |

#### Example Request

```bash
GET /productService/dataSync/products?tenantId=1131&perPage=10&isInitialSync=true&lastSyncedAt=2025-07-15T14:41:58.907Z&cursor=6405854037a87ab2e5bcb0d7
```

#### Example Response - PARENT Product

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 150,
    "list": [
      {
        "_id": "6413e81a27358d27e58efdb3",
        "tenant_id": 1131,
        "item_number": "05-2023-012",
        "type": "PARENT",
        "barcodes": [],
        "title": "Water Dispenser",
        "secondary_language_title": "वॉटर डिस्पेंसर",
        "description": "Premium water dispenser with hot and cold options",
        "secondary_language_description": "",
        "search_identifiers": {
          "active_variant_item_numbers": ["05-2023-015", "05-2023-014"],
          "inactive_variant_item_numbers": [],
          "active_variant_barcodes": [],
          "inactive_variant_barcodes": []
        },
        "product_order": 100,
        "family": {
          "_id": "637703a3afe9390012952855",
          "name": "Water Dispenser"
        },
        "category": {
          "_id": "63770633afe93900129528c8",
          "name": "Standing"
        },
        "subcategory": null,
        "brand": {
          "_id": "639967628081d300121350dc",
          "name": "Black and Orange"
        },
        "is_active": true,
        "is_restocked": false,
        "variants": {
          "type": "Color",
          "values": [
            {
              "_id": "6413e81b27358d27e58efdba",
              "name": "Green",
              "secondary_language_name": "",
              "order": 2
            }
          ]
        },
        "groups": {
          "type": "Size",
          "values": [
            {
              "_id": "6466feff8d1b30bd5a03257c",
              "name": "1mm",
              "secondary_language_name": "",
              "order": 1
            }
          ]
        },
        "variant_count": 2,
        "product_variants": [
          {
            "_id": "6466ff448d1b30bd5a03259f",
            "item_number": "05-2023-015",
            "barcodes": [],
            "is_active": true,
            "is_restocked": false,
            "variant_order": 1,
            "variant": {
              "_id": "6413e81b27358d27e58efdba",
              "name": "Green",
              "secondary_language_name": ""
            },
            "group": {
              "_id": "6466feff8d1b30bd5a03257c",
              "name": "1mm",
              "secondary_language_name": ""
            },
            "inventory_mappings": [
              {
                "branch_id": "6343f50ca2a6e700126d1808",
                "warehouse_id": "636ccd35fc3d2d01fc93c8aa",
                "quantity": 0
              }
            ],
            "packaging_map": {
              "uom_id": "6399679a8081d300121350e1",
              "uom_name": "Feet",
              "min_qty": 25
            },
            "price_mappings": [
              {
                "master_price_id": "642525297d7f06d4f591fd61",
                "price": 20
              }
            ]
          }
        ],
        "inventory_mappings": [
          {
            "branch_id": "6343f50ca2a6e700126d1808",
            "warehouse_id": "636ccd35fc3d2d01fc93c8aa",
            "quantity": 0
          }
        ],
        "price_mappings": [
          {
            "master_price_id": "642525297d7f06d4f591fd61",
            "price": 20,
            "product_variant_id": "6466ff448d1b30bd5a03259f"
          }
        ],
        "tags": [],
        "attributes": [],
        "created_at": "2025-07-15T14:41:58.952Z",
        "updated_at": "2025-07-15T14:41:58.952Z"
      }
    ]
  }
}
```

#### Example Response - SINGLE Product

```json
{
  "_id": "6413e81a27358d27e58efdb4",
  "tenant_id": 1131,
  "item_number": "05-2023-013",
  "type": "SINGLE",
  "barcodes": ["1234567890123"],
  "title": "Single Product Item",
  "secondary_language_title": "",
  "description": "A simple single product without variants",
  "secondary_language_description": "",
  "product_order": 50,
  "family": {
    "_id": "637703a3afe9390012952855",
    "name": "Water Dispenser"
  },
  "category": null,
  "subcategory": null,
  "brand": null,
  "is_active": true,
  "is_restocked": false,
  "inventory_mappings": [
    {
      "branch_id": "6343f50ca2a6e700126d1808",
      "warehouse_id": "636ccd35fc3d2d01fc93c8aa",
      "quantity": 25
    }
  ],
  "packaging_map": {
    "uom_id": "6399679a8081d300121350e1",
    "uom_name": "Pieces",
    "min_qty": 1,
    "qty_ctn": 10
  },
  "price_mappings": [
    {
      "master_price_id": "642525297d7f06d4f591fd61",
      "price": 15.5
    }
  ],
  "tags": [],
  "attributes": [
    {
      "attribute_id": "6413e81b27358d27e58efdbb",
      "type": "MANUAL",
      "name": "Weight",
      "value": "500g"
    }
  ],
  "created_at": "2025-07-15T14:41:58.952Z",
  "updated_at": "2025-07-15T14:41:58.952Z"
}
```

#### Response Fields

##### Common Fields (All Product Types)

| Field                            | Type     | Required | Description                    | Possible Values                   |
| -------------------------------- | -------- | -------- | ------------------------------ | --------------------------------- |
| `_id`                            | ObjectId | Yes      | Unique product identifier      | MongoDB ObjectId                  |
| `tenant_id`                      | Number   | Yes      | Tenant identifier              | >= 1000                           |
| `item_number`                    | String   | Yes      | Product item number            | String                            |
| `type`                           | String   | Yes      | Product type                   | "SINGLE"/"PARENT"/"VARIANT"       |
| `barcodes`                       | Array    | Yes      | Product barcodes               | Array of strings (may be empty)   |
| `title`                          | String   | Yes      | Primary product title          | String                            |
| `secondary_language_title`       | String   | Yes      | Secondary language title       | String or empty string            |
| `description`                    | String   | No       | Product description            | String or empty string            |
| `secondary_language_description` | String   | No       | Secondary language description | String or empty string            |
| `product_order`                  | Number   | Yes      | Display order                  | Integer or null                   |
| `family`                         | Object   | Yes      | Family category reference      | Object or null                    |
| `family._id`                     | ObjectId | Yes\*    | Family category ID             | MongoDB ObjectId                  |
| `family.name`                    | String   | Yes\*    | Family category name           | String                            |
| `category`                       | Object   | No       | Category reference             | Object or null                    |
| `category._id`                   | ObjectId | Yes\*    | Category ID                    | MongoDB ObjectId                  |
| `category.name`                  | String   | Yes\*    | Category name                  | String                            |
| `subcategory`                    | Object   | No       | Subcategory reference          | Object or null                    |
| `subcategory._id`                | ObjectId | Yes\*    | Subcategory ID                 | MongoDB ObjectId                  |
| `subcategory.name`               | String   | Yes\*    | Subcategory name               | String                            |
| `brand`                          | Object   | No       | Brand reference                | Object or null                    |
| `brand._id`                      | ObjectId | Yes\*    | Brand ID                       | MongoDB ObjectId                  |
| `brand.name`                     | String   | Yes\*    | Brand name                     | String                            |
| `is_active`                      | Boolean  | Yes      | Whether product is active      | true/false                        |
| `is_restocked`                   | Boolean  | Yes      | Whether product was restocked  | true/false                        |
| `restocked_at`                   | Date     | No       | Restock timestamp              | ISO 8601 date or null             |
| `tags`                           | Array    | No       | Product tags                   | Array of ObjectIds (may be empty) |
| `attributes`                     | Array    | No       | Product attributes             | Array (may be empty)              |
| `created_at`                     | Date     | Yes      | Creation timestamp             | ISO 8601 date                     |
| `updated_at`                     | Date     | Yes      | Last update timestamp          | ISO 8601 date                     |

##### PARENT Product Specific Fields

| Field                            | Type   | Required | Description                     | Possible Values                |
| -------------------------------- | ------ | -------- | ------------------------------- | ------------------------------ |
| `search_identifiers`             | Object | Yes\*    | Search identifiers for variants | Object                         |
| `.active_variant_item_numbers`   | Array  | Yes      | Active variant item numbers     | Array of strings               |
| `.inactive_variant_item_numbers` | Array  | Yes      | Inactive variant item numbers   | Array of strings               |
| `.active_variant_barcodes`       | Array  | Yes      | Active variant barcodes         | Array of strings               |
| `.inactive_variant_barcodes`     | Array  | Yes      | Inactive variant barcodes       | Array of strings               |
| `variants`                       | Object | Yes      | Variant type configuration      | Object or null                 |
| `variants.type`                  | String | Yes      | Variant type name               | String (e.g., "Color", "Size") |
| `variants.values`                | Array  | Yes      | Variant values                  | Array                          |
| `groups`                         | Object | No       | Group type configuration        | Object or null                 |
| `variant_count`                  | Number | Yes\*    | Number of variants              | >= 0                           |
| `product_variants`               | Array  | Yes\*    | Array of product variants       | Array                          |

##### SINGLE Product Specific Fields

| Field                    | Type     | Required | Description             | Possible Values  |
| ------------------------ | -------- | -------- | ----------------------- | ---------------- |
| `packaging_map`          | Object   | Yes      | Packaging configuration | Object or null   |
| `packaging_map.uom_id`   | ObjectId | Yes      | Unit of measure ID      | MongoDB ObjectId |
| `packaging_map.uom_name` | String   | Yes      | Unit of measure name    | String           |
| `packaging_map.min_qty`  | Number   | Yes      | Minimum quantity        | > 0              |
| `packaging_map.qty_ctn`  | Number   | No       | Quantity per carton     | > 0 or null      |

##### Inventory and Price Mappings

| Field                   | Type     | Required | Description                     | Possible Values          |
| ----------------------- | -------- | -------- | ------------------------------- | ------------------------ |
| `inventory_mappings`    | Array    | Yes      | Inventory by location           | Array (may be empty)     |
| `[].branch_id`          | String   | Yes      | Branch identifier               | String                   |
| `[].warehouse_id`       | String   | Yes      | Warehouse identifier            | String                   |
| `[].quantity`           | Number   | Yes      | Available quantity              | >= 0                     |
| `price_mappings`        | Array    | Yes      | Price configurations            | Array (may be empty)     |
| `[].master_price_id`    | ObjectId | Yes      | Master price reference          | MongoDB ObjectId         |
| `[].price`              | Number   | Yes      | Product price                   | >= 0                     |
| `[].product_variant_id` | ObjectId | No       | Variant reference (PARENT only) | MongoDB ObjectId or null |

**Notes:**

1. When `isInitialSync` is true, only active (`is_active: true`) products are returned
2. VARIANT type products are included in the `product_variants` array of their PARENT product
3. Fields marked with \* are required only when their parent object exists

### 4. Get Image List

**Endpoint:** `GET /images`

**Description:** Retrieves a paginated list of product images.

**Permissions Required:**

- Images: VIEW
- Products: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation             |
| -------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /productService/dataSync/images?tenantId=1131&perPage=10
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 45,
    "list": [
      {
        "_id": "64661b4d2872e47f0b5059ef",
        "product_variant_id": "507f1f77bcf86cd799439015",
        "group_id": "507f1f77bcf86cd799439016",
        "image_name": "product_123.jpg",
        "image_number": 1,
        "s3_url": "https://storage.example.com/images/product_123.jpg",
        "tenant_id": 1131,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-05-18T12:34:21.362Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                | Type     | Required | Description                     | Possible Values    |
| -------------------- | -------- | -------- | ------------------------------- | ------------------ |
| `_id`                | ObjectId | Yes      | Unique image identifier         | MongoDB ObjectId   |
| `product_variant_id` | ObjectId | Yes      | Reference to product variant    | MongoDB ObjectId   |
| `group_id`           | String   | No       | Reference to variant type group | String or null     |
| `image_name`         | String   | Yes      | Image filename                  | String             |
| `image_number`       | Number   | Yes      | Image sequence number           | >= 1               |
| `s3_url`             | String   | No       | S3 storage URL for the image    | URL string or null |
| `tenant_id`          | Number   | Yes      | Tenant identifier               | >= 1000            |
| `created_at`         | Date     | Yes      | Creation timestamp              | ISO 8601 date      |
| `updated_at`         | Date     | Yes      | Last update timestamp           | ISO 8601 date      |

### 5. Get Favorite Product List

**Endpoint:** `GET /favoriteProducts`

**Description:** Retrieves a paginated list of products marked as favorites by the authenticated user(user_role_id passed in header).

**Permissions Required:**

- Products: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation             |
| -------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /productService/dataSync/favoriteProducts?tenantId=1131&perPage=10
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 12,
    "list": [
      {
        "_id": "64661c1b2872e47f0b55cfcb",
        "user_role_id": "648d99c0cd63200012aab771",
        "tenant_id": 1131,
        "product_variant_id": "507f1f77bcf86cd799439015",
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-05-18T12:37:47.460Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                | Type     | Required | Description                        | Possible Values  |
| -------------------- | -------- | -------- | ---------------------------------- | ---------------- |
| `_id`                | ObjectId | Yes      | Unique favorite product identifier | MongoDB ObjectId |
| `user_role_id`       | ObjectId | Yes      | Reference to user role             | MongoDB ObjectId |
| `tenant_id`          | Number   | Yes      | Tenant identifier                  | >= 1000          |
| `product_variant_id` | ObjectId | Yes      | Reference to product variant       | MongoDB ObjectId |
| `created_at`         | Date     | Yes      | Creation timestamp                 | ISO 8601 date    |
| `updated_at`         | Date     | Yes      | Last update timestamp              | ISO 8601 date    |

### 6. Get Deal List

**Endpoint:** `GET /deals`

**Description:** Retrieves a paginated list of active deals (running or scheduled).

**Permissions Required:**

- Deals: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation             |
| -------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /productService/dataSync/deals?tenantId=1131&perPage=10
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 8,
    "list": [
      {
        "_id": "647f460d90da50c7cbfe96b4",
        "deal_id": "DEAL001",
        "tenant_id": 1131,
        "price_id": "507f1f77bcf86cd799439013",
        "deal_type": "DISCOUNT",
        "deal_name": "Summer Sale",
        "secondary_deal_name": "ग्रीष्मकालीन बिक्री",
        "deal_from_date": "2023-06-01T00:00:00.000Z",
        "deal_to_date": "2023-08-31T23:59:59.000Z",
        "sales_persons": ["648d99c0cd63200012aab771"],
        "deal_status": "RUNNING",
        "sort_type": "MANUAL",
        "deal_product_counter": 25,
        "click_count": 150,
        "view_count": 500,
        "add_to_cart_count": 75,
        "unit_sold_count": 45,
        "sales_base_price": 1125000,
        "sales_tax": 202500,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-06-06T14:43:42.509Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                  | Type     | Required | Description                        | Possible Values                             |
| ---------------------- | -------- | -------- | ---------------------------------- | ------------------------------------------- |
| `_id`                  | ObjectId | Yes      | Unique deal identifier             | MongoDB ObjectId                            |
| `deal_id`              | String   | Yes      | Human-readable deal identifier     | String                                      |
| `tenant_id`            | Number   | Yes      | Tenant identifier                  | >= 1000                                     |
| `price_id`             | ObjectId | Yes      | Reference to master price          | MongoDB ObjectId                            |
| `deal_type`            | String   | Yes      | Deal type                          | "DISCOUNT"/"BULK_PRICING"/"BUY_X_AND_GET_Y" |
| `deal_name`            | String   | No       | Primary deal name                  | String or null                              |
| `secondary_deal_name`  | String   | No       | Secondary language deal name       | String or null                              |
| `deal_from_date`       | Date     | No       | Deal start date                    | ISO 8601 date or null                       |
| `deal_to_date`         | Date     | No       | Deal end date                      | ISO 8601 date or null                       |
| `sales_persons`        | Array    | No       | Array of salesperson user role IDs | Array of ObjectIds (may be empty)           |
| `deal_status`          | String   | Yes      | Deal status                        | String                                      |
| `sort_type`            | String   | Yes      | Deal sorting type                  | "MANUAL"/"PRICE"/"INVENTORY" or null        |
| `deal_product_counter` | Number   | Yes      | Number of products in deal         | >= 0                                        |
| `click_count`          | Number   | Yes      | Unique click count                 | >= 0                                        |
| `view_count`           | Number   | Yes      | View count                         | >= 0                                        |
| `add_to_cart_count`    | Number   | Yes      | Add to cart count                  | >= 0                                        |
| `unit_sold_count`      | Number   | Yes      | Units sold count                   | >= 0                                        |
| `sales_base_price`     | Number   | Yes      | Total sales base price             | >= 0                                        |
| `sales_tax`            | Number   | Yes      | Total sales tax                    | >= 0                                        |
| `created_at`           | Date     | Yes      | Creation timestamp                 | ISO 8601 date                               |
| `updated_at`           | Date     | Yes      | Last update timestamp              | ISO 8601 date                               |

### 7. Get Deal Product List

**Endpoint:** `POST /dealProducts`

**Description:** Retrieves a paginated list of products associated with specific deals.

**Permissions Required:**

- Deals: VIEW

#### Request Body Parameters

| Parameter      | Type    | Required | Description                                   | Validation                                |
| -------------- | ------- | -------- | --------------------------------------------- | ----------------------------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000                       |
| `dealIds`      | Array   | Yes      | Array of deal IDs                             | Min 1 deal, all must be MongoDB ObjectIds |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1                          |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format                     |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId                    |

#### Example Request

```bash
POST /productService/dataSync/dealProducts
Content-Type: application/json

{
  "tenantId": 1131,
  "dealIds": ["64638c1bbce4f83c05c81276", "647f0f4d9a1bed63a46da77f"],
  "perPage": 10,
  "lastSyncedAt": "2023-06-05T07:12:29.152Z",
  "cursor": "647d88993428a671708ffb8b"
}
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 15,
    "list": [
      {
        "_id": "647d88993428a671708ffb8b",
        "deal_id": "64638c1bbce4f83c05c81276",
        "price_id": "507f1f77bcf86cd799439013",
        "tenant_id": 1131,
        "product_id": "507f1f77bcf86cd799439015",
        "parent_id": null,
        "is_active": true,
        "discount_type": "PERCENT",
        "percent": 15,
        "amount": 0,
        "discounted_price": 21250,
        "first_tier": {
          "product_qty": 10,
          "price": 22500
        },
        "second_tier": {
          "product_qty": 20,
          "price": 21250
        },
        "third_tier": {
          "product_qty": 50,
          "price": 20000
        },
        "buy_product": 1,
        "free_product": 0,
        "deal_product_sequence": 1,
        "deal_from_date": "2023-06-01T00:00:00.000Z",
        "deal_to_date": "2023-08-31T23:59:59.000Z",
        "click_count": 25,
        "view_count": 100,
        "add_to_cart_count": 15,
        "unit_sold_count": 8,
        "sales_base_price": 170000,
        "sales_tax": 30600,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2023-06-05T07:12:29.152Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                            | Type     | Required | Description                           | Possible Values            |
| -------------------------------- | -------- | -------- | ------------------------------------- | -------------------------- |
| `count`                          | Number   | Yes      | Total number of records               | >= 0                       |
| `list`                           | Array    | Yes      | Array of deal products                | Array (may be empty)       |
| `list[]._id`                     | ObjectId | Yes      | Unique deal product identifier        | MongoDB ObjectId           |
| `list[].deal_id`                 | ObjectId | Yes      | Reference to deal                     | MongoDB ObjectId           |
| `list[].price_id`                | ObjectId | Yes      | Reference to master price             | MongoDB ObjectId           |
| `list[].tenant_id`               | Number   | Yes      | Tenant identifier                     | >= 1000                    |
| `list[].product_id`              | ObjectId | Yes      | Reference to product variant          | MongoDB ObjectId           |
| `list[].parent_id`               | ObjectId | No       | Reference to parent product           | MongoDB ObjectId or null   |
| `list[].is_active`               | Boolean  | Yes      | Whether deal product is active        | true/false                 |
| `list[].discount_type`           | String   | No       | Discount type                         | "PERCENT"/"AMOUNT" or null |
| `list[].percent`                 | Number   | No       | Discount percentage                   | 0-100 or null              |
| `list[].amount`                  | Number   | No       | Discount amount                       | >= 0 or null               |
| `list[].discounted_price`        | Number   | No       | Final discounted price                | >= 0 or null               |
| `list[].first_tier`              | Object   | No       | First tier pricing                    | Object or null             |
| `list[].first_tier.product_qty`  | Number   | Yes\*    | First tier quantity threshold         | > 0                        |
| `list[].first_tier.price`        | Number   | Yes\*    | First tier price                      | >= 0                       |
| `list[].second_tier`             | Object   | No       | Second tier pricing                   | Object or null             |
| `list[].second_tier.product_qty` | Number   | Yes\*    | Second tier quantity threshold        | > 0                        |
| `list[].second_tier.price`       | Number   | Yes\*    | Second tier price                     | >= 0                       |
| `list[].third_tier`              | Object   | No       | Third tier pricing                    | Object or null             |
| `list[].third_tier.product_qty`  | Number   | Yes\*    | Third tier quantity threshold         | > 0                        |
| `list[].third_tier.price`        | Number   | Yes\*    | Third tier price                      | >= 0                       |
| `list[].buy_product`             | Number   | No       | Buy X quantity for buy X get Y deals  | >= 0 or null               |
| `list[].free_product`            | Number   | No       | Free Y quantity for buy X get Y deals | >= 0 or null               |
| `list[].deal_product_sequence`   | Number   | No       | Display sequence order                | >= 0 or null               |
| `list[].deal_from_date`          | Date     | No       | Deal start date                       | ISO 8601 date or null      |
| `list[].deal_to_date`            | Date     | No       | Deal end date                         | ISO 8601 date or null      |
| `list[].click_count`             | Number   | No       | Unique click count                    | >= 0 or null               |
| `list[].view_count`              | Number   | No       | View count                            | >= 0 or null               |
| `list[].add_to_cart_count`       | Number   | No       | Add to cart count                     | >= 0 or null               |
| `list[].unit_sold_count`         | Number   | No       | Units sold count                      | >= 0 or null               |
| `list[].sales_base_price`        | Number   | No       | Total sales base price                | >= 0 or null               |
| `list[].sales_tax`               | Number   | No       | Total sales tax                       | >= 0 or null               |
| `list[].created_at`              | Date     | Yes      | Creation timestamp                    | ISO 8601 date              |
| `list[].updated_at`              | Date     | Yes      | Last update timestamp                 | ISO 8601 date              |

\*Required if parent object exists

### 8. Get Cart Item List

**Endpoint:** `GET /cartItems`

**Description:** Retrieves a paginated list of cart items for the authenticated user.

**Permissions Required:**

- Orders: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation             |
| -------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /productService/dataSync/cartItems?tenantId=1131&perPage=2
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 5,
    "list": [
      {
        "_id": "683812342a316f0179194020",
        "last_cart_action": "ADD_ITEM_TO_CART",
        "cart_id": "1131_cart_648d99c0cd63200012aab771",
        "tenant_id": 1131,
        "product_variant_id": "507f1f77bcf86cd799439015",
        "variant_id": "VAR001",
        "item_number": "PROD001",
        "parent_item_number": "PARENT001",
        "product_name": "Smartphone",
        "product_secondary_name": "स्मार्टफोन",
        "variant_name": "128GB Black",
        "variant_secondary_name": "128GB काला",
        "group_name": "Storage",
        "group_secondary_name": "स्टोरेज",
        "master_price_id": "507f1f77bcf86cd799439013",
        "base_price": 25000,
        "tax": 4500,
        "quantity": 2,
        "min_qty": 1,
        "uom_id": "507f1f77bcf86cd799439014",
        "uom_name": "Pieces",
        "item_comment": ["Handle with care"],
        "customer_user_role_id": "648d99c0cd63200012aab771",
        "sales_user_role_id": "648d99c0cd63200012aab772",
        "tax_calculation_info": {
          "tax_id": "683812342a316f0179194020",
          "type": "GROUP",
          "tax_name": "VAT",
          "tax_rate": 20.025,
          "tax_calculation": "PERCENTAGE",
          "calculated_tax": 154,
          "group_taxes": {
            "tax_id": "683812342a316f0179194020",
            "tax_name": "VAT",
            "tax_rate": 20.025,
            "tax_calculation": "PERCENTAGE",
            "calculated_tax": 154
          },
          "price": "INCLUDE"
        },
        "original_price": 25000,
        "created_at": "2023-01-15T10:30:00.000Z",
        "updated_at": "2025-05-29T07:52:20.513Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                                                     | Type     | Required | Description                                            | Possible Values                                                                               |
| --------------------------------------------------------- | -------- | -------- | ------------------------------------------------------ | --------------------------------------------------------------------------------------------- |
| `count`                                                   | Number   | Yes      | Total number of records                                | >= 0                                                                                          |
| `list`                                                    | Array    | Yes      | Array of cart items                                    | Array (may be empty)                                                                          |
| `list[]._id`                                              | ObjectId | Yes      | Unique cart item identifier                            | MongoDB ObjectId                                                                              |
| `list[].last_cart_action`                                 | String   | Yes      | Last action performed on cart item                     | "ADD_ITEM_TO_CART"/"UPDATE_QUANTITY"/"REMOVE_FROM_CART"/"ADD_ITEM_COMMENT"/"EDIT_ITEM_PRICE". |
| `list[].cart_id`                                          | String   | Yes      | Cart identifier (format: {tenantId}_cart_{userRoleId}) | String                                                                                        |
| `list[].tenant_id`                                        | Number   | Yes      | Tenant identifier                                      | >= 1000                                                                                       |
| `list[].product_variant_id`                               | ObjectId | Yes      | Reference to product variant                           | MongoDB ObjectId                                                                              |
| `list[].variant_id`                                       | String   | No       | Variant identifier                                     | String or null                                                                                |
| `list[].item_number`                                      | String   | Yes      | Product item number                                    | String                                                                                        |
| `list[].parent_item_number`                               | String   | No       | Parent variant item number                             | String or null                                                                                |
| `list[].product_name`                                     | String   | Yes      | Primary product name                                   | String                                                                                        |
| `list[].product_secondary_name`                           | String   | No       | Secondary language product name                        | String or null                                                                                |
| `list[].variant_name`                                     | String   | No       | Variant name                                           | String or null                                                                                |
| `list[].variant_secondary_name`                           | String   | No       | Secondary language variant name                        | String or null                                                                                |
| `list[].group_name`                                       | String   | No       | Group name                                             | String or null                                                                                |
| `list[].group_secondary_name`                             | String   | No       | Secondary language group name                          | String or null                                                                                |
| `list[].master_price_id`                                  | ObjectId | Yes      | Reference to master price                              | MongoDB ObjectId                                                                              |
| `list[].base_price`                                       | Number   | Yes      | Base price per unit                                    | >= 0                                                                                          |
| `list[].tax`                                              | Number   | Yes      | Tax amount                                             | >= 0                                                                                          |
| `list[].quantity`                                         | Number   | Yes      | Item quantity                                          | > 0                                                                                           |
| `list[].min_qty`                                          | Number   | Yes      | Minimum quantity allowed                               | > 0                                                                                           |
| `list[].uom_id`                                           | ObjectId | Yes      | Reference to unit of measure                           | MongoDB ObjectId                                                                              |
| `list[].uom_name`                                         | String   | Yes      | Unit of measure name                                   | String                                                                                        |
| `list[].item_comment`                                     | Array    | No       | Array of item comments                                 | Array of strings (may be empty) or null                                                       |
| `list[].customer_user_role_id`                            | ObjectId | Yes      | Reference to customer user role                        | MongoDB ObjectId                                                                              |
| `list[].sales_user_role_id`                               | ObjectId | No       | Reference to sales user role                           | MongoDB ObjectId or null                                                                      |
| `list[].tax_calculation_info`                             | Object   | No       | Tax calculation details                                | Object or null                                                                                |
| `list[].tax_calculation_info.tax_id`                      | ObjectId | Yes\*    | Reference to tax configuration                         | MongoDB ObjectId                                                                              |
| `list[].tax_calculation_info.type`                        | String   | Yes\*    | Tax type                                               | "SINGLE"/"GROUP"                                                                              |
| `list[].tax_calculation_info.tax_name`                    | String   | Yes\*    | Tax name                                               | String                                                                                        |
| `list[].tax_calculation_info.tax_rate`                    | Number   | No       | Tax rate percentage                                    | >= 0 or null                                                                                  |
| `list[].tax_calculation_info.tax_calculation`             | String   | Yes\*    | Tax calculation method                                 | "PERCENTAGE"/"AMOUNT"                                                                         |
| `list[].tax_calculation_info.calculated_tax`              | Number   | No       | Calculated tax amount                                  | >= 0 or null                                                                                  |
| `list[].tax_calculation_info.group_taxes`                 | Object   | No       | Group tax details (when type is GROUP)                 | Object or null                                                                                |
| `list[].tax_calculation_info.group_taxes.tax_id`          | ObjectId | Yes\*    | Reference to group tax                                 | MongoDB ObjectId                                                                              |
| `list[].tax_calculation_info.group_taxes.tax_name`        | String   | Yes\*    | Group tax name                                         | String                                                                                        |
| `list[].tax_calculation_info.group_taxes.tax_rate`        | Number   | Yes\*    | Group tax rate percentage                              | >= 0                                                                                          |
| `list[].tax_calculation_info.group_taxes.tax_calculation` | String   | Yes\*    | Group tax calculation method                           | "PERCENTAGE"/"AMOUNT"                                                                         |
| `list[].tax_calculation_info.group_taxes.calculated_tax`  | Number   | Yes\*    | Calculated group tax amount                            | >= 0                                                                                          |
| `list[].tax_calculation_info.price`                       | String   | Yes\*    | Price inclusion type                                   | "INCLUDE"/"EXCLUDE"                                                                           |
| `list[].original_price`                                   | Number   | No       | Original display price                                 | >= 0 or null                                                                                  |
| `list[].created_at`                                       | Date     | Yes      | Creation timestamp                                     | ISO 8601 date                                                                                 |
| `list[].updated_at`                                       | Date     | Yes      | Last update timestamp                                  | ISO 8601 date                                                                                 |

### 9. Get Order List

**Endpoint:** `GET /orders`

**Description:** Retrieves a paginated list of orders created by the authenticated user within the last 3 months.

**Permissions Required:**

- Orders: VIEW

#### Request Parameters

| Parameter      | Type    | Required | Description                                   | Validation             |
| -------------- | ------- | -------- | --------------------------------------------- | ---------------------- |
| `tenantId`     | Integer | Yes      | Tenant identifier                             | Minimum value: 1000    |
| `perPage`      | Integer | Yes      | Number of records per page                    | Minimum value: 1       |
| `lastSyncedAt` | String  | No       | ISO date string from last record's updated_at | Valid ISO date format  |
| `cursor`       | String  | No       | MongoDB ObjectId from last record             | Valid MongoDB ObjectId |

#### Example Request

```bash
GET /productService/dataSync/orders?tenantId=1131&perPage=2
```

#### Example Response

```json
{
  "status": "success",
  "message": null,
  "data": {
    "count": 12,
    "list": [
      {
        "_id": "685d13e6f60a980d2f1bd09d",
        "tenant_id": 1131,
        "customer_user_role_id": "648d99c0cd63200012aab771",
        "external_id": "EXT001",
        "sales_user_role_id": "648d99c0cd63200012aab772",
        "sales_person_name": "John Doe",
        "customer_name": "Jane Smith",
        "customer_legal_name": "Smith Enterprises",
        "customer_primary_contact_name": "Jane Smith",
        "branch_id": "507f1f77bcf86cd799439017",
        "order_number": "ORD-2025-001",
        "order_remark": "Handle with care",
        "order_punching_device_type": "TABLET",
        "order_punching_device_os": "ANDROID",
        "order_app_type": "SALES_APP",
        "order_creator_user_role_id": "648d99c0cd63200012aab771",
        "order_creator_name": "John Doe",
        "order_status": "PENDING",
        "order_status_track": [
          {
            "order_status": "PENDING",
            "time": "2025-06-26T09:55:27.925Z",
            "user_info": {
              "user_role_id": "648d99c0cd63200012aab771",
              "name": "John Doe"
            }
          }
        ],
        "total_amount": 50000,
        "total_tax": 9000,
        "reduce_inventory": false,
        "tax_calculation_info": {
          "tax_id": "683812342a316f0179194020",
          "type": "GROUP",
          "tax_name": "VAT",
          "tax_rate": 20.025,
          "tax_calculation": "PERCENTAGE",
          "calculated_tax": 154,
          "group_taxes": {
            "tax_id": "683812342a316f0179194020",
            "tax_name": "VAT",
            "tax_rate": 20.025,
            "tax_calculation": "PERCENTAGE",
            "calculated_tax": 154
          },
          "price": "INCLUDE"
        },
        "shipping_address": "123 Main Street, City",
        "city_id": "507f1f77bcf86cd799439018",
        "region_id": "507f1f77bcf86cd799439019",
        "shipping_mobile_number": 9876543210,
        "shipping_country_code": "+91",
        "city_name": "Mumbai",
        "region_name": "Maharashtra",
        "created_at": "2025-06-26T09:55:27.925Z",
        "updated_at": "2025-06-26T09:55:27.925Z"
      }
    ]
  }
}
```

#### Response Fields

| Field                                                     | Type     | Required | Description                                            | Possible Values                                                    |
| ----------------------------------------------------      | -------- | -------- | -------------------------------                        | ---------------------------------------                            |
| `count`                                                   | Number   | Yes      | Total number of records                                | >= 0                                                               |
| `list`                                                    | Array    | Yes      | Array of orders                                        | Array (may be empty)                                               |
| `list[]._id`                                              | ObjectId | Yes      | Unique order identifier                                | MongoDB ObjectId                                                   |
| `list[].tenant_id`                                        | Number   | Yes      | Tenant identifier                                      | >= 1000                                                            |
| `list[].customer_user_role_id`                            | ObjectId | Yes      | Reference to customer user role                        | MongoDB ObjectId                                                   |
| `list[].external_id`                                      | String   | No       | External order identifier                              | String or null                                                     |
| `list[].sales_user_role_id`                               | ObjectId | Yes      | Reference to sales user role                           | MongoDB ObjectId                                                   |
| `list[].sales_person_name`                                | String   | Yes      | Salesperson name                                       | String                                                             |
| `list[].customer_name`                                    | String   | Yes      | Customer name                                          | String                                                             |
| `list[].customer_legal_name`                              | String   | Yes      | Customer legal name                                    | String                                                             |
| `list[].customer_primary_contact_name`                    | String   | No       | Primary contact name                                   | String or null                                                     |
| `list[].branch_id`                                        | ObjectId | Yes      | Reference to branch                                    | MongoDB ObjectId                                                   |
| `list[].order_number`                                     | String   | Yes      | Human-readable order number                            | String                                                             |
| `list[].order_remark`                                     | String   | No       | Order remarks                                          | String or null                                                     |
| `list[].order_punching_device_type`                       | String   | Yes      | Device type                                            | "TABLET"/"MOBILE"                                                  |
| `list[].order_punching_device_os`                         | String   | Yes      | Device OS                                              | "IOS"/"ANDROID"                                                    |
| `list[].order_app_type`                                   | String   | Yes      | App type                                               | "SALES_APP"/"CUSTOMER_APP"/"SUPERVISOR_APP"/"TENANT_OWNER"/"ADMIN" |
| `list[].order_creator_user_role_id`                       | ObjectId | No       | Reference to order creator                             | MongoDB ObjectId or null                                           |
| `list[].order_creator_name`                               | String   | No       | Order creator name                                     | String or null                                                     |
| `list[].order_status`                                     | String   | Yes      | Order status                                           | See status values in appendix                                      |
| `list[].order_status_track`                               | Array    | Yes      | Order status history                                   | Array (may be empty)                                               |
| `list[].order_status_track[].order_status`                | String   | Yes      | Status in tracking                                     | See status values in appendix                                      |
| `list[].order_status_track[].time`                        | Date     | Yes      | Status change time                                     | ISO 8601 date                                                      |
| `list[].order_status_track[].user_info`                   | Object   | No       | User who changed status                                | Object or null                                                     |
| `list[].order_status_track[].user_info.user_role_id`      | ObjectId | Yes\*    | User role ID                                           | MongoDB ObjectId                                                   |
| `list[].order_status_track[].user_info.name`              | String   | Yes\*    | User name                                              | String                                                             |
| `list[].total_amount`                                     | Number   | Yes      | Total order amount                                     | >= 0                                                               |
| `list[].total_tax`                                        | Number   | Yes      | Total tax amount                                       | >= 0                                                               |
| `list[].reduce_inventory`                                 | Boolean  | Yes      | Whether to reduce inventory                            | true/false                                                         |
| `list[].tax_calculation_info`                             | Object   | No       | Tax calculation details                                | Object or null                                                     |
| `list[].tax_calculation_info.tax_id`                      | ObjectId | Yes\*    | Reference to tax configuration                         | MongoDB ObjectId                                                   |
| `list[].tax_calculation_info.type`                        | String   | Yes\*    | Tax type                                               | "SINGLE"/"GROUP"                                                   |
| `list[].tax_calculation_info.tax_name`                    | String   | Yes\*    | Tax name                                               | String                                                             |
| `list[].tax_calculation_info.tax_rate`                    | Number   | No       | Tax rate percentage                                    | >= 0 or null                                                       |
| `list[].tax_calculation_info.tax_calculation`             | String   | Yes\*    | Tax calculation method                                 | "PERCENTAGE"/"AMOUNT"                                              |
| `list[].tax_calculation_info.calculated_tax`              | Number   | No       | Calculated tax amount                                  | >= 0 or null                                                       |
| `list[].tax_calculation_info.group_taxes`                 | Object   | No       | Group tax details (when type is GROUP)                 | Object or null                                                     |
| `list[].tax_calculation_info.group_taxes.tax_id`          | ObjectId | Yes\*    | Reference to group tax                                 | MongoDB ObjectId                                                   |
| `list[].tax_calculation_info.group_taxes.tax_name`        | String   | Yes\*    | Group tax name                                         | String                                                             |
| `list[].tax_calculation_info.group_taxes.tax_rate`        | Number   | Yes\*    | Group tax rate percentage                              | >= 0                                                               |
| `list[].tax_calculation_info.group_taxes.tax_calculation` | String   | Yes\*    | Group tax calculation method                           | "PERCENTAGE"/"AMOUNT"                                              |
| `list[].tax_calculation_info.group_taxes.calculated_tax`  | Number   | Yes\*    | Calculated group tax amount                            | >= 0                                                               |
| `list[].tax_calculation_info.price`                       | String   | Yes\*    | Price inclusion type                                   | "INCLUDE"/"EXCLUDE"                                                |
| `list[].shipping_address`                                 | String   | Yes      | Shipping address                                       | String                                                             |
| `list[].city_id`                                          | ObjectId | Yes      | Reference to city                                      | MongoDB ObjectId                                                   |
| `list[].region_id`                                        | ObjectId | Yes      | Reference to region                                    | MongoDB ObjectId                                                   |
| `list[].shipping_mobile_number`                           | Number   | Yes      | Shipping mobile number                                 | Number                                                             |
| `list[].shipping_country_code`                            | String   | Yes      | Country code                                           | String                                                             |
| `list[].city_name`                                        | String   | Yes      | City name                                              | String                                                             |
| `list[].region_name`                                      | String   | Yes      | Region name                                            | String                                                             |
| `list[].created_at`                                       | Date     | Yes      | Creation timestamp                                     | ISO 8601 date                                                      |
| `list[].updated_at`                                       | Date     | Yes      | Last update timestamp                                  | ISO 8601 date                                                      |

\*Required if parent object exists

**Note:** Orders are automatically filtered to include only those created within the last 3 months.

## Integration Notes

### 1. Initial Data Sync Flow

When implementing initial data synchronization, follow this recommended order:

1. **Master Data First** (Product Service)

   ```
   GET /productService/dataSync/masterData
   ```

   - Tax settings
   - Master prices
   - Master units

2. **User and Location Data** (User Service)

   ```
   GET /dataSync/tenantAppSetting
   GET /dataSync/userRoleSettings
   GET /dataSync/userRoles?isInitialSync=true
   GET /dataSync/regions?isInitialSync=true
   POST /dataSync/cities (with isInitialSync=true)
   ```

3. **Product Data** (Product Service)

   ```
   GET /productService/dataSync/categories?isInitialSync=true
   GET /productService/dataSync/products?isInitialSync=true
   GET /productService/dataSync/images
   ```

4. **User-Specific Data** (Product Service)

   ```
   GET /productService/dataSync/favoriteProducts
   GET /productService/dataSync/cartItems
   GET /productService/dataSync/orders
   ```

5. **Deal Data** (Product Service)
   ```
   GET /productService/dataSync/deals
   POST /productService/dataSync/dealProducts
   ```

### 2. Incremental Sync Strategy

For ongoing synchronization after initial sync:

```javascript
// Example incremental sync implementation
async function incrementalSync(endpoint, lastSync) {
  let hasMore = true;
  let cursor = null;
  let lastSyncedAt = lastSync?.timestamp || null;

  while (hasMore) {
    const params = {
      tenantId: 1131,
      perPage: 50,
      ...(lastSyncedAt && { lastSyncedAt }),
      ...(cursor && { cursor }),
    };

    const response = await fetch(`${endpoint}?${new URLSearchParams(params)}`);
    const data = await response.json();

    if (data.data.list.length > 0) {
      // Process records
      await processRecords(data.data.list);

      // Update cursor for next page
      const lastRecord = data.data.list[data.data.list.length - 1];
      cursor = lastRecord._id;
      lastSyncedAt = lastRecord.updated_at;
    }

    hasMore = data.data.list.length === params.perPage;
  }

  // Save sync state
  await saveSyncState(endpoint, { timestamp: lastSyncedAt, cursor });
}
```

### 3. Handling Special Cases

#### Composite IDs

Some endpoints use composite IDs (e.g., userRoleSettings uses `{tenantId}_{userRoleId}`):

```javascript
// Parse composite ID
const [tenantId, userRoleId] = compositeId.split("_");
```

#### User Context

Certain endpoints filter data based on the authenticated user:

- `favoriteProducts` - Returns only the current user's favorites
- `cartItems` - Returns only the current user's cart items
- `orders` - Returns only orders created by the current user

#### Time-Based Filtering

- `orders` endpoint automatically filters to last 3 months
- Consider implementing similar client-side filtering for other endpoints if needed

### 4. Performance Optimization

1. **Batch Processing**

   - Use larger `perPage` values (up to 100) for faster initial sync
   - Process records in batches to avoid memory issues

2. **Parallel Requests**

   - Sync independent data types in parallel:

   ```javascript
   await Promise.all([syncCategories(), syncMasterData(), syncUserRoles()]);
   ```

3. **Caching Strategy**

   - Cache master data (tax settings, units, prices) locally
   - Implement cache invalidation based on `updated_at` timestamps

4. **Network Optimization**
   - Implement retry logic with exponential backoff
   - Use compression for large payloads
   - Consider implementing delta sync for large datasets

### 5. Error Handling Best Practices

```javascript
async function syncWithRetry(syncFunction, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await syncFunction();
    } catch (error) {
      if (error.status === 401) {
        // Re-authenticate
        await refreshAuth();
      } else if (error.status === 429) {
        // Rate limited - wait before retry
        await wait(Math.pow(2, attempt) * 1000);
      } else if (error.status >= 500) {
        // Server error - retry with backoff
        if (attempt === maxRetries) throw error;
        await wait(Math.pow(2, attempt) * 1000);
      } else {
        // Client error - don't retry
        throw error;
      }
    }
  }
}
```

### 6. Data Consistency

1. **Referential Integrity**

   - Sync reference data (categories, brands, etc.) before dependent data (products)
   - Handle missing references gracefully

2. **Conflict Resolution**

   - Use `updated_at` timestamps for conflict detection
   - Implement appropriate merge strategies for conflicts

3. **Transaction Boundaries**
   - Group related updates in transactions where possible
   - Implement rollback mechanisms for failed syncs

### 7. Monitoring and Logging

Implement comprehensive logging for:

- Sync start/end times
- Number of records synced
- Errors and retries
- Performance metrics (requests/second, data volume)
- Sync state persistence

### 8. Security Considerations

1. **Token Management**

   - Implement secure token storage
   - Handle token expiration gracefully
   - Never log authentication tokens

2. **Data Validation**

   - Validate all received data before storage
   - Sanitize data to prevent injection attacks

3. **Encryption**
   - Use HTTPS for all API calls
   - Encrypt sensitive data at rest

### 9. Testing Recommendations

1. **Unit Tests**

   - Test pagination logic
   - Test error handling
   - Test data transformation

2. **Integration Tests**

   - Test full sync flows
   - Test incremental updates
   - Test error scenarios

3. **Performance Tests**
   - Test with large datasets
   - Test concurrent syncs
   - Test network failure scenarios

---

## Appendix

### A. Field Type Reference

| Type     | Description                           | Example                    |
| -------- | ------------------------------------- | -------------------------- |
| ObjectId | MongoDB ObjectId (24-char hex string) | "507f1f77bcf86cd799439011" |
| String   | Text string                           | "Product Name"             |
| Number   | Numeric value (integer or decimal)    | 42, 19.99                  |
| Boolean  | True/False value                      | true, false                |
| Date     | ISO 8601 formatted date string        | "2024-01-15T10:30:00.000Z" |
| Array    | List of values                        | ["value1", "value2"]       |
| Object   | Nested object structure               | { "key": "value" }         |
| null     | Null value                            | null                       |

### B. Common Status Values

#### Order Status

- `PENDING` - Order created but not processed
- `RECEIVED` - Order received by system
- `PROCESSING` - Order being processed
- `SHIPPED` - Order shipped
- `DELIVERED` - Order delivered
- `CANCELLED` - Order cancelled

#### Deal Status

- `SCHEDULED` - Deal scheduled for future
- `RUNNING` - Deal currently active
- `ENDED` - Deal has ended
- `PAUSED` - Deal temporarily paused

### C. Useful Resources

- [MongoDB ObjectId Documentation](https://docs.mongodb.com/manual/reference/method/ObjectId/)
- [ISO 8601 Date Format](https://en.wikipedia.org/wiki/ISO_8601)
- [HTTP Status Codes](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status)

---

**Document Version:** 1.0  
**Last Updated:** November 2024  
**API Version:** v1

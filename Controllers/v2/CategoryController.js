const CategoryModel = new (require("../../Models/CategoryModel"))()
const CategoryService = new (require("../../Services/category/CategoryService"))()

const { VALUES } = require("../../Configs/constants")
const { toLeanOption } = require("../../Utils/helpers")

class CategoryController {

    // Get all category list with total products count.
    getAllCategoryList = async (req, res) => {
        try {
            const {
                tenantId,
                familyIds = [],
            } = req.body

            const categoryFilter = {
                "tenant_id": tenantId,
                "is_active": true,
                "is_deleted": false,
            }

            if (familyIds.length) {
                categoryFilter["$or"] = [
                    {
                        type: {
                            $in: [
                                VALUES.category.CATEGORY,
                                VALUES.category.SUBCATEGORY,
                            ]
                        }
                    },
                    {
                        type: VALUES.category.FAMILY,
                        _id: {
                            "$in": familyIds
                        }
                    }
                ]
            }

            const categoryList = await CategoryModel.allCategory(
                categoryFilter,
                {
                    "category_name": 1,
                    "secondary_language_category_name": 1,
                    "type": 1,
                    "parent_id": 1,
                    "sequence": 1,
                },
                {
                    ...toLeanOption,
                    "sort": {
                        "sequence": 1
                    }
                }
            )

            if (!categoryList.length) {
                return res.handler.success(null, [])
            }

            const { finalList, totalProductCount } = await CategoryService.getCategoriesProductCount({ ...req.body, categoryList })

            return res.handler.success(null, {
                totalProductCount,
                list: finalList
            })
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = CategoryController;

const difference = require("lodash.difference");

const {
    NEW_PRODUCT_TYPE,
    INTEGRATION_CHANNELS
} = require("../../Configs/constants");

const ProductModel = new (require("../../Models/ProductModel"));
const InternalServiceModel = new (require("../../Models/InternalServiceModel"));

module.exports = class {

    syncRestockedItems = async () => {
        const profiler = logger.startTimer()
        logger.info("Started: Sync restocked items")

        try {
            const integrationCredentialsParams = {
                filter: {
                    name: INTEGRATION_CHANNELS.SAP_SERVICE,
                    is_active: true
                },
                projection: "configurations tenant_id",
                options: {
                    lean: true
                }
            }

            const integrationCredentials = await InternalServiceModel.getIntegrationCredentials(integrationCredentialsParams)

            for (let i = 0; i < integrationCredentials.length; i++) {
                try {
                    const sapIntegrationCredentials = integrationCredentials[i];
                    const tenant_id = sapIntegrationCredentials.tenant_id

                    const fetchItems = [
                        InternalServiceModel.getRecentlyRestockedItems({
                            sapIntegrationCredentials
                        }),
                        ProductModel.findProducts(
                            {
                                is_restocked: true,
                                tenant_id,
                                type: {
                                    $in: [
                                        NEW_PRODUCT_TYPE.SINGLE,
                                        NEW_PRODUCT_TYPE.VARIANT
                                    ]
                                }
                            },
                            "item_number",
                            {
                                lean: true
                            }
                        )
                    ]

                    const [
                        restockedItemNumbers,
                        restockedProducts
                    ] = await Promise.all(fetchItems)

                    const alreadyRestockedItemNumbers = restockedProducts.map(product => product.item_number)

                    const newItemNumbers = difference(restockedItemNumbers, alreadyRestockedItemNumbers)
                    const removingItemNumbers = difference(alreadyRestockedItemNumbers, restockedItemNumbers)

                    const updateProductFilter = (itemNumbers, isRestocked) => {

                        const parentProductQuery = {
                            type: NEW_PRODUCT_TYPE.PARENT,
                            $and: [
                                {
                                    active_variant_item_numbers: {
                                        $in: itemNumbers
                                    }
                                }
                            ]
                        }

                        if (!isRestocked) {
                            parentProductQuery.$and.push({
                                active_variant_item_numbers: {
                                    $nin: restockedItemNumbers
                                }
                            })
                        }

                        return {
                            $or: [
                                parentProductQuery,
                                {
                                    type: {
                                        $in: [
                                            NEW_PRODUCT_TYPE.SINGLE,
                                            NEW_PRODUCT_TYPE.VARIANT
                                        ]
                                    },
                                    item_number: {
                                        $in: itemNumbers
                                    }
                                }
                            ]
                        }
                    }

                    const tasks = []

                    if (newItemNumbers.length) {
                        tasks.push(ProductModel.updateProducts(
                            updateProductFilter(newItemNumbers, true),
                            {
                                is_restocked: true,
                                restocked_at: new Date()
                            }
                        ))
                    }

                    if (removingItemNumbers.length) {
                        tasks.push(ProductModel.updateProducts(
                            updateProductFilter(removingItemNumbers, false),
                            {
                                is_restocked: false,
                            }
                        ))
                    }

                    if (tasks.length) {
                        await Promise.all(tasks)
                    }
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "SYNC RESTOCKED ITEMS" })
            logger.info("Completed: Sync restocked items")
        }
    }

}

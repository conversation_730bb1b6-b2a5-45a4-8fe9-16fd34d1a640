const excelJS = require("exceljs")
const path = require("path")

const {
    PRODUCT_TYPE,
    DATA_SHEET,
    FILE_PATH,
    VALUES,
    BUCKET_TYPE,
} = require("../Configs/constants");

const {
    httpService,
    generateFileFromS3Object,
} = require("../Utils/helpers");

const MasterDataModel = new (require("../Models/MasterDataModel"))();
const DataSheetModel = new (require("../Models/DataSheetModel"))();
const ProductModel = new (require("../Models/ProductModel"))();
const ProductDataSheetModel = new (require("../Models/ProductDataSheetModel"))()
const ProductUpdateDataSheetModel = new (require("../Models/data_sheet/ProductUpdateDataSheetModel"))()
const ProductStatusUpdateDataSheetModel = new (require("../Models/data_sheet/ProductStatusUpdateDataSheetModel"))()
const FileUpload = require('../Configs/awsUploader').S3Upload;
const PriceDatasheetModel = require("../Models/PriceDatasheetModel")

class DataSheetController {

    getPriceMappingProducts = async (tenantId) => {
        try {
            const populationArray = [
                {
                    "path": "price_mappings.master_price_id",
                    "select": {
                        "price_name": 1,
                        "price_id": 1,
                    }
                }
            ]

            const variantPopulation = [
                {
                    "path": "price_mappings.master_price_id",
                    "select": {
                        "price_name": 1,
                        "price_id": 1,
                    }
                },
                {
                    "path": "parent_id",
                    "select": {
                        "title": 1,
                        "item_number": 1,
                        "created_at": 1,
                    }
                },
                {
                    "path": "variant_value_id",
                    "select": { "name": 1 }
                },
                {
                    "path": "group_value_id",
                    "select": { "name": 1 }
                }
            ]
            const promises = []

            const filter = {
                "tenant_id": tenantId,
                "is_deleted": false,
            }

            const singleProductPromise =
                ProductModel.getProductsWithPopulation(
                    {
                        ...filter,
                        "type": PRODUCT_TYPE.SINGLE,
                    },
                    {
                        "title": 1,
                        "item_number": 1,
                        "price_mappings": 1,
                        "type": 1,
                        "created_at": 1,
                        "is_active": 1,
                    },
                    {
                        "lean": true,
                        "populate": populationArray
                    },
                )

            const variantProductPromise =
                ProductModel.getProductsWithPopulation(
                    {
                        ...filter,
                        "type": PRODUCT_TYPE.VARIANT,
                    },
                    {
                        "variant_value_id": 1,
                        "group_value_id": 1,
                        "price_mappings": 1,
                        "parent_id": 1,
                        "item_number": 1,
                        "created_at": 1,
                        "is_active": 1,
                    },
                    {
                        "lean": true,
                        "populate": variantPopulation
                    },
                )

            promises.push(
                singleProductPromise,
                variantProductPromise,
            )

            const [singleProducts, variantProducts] = await Promise.all(promises)

            const newVariantProducts = variantProducts.map((product) => {
                const price = product.price_mappings
                    .map(element => ({
                        "price": element.price,
                        "priceName": element.master_price_id?.price_name,
                        "priceId": element.master_price_id?.price_id
                    }))

                const group_name = product.group_value_id?.name
                const variant_name = product.variant_value_id?.name || ""

                return ({
                    "variant_id": product._id,
                    "name":
                        product.parent_id?.title +
                        " - " + (
                            group_name
                                ? group_name + " / "
                                : ""
                        )
                        + variant_name,
                    "itemNumber": product.item_number,
                    "priceDetails": price,
                    "created_at": product.parent_id?.created_at,
                    "parentItemNumber": product.parent_id?.item_number,
                    "isActive": product.is_active,
                })
            })

            const newSingleProducts = singleProducts.map((product) => {
                const price = product.price_mappings
                    .map(element => ({
                        "price": element.price,
                        "priceName": element.master_price_id?.price_name,
                        "priceId": element.master_price_id?.price_id
                    }))

                return ({
                    "name": product.title,
                    "itemNumber": product.item_number,
                    "priceDetails": price,
                    "created_at": product.created_at,
                    "isActive": product.is_active,
                })
            })

            return [...newVariantProducts, ...newSingleProducts]
        }
        catch (error) {
            throw error
        }
    }

    getPriceDataSheetUrl = async (tenantId, tenantProducts) => {
        try {
            const allProduct = await this.getPriceMappingProducts(tenantId)
            const tenantMasterPriceList = await MasterDataModel.masterPriceList(tenantId)

            if (tenantProducts) {
                return {
                    tenantMasterPriceList,
                    allProduct
                }
            }

            const excelFile = await DataSheetModel.priceExcel(
                tenantId,
                tenantMasterPriceList,
                allProduct
            )
            return excelFile
        }
        catch (error) {
            throw error
        }
    }

    getInventoryDataSheetUrl = async (req, body) => {
        try {
            const { tenantId, tenantProducts } = body || req.query

            const variantPopulation = [
                {
                    "path": "parent_id",
                    "select": {
                        "title": 1,
                        "item_number": 1,
                        "created_at": 1,
                    }
                },
                {
                    "path": "variant_value_id",
                    "select": { "name": 1 }
                },
                {
                    "path": "group_value_id",
                    "select": { "name": 1 }
                }
            ]
            const promises = []

            const singleProductPromise =
                ProductModel.getProductsWithPopulation(
                    {
                        "tenant_id": tenantId,
                        "is_deleted": false,
                        "type": PRODUCT_TYPE.SINGLE,
                    },
                    {
                        "title": 1,
                        "item_number": 1,
                        "inventory_mappings": 1,
                        "type": 1,
                        "created_at": 1,
                        "is_active": 1,
                    },
                    {
                        "lean": true
                    }
                )

            const variantProductPromise =
                ProductModel.getProductsWithPopulation(
                    {
                        "tenant_id": tenantId,
                        "is_deleted": false,
                        "type": PRODUCT_TYPE.VARIANT,
                    },
                    {
                        "variant_value_id": 1,
                        "group_value_id": 1,
                        "inventory_mappings": 1,
                        "parent_id": 1,
                        "item_number": 1,
                        "created_at": 1,
                        "is_active": 1,
                    },
                    {
                        "lean": true,
                        "populate": variantPopulation,
                    },
                )

            promises.push(
                singleProductPromise,
                variantProductPromise,
            )

            const [singleProducts, variantProducts] = await Promise.all(promises)

            const newVariantProducts = variantProducts.map((product) => {
                const group_name = product.group_value_id?.name
                const variant_name = product.variant_value_id?.name || ""

                return ({
                    "_id": product._id,
                    "name":
                        product.parent_id?.title +
                        " - " + (
                            group_name
                                ? group_name + " / "
                                : ""
                        )
                        + variant_name,
                    "itemNumber": product.item_number,
                    "inventoryDetails": product.inventory_mappings,
                    "parentItemNumber": product.parent_id?.item_number,
                    "created_at": product.parent_id?.created_at,
                    "isActive": product.is_active,
                })
            })

            const newSingleProducts = singleProducts.map((product) => {
                return ({
                    "_id": product._id,
                    "name": product.title,
                    "itemNumber": product.item_number,
                    "inventoryDetails": product.inventory_mappings,
                    "created_at": product.created_at,
                    "isActive": product.is_active,
                })
            })
            const allProduct =
                [...newVariantProducts, ...newSingleProducts]

            const branches = await httpService(req)
                .get(
                    "tenantBranches",
                    {
                        "params": {
                            "tenantId": tenantId
                        }
                    }
            )

            if (tenantProducts) {
                return {
                    "inventory": branches?.data?.data,
                    allProduct
                }
            }

            const excelFile = await DataSheetModel.inventoryExcel(
                tenantId,
                branches?.data?.data,
                allProduct
            )
            return excelFile
        }
        catch (error) {
            throw error
        }
    }

    getProductExportFile = async (tenantId, operationType, fileType, apiVersion) => {
        try {
            let key = FILE_PATH.DATA_SHEET.PRODUCT_SAMPLE
            const fileName = `Sample_Product_Import_Template_${new Date().toISOString().split('T')[0]}_${new Date().getTime()}.xlsx`
            const folderName = "DataSheets"

            if (
                operationType === DATA_SHEET.OPERATION_TYPE.UPDATE ||
                fileType === DATA_SHEET.FILE_TYPE.WITH_GROUP
            ) {
                key = FILE_PATH.DATA_SHEET.PRODUCT_SAMPLE_WITH_GROUP
            }

            await generateFileFromS3Object(process.env.AWS_LOCALES_BUCKET_BASE_URL + key, folderName, fileName)
            const sheetPath = path.join(__dirname, `/../Assets/${folderName}/${fileName}`)

            const workbook = new excelJS.Workbook()
            const worksheet = await workbook.xlsx.readFile(sheetPath)

            await DataSheetModel.setValuesSheetInProduct(tenantId, worksheet)

            const familyArgs = [tenantId, worksheet, "family", VALUES.category.FAMILY, VALUES.category.CATEGORY, true]
            await DataSheetModel.setCategorySheetInProduct(...familyArgs)

            if (operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
                await ProductDataSheetModel.setProductsInSheet(tenantId, worksheet, apiVersion)
            }
            const categoryArgs = [tenantId, worksheet, "category", VALUES.category.CATEGORY, VALUES.category.SUBCATEGORY, false]
            await DataSheetModel.setCategorySheetInProduct(...categoryArgs)

            // Fix the issue: Losing formatting/data validations after writing the file
            workbook.clearThemes()

            await workbook.xlsx.writeFile(sheetPath)

            const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

            await fileUpload.uploadFiles(
                `${FILE_PATH.DATA_SHEET.EXPORT}/${tenantId}/product/${operationType.toLocaleLowerCase()}/export`,
                'product.xlsx',
                sheetPath
            )

            const url = `${VALUES.awsPublicBucketBaseURL + process.env.AWS_BUCKET_FOLDER}dataSheet/${tenantId}/product/${operationType.toLocaleLowerCase()}/export/product.xlsx`
            return url
        }
        catch (error) {
            throw new Error(error)
        }
    }

    getDataSheet = async (req, res) => {
        try {
            const {
                tenantId,
                dataType,
                operationType,
                fileType = DATA_SHEET.FILE_TYPE.SIMPLE,
                apiVersion,
            } = req.query

            let excelFile

            if (dataType === DATA_SHEET.DATA_TYPE.PRODUCT) {
                excelFile = await this.getProductExportFile(tenantId, operationType, fileType, apiVersion)
            }
            else if (dataType === DATA_SHEET.DATA_TYPE.PRICE) {
                excelFile = await this.getPriceDataSheetUrl(tenantId)
            }
            else if (dataType === DATA_SHEET.DATA_TYPE.INVENTORY) {
                excelFile = await this.getInventoryDataSheetUrl(req)
            }
            return res.handler.success(null, excelFile)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    updateDataSheet = async (req, res) => {
        try {
            const {
                tenantId,
                fileName,
                dataType,
                operationType,
                type,
                updateType = DATA_SHEET.UPDATE_TYPE.DETAILS,
                importData,
            } = req.body

            const folderName = "DataSheets"
            const sheetPath = path.join(__dirname, `/../Assets/${folderName}/${fileName}`)

            const isUpdateTypeStatus = updateType === DATA_SHEET.UPDATE_TYPE.STATUS
            let filePath = fileName

            if (isUpdateTypeStatus) {
                filePath = DATA_SHEET.UPDATE_TYPE.STATUS.toLowerCase() + "/" + filePath
            }

            const s3FilePath =
                VALUES.awsPublicBucketBaseURL +
                VALUES.awsBucketName +
                `dataSheet/${tenantId}/${dataType.toLowerCase()}/${operationType.toLowerCase()}/${type.toLocaleLowerCase()}/${filePath}`

            await generateFileFromS3Object(s3FilePath, folderName, fileName)

            const workbook = new excelJS.Workbook()
            let fileExtension = "csv"

            if (
                dataType === DATA_SHEET.DATA_TYPE.PRODUCT &&
                !isUpdateTypeStatus
            ) {
                fileExtension = "xlsx"
            }
            const worksheet = await workbook[fileExtension].readFile(sheetPath)

            if (dataType === DATA_SHEET.DATA_TYPE.PRODUCT) {
                let sheetUpdate = false

                if (isUpdateTypeStatus) {
                    sheetUpdate = await ProductStatusUpdateDataSheetModel.updateProductImportDataSheet(req, worksheet)
                }
                else if (operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
                    sheetUpdate = await ProductDataSheetModel.updateProductImportDataSheet(req, worksheet)
                }
                else if (operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
                    sheetUpdate = await ProductUpdateDataSheetModel.updateProductImportDataSheet(req, worksheet)
                }

                if (!sheetUpdate) {
                    return res.handler.success(null, false)
                }
            }
            else if (dataType === DATA_SHEET.DATA_TYPE.PRICE) {
                if (operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
                    if (type === DATA_SHEET.TYPE.IMPORT) {
                        const Model = new PriceDatasheetModel(this)

                        const sheetUpdate = await Model.updatePriceImportDataSheet(tenantId, operationType, dataType, worksheet, req)

                        if (!sheetUpdate) {
                            return res.handler.success(null, false)
                        }
                    }
                }
            }
            else if (dataType === DATA_SHEET.DATA_TYPE.INVENTORY) {
                if (operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
                    if (type === DATA_SHEET.TYPE.IMPORT) {
                        const Model = new PriceDatasheetModel(this)

                        const sheetUpdate = await Model.updatePriceImportDataSheet(tenantId, operationType, dataType, worksheet, req)

                        if (!sheetUpdate) {
                            return res.handler.success(null, false)
                        }
                    }
                }
            }
            await workbook[fileExtension].writeFile(sheetPath)

            const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)
            let importUploadPath = "import"

            if (isUpdateTypeStatus) {
                importUploadPath += "/" + DATA_SHEET.UPDATE_TYPE.STATUS.toLowerCase()
            }

            await fileUpload.uploadFiles(
                `${FILE_PATH.DATA_SHEET.EXPORT}/${tenantId}/${dataType.toLowerCase()}/${operationType.toLowerCase()}/${importUploadPath}`,
                fileName,
                sheetPath
            )

            return res.handler.success(null, true)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = DataSheetController;

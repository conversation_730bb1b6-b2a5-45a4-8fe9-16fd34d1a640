const excelJS = require("exceljs")
const fs = require('fs');
const path = require("path")

const difference = require('lodash.difference');

const InternalServiceModel = new (require("../Models/InternalServiceModel"))()
const OrderExportTaskModel = new (require("../Models/OrderExportTaskModel"))()
const OrderModel = new (require("../Models/OrderModel"));
const MasterDataModel = new (require("../Models/MasterDataModel"))();

const FileUpload = require('../Configs/awsUploader').S3Upload;

const { sendEmail } = require("../Configs/Mailer")
const { formatAmount, toLeanOption } = require("../Utils/helpers")
const { sentOrderExportMessageToSQS } = require("../SQS/OrderExportQueue")

const {
    FILE_PATH,
    BUCKET_TYPE,
    EMAIL_ID,
    ORDER_EXPORT,
    PRIMITIVE_ROLES,
    VALUES,
} = require("../Configs/constants");

module.exports = class {

    getOrderStatusList = async (req, res) => {
        try {
            const data = {
                orderStatusList: Object.keys(ORDER_EXPORT.ORDER_STATUS_LIST)
            }

            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    exportOrders = async (req, res) => {
        try {
            const {
                tenantId,
                startDate,
                endDate,
                timezone,
                orderStatusList
            } = req.body

            const filename = `order_export_report_${new Date().toISOString().split('T')[0]}_${new Date().getTime()}.xlsx`

            const filter = {
                tenant_id: tenantId
            }

            if (req.headers.userRoleDetails.name === PRIMITIVE_ROLES.SYSTEM_OWNER) {
                filter.user_role_id = req.headers.userroleid
            }
            else {
                filter.user_role_name = {
                    $ne: PRIMITIVE_ROLES.SYSTEM_OWNER
                }
            }

            const lastOrderExportTask = await OrderExportTaskModel.getLastOrderExportTask(filter, "status")

            if (lastOrderExportTask?.status === ORDER_EXPORT.STATUS.PENDING)
                return res.handler.forbidden("order_export_already_in_queue")

            if (lastOrderExportTask?.status === ORDER_EXPORT.STATUS.IN_PROGRESS)
                return res.handler.forbidden("order_export_already_in_progress")

            const order = await OrderModel.findOrder(
                {
                    tenant_id: tenantId,
                    order_status: {
                        $in: orderStatusList
                    },
                    created_at: {
                        $gte: new Date(momentTimezone.tz(startDate, timezone).startOf("day")),
                        $lte: new Date(momentTimezone.tz(endDate, timezone).endOf("day")),
                    }
                },
                undefined,
                {
                    lean: true,
                }
            )
            if (!order)
                return res.handler.notFound("order_not_found");

            const newTask = await OrderExportTaskModel.createOrderExportTask({
                filename,
                status: ORDER_EXPORT.STATUS.PENDING,
                tenant_id: tenantId,
                user_role_name: req.headers.userRoleDetails.name,
                user_role_id: req.headers.userroleid,
                to_user_mail_id: req.headers.userDetails.email,
                start_date: startDate,
                end_date: endDate,
                timezone,
                order_status_list: orderStatusList
            })

            await sentOrderExportMessageToSQS(newTask)

            return res.handler.success("order_export_queued")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    startOrderExportTask = async (exportTask) => {
        const {
            tenant_id,
            filename,
            start_date,
            end_date,
            timezone,
            order_status_list,
            to_user_mail_id,
            _id
        } = exportTask

        let sheetPath
        try {
            const task = await OrderExportTaskModel.findOrderExportTask(
                {
                    _id,
                },
                "status",
                toLeanOption
            )
            if (task.status !== ORDER_EXPORT.STATUS.PENDING) {
                //If the task status is not pending, stop the process
                return
            }

            await OrderExportTaskModel.updateOrderExportTaskById(_id, {
                status: ORDER_EXPORT.STATUS.IN_PROGRESS
            })

            const rows = await this.getOrderRows(
                start_date,
                end_date,
                timezone,
                order_status_list,
                tenant_id
            )

            sheetPath = await this.createOrderExportFile(rows, filename)

            const fileUrl = await this.uploadOrderExportFile(tenant_id, filename, sheetPath)

            await this.sendOrderExportEmail(
                exportTask,
                fileUrl,
            )

            await OrderExportTaskModel.updateOrderExportTaskById(_id, {
                status: ORDER_EXPORT.STATUS.COMPLETED
            })
        }
        catch (err) {
            let errorMessages = [
                err.message
            ]

            try {
                await OrderExportTaskModel.updateOrderExportTaskById(_id, {
                    status: ORDER_EXPORT.STATUS.FAILED,
                    failure_message: err.message
                })

                if (sheetPath) {
                    fs.unlinkSync(sheetPath)
                }
            }
            catch (error) {
                errorMessages.push(error.message)
                logger.error(error)
            }
            finally {
                try {
                    await this.sendOrderFailureEmail(
                        to_user_mail_id,
                        tenant_id,
                        errorMessages
                    )
                }
                catch (error) {
                    logger.error(error)
                }
                finally {
                    throw err
                }
            }
        }
    }

    createOrderExportFile = async (
        rows,
        filename
    ) => {
        const folderName = "DataSheets"

        const sheetPath = path.join(__dirname, `/../Assets/${folderName}/${filename}`)

        const workbook = new excelJS.Workbook()

        const worksheet = workbook.addWorksheet("OrderSheet")
        worksheet.properties.defaultRowHeight = 15
        worksheet.properties.defaultColWidth = 20

        worksheet.columns = [
            {
                header: "Order Number",
                key: "order_number",
            },
            {
                header: "Order Status",
                key: "order_status",
            },
            {
                header: "Created By",
                key: "order_creator_name",
            },
            {
                header: "Device",
                key: "order_punching_device_type",
            },
            {
                header: "Operating System",
                key: "order_punching_device_os",
            },
            {
                header: "Ordered By",
                key: "order_app_type",
            },
            {
                header: "Subtotal",
                key: "total_amount",
            },
            {
                header: "Tax",
                key: "total_tax",
            },
            {
                header: "Total Amount",
                key: "final_amount",
            },
            {
                header: "Price List",
                key: "price_list",
            },
            {
                header: "External Id",
                key: "external_id",
            },
            {
                header: "Customer Name",
                key: "customer_name",
            },
            {
                header: "Legal Name",
                key: "customer_legal_name",
            },
            {
                header: "Contact Name",
                key: "customer_primary_contact_name",
            },
            {
                header: "Mobile Number",
                key: "customer_mobile_number",
            },
            {
                header: "Sales Person",
                key: "sales_person_name",
            },
            {
                header: "Shipping Mobile Number",
                key: "shipping_mobile_number",
            },
            {
                header: "Shipping Address",
                key: "shipping_address",
                width: 35
            },
            {
                header: "City",
                key: "city_name",
            },
            {
                header: "Region",
                key: "region_name",
            },
            {
                header: "Coordinates",
                key: "gps_coordinates",
                width: 35,
                style: {
                    font: {
                        color: { argb: 'FF0000FF' }, // Blue color
                        underline: true
                    }
                }
            },
            {
                header: "Pre Approved",
                key: "pre_approved",
            },
            {
                header: "Created At",
                key: "created_at"
            }
        ]

        worksheet.addRows(rows)

        worksheet.getRow(1).eachCell(cell => {
            cell.font = {
                bold: true,
                underline: false,
                color: { argb: 'FF000000' }, // Blue color
            }
        })

        await workbook.xlsx.writeFile(sheetPath)

        return sheetPath
    }

    getOrderRows = async (
        startDate,
        endDate,
        timezone,
        orderStatusList,
        tenantId
    ) => {
        const filter = {
            tenant_id: tenantId,
            order_status: {
                $in: orderStatusList
            },
            created_at: {
                $gte: new Date(momentTimezone.tz(startDate, timezone).startOf("day")),
                $lte: new Date(momentTimezone.tz(endDate, timezone).endOf("day")),
            }
        }

        const limit = 500

        const ordersCount = await OrderModel.countOrders(filter)
        const totalPages = Math.ceil(ordersCount / limit)

        let allOrders = []
        let customerUserRoleIdSet = new Set()
        let priceIds = new Set()

        for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
            const orders = await OrderModel.findOrders(
                filter,
                undefined,
                {
                    lean: true,
                    sort: {
                        created_at: 1,
                    },
                    skip: limit * (currentPage - 1),
                    limit
                }
            )

            orders.forEach(order => {
                customerUserRoleIdSet.add(order.customer_user_role_id.toString())
                allOrders.push(order)

                order.master_price_ids.forEach(priceId => {
                    priceIds.add(priceId.toString())
                })
            })
        }

        // Paginate customer data
        const customerLimit = 100
        const customerUserRoleIds = Array.from(customerUserRoleIdSet)
        const customerCount = customerUserRoleIds.length
        const totalCustomerPages = Math.ceil(customerCount / customerLimit)

        const customerPromises = []

        for (let page = 1; page <= totalCustomerPages; page++) {
            const perPage = customerLimit
            const skip = (page - 1) * perPage
            const customerIds = customerUserRoleIds.slice(skip, skip + perPage)

            const customerParams = {
                filter: {
                    _id: {
                        $in: customerIds
                    }
                },
                projection: "customer_name customer_first_name customer_last_name external_id customer_legal_name shipping_address shipping_mobile_number shipping_country_code collection_name gps_coordinates",
                options: {
                    lean: true,
                    populate: [
                        {
                            path: 'user_id',
                            select: "country_code mobile_number",
                        },
                        {
                            path: 'shipping_city_id',
                            select: 'name'
                        },
                        {
                            path: 'shipping_region_id',
                            select: 'name'
                        }
                    ]
                },
            }

            customerPromises.push(
                InternalServiceModel.getCustomers(customerParams)
            )
        }
        let customers = []
        const customerResponse = await Promise.allSettled(customerPromises)

        customerResponse.forEach(result => {
            const {
                status,
                value,
                reason,
            } = result

            if (status === "fulfilled") {
                customers = customers.concat(value)
            }
            else if (status === "rejected") {
                logger.error(reason)
            }
        })

        const tasks = [
            MasterDataModel.findMasterPriceList(
                {
                    _id: {
                        $in: Array.from(priceIds)
                    }
                },
                "price_name",
                {
                    lean: true
                }
            ),
            InternalServiceModel.tenantAppSettings(tenantId, "decimal_points")
        ]

        const [
            masterPriceLists,
            tenantAppSettings
        ] = await Promise.all(tasks)

        const customerData = customers.reduce((data, customer) => {
            data[customer._id] = customer
            return data
        }, {})

        const priceListData = masterPriceLists.reduce((data, priceList) => {
            data[priceList._id] = priceList
            return data
        }, {})

        const decimal_points = tenantAppSettings?.decimal_points ?? 0

        return allOrders.map(order => {
            const row = {
                ...order,
                order_app_type: ORDER_EXPORT.APP_TYPE[order.order_app_type],
                order_punching_device_type: ORDER_EXPORT.DEVICE_TYPES[order.order_punching_device_type],
                order_punching_device_os: ORDER_EXPORT.DEVICE_OS[order.order_punching_device_os],
                order_status: ORDER_EXPORT.ORDER_STATUS_LIST[order.order_status],
                total_amount: formatAmount(order.total_amount, decimal_points),
                total_tax: formatAmount(order.total_tax, decimal_points),
                final_amount: formatAmount(order.total_amount + order.total_tax, decimal_points),
                created_at: momentTimezone(order.created_at).tz(timezone).format("DD/MM/YYYY"),
                pre_approved: order.pre_approved ? "Yes" : "No",
                price_list: order.master_price_ids?.map(
                    priceListId => {
                        return priceListData[priceListId].price_name
                    }
                ).join(", ") ?? "",
            }

            const customer = customerData[order.customer_user_role_id]
            if (customer) {
                row.external_id = customer.external_id
                row.customer_name = customer.customer_name
                row.customer_legal_name = customer.customer_legal_name
                row.customer_primary_contact_name = customer.customer_first_name + " " + customer.customer_last_name

                row.shipping_address = customer.shipping_address

                row.city_name = customer.shipping_city_id?.name
                row.region_name = customer.shipping_region_id?.name

                if (customer.shipping_mobile_number) {
                    if (customer.shipping_country_code) {
                        row.shipping_mobile_number = customer.shipping_country_code + " " + customer.shipping_mobile_number
                    }
                    else {
                        row.shipping_mobile_number = customer.shipping_mobile_number
                    }
                }
                else {
                    row.shipping_mobile_number = undefined
                }

                if (customer.user_id?.mobile_number) {
                    if (customer.user_id?.country_code) {
                        row.customer_mobile_number = customer.user_id?.country_code + " " + customer.user_id?.mobile_number
                    }
                    else {
                        row.customer_mobile_number = customer.user_id?.mobile_number
                    }
                }
                else {
                    row.customer_mobile_number = undefined
                }

                if (
                    customer.gps_coordinates?.latitude &&
                    customer.gps_coordinates?.longitude
                ) {
                    row.gps_coordinates = {
                        text: `${customer.gps_coordinates.latitude},${customer.gps_coordinates.longitude}`,
                        hyperlink: `https://maps.google.com/?q=${customer.gps_coordinates.latitude},${customer.gps_coordinates.longitude}`
                    }
                }
                else {
                    row.gps_coordinates = undefined
                }
            }

            return row
        })
    }

    uploadOrderExportFile = async (
        tenantId,
        filename,
        sheetPath
    ) => {
        const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

        await fileUpload.uploadFiles(
            FILE_PATH.DATA_SHEET.EXPORT +
            `/${tenantId}/order/update/export`,
            filename,
            sheetPath
        )

        return FILE_PATH.S3_URL.DATA_SHEET.ORDER_EXPORT(filename, tenantId)
    }

    getSupportEmailIds = (userEmail) => {
        return difference([EMAIL_ID.DEEP], [userEmail])
    }

    sendOrderExportEmail = async (
        exportTask,
        excelFile,
    ) => {
        const {
            tenant_id,
            start_date,
            end_date,
            order_status_list,
            to_user_mail_id
        } = exportTask

        let status = "All"
        if (order_status_list.length < Object.keys(ORDER_EXPORT.ORDER_STATUS_LIST).length) {
            status = order_status_list.map(
                orderStatus => {
                    return ORDER_EXPORT.ORDER_STATUS_LIST[orderStatus]
                }
            ).join(", ")
        }

        const payload = {
            from: EMAIL_ID.NOTIFICATION,
            to: to_user_mail_id,
            subject: "Success: Order Export Process",
            data: {
                html: `
                Order export processed successfully.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenant_id}
                <br/><br/>

                <b> Start Date: </b> ${moment(start_date).format("DD/MM/YYYY")}
                <br/><br/>

                <b> End Date: </b> ${moment(end_date).format("DD/MM/YYYY")}
                <br/><br/>

                <b> Status: </b> ${status}
                <br/><br/>

                <b> Excel Sheet URL: </b> ${excelFile}
                `
            },
        }

        await sendEmail(payload)
    }

    sendOrderFailureEmail = async (
        userEmail,
        tenantId,
        errors
    ) => {
        const payload = {
            from: EMAIL_ID.NOTIFICATION,
            subject: "Failed: Order Export Process",
            data: {
                html: `
                Unable to generate an excel file for order export.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Reason: </b>
                <br/>

                ${errors.join(",<br/>")}
                `
            },
        }

        let tasks = [
            sendEmail({
                to: userEmail,
                ...payload,
            })
        ]

        const supportEmails = this.getSupportEmailIds(userEmail)
        if (supportEmails.length) {
            tasks.push(sendEmail({
                to: supportEmails,
                ...payload,
            }))
        }

        await Promise.all(tasks)
    }

    deleteOldOrderExportTasks = async () => {
        const profiler = logger.startTimer()
        logger.info("Started: Delete old order export tasks")

        try {
            const filter = {
                created_at: {
                    $lte: moment().subtract(4, "day")
                },
                status: {
                    $in: [
                        ORDER_EXPORT.STATUS.COMPLETED,
                        ORDER_EXPORT.STATUS.FAILED,
                        ORDER_EXPORT.STATUS.IN_PROGRESS,
                    ]
                }
            }

            const orderExportsCount = await OrderExportTaskModel.countTasks(filter)

            const limit = 500
            const totalPages = Math.ceil(orderExportsCount / limit)

            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC)

            for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
                try {
                    const orderExports = await OrderExportTaskModel.findOrderExportTasks(
                        filter,
                        "_id tenant_id filename",
                        {
                            lean: true,
                            sort: {
                                created_at: 1,
                            },
                            skip: limit * (currentPage - 1),
                            limit
                        },
                    );

                    const ids = []
                    const deleteObjects = []

                    orderExports.forEach((orderExport) => {
                        ids.push(orderExport._id)

                        deleteObjects.push({
                            "Key": `${FILE_PATH.DATA_SHEET.EXPORT}/${orderExport.tenant_id}/order/update/export/${orderExport.filename}`
                        })
                    })

                    await publicS3.deleteFiles(deleteObjects)

                    await OrderExportTaskModel.deleteOrderExportTasks({
                        _id: {
                            $in: ids
                        }
                    })
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "DELETE OLD ORDER EXPORT TASKS" })
            logger.info("Completed: Delete old order export tasks")
        }
    }

    handleOrderExportDLQ = async (exportTask) => {
        try {
            const failureMessage = `We are experiencing an issue processing your request at the moment.
Our team has been notified and is working to resolve the problem as soon as possible. Please try again later.

If this problem persists, contact our support team with the reference id: ${exportTask._id} of the export task`

            const {
                tenant_id,
                to_user_mail_id,
                _id
            } = exportTask

            await OrderExportTaskModel.updateOrderExportTaskById(_id, {
                status: ORDER_EXPORT.STATUS.FAILED,
                failure_message: failureMessage
            })

            await this.sendOrderFailureEmail(
                to_user_mail_id,
                tenant_id,
                [failureMessage]
            )
        }
        catch (error) {
            logger.error(error)
        }
    }

}

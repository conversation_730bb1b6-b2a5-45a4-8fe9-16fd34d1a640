const difference = require('lodash.difference');

const MasterDataModel = new (require('../Models/MasterDataModel'))();
const DealModel = new (require('../Models/DealModel'))();
const DealProductModel = new (require('../Models/DealProductModel'))();
const ProductModel = new (require('../Models/ProductModel'))();

const {
	DEAL_TYPE,
	DEAL_STATUS,
	ACTIVE_STATUS_ENUM,
	INCREMENT_DEAL_PRODUCT_NUMBER_BY,
	NEW_PRODUCT_TYPE,
	DEAL_STATISTICS_EVENT_TYPE,
} = require('../Configs/constants');

const { sendDealStatMessageToSQS } = require('../SQS/DealStatisticsQueue');

class DealController {
	async createDeal(req, res) {
		try {
			const body = req.body;
			const createDeal = await DealModel.createDeal(body, req.headers);
			await createDeal.save();
			return res.handler.success('create_deal', createDeal);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async updateDeal(req, res) {
		try {
			const body = req.body;
			let saveDeal = false;
			const dealDetails = await DealModel.getDeal({ _id: body.dealId });
			if (!dealDetails) {
				return res.handler.notFound('deal_not_found');
			}
			let updateDealIdCounter = false;
			if (!dealDetails.deal_id) {
				const tenantDealCounter = await MasterDataModel.getTenantInfo(
					body.tenantId,
					'deal_counter',
				);
				dealDetails.deal_id = `DN${(tenantDealCounter.deal_counter += 1)}`;
				updateDealIdCounter = true;
			}

			dealDetails.deal_name = body.dealName;
			dealDetails.secondary_deal_name = body.secondaryDealName;

			// TODO: dealFromDate and dealToDate format is YYYY-MM-DD
			dealDetails.deal_from_date = body.dealFromDate;
			dealDetails.deal_to_date = body.dealToDate;
			dealDetails.sales_persons = body.salesPersonId;
			dealDetails.updated_by = req.headers.userDetails._id;

			if (body.dealSortType) {
				dealDetails.sort_type = body.dealSortType;
			}

			if (dealDetails.deal_status === DEAL_STATUS.PROGRESS) {
				dealDetails.deal_status = DEAL_STATUS.SCHEDULED;
			}

			let allProducts = [];
			if (dealDetails.deal_product_counter > 0) {
				allProducts = await DealModel.getCurrentDealAllProducts(dealDetails._id);
				if (allProducts.length) {
					const ids = allProducts.map(item => item.product_id);
					saveDeal = await DealModel.checkCurrentDealProducts(ids, dealDetails);
				} else {
					saveDeal = true;
				}
			} else {
				saveDeal = true;
			}

			if (!saveDeal) {
				return res.handler.badRequest('error_deal_update');
			}

			await dealDetails.save();
			if (updateDealIdCounter) {
				await MasterDataModel.updateTenantInfo(body.tenantId, {
					deal_counter: 1,
				});
			}

			await DealModel.getDealProductsAndUpdate(
				body.dealId,
				body.dealFromDate,
				body.dealToDate,
			);

			if (body.products) {
				for (let index = 0; index < body.products.length; index++) {
					const dealProductId = body.products[index].dealProductId;
					const getDealProduct = await DealModel.getDealProduct(dealProductId);

					if (getDealProduct) {
						switch (body.products[index].dealType) {
							case DEAL_TYPE.DISCOUNT:
								getDealProduct.percent = body.products[index].percent;
								getDealProduct.amount = body.products[index].amount;
								getDealProduct.discounted_price = body.products[index].discountedPrice;
								getDealProduct.discount_type =
									body.products[index].discountType;

								break;

							case DEAL_TYPE.BULK_PRICING:
								getDealProduct.first_tier = body.products[index]?.firstTier;
								getDealProduct.second_tier = body.products[index]?.secondTier;
								getDealProduct.third_tier = body.products[index]?.thirdTier;

								break;

							case DEAL_TYPE.BUY_X_AND_GET_Y:
								getDealProduct.buy_product = body.products[index].buyProduct;
								getDealProduct.free_product =
									body.products[index].freeProduct;

								break;
						}

						await getDealProduct.save();
					}
				}
			}

			const promises = [];
			if (body.dealProductSequence) {

				for (let index = 0; index < body.dealProductSequence.length; index++) {
					const dealProductId = body.dealProductSequence[index].dealProductId;
					const newProductSequence = body.dealProductSequence[index].dealProductSequence;

					promises.push(DealModel.updateMultipleDealProducts({ _id: dealProductId }, { deal_product_sequence: newProductSequence, updated_by: req.headers.userDetails._id }))
				}
				await Promise.all(promises);
			}

			return res.handler.success('update_deal', dealDetails);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async dealList(req, res) {
		try {
			const dealListing = await DealModel.getDealList(req.query);
			return res.handler.success(null, dealListing);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async productList(req, res) {
		try {
			const productList = await DealModel.productList(req.query);
			return res.handler.success(null, productList);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async getDealDetail(req, res) {
		try {
			let { dealId } = req.query;
			const dealDetails = await DealModel.getDeal(
				{
					_id: dealId,
				},
				null,
				{ lean: true },
			);
			if (!dealDetails) {
				return res.handler.notFound('deal_not_found');
			}
			return res.handler.success(null, dealDetails);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async addDealProduct(req, res) {
		try {
			const body = req.body;
			const failedIds = [];
			const toDate = moment(body.dealToDate).toDate();
			const fromDate = moment(body.dealFromDate).toDate();
			const dealDetail = {
				_id: body.dealId,
				deal_to_date: toDate,
				deal_from_date: fromDate,
			};
			const ids = body.products.map(item =>
				item.productId,
			);

			const getDeal = await DealModel.getDeal({ _id: body.dealId });
			if (!getDeal) {
				return res.handler.notFound('deal_not_found');
			}
			for (let index = 0; index < body.products.length; index++) {
				const productId = body.products[index].productId;
				dealDetail['price_id'] = getDeal.price_id;
				const allowCreate = await DealModel.checkDealProduct(productId, dealDetail);
				const productExist = await DealModel.checkDealProduct(productId, dealDetail, true);

				if (allowCreate || productExist) {
					failedIds.push(productId);
				} else {
					const addDealProduct = await DealModel.addDealProduct(
						body,
						productId,
						getDeal.price_id,
						req.headers,
					);
					getDeal.deal_product_counter += 1;
					addDealProduct.deal_product_sequence =
						getDeal.deal_product_counter * INCREMENT_DEAL_PRODUCT_NUMBER_BY;
					addDealProduct.deal_from_date = getDeal.deal_from_date
						? getDeal.deal_from_date
						: body?.dealFromDate;
					addDealProduct.deal_to_date = getDeal.deal_to_date
						? getDeal.deal_to_date
						: body?.dealToDate;

					if (body.products[index].parentId) {
						addDealProduct.parent_id = body.products[index].parentId;
					}

					try {
						await getDeal.save();
						await addDealProduct.save();
					}
					catch (error) {
						logger.error(error)

						if (productId) {
							failedIds.push(productId);
						}
					}
				}
			}
			const successIds = difference(ids, failedIds);
			if (successIds.length == 0) {
				return res.handler.success('deal_product_add_unsuccess', {
					successIds,
					failedIds,
				});
			}
			return res.handler.success('deal_product_add_success', {
				successIds,
				failedIds,
			});
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async dealProductList(req, res) {
		try {
			const dealProductList = await DealModel.getCurrentDealProductList(req.query);
			return res.handler.success(null, dealProductList);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async updateDealStatus(req, res) {
		try {
			const {
				dealIds,
				status
			} = req.body;

			if (status === DEAL_STATUS.ARCHIVED) {
				const deals = await DealModel.getDeals(
					{
						_id: {
							$in: dealIds
						}
					},
					{
						deal_to_date: 1,
						deal_from_date: 1,
						deal_status: 1
					},
					{
						lean: true
					}
				)

				const isRunning = deals.find((value) => {
					const dealFromDate = new Date(value?.deal_from_date),
						dealToDate = new Date(value?.deal_to_date),
						currentDate = new Date();

					if (
						value?.deal_status === DEAL_STATUS.PAUSED ||
						value?.deal_status === DEAL_STATUS.CANCELLED ||
						value?.deal_status === DEAL_STATUS.ARCHIVED
					) {
						return false;
					}

					if (currentDate >= dealFromDate && currentDate <= dealToDate) {
						return true;
					} else if (currentDate < dealFromDate || currentDate > dealToDate) {
						return false;
					} else {
						return false;
					}
				})

				if (isRunning) {
					return res.handler.validationError("running_deal_can_not_archived");
				}
			}

			const updatedDeals = await DealModel.updateMultipleDeals(
				{
					_id: {
						$in: dealIds
					}
				},
				{
					$set: {
						deal_status: status
					}
				},
				{
					multi: true
				},
			);

			if (updatedDeals.modifiedCount) {
				return res.handler.success('deal_status_update_success');
			}
			return res.handler.success('deal_status_update_unsuccess');
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async deleteDeals(req, res) {
		try {
			const body = req.body;
			const deletedDeal = await DealModel.deleteMultipleDeals(body.dealIds);

			if (deletedDeal.deletedCount) {
				await DealModel.deleteDealProducts(body.dealIds);
				return res.handler.success('deal_delete_success');
			}
			return res.handler.success('deal_delete_unsuccess');
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async deleteDealProducts(req, res) {
		try {
			const body = req.body;
			const deletedDealProduct = await DealModel.deleteMultipleDealProducts(
				body.dealProductId,
			);

			if (deletedDealProduct.deletedCount) {
				return res.handler.success('deal_product_delete_success');
			}
			return res.handler.success('deal_product_delete_unsuccess');
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async updateDealProductStatus(req, res) {
		try {
			const body = req.body;
			const is_active = !!(body.status == ACTIVE_STATUS_ENUM.ACTIVE);
			const updatedProduct = await DealModel.updateMultipleDealProducts(
				{ _id: { $in: body.dealProductId } },
				{ $set: { is_active } },
				{ multi: true },
			);

			if (updatedProduct.modifiedCount) {
				return res.handler.success('deal_product_status_update_success');
			}
			return res.handler.success('deal_product_status_update_unsuccess');
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async changeDealProductSequence(req, res) {
		try {
			const body = req.body;
			const getDealProduct = await DealModel.getSingleDealProduct({
				_id: body.dealProductId,
				deal_id: body.dealId,
			});

			if (!getDealProduct) {
				return res.handler.notFound('deal_not_found');
			}
			getDealProduct.deal_product_sequence = body.dealProductSequence;
			await getDealProduct.save();

			return res.handler.success('deal_product_sequence_updated');
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	async nextDealProductList(req, res) {
		try {
			// const body = req.query;
			const dealDetails = await DealModel.getDeal({ _id: req.query.dealId });

			const body = {
				dealId: req.query.dealId,
				priceId: dealDetails.price_id,
				dealProductSequence: req.query.dealProductSequence,
				nextDealProductCount: req.query.nextDealProductCount
			}
			const getNextDealProduct = await DealModel.getCurrentDealProductList(body);

			return res.handler.success(null, getNextDealProduct);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	// TODO: add out of stock product parameter
	async getRunningDealsProductList(req, res) {
		try {
			const getRunningDealsProductList = await DealModel.getRunningDealsProductList(
				req.query,
			);
			return res.handler.success(null, getRunningDealsProductList);
		} catch (error) {
			return res.handler.serverError(error);
		}
	}

	getProductListByItemNumbers = async (req, res) => {
		try {
			const data = await DealProductModel.getProductListByItemNumbers(req)
			return res.handler.success(null, data)
		}
		catch (error) {
			return res.handler.serverError(error)
		}
	}

	findProducts = async (tenantId, itemNumbers, priceId, dealType) => {
		const uniqueItemNumbers = itemNumbers.map(itemNumber => `${tenantId}_${itemNumber}`)

		const productParams = {
			"filter": {
				"is_deleted": false,
				"tenant_id": tenantId,
				"unique_item_number": { "$in": uniqueItemNumbers },
				"type": {
					"$in": [
						NEW_PRODUCT_TYPE.SINGLE,
						NEW_PRODUCT_TYPE.VARIANT
					]
				},
				"price_mappings": {
					"$elemMatch": {
						"master_price_id": new mongoose.Types.ObjectId(priceId),
						"price": { "$gt": 0 }
					}
				}
			},
			"projection": `
				item_number
			`,
			"options": { lean: true }
		}

		if (dealType === DEAL_TYPE.BULK_PRICING) {
			productParams["filter"]["packaging_map.qty_ctn"] = { $gt: 0 }
		}

		return ProductModel.findProducts(
			productParams.filter,
			productParams.projection,
			productParams.options,
		)
	}

	segregateDealProducts = async (
		tenantId,
		priceId,
		dealId,
		productIds,
		dealFromDate,
		dealToDate,
		productItemNumberMapping,
		sameDealItemNumberSet,
		anotherDealItemNumberSet,
	) => {
		const limit = 50

		const dealProductsParams = {
			"filter": {
				"tenant_id": tenantId,
				"price_id": new mongoose.Types.ObjectId(priceId),
				"product_id": { "$in": productIds },
				"deal_to_date": {
					"$gte": new Date(dealFromDate).toISOString()
				},
				"deal_from_date": {
					"$lte": new Date(dealToDate).toISOString()
				},
			},
			"projection": `
				deal_id
				product_id
				deal_from_date
				deal_to_date
			`,
			"options": {
				"lean": true,
				limit,
				"skip": 0
			}
		}

		const totalProducts = await DealModel.getDealProductsCount(dealProductsParams.filter)
		const totalPages = Math.ceil(totalProducts / limit)

		for (let i = 1; i <= totalPages; i++) {
			const skip = limit * (i - 1)

			const dealProducts = await DealModel.findDealProducts(
				dealProductsParams.filter,
				dealProductsParams.projection,
				{
					...dealProductsParams.options,
					skip,
				},
			)

			if (dealProducts.length) {
				dealProducts.forEach((dealProduct, index) => {
					const itemNumber = productItemNumberMapping.get(dealProduct.product_id.toString())
					const isSameDealId = dealProduct.deal_id.toString() === dealId

					if (itemNumber && isSameDealId) {
						sameDealItemNumberSet.add(itemNumber)
					}
					else {
						const currentDealStartDate = new Date(dealFromDate)
						const currentDealEndDate = new Date(dealToDate)

						const dealStartDate = new Date(dealProduct.deal_from_date)
						const dealEndDate = new Date(dealProduct.deal_to_date)

						if (
							(dealStartDate <= currentDealStartDate && currentDealStartDate <= dealEndDate) ||
							(dealStartDate <= currentDealEndDate && currentDealEndDate <= dealEndDate) ||
							(currentDealStartDate <= dealStartDate && dealStartDate <= currentDealEndDate) ||
							(currentDealStartDate <= dealEndDate && dealEndDate <= currentDealEndDate)
						) {
							if (itemNumber) {
								anotherDealItemNumberSet.add(itemNumber)
							}
						}
						else {
							if (itemNumber && isSameDealId) {
								sameDealItemNumberSet.add(itemNumber)
							}
						}
					}
				})
			}
		}
	}

	checkValidItems = async (req, res) => {
		try {
			const {
				tenantId,
				dealId,
				dealType,
				priceId,
				itemNumbers,
				dealFromDate,
				dealToDate,
			} = req.body

			let validItemNumbers = []
			let invalidItemNumbers = itemNumbers
			const sameDealItemNumberSet = new Set()
			const anotherDealItemNumberSet = new Set()

			const products = await this.findProducts(
				tenantId,
				itemNumbers,
				priceId,
				dealType,
			)

			if (products.length) {
				const productIds = []
				const productItemNumbers = []
				const productItemNumberMapping = new Map()

				products.forEach(product => {
					productIds.push(product._id)
					productItemNumbers.push(product.item_number)
					productItemNumberMapping.set(product._id.toString(), product.item_number)
				})

				invalidItemNumbers = difference(itemNumbers, productItemNumbers)
				validItemNumbers = difference(itemNumbers, invalidItemNumbers)

				if (invalidItemNumbers.length !== itemNumbers.length) {
					await this.segregateDealProducts(
						tenantId,
						priceId,
						dealId,
						productIds,
						dealFromDate,
						dealToDate,
						productItemNumberMapping,
						sameDealItemNumberSet,
						anotherDealItemNumberSet,
					)
				}
			}

			const sameDealItemNumbers = [...sameDealItemNumberSet]
			const anotherDealItemNumbers = [...anotherDealItemNumberSet]

			const totalInvalidItemNumbers = [
				...sameDealItemNumbers,
				...anotherDealItemNumbers,
			]
			validItemNumbers = difference(validItemNumbers, totalInvalidItemNumbers)

			return res.handler.success(null, {
				validItemNumbers,
				invalidItemNumbers,
				sameDealItemNumbers,
				anotherDealItemNumbers,
			})
		}
		catch (error) {
			return res.handler.serverError(error)
		}
	}

	async updateDealStatistics(req, res) {
		try {
			const {
				tenantId,
				dealId,
				dealProductId,
				customerUserRoleId,
			} = req.body;

			const userRoleId = req.headers.userroleid;

			const dealProductInfo = await DealModel.getSingleDealProduct(
				{
					_id: dealProductId,
					tenant_id: tenantId,
					deal_id: dealId
				},
				{
					_id: 1,
					product_id: 1
				},
				{
					lean: true
				}
			);

			if (!dealProductInfo) {
				return res.handler.notFound("deal_product_not_found");
			}

			const messageBody = {
				dealId,
				dealProductId,
				tenantId,
				userRoleId,
				customerUserRoleId,
				productId: dealProductInfo.product_id,
				ts: new Date().toISOString(),
				dealStatisticsEventType: DEAL_STATISTICS_EVENT_TYPE.CLICK_VIEW_COUNT_UPDATE
			}
			await sendDealStatMessageToSQS(messageBody, tenantId);

			return res.handler.success("event_queued");
		}
		catch (error) {
			return res.handler.serverError(error);
		}
	}
}

module.exports = DealController;

const ProductNotificationService = new (require("../Services/notifications/ProductNotificationService"))()

class NotificationController {

    async sentNotificationsForNewProducts() {
        const profiler = logger.startTimer()
        logger.info("Started: Sent notifications for New Products")

        try {
            const eligibleTenants = await ProductNotificationService.getEligibleTenants()

            if (!eligibleTenants.length) {
                logger.info("sentNotificationsForNewProducts: No eligible tenants found for notification")
                return
            }

            for (let i = 0; i < eligibleTenants.length; i++) {
                try {
                    const tenantId = eligibleTenants[i]._id

                    const products = await ProductNotificationService.getNewProducts(tenantId)
                    if (!products.length) {
                        logger.info(`sentNotificationsForNewProducts: No new products found for tenant: ${tenantId}`)
                        continue
                    }

                    const recentProductPriceListIdSet = ProductNotificationService.fetchAllPriceList(products)
                    if (!recentProductPriceListIdSet.size) {
                        logger.info(`sentNotificationsForNewProducts: No recent price list found for tenant: ${tenantId}`)
                        continue
                    }

                    const priceListIds = Array.from(recentProductPriceListIdSet)
                    const { customers, salespersonSettings } = await ProductNotificationService.fetchRecipientsData(tenantId, priceListIds)

                    if (!customers.length && !salespersonSettings.length) {
                        logger.info(`sentNotificationsForNewProducts: No notifications have been sent as 'no customers and salespersonSettings found' for tenant: ${tenantId}`)
                        continue
                    }

                    const notificationData = await ProductNotificationService.prepareNotificationData(
                        products, customers, salespersonSettings, tenantId
                    )
                    await ProductNotificationService.sendNotifications(tenantId, notificationData)
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "SENT NOTIFICATIONS FOR NEW PRODUCTS" })
            logger.info(`Completed: Sent notifications for New Products\n`)
        }
    }
}

module.exports = NotificationController

const OrderModel = new (require("../Models/OrderModel"));

module.exports = class {

    async getOrderDetails(req, res) {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query

            if (typeof (projection) === "string") {
                projection = JSON.parse(projection)
            }

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const orderDetail = await OrderModel.findOrder(filter, projection, options)

            if (!orderDetail) {
                return res.handler.notFound("order_not_found");
            }
            return res.handler.success(null, orderDetail)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getOrdersList(req, res) {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query

            if (typeof (projection) === "string") {
                projection = JSON.parse(projection)
            }

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const orders = await OrderModel.findOrders(filter, projection, options)
            return res.handler.success(null, orders)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateOrders(req, res) {
        try {
            const {
                filter,
                updateFields
            } = req.body

            const updateResult = await OrderModel.updateBulkOrders(
                filter,
                updateFields,
                req.headers
            )
            
            return res.handler.success(null, updateResult)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}

const UserModel = new (require("../Models/auth"))();

class AuthController {
    async Signup(req, res) {
        try {
            let userExists = await UserModel.findUserByUserName(req.body.userName);
            if(userExists) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'VALIDATION.EXISTS.EMAIL');
            }
    
            userExists = await UserModel.findUserByEmail(req.body.email);
            if(userExists) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'VALIDATION.EXISTS.USER');
            }
            const user = await UserModel.signup(req.body);
            await user.save();

            return res.handler.success();
        } catch(error) {
            return res.handler.serverError(error);
        }

    }
}

module.exports = AuthController;
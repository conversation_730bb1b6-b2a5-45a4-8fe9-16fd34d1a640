const OrderModel = new (require("../Models/OrderModel"))();
const OrderReportModel = new (require("../Models/OrderReportModel"))();

const { ORDER_STATUS_TYPES } = require("../Configs/constants");

const {
    stringifyObjectId,
} = require("../Utils/helpers");

class OrderReportController {

    async orderStatistics(req, res) {
        const profiler = logger.startTimer()
        logger.info("Started: sales person's report job")

        try {
            const tenants = await OrderReportModel.getTenantList()

            if (!tenants?.length) {
                throw new Error("No tenants found.")
            }

            for (const tenantInfo of tenants) {
                const tenantId = tenantInfo._id;
                const timezone = tenantInfo.country.timezone;

                const logMessage = (message) => {
                    logger.info(`Tenant ID: ${tenantId} -> Message: ${message}`);
                };

                const salesPersons = await OrderReportModel.getSalesPersonList(tenantId)

                if (!salesPersons?.length) {
                    logMessage("Skipped this tenant as it doesn't have any sales persons");
                    continue; // no salesperson found in this tenant so skip it
                }

                const todayDate = momentTimezone.tz(timezone).subtract(1, "days").startOf("day");
                const startDate = momentTimezone.tz(timezone).subtract(1, "days").startOf("day").format();
                const endDate = momentTimezone.tz(timezone).subtract(1, "days").endOf("day").format();

                const salesPersonIds = salesPersons.map(person => new mongoose.Types.ObjectId(person._id))
                const promises = []

                const orders = await OrderModel.findOrders(
                    {
                        sales_user_role_id: {
                            $in: salesPersonIds,
                        },
                        order_status: {
                            $in: [
                                ORDER_STATUS_TYPES.RECEIVED,
                                ORDER_STATUS_TYPES.RELEASED,
                                ORDER_STATUS_TYPES.PREPARING,
                                ORDER_STATUS_TYPES.SHIPPED,
                                ORDER_STATUS_TYPES.DELIVERED,
                            ]
                        },
                        created_at: {
                            $gte: new Date(startDate),
                            $lte: new Date(endDate),
                        },
                    },
                    {
                        sales_user_role_id: 1,
                        customer_user_role_id: 1,
                        total_amount: 1,
                        total_tax: 1,
                    }
                )

                const salesPersonsMap = new Map()
                const customersMap = new Map()

                for (let i = 0; i < orders.length; ++i) {
                    const {
                        sales_user_role_id,
                        customer_user_role_id,
                        total_amount = 0,
                        total_tax = 0,
                    } = orders[i]

                    // generate sales person mapping.
                    OrderReportModel.generateMapping(
                        salesPersonsMap,
                        sales_user_role_id,
                        total_amount,
                        total_tax,
                    )

                    // generate customer mapping.
                    OrderReportModel.generateMapping(
                        customersMap,
                        customer_user_role_id,
                        total_amount,
                        total_tax,
                    )
                }

                // prepare sales person data.
                OrderReportModel.prepareReportData(
                    salesPersonsMap,
                    tenantId,
                    todayDate,
                    timezone,
                    promises,
                )

                // prepare customer data.
                OrderReportModel.prepareReportData(
                    customersMap,
                    tenantId,
                    todayDate,
                    timezone,
                    promises,
                )

                if (promises.length) {
                    await Promise.allSettled(promises)
                }
            }
            // return res.handler.success();
        }
        catch (error) {
            logger.error(error)
            // return res.handler.serverError(error);
        }
        finally {
            profiler.done({ message: "TOTAL SALES PERSON ORDER STATISTIC JOB RUN TIME" })
            logger.info(`Completed: sales person's order job\n`)
        }
    }

    getOrderReportList = async (req, res) => {
        try {
            const orderReportList = await OrderReportModel.getOrderReportList(req.query)
            return res.handler.success(null, orderReportList)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    // past orders script for order   
    pastOrderStatistic = async (req, res) => {
        try {
            const tenants = await OrderReportModel.getTenantList()

            if (!tenants?.length) {
                return res.handler.notFound("tenants_not_found")
            }

            for (const tenantInfo of tenants) {
                const tenantId = tenantInfo._id;
                const timezone = tenantInfo.country.timezone;

                const salesPersons = await OrderReportModel.getSalesPersonList(tenantId)

                if (!salesPersons?.length) {
                    continue; // no salesperson found in this tenant so skip it
                }
                for (const salesPerson of salesPersons) {
                    const sales_user_role_id = salesPerson._id;

                    const historicalOrderData = await OrderModel.findOrders(
                        {
                            tenant_id: tenantId,
                            sales_user_role_id: new mongoose.Types.ObjectId(sales_user_role_id),
                            order_status: {
                                $in: [
                                    ORDER_STATUS_TYPES.RECEIVED,
                                    ORDER_STATUS_TYPES.RELEASED,
                                    ORDER_STATUS_TYPES.PREPARING,
                                    ORDER_STATUS_TYPES.SHIPPED,
                                    ORDER_STATUS_TYPES.DELIVERED,
                                ]
                            },
                        },
                        {
                            created_at: 1,
                            sales_user_role_id: 1,
                            customer_user_role_id: 1,
                            total_amount: 1,
                            total_tax: 1,
                        }
                    )

                    // Use a Map to store date range data
                    const salesPersonDateRangeMap = new Map();
                    const customerDateRangeMap = new Map();

                    historicalOrderData.forEach(orderData => {
                        const {
                            created_at,
                            sales_user_role_id,
                            customer_user_role_id,
                            total_amount,
                            total_tax,
                        } = orderData;

                        const salesPersonId = stringifyObjectId(sales_user_role_id)
                        const customerId = stringifyObjectId(customer_user_role_id)

                        const orderDate = momentTimezone.tz(created_at, timezone).format();
                        const startDate = momentTimezone.tz(created_at, timezone).startOf("day");
                        const endDate = momentTimezone.tz(created_at, timezone).endOf("day");

                        const salesPersonDateRange = `${startDate.format()}_${endDate.format()}_${salesPersonId}`
                        const customerDateRange = `${startDate.format()}_${endDate.format()}_${customerId}`

                        // generate sales person mapping.
                        const params = {
                            dateRangeMap: salesPersonDateRangeMap,
                            dateRange: salesPersonDateRange,
                            userRoleId: salesPersonId,
                            startDate,
                            endDate,
                            orderDate,
                            totalAmount: total_amount,
                            totalTax: total_tax,
                        }
                        OrderReportModel.generatePastOrderMapping(params)

                        const customerParams = {
                            ...params,
                            dateRangeMap: customerDateRangeMap,
                            dateRange: customerDateRange,
                            userRoleId: customerId,
                        }
                        // generate customer mapping.
                        OrderReportModel.generatePastOrderMapping(customerParams)
                    })

                    const promises = []

                    // prepare sales person data.
                    const preparedData = {
                        dateRangeMap: salesPersonDateRangeMap,
                        tenantId,
                        timezone,
                        promises,
                    }
                    OrderReportModel.preparePastReportData(preparedData)

                    // prepare sales person data.
                    const preparedCustomerData = {
                        ...preparedData,
                        dateRangeMap: customerDateRangeMap,
                    }
                    OrderReportModel.preparePastReportData(preparedCustomerData)

                    if (promises.length) {
                        await Promise.allSettled(promises)
                    }
                }
            }
            return res.handler.success(null)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
        finally {
            logger.info(`Completed: old order job\n`);
        }
    };
}

module.exports = OrderReportController;

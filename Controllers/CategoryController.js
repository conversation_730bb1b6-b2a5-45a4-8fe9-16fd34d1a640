const isEmpty = require("lodash.isempty");

const CategoryModel = new (require("../Models/CategoryModel"))();
const InternalServiceModel = new (require("../Models/InternalServiceModel"));

const {
    VALUES,
    ENTITY_STATUS,
    INCREMENT_PRODUCT_NUMBER_BY,
    PRODUCT_FILTER_TYPES,
    FILE_PATH,
} = require('../Configs/constants');

class CategoryController {
    async addAndUpdateCategory(req, res) {
        try {

            const body = req.body;
            body.tenantId = Number(body.tenantId);

            var categoryInfo = await CategoryModel.categoryById(body.categoryId)
            var categoryExist = await CategoryModel.existCategoryName(body.categoryId, body.categoryName, body.tenantId, body.type, body.parentFamilyId, body.parentCategoryId)

            if (!categoryInfo) {
                if (body.type === VALUES.category.FAMILY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_family_category');
                    }
                    const countCategory = await CategoryModel.countCategory(VALUES.category.FAMILY, body.tenantId)

                    body.category_name = body.categoryName;
                    body.secondary_language_category_name = body.secondaryCategoryName;
                    body.tenant_id = body.tenantId;
                    body.type = body.type;
                    body.is_active = body.isActive;
                    body.sequence = countCategory + 1;
                    body.image_name = body.imageName;
                    var addFamilyCategory = await CategoryModel.addFamilyCategory(body, req.headers);
                    await addFamilyCategory.save();

                    return res.handler.success("added_family_category");
                } else if (body.type === VALUES.category.CATEGORY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_category');
                    }
                    const countCategory = await CategoryModel.countCategory(VALUES.category.CATEGORY, body.tenantId, body.parentFamilyId)

                    body.category_name = body.categoryName;
                    body.secondary_language_category_name = body.secondaryCategoryName;
                    body.tenant_id = body.tenantId;
                    body.type = body.type;
                    body.family_id = body.parentFamilyId;
                    body.parent_id = body.parentFamilyId;
                    body.is_active = body.isActive;
                    body.sequence = countCategory + 1;

                    var addCategory = await CategoryModel.addCategory(body, req.headers);
                    await addCategory.save();

                    return res.handler.success("added_category");
                } else if (body.type === VALUES.category.SUBCATEGORY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_sub_category');
                    }
                    const countCategory = await CategoryModel.countCategory(VALUES.category.SUBCATEGORY, body.tenantId, body.parentCategoryId)

                    body.category_name = body.categoryName;
                    body.secondary_language_category_name = body.secondaryCategoryName;
                    body.tenant_id = body.tenantId;
                    body.type = body.type;
                    body.family_id = body.parentFamilyId;
                    body.parent_id = body.parentCategoryId;
                    body.is_active = body.isActive;
                    body.sequence = countCategory + 1;

                    var addSubCategory = await CategoryModel.addCategory(body, req.headers);
                    await addSubCategory.save();

                    return res.handler.success("added_subcategory");
                }


            } else {
                if (body.type === VALUES.category.FAMILY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_family_category');
                    }

                    categoryInfo.category_name = body.categoryName;
                    categoryInfo.secondary_language_category_name = body.secondaryCategoryName;
                    categoryInfo.tenant_id = body.tenantId;
                    categoryInfo.type = body.type;
                    categoryInfo.is_active = body.isActive;
                    categoryInfo.updated_by = req.headers.userDetails._id
                    categoryInfo.image_name = body.imageName;

                } else if (body.type === VALUES.category.CATEGORY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_category');
                    }
                    if (categoryInfo.parent_id != body.parentFamilyId) {
                        const countCategory = await CategoryModel.countCategory(VALUES.category.CATEGORY, body.tenantId, body.parentFamilyId)
                        categoryInfo.sequence = countCategory + 1
                    }
                    categoryInfo.category_name = body.categoryName;
                    categoryInfo.secondary_language_category_name = body.secondaryCategoryName;
                    categoryInfo.tenant_id = body.tenantId;
                    categoryInfo.type = body.type;
                    categoryInfo.family_id = body.parentFamilyId;
                    categoryInfo.parent_id = body.parentFamilyId;
                    categoryInfo.is_active = body.isActive;
                    categoryInfo.updated_by = req.headers.userDetails._id
                } else if (body.type === VALUES.category.SUBCATEGORY) {
                    if (categoryExist.length > 0) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'exist_sub_category');
                    }
                    if (categoryInfo.parent_id != body.parentCategoryId) {
                        const countCategory = await CategoryModel.countCategory(VALUES.category.SUBCATEGORY, body.tenantId, body.parentCategoryId)
                        categoryInfo.sequence = countCategory + 1
                    }
                    categoryInfo.category_name = body.categoryName;
                    categoryInfo.secondary_language_category_name = body.secondaryCategoryName;
                    categoryInfo.tenant_id = body.tenantId;
                    categoryInfo.type = body.type;
                    categoryInfo.is_active = body.isActive;
                    categoryInfo.family_id = body.parentFamilyId;
                    categoryInfo.parent_id = body.parentCategoryId;
                    categoryInfo.updated_by = req.headers.userDetails._id
                }

                await categoryInfo.save();

                return res.handler.success("updated_edited");
            }

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async categoryList(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);
            const result = await CategoryModel.categoryList(body.tenantId, body.type, body.status, body.categoryId, body.searchKey)
            const data = [];
            switch (body.type) {
                case VALUES.category.FAMILY:
                    for (let index = 0; index < result.length; index++) {
                        const categoryId = result[index]._id;
                        const category = result[index];
                        if (category?.image_name) {
                            category.image_name = `${FILE_PATH.S3_URL.CATEGORIES_FAMILY_IMAGES}/${body.tenantId}/${category.image_name}`;
                        }
                        const count = await CategoryModel.categoryProductCount(body.tenantId, categoryId, undefined, undefined)
                        // const totalProductCount = await CategoryModel.productCount(categoryId)
                        const categoryExist = await CategoryModel.categoryExist(body.tenantId, categoryId)
                        const obj = {
                            category,
                            count,
                            // totalProductCount
                        };
                        categoryExist ? "" : obj['deleteAllow'] = true;
                        data.push(obj)
                    }

                    return res.handler.success(null, data);

                case VALUES.category.CATEGORY:
                    for (let index = 0; index < result.length; index++) {
                        const categoryId = result[index]._id;
                        const familyId = result[index].parent_id;
                        const category = result[index];
                        const count = await CategoryModel.categoryProductCount(body.tenantId, familyId, categoryId, undefined)
                        // const totalProductCount = await CategoryModel.productCount(familyId, categoryId)
                        const subCategoryExist = await CategoryModel.categoryExist(body.tenantId, categoryId)
                        const obj = {
                            category,
                            count,
                            // totalProductCount
                        };
                        subCategoryExist ? "" : obj['deleteAllow'] = true;
                        data.push(obj)
                    }

                    return res.handler.success(null, data);

                case VALUES.category.SUBCATEGORY:
                    for (let index = 0; index < result.length; index++) {
                        const SubCategoryId = result[index]._id;
                        const categoryId = result[index].parent_id;
                        const familyId = result[index].family_id;
                        const category = result[index];
                        const count = await CategoryModel.categoryProductCount(body.tenantId, familyId, categoryId, SubCategoryId)
                        let obj = {
                            category,
                            count
                        };
                        data.push(obj)
                    }

                    return res.handler.success(null, data);

            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async sequenceCategory(req, res) {
        try {
            const body = req.body;
            const promises = [];

            for (let index = 0; index < body.categoryId.length; index++) {
                const element = body.categoryId[index];

                promises.push(CategoryModel.sequenceCategory(element, index + 1, body.type, req.headers))
            }
            await Promise.all(promises);

            return res.handler.success("sequence_changed");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteCategory(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);
            const productList = await CategoryModel.getProductList(body.familyId, body.categoryId, body.subCategoryId)

            let newCategoryInfo;
            if (body.newSubCategoryId && body.newCategoryId && body.newFamilyId) {
                newCategoryInfo = await CategoryModel.getCategory(body.newSubCategoryId);
                if (newCategoryInfo.family_id._id.toString() !== body.newFamilyId && newCategoryInfo.parent_id._id.toString() !== body.newCategoryId) {
                    return res.handler.notFound('category_not_found');
                }
            } else if (!body.newSubCategoryId && body.newCategoryId && body.newFamilyId) {
                newCategoryInfo = await CategoryModel.getCategory(body.newCategoryId);
                if (newCategoryInfo.parent_id._id.toString() !== body.newFamilyId) {
                    return res.handler.notFound('category_not_found');
                }
            } else if (!body.newSubCategoryId && !body.newCategoryId && body.newFamilyId) {
                newCategoryInfo = await CategoryModel.getCategory(body.newFamilyId);
            }

            // if (!newCategoryInfo || newCategoryInfo.is_deleted) {
            //     return res.handler.notFound('category_not_found');
            // }

            if (newCategoryInfo) {
                const productPromises = [];
                for (let i = 0; i < productList.length; i++) {
                    newCategoryInfo.product_counter += 1;
                    const product = productList[i];
                    product.product_order = newCategoryInfo.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
                    product.family_id = body.newFamilyId;

                    if (body.newSubCategoryId) {
                        product.family_id = body.newFamilyId;
                        product.category_id = body.newCategoryId;
                        product.subcategory_id = body.newSubCategoryId;
                    } else if (body.newCategoryId) {
                        product.family_id = body.newFamilyId;
                        product.category_id = body.newCategoryId;
                        product.subcategory_id = null;
                    } else {
                        product.family_id = body.newFamilyId;
                        product.category_id = null;
                        product.subcategory_id = null;
                    }
                    productPromises.push(product.save());
                }
                productPromises.push(newCategoryInfo.save());

                await Promise.all(productPromises);
            }

            // if (productList.length !== 0) {
            //     await CategoryModel.updateCategory(productList, body.newFamilyId, body.newCategoryId, body.newSubCategoryId)
            // }


            switch (body.type) {
                case VALUES.category.SUBCATEGORY:
                    await CategoryModel.deleteCategory(body.subCategoryId, req.headers)
                    break;

                case VALUES.category.CATEGORY:
                    const subCategoryList = await CategoryModel.categoryList(body.tenantId, VALUES.category.SUBCATEGORY, ENTITY_STATUS.ALL, body.categoryId)
                    await CategoryModel.deleteCategory(subCategoryList, req.headers)
                    await CategoryModel.deleteCategory(body.categoryId, req.headers)
                    break;

                case VALUES.category.FAMILY:
                    const categoryList = await CategoryModel.categoryList(body.tenantId, VALUES.category.CATEGORY, ENTITY_STATUS.ALL, body.familyId)
                    for (let index = 0; index < categoryList.length; index++) {
                        const categoryId = categoryList[index]._id;
                        const subCategoryList = await CategoryModel.categoryList(body.tenantId, VALUES.category.SUBCATEGORY, ENTITY_STATUS.ALL, categoryId)
                        await CategoryModel.deleteCategory(subCategoryList, req.headers)
                    }
                    await CategoryModel.deleteCategory(categoryList, req.headers)
                    await CategoryModel.deleteCategory(body.familyId, req.headers)
                    break;
            }

            return res.handler.success("deleted_category");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCategory(req, res) {
        try {
            const body = req.query;
            const result = await CategoryModel.getCategory(body.categoryId);

            if (result?.image_name){
                result.image_name = `${FILE_PATH.S3_URL.CATEGORIES_FAMILY_IMAGES}/${result.tenant_id}/${result.image_name}`;
            }
            // result.map((element)=> {
            return res.handler.success(null, result);
            // })

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    // Get all category list with total products count.
    async getAllCategoryList(req, res) {
        try {
            const {
                tenantId,
                priceListId,
                hideOutOfStock,
                branchId,
                filters,
                familyIds = [],
            } = req.body

            const familyMatch = {}

            if (familyIds.length) {
                familyMatch._id = {
                    "$in": familyIds
                }
            }

            if (!isEmpty(filters)) {
                if (filters.productType?.includes(PRODUCT_FILTER_TYPES.NEW_PRODUCTS)) {
                    const settings = await InternalServiceModel.tenantAppSettings(tenantId, "consider_new_item")

                    const days = "consider_new_item" in settings
                        ? settings.consider_new_item
                        : 30

                    filters.newItemDays = days
                }
            }

            const familyList = await CategoryModel
                .getAllCategoryList(
                    tenantId,
                    priceListId,
                    VALUES.category.FAMILY,
                    null,
                    null,
                    hideOutOfStock,
                    branchId,
                    filters,
                    familyMatch,
                )

            for (let i = 0, familyLength = familyList.length; i < familyLength; i++) {
                const family = familyList[i]

                const categoryList = await CategoryModel
                    .getAllCategoryList(
                        tenantId,
                        priceListId,
                        VALUES.category.CATEGORY,
                        family._id,
                        null,
                        hideOutOfStock,
                        branchId,
                        filters,
                    )

                // Set category of the family.
                family.categoryList = categoryList

                for (let j = 0, categoryLength = categoryList.length; j < categoryLength; j++) {
                    const category = categoryList[j]

                    const subCategoryList = await CategoryModel
                        .getAllCategoryList(
                            tenantId,
                            priceListId,
                            VALUES.category.SUBCATEGORY,
                            family._id,
                            category._id,
                            hideOutOfStock,
                            branchId,
                            filters,
                        )

                    // Set sub category of category of the family.
                    family.categoryList[j].subCategoryList = subCategoryList
                }
            }
            return res.handler.success(null, familyList)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async allCategoryProductList(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);
            const productData = [];
            const categoryProductArr = [];
            const subCategoryProductArr = [];
            const familyCategoryProductArr = [];
            let family = { 'type': VALUES.category.FAMILY };
            let category = { 'type': VALUES.category.CATEGORY };
            let subCategory = { 'type': VALUES.category.SUBCATEGORY };
            let countExist = { 'productExist': false };

            const familyCategory = await CategoryModel.getCategory(body.familyId)
            const count = await CategoryModel.categoryAndSubcategoryExist(body.tenantId, body.familyId)
            const familyCategoryProductList = await CategoryModel.productList(req)

            const familyCategoryName = familyCategory.category_name;
            let familyCategoryObj = { 'category_name': familyCategoryName, 'category_id': body.familyId, 'list': familyCategoryProductList.list, 'count': familyCategoryProductList.count }
            familyCategoryProductArr.push(familyCategoryObj)

            const categoryList = await CategoryModel.categoryList(body.tenantId, VALUES.category.CATEGORY, ENTITY_STATUS.ALL, body.familyId)

            for (let index = 0; index < categoryList.length; index++) {
                const categoryName = categoryList[index].category_name;
                const categoryId = categoryList[index]._id;

                const categoryProductList = await CategoryModel.productList(req, categoryId)
                const subcategoryList = await CategoryModel.categoryList(body.tenantId, VALUES.category.SUBCATEGORY, ENTITY_STATUS.ALL, categoryId)

                for (let index = 0; index < subcategoryList.length; index++) {
                    const subCategoryName = subcategoryList[index].category_name;
                    const subCategoryId = subcategoryList[index]._id;
                    const categoryId = subcategoryList[index].parent_id;

                    const subCategoryProductList = await CategoryModel.productList(req, categoryId, subCategoryId)
                    let subCategoryObj = { 'category_name': subCategoryName, 'category_id': subCategoryId, 'parent_id': categoryId, 'list': subCategoryProductList.list, 'count': subCategoryProductList.count }

                    subCategoryProductArr.push(subCategoryObj)
                }

                let categoryObj = { 'category_name': categoryName, 'category_id': categoryId, 'list': categoryProductList.list, 'count': categoryProductList.count }

                categoryProductArr.push(categoryObj)
            }

            subCategory["list"] = subCategoryProductArr
            productData.push(subCategory)

            category['list'] = categoryProductArr
            productData.push(category)

            family["list"] = familyCategoryProductArr
            productData.push(family)

            if (!count) {
                productData.push(countExist)
            }
            return res.handler.success(null, productData);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async productList(req, res) {
        try {

            const result = await CategoryModel.productList(req)

            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async productSequence(req, res) {
        try {
            const {
                productId,
                productSequence,
            } = req.body;

            await CategoryModel.productSequence(productId, productSequence, req.headers)
            return res.handler.success("product_sequence_changed");
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async topCategory(req, res) {
        try {
            const tenantId = req.query.tenantId;
            const productArr = [];
            let allCategory = await CategoryModel.allCategory({ tenant_id: tenantId, is_deleted: false }, { tenant_id: 1, category_name: 1, type: 1 })
            const lastMonthTopCategory = await CategoryModel.topCategoryStatistic(tenantId, { $gte: moment().add(-1, "month").format("YYYY-MM-DD")})
            if (lastMonthTopCategory.length) {
                lastMonthTopCategory.map(item => {
                    productArr.push(item._id)
                })

                const lastDayCategorySale = await CategoryModel.topCategoryStatistic(tenantId, { $eq: moment().add(-1, "day").format("YYYY-MM-DD") }, productArr)
                const todayDayCategorySale = await CategoryModel.topCategoryStatistic(tenantId, { $eq: moment().add("day").format("YYYY-MM-DD") }, productArr)

                lastMonthTopCategory.map(item => {
                    const lastDatObj = lastDayCategorySale.find(items => item?._id?.toString() === items?._id?.toString())
                    const todayObj = todayDayCategorySale.find(data => item?._id?.toString() === data?._id?.toString())
                    item.last_day_sale_amount = lastDatObj?.total_amount || 0
                    item.today_sale_amount = todayObj?.total_amount || 0
                })

                const newArr = []
                for (let index = 0; index < allCategory.length; index++) {
                    let obj = {};
                    const categoryId = allCategory[index]._id;
                    const tempObj = lastMonthTopCategory.find(item => item?._id?.toString() === categoryId?.toString())

                    obj["_id"] = categoryId
                    obj["category_name"] = allCategory[index].category_name
                    obj["last_day_sale_amount"] = tempObj?.last_day_sale_amount || 0
                    obj["today_sale_amount"] = tempObj?.today_sale_amount || 0
                    obj["total_amount"] = tempObj?.total_amount || 0
                    newArr.push(obj)
                }
                newArr.sort((a, b) => Number(b.total_amount) - Number(a.total_amount))
                newArr.splice(6)

                return res.handler.success(null, newArr);
            } else {
                allCategory.splice(6)
                let newArr = [];
                for (let index = 0; index < allCategory.length; index++) {
                    let obj = {};
                    obj["_id"] = allCategory[index]._id
                    obj["category_name"] = allCategory[index].category_name
                    obj["last_day_sale_amount"] = 0
                    obj["today_sale_amount"] = 0
                    obj["total_amount"] = 0
                    newArr.push(obj)
                }

                return res.handler.success(null, newArr);
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = CategoryController;
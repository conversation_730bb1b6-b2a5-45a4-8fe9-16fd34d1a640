const ConfigurationModel = new (require("../Models/ConfigurationModel"))();

const { VALUES, ENTITY_STATUS } = require('../Configs/constants');

class ConfigurationController {
    async addAndEditTax(req, res) {
        try {
            const body = req.body;

            body.tenantId = Number(body.tenantId);

            const taxInfo = await ConfigurationModel.taxById(body.taxId);

            if (taxInfo) {
                switch (taxInfo.type) {
                    case VALUES.TAX_TYPE.SINGLE:

                        taxInfo.tax_name = body.taxName[0];
                        taxInfo.tax_calculation = body.taxCalculation[0];
                        taxInfo.tax_rate = body.taxRate[0];
                        taxInfo.status = body.status;
                        taxInfo.updated_by = req.headers.userDetails._id;

                        await taxInfo.save();

                        return res.handler.success("updated_tax");

                    case VALUES.TAX_TYPE.GROUP:

                        const obj = [];
                        let tax

                        for (let index = 0; index < body.taxName.length && index < body.taxCalculation.length && index < body.taxRate.length; index++) {
                            const tax_name = body.taxName[index];
                            const tax_calculation = body.taxCalculation[index];
                            const tax_rate = body.taxRate[index];

                            obj.push(tax = { tax_name, tax_calculation, tax_rate })
                        }

                        taxInfo.type = VALUES.TAX_TYPE.GROUP;
                        taxInfo.tax_name = body.groupName;
                        taxInfo.status = body.status;
                        taxInfo.updated_at = new Date().toISOString();

                        await taxInfo.save();
                        await ConfigurationModel.groupValue(body.taxId)

                        for (let index = 0; index < obj.length; index++) {
                            const element = obj[index];

                            body.group_value_type = VALUES.TAX_TYPE.GROUP_SINGLE_VALUE;
                            body.group_id = body.taxId;
                            body.group_value_tax_name = element.tax_name;
                            body.group_value_tax_calculation = element.tax_calculation;
                            body.group_value_tax_rate = element.tax_rate;

                            const addGroupSingleValueTax = await ConfigurationModel.addTax(body, req.headers);
                            await addGroupSingleValueTax.save();
                        }
                        return res.handler.success("updated_tax");
                }
            }

            switch (body.type) {
                case VALUES.TAX_TYPE.SINGLE:

                    body.tenant_id = body.tenantId;
                    body.type = VALUES.TAX_TYPE.SINGLE;
                    body.tax_name = body.taxName[0];
                    body.tax_calculation = body.taxCalculation[0];
                    body.tax_rate = body.taxRate[0];
                    body.status = body.status;

                    const addTax = await ConfigurationModel.addTax(body, req.headers);
                    await addTax.save();

                    return res.handler.success("added_tax");

                case VALUES.TAX_TYPE.GROUP:
                    const obj = [];
                    let tax

                    for (let index = 0; index < body.taxName.length && index < body.taxCalculation.length && index < body.taxRate.length; index++) {
                        const tax_name = body.taxName[index];
                        const tax_calculation = body.taxCalculation[index];
                        const tax_rate = body.taxRate[index];

                        obj.push(tax = { tax_name, tax_calculation, tax_rate })
                    }

                    body.tenant_id = body.tenantId;
                    body.type = VALUES.TAX_TYPE.GROUP;
                    body.tax_name = body.groupName;
                    body.status = body.status;

                    const addGroupTax = await ConfigurationModel.addTax(body, req.headers);
                    await addGroupTax.save();

                    if (addGroupTax) {
                        for (let index = 0; index < obj.length; index++) {
                            const element = obj[index];

                            body.group_value_type = VALUES.TAX_TYPE.GROUP_SINGLE_VALUE;
                            body.group_id = addGroupTax._id;
                            body.tenant_id = body.tenantId;
                            body.group_value_tax_name = element.tax_name;
                            body.group_value_tax_calculation = element.tax_calculation;
                            body.group_value_tax_rate = element.tax_rate;

                            const addGroupSingleValueTax = await ConfigurationModel.addTax(body, req.headers);
                            await addGroupSingleValueTax.save();

                        }
                        return res.handler.success("added_tax");
                    }
            }

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeTaxStatus(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);

            await ConfigurationModel.updateTaxStatus(body.taxId, body.status, req.headers)

            let message;
            switch (body.status) {
                case ENTITY_STATUS.ACTIVE:
                    message = "tax_active"
                    break;

                default:
                    message = "tax_inactive"
                    break;
            }

            return res.handler.success(message);

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async taxList(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);

            if (body.textSetting === true) {
                const result = await ConfigurationModel.masterTaxInfo(body.tenantId, { created_by: 0, updated_by: 0, created_at: 0, updated_at: 0 });
                return res.handler.success(null, result);
            }

            if (body.taxId) {
                const taxDetail = await ConfigurationModel.taxById(body.taxId)

                if (taxDetail.type === VALUES.TAX_TYPE.GROUP) {
                    const groupTax = await ConfigurationModel.taxList(body.tenantId, body.status, VALUES.TAX_TYPE.GROUP, body.taxId)
                    return res.handler.success(null, { taxDetail, groupTax });
                }

                return res.handler.success(null, taxDetail);
            }

            const result = await ConfigurationModel.taxList(body.tenantId, body.status, body.type, body.groupId)

            return res.handler.success(null, result);

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteTax(req, res) {
        try {
            const body = req.query;

            const result = await ConfigurationModel.taxById(body.taxId)

            if (result.type === VALUES.TAX_TYPE.GROUP) {
                await ConfigurationModel.groupValue(body.taxId)
            }

            await result.deleteOne();

            return res.handler.success("delete_tax");

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async masterTaxSetting(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);

            const masterTaxInfo = await ConfigurationModel.masterTaxInfo(body.tenantId);

            if (masterTaxInfo) {
                masterTaxInfo.enable_tax = body.enableTax;
                masterTaxInfo.price = body.price;
                masterTaxInfo.default_tax = body.defaultTax;
                masterTaxInfo.universal_tax = body.universalTax;

                await masterTaxInfo.save();

                return res.handler.success("updated_tax_settings");
            }

            body.tenant_id = body.tenantId;
            body.enable_tax = body.enableTax;
            body.price = body.price;
            body.default_tax = body.defaultTax;
            body.universal_tax = body.universalTax;

            const result = await ConfigurationModel.addMasterTax(body, req.headers)
            await result.save();

            return res.handler.success("added_tax_settings");

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getMasterTaxSetting(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);

            const result = await ConfigurationModel.masterTaxInfo(body.tenantId);

            return res.handler.success(null, result);

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = ConfigurationController;

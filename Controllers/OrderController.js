const OrderModel = new (require("../Models/OrderModel"));
const sendFailureEmail = require("../Middleware/SendFailureEmail").default

const ProductModel = new (require("../Models/ProductModel"));
const InternalServiceModel = new (require("../Models/InternalServiceModel"));

const PlaceOrderHelper = new (require("../Utils/placeOrderHelper"))
const OrderService = require("../Services/orders/OrderService");

const {
    NEW_PRODUCT_TYPE,
    CART_ACTIONS,
    ORDER_STATUS_TYPES,
    ORDER_APP_TYPE,
    ORDER_ITEM_LISTING_TYPES,
    DURATION_PERIOD_OPTIONS,
    SALES_TYPE,
    SALE_DURATION_TYPE,
    DEAL_TYPE,
    DEAL_STATUS,
    FALSELY_VALUES,
    STATUS_CODES
} = require("../Configs/constants");

const {
    sendCatalogModeEnabledEmail
} = require("../Utils/email_templates");

const {
    httpService,
    roundOf,
    excludeProjections
} = require("../Utils/helpers");

class OrderController {

    placeOrder = async (req, res) => {
        let session, Order;

        try {
            const hasErrMsg = await PlaceOrderHelper.handleMissingShipData(req)

            if (hasErrMsg) {
                return res.handler.badRequest(hasErrMsg)
            }

            const {
                tenantId,
                customerUserRoleId,
                salesPersonRoleId,
                customerName,
                salePersonName,
                branchId,
                externalId,
                orderAppType,
                customer_legal_name,
                orderPunchDeviceType,
                orderPunchDeviceOs,
                orderRemark,
                ShippingAddress,
                cityId,
                cityName,
                regionId,
                regionName,
                shippingMobileNumber,
                shippingCountryCode,
                shippingCoordinates,
                customerPrimaryContactName,
            } = req.body

            // Variables to store data for use outside transaction
            let cartItems, totalQty;
            const userroleid = req.headers.userroleid

            const cartId =
                customerUserRoleId === userroleid
                    ? `${tenantId}_${customerUserRoleId}`
                    : `${tenantId}_${customerUserRoleId}_${userroleid}`

            cartItems = await OrderModel.findCartItems(
                {
                    cart_id: cartId,
                },
                undefined,
                {
                    "sort": { "created_at": -1 },
                    "lean": true
                }
            )

            if (!cartItems.length) {
                sendFailureEmail(req, "No cart items found")
                    .catch(error => {
                        logger.error(error, {
                            errorMessage: "placeOrder: no_cart_items_found"
                        })
                    })
                return res.handler.notFound("no_cart_items_found")
            }

            // Start mongoose session for transaction
            session = await mongoose.startSession();

            // Start transaction
            const transactionResult = await session.withTransaction(async () => {
                let [OrderDoc, OrderCounter] = await Promise.all([
                    OrderModel.addOrder(),
                    httpService(req).get("currentPrefix", { params: { tenantId: tenantId } })
                ])
                OrderCounter = OrderCounter?.data?.data

                if (!OrderDoc) {
                    throw new Error("🚀 ~ Could not find the order counter info ~ 🚀")
                }

                OrderDoc.tenant_id = tenantId
                OrderDoc.customer_user_role_id = customerUserRoleId
                OrderDoc.sales_user_role_id = salesPersonRoleId
                OrderDoc.sales_person_name = salePersonName
                OrderDoc.customer_name = customerName
                OrderDoc.branch_id = branchId
                OrderDoc.external_id = externalId
                OrderDoc.order_creator_user_role_id = req.headers.userroleid
                OrderDoc.order_creator_name =
                    orderAppType === ORDER_APP_TYPE.CUSTOMER_APP
                        ? customer_legal_name
                        : `${req.headers.userDetails.first_name} ${req.headers.userDetails.last_name}`
                //TODO: need to save reduce_inventory option while saving the order items

                OrderDoc.order_number = `${OrderCounter.prefix}${OrderCounter.counter}`
                OrderDoc.order_punching_device_type = orderPunchDeviceType
                OrderDoc.order_punching_device_os = orderPunchDeviceOs
                OrderDoc.order_remark = orderRemark
                //TODO: need to check for below roles. What type of order status needs to set, when below profiles place the order
                OrderDoc.order_app_type = orderAppType
                OrderDoc.order_status =
                    orderAppType === ORDER_APP_TYPE.CUSTOMER_APP
                        ? ORDER_STATUS_TYPES.PENDING
                        : ORDER_STATUS_TYPES.RECEIVED

                OrderDoc.order_status_track = [{
                    order_status: OrderDoc.order_status,
                    time: new Date(),
                    user_info: {
                        user_role_id: userroleid,
                        name: OrderDoc.order_creator_name,
                    }
                }]

                OrderDoc.shipping_address = ShippingAddress;
                OrderDoc.city_id = cityId;
                OrderDoc.city_name = cityName;
                OrderDoc.region_id = regionId;
                OrderDoc.region_name = regionName;
                OrderDoc.shipping_mobile_number = shippingMobileNumber;
                OrderDoc.shipping_country_code = shippingCountryCode;
                OrderDoc.gps_coordinate = shippingCoordinates;
                OrderDoc.created_by = req.headers.userDetails?._id;
                OrderDoc.update_by = req.headers.userDetails?._id;
                OrderDoc.customer_primary_contact_name = customerPrimaryContactName;
                OrderDoc.customer_legal_name = customer_legal_name;
                OrderDoc.unique_order_number = `${OrderDoc.tenant_id}${OrderDoc.order_number}`;

                // Pre-approved flag will be set to false by default and updated outside transaction if check is successful
                OrderDoc.pre_approved = false;

                let order_total_amount = 0
                let order_total_tax = 0
                totalQty = 0

                const taxCalculationMap = new Map()
                const uniqueMasterPriceIdsSet = new Set();
                const orderItems = []

                for (let i = 0; i < cartItems.length; i++) {
                    const cItem = cartItems[i]

                    const [
                        parent_id,
                        variant_value_id,
                        group_value_id,
                    ] = cItem.variant_id?.split("_") || []

                    const OrderItem = await OrderModel.addOrderItem()

                    OrderItem.tenant_id = tenantId
                    OrderItem.branch_id = branchId
                    OrderItem.order_id = OrderDoc._id
                    OrderItem.sort_order = i + 1
                    OrderItem.customer_user_role_id = customerUserRoleId
                    OrderItem.sales_user_role_id = salesPersonRoleId
                    OrderItem.product_variant_id = cItem.product_variant_id
                    OrderItem.variant_id = cItem.variant_id
                    OrderItem.parent_id = parent_id
                    OrderItem.variant_value_id = variant_value_id
                    OrderItem.group_value_id = group_value_id
                    OrderItem.item_number = cItem.item_number
                    OrderItem.parent_item_number = cItem.parent_item_number
                    OrderItem.product_name = cItem.product_name
                    OrderItem.variant_name = cItem.variant_name
                    OrderItem.product_secondary_name = cItem.product_secondary_name
                    OrderItem.variant_secondary_name = cItem.variant_secondary_name
                    OrderItem.group_secondary_name = cItem.group_secondary_name
                    OrderItem.group_name = cItem.group_name

                    totalQty += cItem.quantity
                    if (cItem.deal_info?.deal_type === DEAL_TYPE.BUY_X_AND_GET_Y) {
                        totalQty += cItem.deal_info.free_item_count
                    }
                    OrderItem.quantity = cItem.quantity

                    OrderItem.base_price = cItem.base_price
                    OrderItem.original_price = cItem.original_price
                    order_total_amount += (cItem.base_price * cItem.quantity)

                    OrderItem.tax = cItem.tax;
                    order_total_tax += (cItem.tax * cItem.quantity);
                    OrderItem.master_price_id = cItem.master_price_id;

                    const masterPriceIdString = cItem.master_price_id.toString();

                    if (!uniqueMasterPriceIdsSet.has(masterPriceIdString)) {
                        uniqueMasterPriceIdsSet.add(masterPriceIdString);
                    }
                    OrderItem.item_comment = cItem.item_comment || [];

                    if (cItem.deal_info) {
                        OrderItem.item_comment.push(`${cItem.deal_info.deal_name} || ${cItem.deal_info.deal_number} Deal was applied.`);
                        OrderItem.deal_info = cItem.deal_info;
                    }

                    OrderItem.uom_id = cItem.uom_id;
                    OrderItem.uom_name = cItem.uom_name;
                    //TODO: need to save reduce_inventory option while saving the order items
                    OrderItem.reduce_inventory = false;
                    OrderItem.tax_calculation_info = cItem.tax_calculation_info;

                    // Add OrderItem to array instead of pushing save promise
                    orderItems.push(OrderItem)

                    OrderModel.updateTaxCalculationMap(cItem.tax_calculation_info, taxCalculationMap, cItem.quantity)
                }

                OrderDoc.total_amount = roundOf(order_total_amount, 3)
                OrderDoc.total_tax = roundOf(order_total_tax, 3)
                OrderDoc.tax_calculation_info = taxCalculationMap
                OrderDoc.master_price_ids = Array.from(uniqueMasterPriceIdsSet);

                // Execute all database operations within transaction
                await Promise.all([
                    ...orderItems.map(orderItem => orderItem.save({ session })),
                    OrderDoc.save({ session }),
                    OrderModel.deleteCartItems({ cart_id: cartId }, { session }),
                ])

                // Store Order reference for use outside transaction
                Order = OrderDoc
            })

            // TODO: This is the temporary solution to decouple pre-approved status update from place order. For robust solution, we need to use AWS-SQS
            OrderService.updatePreApprovedStatus(customerUserRoleId, tenantId, Order?._id)
                .catch(error => {
                    logger.error(error, {
                        errorMessage: "placeOrder: Error in pre-approved status update"
                    })
                })

            try {
                const oData = {
                    "tenantId": Order.tenant_id,
                    "branchId": Order.branch_id,
                    "salesPersonName": Order.sales_person_name,
                    "customerLegalName": Order.customer_legal_name,
                    "customerUserRoleId": Order.customer_user_role_id,
                    "salesPersonUserRoleId": Order.sales_user_role_id,

                    "_id": Order._id,
                    "orderId": Order.order_number,
                    "orderDate": Order.created_at,
                    "numberOfItems": cartItems.length,
                    "totalQty": totalQty,
                    "orderAmount": Order.total_amount + Order.total_tax,
                    "orderStatus": Order.order_status,
                    "orderStatusTrack": Order.order_status_track,

                    "shippingAddress": Order.shipping_address,
                    "shippingCity": Order.city_name,
                    "shippingRegion": Order.region_name,
                    "shippingMobileNumber": `${Order.shipping_country_code} ${Order.shipping_mobile_number}`
                }

                httpService(req)
                    .post("sendOrderEmails", oData)
                    .catch(error => {
                        logger.error(error, {
                            errorMessage: "placeOrder: COULD NOT SEND NEW_ORDER NOTIFICATIONS"
                        })
                    })
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "placeOrder: ERROR SENDING NEW_ORDER NOTIFICATIONS"
                })
            }
            return res.handler.success("order_placed_successfully", Order)
        }
        catch (error) {
            sendFailureEmail(req, error)
                .catch(error => {
                    logger.error(error, {
                        errorMessage: "placeOrder: COULD NOT SEND EMAIL(S) from catch block"
                    })
                })

            // Check for duplicate order number error
            if (error?.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE && error?.keyValue?.unique_order_number) {
                return res.handler.conflict('could_not_process_order');
            }
            const newError = new Error(error)

            newError.metadata = {
                order_number: Order?.order_number,
            }
            return res.handler.serverError(newError, newError.metadata)
        }
        finally {
            await session?.endSession()
        }
    }

    getOrder = async (req, res) => {
        try {
            let orderDetail;
            const {
                orderItemListingType = ORDER_ITEM_LISTING_TYPES.PAGINATION,
                orderId,
                tenantId,
                branchId,
                orderDetails,
            } = req.query;

            const filter = {
                "_id": orderId,
                "tenant_id": tenantId,
            };

            if (branchId) {
                filter["branch_id"] = branchId
            }

            if (orderDetails) {
                orderDetail = await OrderModel.findOrder(filter)

                if (!orderDetail) {
                    return res.handler.notFound("order_not_found");
                }
                return res.handler.success(null, orderDetail)
            }

            switch (orderItemListingType) {
                case ORDER_ITEM_LISTING_TYPES.PAGINATION:
                    orderDetail = await OrderModel.getOrder(req);
                    break;

                case ORDER_ITEM_LISTING_TYPES.ALL:
                    orderDetail = await OrderModel.findOrderItems(
                        {
                            "order_id": orderId,
                            "tenant_id": tenantId
                        },
                        {
                            "created_at": 0,
                            "updated_at": 0,
                            "__v": 0,
                            "updated_by": 0
                        },
                        {
                            "lean": true
                        }
                    )
                    break;

                default:
                    orderDetail = await OrderModel.getOrder(req);
                    break;
            }
            return res.handler.success(null, orderDetail)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listOrders(req, res) {
        try {
            const {
                page = 1,
                perPage = 10,
                tenantId = "",
                searchKey = "",
                orderStatus = null,
                branchId = null,
                salesPersonRoleId = null,
                masterPriceId = null,
                customerUserRoleId = null,
                orderAppType = null,
                orderCountStatusType = null,
                onlyCount = false,
                duration = null,
                durationPeriod = DURATION_PERIOD_OPTIONS.months,

                //Only PORTAL FILTER PARAMS
                portalAppTypeFilter = null,
            } = req.query;

            const userroleid = req.headers.userroleid;
            const offset = (page - 1) * perPage;
            const pipeline = [];

            const $match = {
                "tenant_id": tenantId,
            };

            const $and = [];
            let $sort = { "created_at": -1 }

            if (branchId) {
                $match["branch_id"] = new mongoose.Types.ObjectId(branchId)
            }

            //APPLICATION FILTER
            if (duration) {
                $match["created_at"] = {
                    "$gte": moment().add((-1) * duration, durationPeriod).toDate()
                }
            }

            if (salesPersonRoleId) {
                $match["sales_user_role_id"] = new mongoose.Types.ObjectId(salesPersonRoleId);
            }
            else if (customerUserRoleId) {
                $match["customer_user_role_id"] = new mongoose.Types.ObjectId(customerUserRoleId);
            } else if (orderAppType === ORDER_APP_TYPE.SUPERVISOR_APP) {
                const salesPersonsRes = await httpService(req)
                    .get(
                        "salesPersonsBySupervisorId",
                        {
                            "params": {
                                tenantId,
                                "supervisorUserRoleId": userroleid
                            }
                        }
                    );

                $and.push({
                    $or: [
                        {
                            "sales_user_role_id":
                            {
                                "$in":
                                    salesPersonsRes?.data?.data
                                        ? salesPersonsRes.data.data.map(sId => mongoose.Types.ObjectId(sId._id))
                                        : []
                            }
                        },
                        {
                            "order_creator_user_role_id": new mongoose.Types.ObjectId(userroleid),
                            "order_app_type": ORDER_APP_TYPE.SUPERVISOR_APP
                        }
                    ]
                })
            }
            // for ADMIN & TENANT_OWNER no need to add filter as we need to show all the orders from the tenant

            if (searchKey) {
                $and.push({
                    $or: [
                        {
                            "sales_person_name": {
                                "$regex": searchKey,
                                "$options": "i"
                            }
                        },
                        {
                            "order_number": {
                                "$regex": searchKey,
                                "$options": "i"
                            }
                        }
                    ]
                })
            }

            let sortingPipeline = []

            if (orderStatus) {
                if (orderStatus === ORDER_STATUS_TYPES.DASHBOARD_ORDERS) {
                    $match["order_status"] = {
                        "$ne": ORDER_STATUS_TYPES.CANCELLED
                    }
                }
                else {
                    $match["order_status"] = orderStatus;
                }

                if (
                    orderStatus === ORDER_STATUS_TYPES.SHIPPED ||
                    orderStatus === ORDER_STATUS_TYPES.DELIVERED
                ) {
                    $sort = { "updated_at": -1 };
                }

                if (orderStatus === ORDER_STATUS_TYPES.RECEIVED) {
                    $match["order_status"] = {
                        $in: [
                            ORDER_STATUS_TYPES.RELEASED,
                            ORDER_STATUS_TYPES.RECEIVED
                        ]
                    }

                    sortingPipeline = [
                        {
                            $addFields: {
                                sortIndex: {
                                    $indexOfArray: [
                                        [
                                            ORDER_STATUS_TYPES.RELEASED,
                                            ORDER_STATUS_TYPES.RECEIVED
                                        ], "$order_status",
                                    ]
                                }
                            }
                        },
                    ]

                    $sort = {
                        sortIndex: 1,
                        ...$sort
                    }
                }

            }

            if (masterPriceId) {
                $match["master_price_ids"] = new mongoose.Types.ObjectId(masterPriceId);
            }

            //PORTAL FILTER
            if (portalAppTypeFilter) {
                $match["order_app_type"] = portalAppTypeFilter
            }

            if ($and.length) {
                $match.$and = $and
            }

            pipeline.push({ $match })

            if (onlyCount) {
                pipeline.push(
                    {
                        "$match": {
                            "order_status": orderCountStatusType
                        }
                    },
                    {
                        "$count": "order_count"
                    }
                );

                let orderCount = await OrderModel.aggregateOrders(pipeline);

                orderCount = orderCount[0] || { "order_count": 0 };

                return res.handler.success("order_count", orderCount);
            }
            else {
                pipeline.push(
                    {
                        "$addFields": {
                            "order_status_changed_at": {
                                "$last": "$order_status_track.time"
                            }
                        }
                    },
                    {
                        "$project": {
                            "order_remark": 0,
                            "shipping_address": 0,
                            "city_id": 0,
                            "city_name": 0,
                            "region_id": 0,
                            "region_name": 0,
                            "shipping_mobile_number": 0,
                            "shipping_country_code": 0,
                            "gps_coordinate": 0,
                            "created_by": 0,
                            "update_by": 0,
                            "__v": 0,
                            "order_status_track": 0
                        }
                    },
                    ...sortingPipeline,
                    {
                        $sort
                    },
                    {
                        "$facet": {
                            "orders": [
                                { "$skip": offset },
                                { "$limit": perPage }
                            ],
                            "count": [{ "$count": "count" }]
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$orders"
                        }
                    },
                );

                if (Object.values(ORDER_APP_TYPE).includes(orderAppType)) {
                    pipeline.push(
                        {
                            "$lookup": {
                                "from": "order_items",
                                "localField": "orders._id",
                                "foreignField": "order_id",
                                "as": "orders.order_items",
                                "pipeline": [
                                    {
                                        "$project": {
                                            "product_variant_id": 1,
                                            "product_name": 1,
                                            "variant_name": 1,
                                            "group_name": 1,
                                            "sort_order": 1,
                                        }
                                    },
                                    {
                                        "$sort": {
                                            "sort_order": 1,
                                        }
                                    },
                                    {
                                        "$limit": 12
                                    },
                                    {
                                        "$lookup": {
                                            "from": "images_2.0",
                                            "localField": "product_variant_id",
                                            "foreignField": "product_variant_id",
                                            "as": "cover_image",
                                            "pipeline": [
                                                {
                                                    "$match": {
                                                        "group_id": null
                                                    }
                                                },
                                                {
                                                    "$project": {
                                                        "image_name": 1,
                                                        "s3_url": 1,
                                                        "image_number": 1,
                                                        "updated_at": 1,
                                                    }
                                                },
                                                {
                                                    "$sort": { "image_name": 1 }
                                                },
                                                {
                                                    "$limit": 1
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "$addFields": {
                                            "cover_image": {
                                                "$first": "$cover_image"
                                            },
                                        }
                                    },
                                    {
                                        "$lookup": {
                                            "from": "products_2.0",
                                            "localField": "product_variant_id",
                                            "foreignField": "_id",
                                            "as": "product",
                                            "pipeline": [
                                                {
                                                    "$project": {
                                                        "parent_id": 1,
                                                        "group_value_id": 1,
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    {
                                        "$addFields": {
                                            "product": {
                                                "$first": "$product",
                                            },
                                        },
                                    },
                                    {
                                        "$lookup": {
                                            "from": "images_2.0",
                                            "localField": "product.parent_id",
                                            "foreignField": "product_variant_id",
                                            "as": "group_cover_image",
                                            "let": {
                                                "groupId": "$product.group_value_id"
                                            },
                                            "pipeline": [
                                                {
                                                    "$match": {
                                                        "$expr": {
                                                            "$eq": ["$group_id", "$$groupId"],
                                                        },
                                                        "group_id": { "$ne": null },
                                                    },
                                                },
                                                {
                                                    "$project": {
                                                        "image_name": 1,
                                                        "image_number": 1,
                                                        "s3_url": 1,
                                                        "updated_at": 1,
                                                    },
                                                },
                                                {
                                                    "$sort": { "image_number": 1 },
                                                },
                                                {
                                                    "$limit": 1,
                                                },
                                            ],
                                        },
                                    },
                                    {
                                        "$lookup": {
                                            "from": "images",
                                            "localField": "product.parent_id",
                                            "foreignField": "product_variant_id",
                                            "as": "parent_cover_image",
                                            "pipeline": [
                                                {
                                                    "$match": {
                                                        "group_id": null
                                                    }
                                                },
                                                {
                                                    "$project": {
                                                        "image_name": 1,
                                                        "image_number": 1,
                                                        'product_variant_id': 1,
                                                        "s3_url": 1,
                                                        "updated_at": 1,
                                                    },
                                                },
                                                {
                                                    "$sort": { "image_number": 1 },
                                                },
                                                {
                                                    "$limit": 1,
                                                },
                                            ],
                                        },
                                    },
                                    {
                                        "$addFields": {
                                            "group_cover_image": {
                                                "$first": "$group_cover_image"
                                            },
                                            "parent_cover_image": {
                                                "$first": "$parent_cover_image"
                                            },
                                        }
                                    },
                                    {
                                        "$project": {
                                            "product": 0,
                                        }
                                    }
                                ]
                            },
                        }
                    )
                }
                const data = await OrderModel.aggregateOrders(pipeline);

                const list =
                    data.length
                        ? data.map(o => o.orders)
                        : [];
                const count = data[0]?.count[0]?.count || 0;

                return res.handler.success("order_list", { list, count });
            }
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateOrderStatus(req, res) {
        try {
            const {
                orderIds,
                orderStatus,
                tenantId
            } = req.body;

            await OrderModel.updateOrderStatus(orderIds, orderStatus, req.headers)

            let message;
            switch (orderStatus) {
                case ORDER_STATUS_TYPES.PENDING:
                    message = "update_pending_order_status"
                    break;

                case ORDER_STATUS_TYPES.RECEIVED:
                    message = "update_received_order_status"
                    break;

                case ORDER_STATUS_TYPES.RELEASED:
                    message = "update_released_order_status"
                    break;

                case ORDER_STATUS_TYPES.PREPARING:
                    message = "update_preparing_order_status"
                    break;

                case ORDER_STATUS_TYPES.SHIPPED:
                    message = "update_shipped_order_status"
                    break;

                case ORDER_STATUS_TYPES.DELIVERED:
                    message = "update_delivered_order_status"
                    break;

                case ORDER_STATUS_TYPES.CANCELLED:
                    message = "update_cancelled_order_status"
                    break;

                case ORDER_STATUS_TYPES.ON_HOLD:
                    message = "update_onhold_order_status"
                    break;
            }

            if (
                Array.isArray(orderIds) &&
                (
                    orderStatus === ORDER_STATUS_TYPES.RECEIVED ||
                    orderStatus === ORDER_STATUS_TYPES.PREPARING ||
                    orderStatus === ORDER_STATUS_TYPES.SHIPPED ||
                    orderStatus === ORDER_STATUS_TYPES.DELIVERED
                )
            ) {
                const orderEmailPipeline = [
                    {
                        $match: {
                            _id: {
                                $in: orderIds.map(id => {
                                    return new mongoose.Types.ObjectId(id)
                                }),
                            }
                        }
                    },
                    {
                        $lookup: {
                            from: "order_items",
                            localField: "_id",
                            foreignField: "order_id",
                            as: "item_info",
                            pipeline: [
                                {
                                    $facet: {
                                        numberOfItems: [{ $count: "item_count" }],
                                        totalQty: [{
                                            $project: {
                                                order_id: 1,
                                                quantity: 1
                                            }
                                        },
                                        {
                                            $group: {
                                                _id: { order_id: '$order_id' },
                                                qty: { $sum: "$quantity" }
                                            }
                                        }]
                                    }
                                }
                            ]
                        }
                    },
                    {
                        $unwind: {
                            path: "$item_info"
                        }
                    },
                    {
                        $unwind: {
                            path: "$item_info.numberOfItems"
                        }
                    },
                    {
                        $unwind: {
                            path: "$item_info.totalQty"
                        }
                    },
                    {
                        $project: {
                            tenantId: "$tenant_id",
                            branchId: "$branch_id",

                            salesPersonName: "$sales_person_name",
                            customerLegalName: "$customer_legal_name",
                            customerUserRoleId: "$customer_user_role_id",
                            salesPersonUserRoleId: "$sales_user_role_id",

                            external_id: "$external_id",
                            pre_approved: "$pre_approved",

                            orderId: "$order_number",
                            orderDate: "$created_at",
                            numberOfItems: "$item_info.numberOfItems.item_count",
                            totalQty: "$item_info.totalQty.qty",
                            orderAmount: { $sum: ["$total_amount", "$total_tax"] },
                            orderStatus: "$order_status",
                            orderStatusTrack: "$order_status_track",
                            shippingAddress: "$shipping_address",
                            shippingCity: "$city_name",
                            shippingRegion: "$region_name",
                            shippingMobileNumber: { $concat: ["$shipping_country_code", " ", { $toString: { $toLong: "$shipping_mobile_number" } }] }
                        }
                    }
                ];

                const emailPromises = [];
                const ordersWithEmailData = await OrderModel.aggregateOrders(orderEmailPipeline);

                if (Array.isArray(ordersWithEmailData)) {
                    ordersWithEmailData.forEach(oData => {
                        emailPromises.push(
                            httpService(req).post("sendOrderEmails", oData)
                        );
                    });

                    Promise.all(emailPromises).catch(error => {
                        logger.error(error, {
                            errorMessage: "COULD NOT SEND EMAIL(S) => OrderController.js:1543 ~ OrderController ~ updateOrderStatus"
                        })
                    });
                }
            }
            //============================ Start: Flow for check customer pre approved flag ============================
            try {
                if (
                    [
                        ORDER_STATUS_TYPES.RECEIVED,
                        ORDER_STATUS_TYPES.RELEASED,
                    ].includes(orderStatus)
                ) {
                    const orders = await OrderModel.findOrders(
                        {
                            _id: {
                                $in: [
                                    orderIds
                                ]
                            },
                            external_id: {
                                $exists: true,
                                $nin: FALSELY_VALUES
                            }
                        },
                        "pre_approved external_id",
                        {
                            lean: true
                        }
                    )

                    const externalIds = orders.map(order => {
                        return order.external_id
                    })

                    if (externalIds.length) {
                        const {
                            statuscode,
                            data,
                            error
                        } = await InternalServiceModel.checkCustomersPreApprovedStatus(
                            tenantId,
                            externalIds
                        )

                        if (statuscode === STATUS_CODES.SUCCESS) {
                            let updatePreApproveTrue = []
                            let updatePreApproveFalse = []

                            orders.forEach(order => {
                                if (order.external_id) {
                                    const pre_approved = data?.[order.external_id] ?? false

                                    if (order.pre_approved !== pre_approved) {
                                        if (pre_approved) {
                                            updatePreApproveTrue.push(order._id)
                                        }
                                        else {
                                            updatePreApproveFalse.push(order._id)
                                        }
                                    }
                                }
                            })

                            let tasks = []

                            if (updatePreApproveTrue.length) {
                                tasks.push(OrderModel.updateOrder(
                                    {
                                        _id: {
                                            $in: updatePreApproveTrue
                                        }
                                    },
                                    {
                                        pre_approved: true
                                    }
                                ))
                            }

                            if (updatePreApproveFalse.length) {
                                tasks.push(OrderModel.updateOrder(
                                    {
                                        _id: {
                                            $in: updatePreApproveFalse
                                        }
                                    },
                                    {
                                        pre_approved: false
                                    }
                                ))
                            }

                            const results = await Promise.allSettled(tasks)
                            results.forEach(result => {
                                if (result.reason) {
                                    logger.error(result.reason)
                                }
                            })
                        }
                        else {
                            logger.error(error)
                        }
                    }
                }
            }
            catch (error) {
                logger.error(error)
            }
            //============================ End: Flow for check customer pre approved flag ============================

            return res.handler.success(message);

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getOrderStats(req, res) {
        try {
            const { userRoleId } = req.query;
            if (userRoleId) {
                const result = await OrderModel.getSalesStatistic({ _id: userRoleId });
                return res.handler.success("order_stats", { accepted_order_count: result?.accepted_order_count || 0, total_sales: (result?.total_sales || 0) + (result?.total_tax || 0) });
            }
            const result = await OrderModel.getSalesStatistics({ _id: { $in: req.body } });
            return res.handler.success("order_stats", result.map((item) => ({ accepted_order_count: item?.accepted_order_count || 0, total_sales: (+item?.total_sales || 0) + (+item?.total_tax || 0), _id: item._id })));
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    getDashboardSummary = async (req, res) => {
        try {
            const { tenantId, branchId } = req.query

            const order_status = {
                $in: [
                    ORDER_STATUS_TYPES.RELEASED,
                    ORDER_STATUS_TYPES.RECEIVED,
                    ORDER_STATUS_TYPES.PREPARING,
                    ORDER_STATUS_TYPES.SHIPPED,
                    ORDER_STATUS_TYPES.DELIVERED
                ]
            }
            const allSummaries = []
            const daysList = [-13, -6, -5, -4, -3, -2, -1, 0]

            // ----------------------------- Orders Summary -----------------------------

            const orderSummaryPromises = []

            daysList.forEach((day) => {
                orderSummaryPromises.push(
                    OrderModel.countOrders({
                        "tenant_id": tenantId,
                        "branch_id": branchId,
                        "created_at": {
                            "$gte":
                                moment()
                                    .add(day, "days")
                                    .format("YYYY-MM-DD")
                        },
                        order_status
                    })
                )
            })

            const statusList = [
                ORDER_STATUS_TYPES.RECEIVED,
                ORDER_STATUS_TYPES.RELEASED,
                ORDER_STATUS_TYPES.PREPARING,
            ]

            statusList.forEach(order_status => {
                orderSummaryPromises.push(
                    OrderModel.countOrders({
                        "tenant_id": tenantId,
                        "branch_id": branchId,
                        order_status
                    })
                )
            })

            const [
                last14Days = 0,
                last7Days = 0,
                last6Days = 0,
                last5Days = 0,
                last4Days = 0,
                last3Days = 0,
                last2Days = 0,
                last1Days = 0,
                receivedOrders = 0,
                releasedOrders = 0,
                preparingOrders = 0,
            ] = await Promise.all(orderSummaryPromises)

            const lastWeekOrders = last14Days - last7Days
            const firstDayOrders = last1Days
            const secondDayOrders = last2Days - last1Days
            const thirdDayOrders = last3Days - last2Days
            const fourthDayOrders = last4Days - last3Days
            const fifthDayOrders = last5Days - last4Days
            const sixthDayOrders = last6Days - last5Days
            const seventhDayOrders = last7Days - last6Days

            const arrayOfOrders = [
                firstDayOrders,
                secondDayOrders,
                thirdDayOrders,
                fourthDayOrders,
                fifthDayOrders,
                sixthDayOrders,
                seventhDayOrders,
            ]

            allSummaries.push({
                "name": "Orders",
                "data": arrayOfOrders,
                "weekCount": last7Days,
                "lastWeekCount": lastWeekOrders,
                "receivedOrders": receivedOrders + releasedOrders,
                "preparingOrders": preparingOrders,
            })

            // ----------------------------- Sales Summary -----------------------------

            const salesSummaryPromises = []

            daysList.forEach((day) => {
                salesSummaryPromises.push(
                    OrderModel.findOrders(
                        {
                            "tenant_id": tenantId,
                            "branch_id": branchId,
                            "created_at": {
                                "$gte":
                                    moment()
                                        .add(day, "days")
                                        .format("YYYY-MM-DD")
                            },
                            order_status
                        },
                        {
                            "total_amount": 1,
                            "total_tax": 1
                        }
                    )
                )
            })

            const [
                last14DaySales = [],
                last7DaySales = [],
                last6DaySales = [],
                last5DaySales = [],
                last4DaySales = [],
                last3DaySales = [],
                last2DaySales = [],
                last1DaySales = [],
            ] = await Promise.all(salesSummaryPromises)

            const getSalesAmount = (salesData) => {
                if (salesData?.length) {
                    return salesData
                        .map(item => item.total_amount + item.total_tax)
                        .reduce((prev, next) => prev + next)
                }
                return 0
            }

            const last14DaySalesAmount = getSalesAmount(last14DaySales)
            const last7DaySalesAmount = getSalesAmount(last7DaySales)
            const last6DaySalesAmount = getSalesAmount(last6DaySales)
            const last5DaySalesAmount = getSalesAmount(last5DaySales)
            const last4DaySalesAmount = getSalesAmount(last4DaySales)
            const last3DaySalesAmount = getSalesAmount(last3DaySales)
            const last2DaySalesAmount = getSalesAmount(last2DaySales)
            const last1DaySalesAmount = getSalesAmount(last1DaySales)

            const lastWeekSalesAmount = last14DaySalesAmount - last7DaySalesAmount
            const firstDaySalesAmount = last1DaySalesAmount
            const secondDaySalesAmount = last2DaySalesAmount - last1DaySalesAmount
            const thirdDaySalesAmount = last3DaySalesAmount - last2DaySalesAmount
            const fourthDaySalesAmount = last4DaySalesAmount - last3DaySalesAmount
            const fifthDaySalesAmount = last5DaySalesAmount - last4DaySalesAmount
            const sixthDaySalesAmount = last6DaySalesAmount - last5DaySalesAmount
            const seventhDaySalesAmount = last7DaySalesAmount - last6DaySalesAmount

            const arrayOfSalesAmount = [
                firstDaySalesAmount,
                secondDaySalesAmount,
                thirdDaySalesAmount,
                fourthDaySalesAmount,
                fifthDaySalesAmount,
                sixthDaySalesAmount,
                seventhDaySalesAmount,
            ]

            allSummaries.push({
                "name": "Sales Amount",
                "data": arrayOfSalesAmount,
                "weekCount": last7DaySalesAmount,
                "lastWeekCount": lastWeekSalesAmount,
            })

            // ----------------------------- Shipped Summary -----------------------------

            const shippedSummaryPromises = []

            daysList.forEach((day) => {
                shippedSummaryPromises.push(
                    OrderModel.countOrders({
                        "tenant_id": tenantId,
                        "branch_id": branchId,
                        "created_at": {
                            "$gte":
                                moment()
                                    .add(day, "days")
                                    .format("YYYY-MM-DD")
                        },
                        "order_status": ORDER_STATUS_TYPES.SHIPPED
                    })
                )
            })

            const [
                last14DayShipped = 0,
                last7DayShipped = 0,
                last6DayShipped = 0,
                last5DayShipped = 0,
                last4DayShipped = 0,
                last3DayShipped = 0,
                last2DayShipped = 0,
                last1DayShipped = 0,
            ] = await Promise.all(shippedSummaryPromises)

            const lastWeekShipped = last14DayShipped - last7DayShipped
            const firstDayShipped = last1DayShipped
            const secondDayShipped = last2DayShipped - last1DayShipped
            const thirdDayShipped = last3DayShipped - last2DayShipped
            const fourthDayShipped = last4DayShipped - last3DayShipped
            const fifthDayShipped = last5DayShipped - last4DayShipped
            const sixthDayShipped = last6DayShipped - last5DayShipped
            const seventhDayShipped = last7DayShipped - last6DayShipped

            const arrayOfShipped = [
                firstDayShipped,
                secondDayShipped,
                thirdDayShipped,
                fourthDayShipped,
                fifthDayShipped,
                sixthDayShipped,
                seventhDayShipped,
            ]

            allSummaries.push({
                "name": "Shipped",
                "data": arrayOfShipped,
                "weekCount": last7DayShipped,
                "lastWeekCount": lastWeekShipped,
            })
            return res.handler.success(null, allSummaries)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getSalesState(req, res) {
        try {
            const body = req.body
            let obj = {}
            if (body.type === SALES_TYPE.ALL) {
                if (body.durationPeriod === SALE_DURATION_TYPE.DAY) {
                    const last7DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-6, "days").format("YYYY-MM-DD"))
                    const last6DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-5, "days").format("YYYY-MM-DD"))
                    const last5DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-4, "days").format("YYYY-MM-DD"))
                    const last4DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-3, "days").format("YYYY-MM-DD"))
                    const last3DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-2, "days").format("YYYY-MM-DD"))
                    const last2DayShales = await OrderModel.salesStatistics(body.branchId, moment().add(-1, "days").format("YYYY-MM-DD"))
                    const last1DayShales = await OrderModel.salesStatistics(body.branchId, moment().format("YYYY-MM-DD"))

                    const firstDaySalesAmount = last1DayShales.length ? last1DayShales[0].total_amount : 0
                    const secondDaySalesAmount = (last2DayShales.length ? last2DayShales[0].total_amount : 0) - (last1DayShales.length ? last1DayShales[0].total_amount : 0)
                    const thirdDaySalesAmount = (last3DayShales.length ? last3DayShales[0].total_amount : 0) - (last2DayShales.length ? last2DayShales[0].total_amount : 0)
                    const fourthDaySalesAmount = (last4DayShales.length ? last4DayShales[0].total_amount : 0) - (last3DayShales.length ? last3DayShales[0].total_amount : 0)
                    const fifthDaySalesAmount = (last5DayShales.length ? last5DayShales[0].total_amount : 0) - (last4DayShales.length ? last4DayShales[0].total_amount : 0)
                    const sixthDaySalesAmount = (last6DayShales.length ? last6DayShales[0].total_amount : 0) - (last5DayShales.length ? last5DayShales[0].total_amount : 0)
                    const seventhDaySalesAmount = (last7DayShales.length ? last7DayShales[0].total_amount : 0) - (last6DayShales.length ? last6DayShales[0].total_amount : 0)

                    obj["yAxis"] = [firstDaySalesAmount, secondDaySalesAmount, thirdDaySalesAmount, fourthDaySalesAmount, fifthDaySalesAmount, sixthDaySalesAmount, seventhDaySalesAmount]
                    obj["xAxis"] = [moment().format("YYYY-MM-DD"), moment().add(-1, "days").format("YYYY-MM-DD"), moment().add(-2, "days").format("YYYY-MM-DD"), moment().add(-3, "days").format("YYYY-MM-DD"), moment().add(-4, "days").format("YYYY-MM-DD"), moment().add(-5, "days").format("YYYY-MM-DD"), moment().add(-6, "days").format("YYYY-MM-DD")]
                } else if (body.durationPeriod === SALE_DURATION_TYPE.WEEK) {
                    const last7WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-7, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last6WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-6, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last5WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-5, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last4WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-4, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last3weekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-3, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last2WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-2, "weeks").add(1, "days").format("YYYY-MM-DD"))
                    const last1WeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-1, "weeks").add(1, "days").format("YYYY-MM-DD"))

                    const firstWeekSalesAmount = last1WeekShales.length ? last1WeekShales[0].total_amount : 0
                    const secondWeekSalesAmount = (last2WeekShales.length ? last2WeekShales[0].total_amount : 0) - (last1WeekShales.length ? last1WeekShales[0].total_amount : 0)
                    const thirdWeekSalesAmount = (last3weekShales.length ? last3weekShales[0].total_amount : 0) - (last2WeekShales.length ? last2WeekShales[0].total_amount : 0)
                    const fourthWeekSalesAmount = (last4WeekShales.length ? last4WeekShales[0].total_amount : 0) - (last3weekShales.length ? last3weekShales[0].total_amount : 0)
                    const fifthWeekSalesAmount = (last5WeekShales.length ? last5WeekShales[0].total_amount : 0) - (last4WeekShales.length ? last4WeekShales[0].total_amount : 0)
                    const sixthWeekSalesAmount = (last6WeekShales.length ? last6WeekShales[0].total_amount : 0) - (last5WeekShales.length ? last5WeekShales[0].total_amount : 0)
                    const seventhWeekSalesAmount = (last7WeekShales.length ? last7WeekShales[0].total_amount : 0) - (last6WeekShales.length ? last6WeekShales[0].total_amount : 0)

                    obj["yAxis"] = [firstWeekSalesAmount, secondWeekSalesAmount, thirdWeekSalesAmount, fourthWeekSalesAmount, fifthWeekSalesAmount, sixthWeekSalesAmount, seventhWeekSalesAmount]
                    obj["xAxis"] = [moment().add(-1, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-2, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-3, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-4, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-5, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-6, "weeks").add(1, "days").format("YYYY-MM-DD"), moment().add(-7, "weeks").add(1, "days").format("YYYY-MM-DD")]
                } else if (body.durationPeriod === SALE_DURATION_TYPE.MONTH) {
                    const last12MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-11, "month").format("YYYY-MM-DD"))
                    const last11MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-10, "month").format("YYYY-MM-DD"))
                    const last10MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-9, "month").format("YYYY-MM-DD"))
                    const last9MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-8, "month").format("YYYY-MM-DD"))
                    const last8MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-7, "month").format("YYYY-MM-DD"))
                    const last7MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-6, "month").format("YYYY-MM-DD"))
                    const last6MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-5, "month").format("YYYY-MM-DD"))
                    const last5MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-4, "month").format("YYYY-MM-DD"))
                    const last4MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-3, "month").format("YYYY-MM-DD"))
                    const last3MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-2, "month").format("YYYY-MM-DD"))
                    const last2MonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-1, "month").format("YYYY-MM-DD"))
                    const last1MonthShales = await OrderModel.salesStatistics(body.branchId, moment().format("YYYY-MM"))

                    const firstMonthSalesAmount = last1MonthShales.length ? last1MonthShales[0].total_amount : 0
                    const secondMonthSalesAmount = (last2MonthShales.length ? last2MonthShales[0].total_amount : 0) - (last1MonthShales.length ? last1MonthShales[0].total_amount : 0)
                    const thirdMonthSalesAmount = (last3MonthShales.length ? last3MonthShales[0].total_amount : 0) - (last2MonthShales.length ? last2MonthShales[0].total_amount : 0)
                    const fourthMonthSalesAmount = (last4MonthShales.length ? last4MonthShales[0].total_amount : 0) - (last3MonthShales.length ? last3MonthShales[0].total_amount : 0)
                    const fifthMonthSalesAmount = (last5MonthShales.length ? last5MonthShales[0].total_amount : 0) - (last4MonthShales.length ? last4MonthShales[0].total_amount : 0)
                    const sixthMonthSalesAmount = (last6MonthShales.length ? last6MonthShales[0].total_amount : 0) - (last5MonthShales.length ? last5MonthShales[0].total_amount : 0)
                    const seventhMonthSalesAmount = (last7MonthShales.length ? last7MonthShales[0].total_amount : 0) - (last6MonthShales.length ? last6MonthShales[0].total_amount : 0)
                    const eightMonthSalesAmount = (last8MonthShales.length ? last8MonthShales[0].total_amount : 0) - (last7MonthShales.length ? last7MonthShales[0].total_amount : 0)
                    const nineMonthSalesAmount = (last9MonthShales.length ? last9MonthShales[0].total_amount : 0) - (last8MonthShales.length ? last8MonthShales[0].total_amount : 0)
                    const tenMonthSalesAmount = (last10MonthShales.length ? last10MonthShales[0].total_amount : 0) - (last9MonthShales.length ? last9MonthShales[0].total_amount : 0)
                    const elevenMonthSalesAmount = (last11MonthShales.length ? last11MonthShales[0].total_amount : 0) - (last10MonthShales.length ? last10MonthShales[0].total_amount : 0)
                    const twelveMonthSalesAmount = (last12MonthShales.length ? last12MonthShales[0].total_amount : 0) - (last11MonthShales.length ? last11MonthShales[0].total_amount : 0)

                    obj["yAxis"] = [firstMonthSalesAmount, secondMonthSalesAmount, thirdMonthSalesAmount, fourthMonthSalesAmount, fifthMonthSalesAmount, sixthMonthSalesAmount, seventhMonthSalesAmount, eightMonthSalesAmount, nineMonthSalesAmount, tenMonthSalesAmount, elevenMonthSalesAmount, twelveMonthSalesAmount]
                    obj["xAxis"] = [moment().format("YYYY-MM-DD"), moment().add(-1, "month").format("YYYY-MM-DD"), moment().add(-2, "month").format("YYYY-MM-DD"), moment().add(-3, "month").format("YYYY-MM-DD"), moment().add(-4, "month").format("YYYY-MM-DD"), moment().add(-5, "month").format("YYYY-MM-DD"), moment().add(-6, "month").format("YYYY-MM-DD"), moment().add(-7, "month").format("YYYY-MM-DD"), moment().add(-8, "month").format("YYYY-MM-DD"), moment().add(-9, "month").format("YYYY-MM-DD"), moment().add(-10, "month").format("YYYY-MM-DD"), moment().add(-11, "month").format("YYYY-MM-DD")]
                }
                obj["duration"] = body.durationPeriod
                return res.handler.success(null, obj)

            } else if (body.type === SALES_TYPE.SALES_PERSON) {
                const salesPerson = body.salesPerson.map(item => {
                    const name = item.user_id.first_name + ' ' + item.user_id.last_name
                    const _id = item._id
                    return { _id, name }
                })

                if (body.durationPeriod === SALE_DURATION_TYPE.DAY) {
                    const lastDayShales = await OrderModel.salesStatistics(body.branchId, moment().format("YYYY-MM-DD"), true)
                    salesPerson.map(item => {
                        const tempObj = lastDayShales.find(items => item._id === items._id.toString())
                        item.totalAmount = tempObj?.total_amount || 0
                    })
                } else if (body.durationPeriod === SALE_DURATION_TYPE.WEEK) {

                    const lastWeekShales = await OrderModel.salesStatistics(body.branchId, moment().add(-1, "weeks").add(1, "days").format("YYYY-MM-DD"), true)
                    salesPerson.map(item => {
                        const tempObj = lastWeekShales.find(items => item._id === items._id.toString())
                        item.totalAmount = tempObj?.total_amount || 0
                    })

                } else if (body.durationPeriod === SALE_DURATION_TYPE.MONTH) {
                    const lastMonthShales = await OrderModel.salesStatistics(body.branchId, moment().add(-1, "months").add(1, "days").format("YYYY-MM-DD"), true)
                    salesPerson.map(item => {
                        const tempObj = lastMonthShales.find(items => item._id === items._id.toString())
                        item.totalAmount = tempObj?.total_amount || 0
                    })

                }
                salesPerson.sort((a, b) => Number(b.totalAmount) - Number(a.totalAmount));
                salesPerson.splice(8)

                return res.handler.success(null, salesPerson)
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateCustomerWithOrders(req, res) {
        try {
            const {
                customerUserRoleId,
                updateInformation,
                apiVersion,
            } = req.body

            const { shippingMobileNumber } = updateInformation

            /** Update information in the customer. */
            const payload = {
                "filter": {
                    "_id": customerUserRoleId,
                },
                "updateFields": {
                    "shipping_mobile_number": shippingMobileNumber
                }
            }
            const customerUpdate = await httpService(req).put("customer", payload)

            if (customerUpdate?.status === STATUS_CODES.SUCCESS) {
                logger.info("Customer updated successfully from updateCustomerWithOrders")
            }

            /** Update information in the existing orders */
            const orderFilter = {
                "customer_user_role_id": customerUserRoleId,
                "order_status": {
                    "$nin": [ORDER_STATUS_TYPES.DELIVERED, ORDER_STATUS_TYPES.CANCELLED]
                },
            }
            const orderUpdateFields = {}

            if (shippingMobileNumber) {
                orderUpdateFields["shipping_mobile_number"] = shippingMobileNumber
            }

            const updateResult = await OrderModel.updateBulkOrders(orderFilter, orderUpdateFields, req.headers)
            if (updateResult.modifiedCount) {
                return res.handler.success("update_orders_success")
            }

            return res.handler.success("update_orders_unsuccess")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }


    async cartActions(req, res) {
        try {
            const body = req.body;
            const userroleid = req.headers.userroleid;
            const cartId = body.customerUserRoleId === userroleid ? `${body.tenantId}_${body.customerUserRoleId}` : `${body.tenantId}_${body.customerUserRoleId}_${userroleid}`;

            const validProductActions = [
                CART_ACTIONS.UPDATE_QUANTITY,
                CART_ACTIONS.ADD_ITEM_COMMENT,
                CART_ACTIONS.EDIT_ITEM_PRICE,
                CART_ACTIONS.ADD_ITEM_TO_CART,
            ];

            if (!body.cartItemId && body.actionType === CART_ACTIONS.REMOVE_FROM_CART) { // remove  cartItem with productId
                validProductActions.push(CART_ACTIONS.REMOVE_FROM_CART)
            }
            const checkValidateProductDetails = validProductActions.includes(body.actionType);

            const productDetails = await ProductModel.findProduct({ _id: body.productVariantId, tenant_id: body.tenantId },
                { _v: 0, created_by: 0, update_by: 0, });


            if (checkValidateProductDetails
                && (!productDetails || !productDetails.is_active || productDetails.is_deleted || productDetails.type === NEW_PRODUCT_TYPE.PARENT)
            ) {
                return res.handler.badRequest("in_valid_product");
            }

            let cartItem;
            const cartItemFilter = { cart_id: cartId, customer_user_role_id: body.customerUserRoleId };

            if (body.cartItemId) {
                cartItemFilter["_id"] = body.cartItemId;
            } else {
                cartItemFilter["product_variant_id"] = productDetails._id;
            }

            switch (body.actionType) {
                case CART_ACTIONS.ADD_ITEM_TO_CART:
                    {
                        let price_mappings = productDetails?.price_mappings;
                        let filter = { cart_id: cartId, product_variant_id: body.productVariantId, customer_user_role_id: body.customerUserRoleId };
                        let priceMap = price_mappings?.find(p => p.master_price_id.toString() === body.masterPriceId);
                        if (!priceMap || !priceMap.price) {
                            return res.handler.notFound("can_not_find_price");
                        }

                        const { activeProductDealDetails } = await OrderModel.getActiveDealInfo(productDetails, priceMap.master_price_id, body.salesPersonRoleId);
                        const { cartItemDealInfo, isDealApplied, dealPrice, invalidDealConfiguration } = await OrderModel.calculateDealPrice(activeProductDealDetails, priceMap.price, body.quantity, productDetails.packaging_map.qty_ctn);

                        if (invalidDealConfiguration) {
                            return res.handler.conflict('invalid_deal_found');
                        }

                        const { taxInfo, taxCalculations, taxValues } = await OrderModel.generateTaxDetailsForCart(productDetails, dealPrice, priceMap.price);

                        await productDetails
                            .populate([
                                { path: "parent_id", select: { title: 1, item_number: 1, secondary_language_title: 1 } },
                                { path: "variant_value_id", select: { name: 1, secondary_language_name: 1 } },
                                { path: "group_value_id", select: { name: 1, secondary_language_name: 1 } },
                            ])
                        const parentDetails = productDetails.parent_id;
                        let updateInfo = {
                            tenant_id: body.tenantId,
                            cart_id: cartId,
                            product_variant_id: productDetails._id,
                            variant_id: productDetails?.variant_id,
                            item_number: productDetails.item_number,
                            parent_item_number: parentDetails?.item_number,
                            product_name: productDetails.title || parentDetails.title,
                            product_secondary_name: productDetails.secondary_language_title || parentDetails?.secondary_language_title,
                            variant_name: productDetails?.variant_value_id?.name,
                            variant_secondary_name: productDetails?.variant_value_id?.secondary_language_name,
                            group_secondary_name: productDetails?.group_value_id?.secondary_language_name,
                            group_name: productDetails?.group_value_id?.name,
                            master_price_id: body.masterPriceId,
                            quantity: body.quantity,
                            item_comment: body.itemComment,
                            customer_user_role_id: body.customerUserRoleId,
                            sales_user_role_id: body.salesPersonRoleId,
                            created_by: req.headers.userDetails?._id,
                            update_by: req.headers.userDetails?._id,
                            last_cart_action: CART_ACTIONS.ADD_ITEM_TO_CART,
                            uom_id: body.uomId,
                            uom_name: body.uomName,
                            min_qty: body.minQty,
                            base_price: taxCalculations.basePrice,
                            tax: taxCalculations.calculatedTax,
                            tax_calculation_info: taxValues.tax_details,
                            original_price: priceMap.price,
                        };
                        if (isDealApplied) {
                            updateInfo['deal_info'] = cartItemDealInfo;

                            OrderModel.dealAppliedInCartMessageToSQS(cartItemDealInfo, updateInfo.tenant_id, updateInfo.cart_id)
                                .catch(error => {
                                    logger.error(error, {
                                        errorMessage: "ERROR ENCOUNTERED FROM ADD_TO_CART_COUNT_UPDATE SQS MESSAGE"
                                    })
                                })
                        }
                        cartItem = await OrderModel.upsertCartItem(filter, updateInfo, { new: true, upsert: true });
                        break;
                    }
                case CART_ACTIONS.UPDATE_QUANTITY:
                    {
                        cartItem = await OrderModel.findCartItem(cartItemFilter);
                        if (!cartItem) {
                            return res.handler.badRequest("invalid_cart_item_id");
                        };
                        let filter = { _id: cartItem._id };
                        let updateInfo = { quantity: body.quantity, update_by: req.headers.userDetails?._id, last_cart_action: CART_ACTIONS.UPDATE_QUANTITY };
                        let shouldUpdateTaxInfo = true;
                        let updatedDealPrice;

                        const { activeProductDealDetails } = await OrderModel.getActiveDealInfo(productDetails, cartItem.master_price_id, cartItem.sales_user_role_id);
                        const { cartItemDealInfo, isDealApplied, dealPrice, invalidDealConfiguration } = await OrderModel.calculateDealPrice(activeProductDealDetails, cartItem.original_price, body.quantity, productDetails.packaging_map.qty_ctn);
                        if (invalidDealConfiguration) {
                            return res.handler.conflict('invalid_deal_found');
                        }
                        updatedDealPrice = dealPrice;
                        if (!activeProductDealDetails && cartItem.deal_info) { // deal is not available for this product now

                            if (!cartItem.deal_info.deal_type !== DEAL_TYPE.DISCOUNT) {
                                const { isDealApplied, dealPrice, cartItemDealInfo, invalidDealConfiguration } = await OrderModel.calculateDealPriceFromCartItem(cartItem.deal_info, body.quantity, productDetails.packaging_map.qty_ctn)
                                if (invalidDealConfiguration) {
                                    return res.handler.conflict('invalid_deal_found');
                                }
                                if (isDealApplied) {
                                    updatedDealPrice = dealPrice
                                    updateInfo['deal_info'] = cartItemDealInfo;
                                } else {
                                    updateInfo['$unset'] = { deal_info: '' };
                                }
                            }
                        } else if (activeProductDealDetails && cartItem.deal_info && isDealApplied) { // applied deal tier or bulk price or discount might have changed
                            updateInfo['deal_info'] = cartItemDealInfo;
                        } else if (activeProductDealDetails && !isDealApplied && cartItem.deal_info) { // deal should be removed as no deal criteria was matched
                            updateInfo['$unset'] = { deal_info: '' };
                            if (cartItem.deal_info.deal_type === DEAL_TYPE.BUY_X_AND_GET_Y) {
                                shouldUpdateTaxInfo = false;
                            }
                        } else if (activeProductDealDetails && !cartItem.deal_info && isDealApplied) { // deal is applied when updated the quantity
                            updateInfo['deal_info'] = cartItemDealInfo;

                            OrderModel.dealAppliedInCartMessageToSQS(cartItemDealInfo, cartItem.tenant_id, cartItem.cart_id)
                                .catch(error => {
                                    logger.error(error, {
                                        errorMessage: "ERROR ENCOUNTERED FROM ADD_TO_CART_COUNT_UPDATE SQS MESSAGE"
                                    })
                                })
                            //need to check for when this product was added to cart ( created_at for cartItem, if it is between from & to dates of deal, only than apply the deal)
                            // if ((activeProductDealDetails.deal_from_date <= cartItem.created_at)
                            //     && (cartItem.created_at <= activeProductDealDetails.deal_to_date)
                            // ) {
                            // }
                        } else if (!activeProductDealDetails && !cartItem.deal_info) { // no deal was applied before and could not found active deal for the product
                            shouldUpdateTaxInfo = false;
                        }

                        if (shouldUpdateTaxInfo) {
                            const { taxCalculations, taxValues } = await OrderModel.generateTaxDetailsForCart(productDetails, updatedDealPrice, cartItem.original_price);
                            updateInfo['base_price'] = taxCalculations.basePrice;
                            updateInfo['tax'] = taxCalculations.calculatedTax;
                            updateInfo['tax_calculation_info'] = taxValues.tax_details;
                        }

                        cartItem = await OrderModel.upsertCartItem(filter, updateInfo, { new: true, upsert: false });
                        break;
                    }
                case CART_ACTIONS.REMOVE_FROM_CART:
                    {
                        cartItem = await OrderModel.findCartItem(cartItemFilter);
                        if (!cartItem) {
                            return res.handler.badRequest("invalid_cart_item_id");
                        };
                        await cartItem.deleteOne();
                        break;
                    }

                case CART_ACTIONS.ADD_ITEM_COMMENT:
                    {
                        cartItem = await OrderModel.findCartItem(cartItemFilter);
                        if (!cartItem) {
                            return res.handler.badRequest("invalid_cart_item_id");
                        };
                        let filter = { _id: body.cartItemId };
                        let updateInfo = { $push: { item_comment: body.itemComment }, last_cart_action: CART_ACTIONS.ADD_ITEM_COMMENT };
                        cartItem = await OrderModel.upsertCartItem(filter, updateInfo, { new: true, upsert: false });
                        break;
                    }
                case CART_ACTIONS.EDIT_ITEM_PRICE:
                    {
                        //TODO: need to check how to update text_calculations of current cart item & deal_price
                        return res.handler.notFound("not_implemented_yet");
                        break;
                    }
            }

            return res.handler.success("cart_updated_successfully", cartItem)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async cartDetails(req, res) {
        try {
            const body = req.query;
            const userroleid = req.headers.userroleid;
            const cartId = body.customerUserRoleId === userroleid ? `${body.tenantId}_${body.customerUserRoleId}` : `${body.tenantId}_${body.customerUserRoleId}_${userroleid}`;

            const todayDate = new Date();
            const pipeline = [];
            pipeline.push(
                {
                    $match: {
                        cart_id: cartId
                    }
                },
                {
                    /**
                     * 🚩🚩🚩🚩🚩🚩🚩🚩🚩🚩
                     *
                     * @description If anyone changes sort order of created_at then need to
                     * make changes in sort order of created_at in place order api too.
                     *
                     * As client requirement is to display the same order of order_item,
                     * after placing an order.
                     */
                    $sort: { created_at: -1 }
                },
                {
                    $lookup: {
                        from: "products_2.0",
                        localField: 'product_variant_id',
                        foreignField: '_id',
                        as: "productInfo",
                        let: {
                            master_price_id: "$master_price_id",
                            sales_user_role_id: "$sales_user_role_id"
                        },
                        pipeline: [
                            {
                                $match: {
                                    is_deleted: false
                                }
                            },
                            ...(await OrderModel.getImgsPipelineStagesFromProductInfo()),
                            {
                                $lookup: {
                                    from: 'deal_products',
                                    localField: '_id',
                                    as: 'onGoingDealInfo',
                                    foreignField: 'product_id',
                                    let: { masterPriceId: "$$master_price_id" },
                                    pipeline: [
                                        {
                                            $match: {
                                                deal_from_date: { $lt: todayDate },
                                                deal_to_date: { $gt: todayDate },
                                                $expr: {
                                                    $eq: ['$price_id', '$$masterPriceId']
                                                }
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: 'deals',
                                                localField: 'deal_id',
                                                foreignField: '_id',
                                                as: 'deal',
                                            }
                                        },
                                        {
                                            $addFields: {
                                                deal: { $first: "$deal" }
                                            }
                                        },
                                        {
                                            $match: {
                                                'deal.deal_status': { $nin: [DEAL_STATUS.CANCELLED, DEAL_STATUS.PAUSED] },
                                                $expr: {
                                                    $in: ['$$sales_user_role_id', '$deal.sales_persons']
                                                }
                                            }
                                        },
                                        {
                                            $project: {
                                                deal_id: '$deal_id',
                                                deal_name: '$deal.deal_name',
                                                deal_number: '$deal.deal_id',
                                                deal_type: '$deal.deal_type',
                                                deal_from_date: '$deal_from_date',
                                                deal_to_date: '$deal_to_date',
                                                secondary_deal_name: "$deal.secondary_deal_name",
                                                deal_product_id: "$_id",

                                                discount_type: "$discount_type",
                                                percent: '$percent',
                                                amount: '$amount',
                                                discounted_price: '$discounted_price',

                                                first_tier: "$first_tier",
                                                second_tier: "$second_tier",
                                                third_tier: "$third_tier",

                                                buy_product: "$buy_product",
                                                free_product: "$free_product",
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                $addFields: {
                                    onGoingDealInfo: { $first: '$onGoingDealInfo' }
                                }
                            },
                            {
                                $project: {
                                    cover_image: 1,
                                    parent_cover_image: 1,
                                    group_cover_image: 1,
                                    type: 1,
                                    parent_id: 1,
                                    packaging_map: 1,
                                    onGoingDealInfo: 1,
                                }
                            }

                        ]
                    }
                },
                {
                    $addFields: {
                        productInfo: { $first: "$productInfo" }
                    }
                }
            );
            const cart = await OrderModel.aggregateCartItems(pipeline);
            return res.handler.success(null, { cartItems: cart });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteCartItems(req, res) {
        try {
            const { tenantId, customerUserRoleId, cartItemIds } = req.query;
            const userroleid = req.headers.userroleid;

            const cartId = (customerUserRoleId === userroleid) ? `${tenantId}_${customerUserRoleId}` : `${tenantId}_${customerUserRoleId}_${userroleid}`;
            await OrderModel.deleteCartItems({ tenant_id: tenantId, cart_id: cartId, _id: { $in: cartItemIds } });

            return res.handler.success("cart_cleared_successfully");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCartItemCount(req, res) {
        try {
            const userroleid = req.headers.userroleid;
            const { tenantId, customerUserRoleId } = req.query;

            const cartId = userroleid === customerUserRoleId ? `${tenantId}_${customerUserRoleId}` : `${tenantId}_${customerUserRoleId}_${userroleid}`;

            const count = await OrderModel.cartItemCount({ tenant_id: tenantId, cart_id: cartId });
            return res.handler.success("item_count_successful", { count });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async createDraft(req, res) {
        try {
            const body = req.body;
            const userroleid = req.headers.userroleid;
            if (body.customerUserRoleId === userroleid) {
                return res.handler.badRequest("can't_create_draft_for_customer");
            }
            const cartId = body.customerUserRoleId === userroleid ? `${body.tenantId}_${body.customerUserRoleId}` : `${body.tenantId}_${body.customerUserRoleId}_${userroleid}`;


            const cartItems = await OrderModel.findCartItems({ cart_id: cartId, customer_user_role_id: body.customerUserRoleId });
            if (!cartItems.length) {
                return res.handler.success('cart_empty');
            }

            // ---------------  CREATE DRAFT  ---------------  //
            const draftId = `${body.tenantId}_${userroleid}`
            let draftCounter = await OrderModel.getDraftCounter({ _id: draftId });
            if (!draftCounter) {
                draftCounter = await OrderModel.addDraftCounter();
                draftCounter._id = draftId;
            }
            draftCounter.counter += 1;

            const draft = await OrderModel.createDraft();
            draft.draft_number = `D${body.tenantId}-${draftCounter.counter}`;
            draft.draft_owner_user_role_id = userroleid
            draft.customer_user_role_id = body.customerUserRoleId;
            draft.sales_user_role_id = body.salesPersonRoleId;
            draft.tenant_id = body.tenantId;
            draft.customer_name = body.customerName;
            draft.customer_primary_contact_name = body.customerPrimaryContactName;
            draft.customer_id = body.customerId;
            draft.external_id = body.externalId;

            draft.created_by = req.headers.userDetails?._id;
            draft.update_by = req.headers.userDetails?._id;

            let total_amount = 0;
            let total_tax = 0;
            const taxCalculationMap = new Map();
            const draftItemPromises = []
            // ---------------  CREATE DRAFT ITEMS  ---------------  //
            for (let i = 0; i < cartItems.length; i++) {
                const cItem = cartItems[i];
                const draftItem = await OrderModel.createDraftItem({ ...cItem.toObject() });
                draftItem.draft_id = draft._id;

                draftItem.created_by = req.headers.userDetails?._id;
                draftItem.updated_by = req.headers.userDetails?._id;
                draftItemPromises.push(draftItem.save());

                total_amount += (cItem.base_price * cItem.quantity);
                total_tax += (cItem.tax * cItem.quantity);
                OrderModel.updateTaxCalculationMap(cItem.tax_calculation_info, taxCalculationMap, cItem.quantity);
            }
            draft.total_amount = roundOf(total_amount, 3);
            draft.total_tax = roundOf(total_tax, 3);
            draft.tax_calculation_info = taxCalculationMap;
            const [draftItems] = await Promise.all([
                Promise.all(draftItemPromises),
                draft.save(),
                draftCounter.save(),
                OrderModel.deleteCartItems({ cart_id: cartId })
            ]);
            return res.handler.success("draft_added", { draft, draftItems: draftItems })
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async draftListing(req, res) {
        try {
            const {
                page = 1,
                perPage = 10,
                tenantId = "",
                searchKey = "",
                customerUserRoleId = null,
                duration = null,
                durationPeriod = DURATION_PERIOD_OPTIONS.months
            } = req.query;
            const offset = (page - 1) * perPage
            const pipeline = [];
            const userroleid = req.headers.userroleid;
            const $match = {
                draft_owner_user_role_id: new mongoose.Types.ObjectId(userroleid),
                tenant_id: tenantId,
            }


            if (customerUserRoleId) {
                $match["customer_user_role_id"] = new mongoose.Types.ObjectId(customerUserRoleId);
            }

            if (duration) {
                $match["created_at"] = {
                    $gte: moment().add((-1) * duration, durationPeriod).toDate()
                }
            }
            if (searchKey) {
                $match["$or"] = [
                    { customer_name: { $regex: searchKey, $options: "i" } },
                    { draft_number: { $regex: searchKey, $options: "i" } },
                ];
            }
            pipeline.push(
                {
                    $match
                },
                {
                    $sort: { created_at: -1 }
                },
                {
                    $facet: {
                        drafts: [{ $skip: offset }, { $limit: perPage }],
                        count: [{ $count: 'count' }]
                    }
                },
                {
                    $unwind: {
                        path: "$drafts"
                    }
                },
                {
                    $lookup: {
                        from: "draft_items_2.0",
                        localField: "drafts._id",
                        foreignField: "draft_id",
                        as: "drafts.draft_items",
                        pipeline: [
                            {
                                $project: {
                                    product_variant_id: 1,
                                    product_name: 1,
                                    variant_id: 1,
                                    variant_name: 1,
                                    group_name: 1
                                }
                            },
                            {
                                $limit: 12
                            },
                            {
                                $lookup: {
                                    from: "products_2.0",
                                    localField: 'product_variant_id',
                                    foreignField: '_id',
                                    as: "productInfo",
                                    pipeline: [
                                        {
                                            $match: {
                                                is_deleted: false
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: '_id',
                                                foreignField: 'product_variant_id',
                                                as: "cover_image",
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: null
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: 'parent_id',
                                                foreignField: 'product_variant_id',
                                                as: "parent_cover_image",
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: null
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: 'parent_id',
                                                foreignField: 'product_variant_id',
                                                as: "group_cover_image",
                                                let: { group_id: "$group_value_id" },
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: { $ne: null },
                                                            $expr: {
                                                                $eq: ["$group_id", "$$group_id"]
                                                            }
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $addFields: {
                                                cover_image: { $first: "$cover_image" },
                                                parent_cover_image: { $first: "$parent_cover_image" },
                                                group_cover_image: { $first: "$group_cover_image" },
                                            }
                                        },
                                        {
                                            $project: {
                                                cover_image: 1,
                                                parent_cover_image: 1,
                                                group_cover_image: 1,
                                                type: 1,
                                                parent_id: 1
                                            }
                                        }

                                    ]
                                }
                            },
                            {
                                $addFields: {
                                    productInfo: { $first: "$productInfo" }
                                }
                            }
                        ]
                    }
                },
                {
                    $project: {
                        tax_calculation_info: 0,
                        __v: 0,
                        created_by: 0,
                        update_by: 0,

                    }
                }
            );

            const data = await OrderModel.aggregateDraft(pipeline);
            const list = data.length ? data.map(d => d.drafts) : [];
            const count = data.length ? data[0].count?.[0].count : 0;
            return res.handler.success("drafts", { list, count });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async draftDetails(req, res) {
        try {
            const { draftId, tenantId } = req.query;
            const userroleid = req.headers.userroleid;
            const $match = {
                tenant_id: tenantId,
                _id: new mongoose.Types.ObjectId(draftId),
                draft_owner_user_role_id: new mongoose.Types.ObjectId(userroleid),
            }
            const pipeline = [];
            pipeline.push(
                {
                    $match
                },
                {
                    $lookup: {
                        from: "draft_items_2.0",
                        localField: "_id",
                        foreignField: "draft_id",
                        as: "draft_items",
                        pipeline: [
                            {
                                $project: {
                                    product_variant_id: 1,
                                    product_name: 1,
                                    variant_id: 1,
                                    variant_name: 1,
                                    group_name: 1,
                                    base_price: 1,
                                    tax: 1,
                                    tax_calculation_info: 1,
                                    quantity: 1,
                                    original_price: 1,
                                    uom_id: 1,
                                    uom_name: 1,
                                    parent_item_number: 1,
                                    item_number: 1,
                                    parent_item_number: 1,
                                    deal_info: 1,
                                }
                            },
                            {
                                $lookup: {
                                    from: "products_2.0",
                                    localField: 'product_variant_id',
                                    foreignField: '_id',
                                    as: "productInfo",
                                    pipeline: [
                                        {
                                            $match: {
                                                is_deleted: false
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: '_id',
                                                foreignField: 'product_variant_id',
                                                as: "cover_image",
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: null
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: 'parent_id',
                                                foreignField: 'product_variant_id',
                                                as: "parent_cover_image",
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: null
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $lookup: {
                                                from: "images_2.0",
                                                localField: 'parent_id',
                                                foreignField: 'product_variant_id',
                                                as: "group_cover_image",
                                                let: { group_id: "$group_value_id" },
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            group_id: { $ne: null },
                                                            $expr: {
                                                                $eq: ["$group_id", "$$group_id"]
                                                            }
                                                        }
                                                    },
                                                    {
                                                        $project: {
                                                            image_name: 1,
                                                            image_number: 1,
                                                            s3_url: 1,
                                                            updated_at: 1,
                                                        }
                                                    },
                                                    {
                                                        $sort: { image_number: 1 }
                                                    },
                                                    {
                                                        $limit: 1,
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            $addFields: {
                                                cover_image: { $first: "$cover_image" },
                                                parent_cover_image: { $first: "$parent_cover_image" },
                                                group_cover_image: { $first: "$group_cover_image" },
                                            }
                                        },
                                        {
                                            $project: {
                                                cover_image: 1,
                                                parent_cover_image: 1,
                                                group_cover_image: 1,
                                                type: 1,
                                                parent_id: 1
                                            }
                                        }

                                    ]
                                }
                            },
                            {
                                $addFields: {
                                    productInfo: { $first: "$productInfo" }
                                }
                            }
                        ]
                    }
                },
                {
                    $project: {
                        __v: 0,
                        created_by: 0,
                        update_by: 0,

                    }
                }
            );

            const data = await OrderModel.aggregateDraft(pipeline);
            if (!data.length) {
                return res.handler.notFound("draft_not_found");
            }
            return res.handler.success("draft_details", data[0]);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteDrafts(req, res) {
        try {
            const { draftIds, tenantId } = req.query;
            const userroleid = req.headers.userroleid;
            const query = {
                tenant_id: tenantId,
                _id: { $in: draftIds },
                draft_owner_user_role_id: userroleid
            }

            const drafts = (await OrderModel.findDrafts(query, { _id: 1 })).map(d => d._id);

            if (!drafts.length) {
                return res.handler.notFound('drafts_not_found');
            }

            await Promise.all([
                OrderModel.deleteDrafts({ _id: { $in: drafts } }),
                OrderModel.deleteDraftItems({ draft_id: { $in: drafts } })
            ])

            return res.handler.success("drafts_deleted_successfully");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async checkOrderUsers(req, res) {
        try {
            const { orderId, token } = req.query;

            const orderDetails = await OrderModel.findOrder({ _id: orderId }, undefined, { lean: true });

            if (!orderDetails) {
                return res.handler.badRequest('invalid_order_id')
            }

            const { data } = await httpService(req).get("checkOrderUsers", { params: { tenantId: orderDetails.tenant_id, userRoleId: token } });
            const { validUser, countryInfo } = data.data;

            if (!validUser) {
                return res.handler.badRequest('invalid_token');
            }

            const pipeline = [
                {
                    $match: {
                        order_id: orderDetails._id
                    }
                },
                {
                    $lookup: {
                        from: "products_2.0",
                        localField: 'product_variant_id',
                        foreignField: '_id',
                        as: "productImgInfo",
                        pipeline: [
                            {
                                $match: {
                                    is_deleted: false
                                }
                            },
                            ...(await OrderModel.getImgsPipelineStagesFromProductInfo()),
                            {
                                $project: {
                                    cover_image: 1,
                                    parent_cover_image: 1,
                                    group_cover_image: 1,
                                    type: 1,
                                    parent_id: 1,
                                    packaging_map: 1,
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        productImgInfo: { $first: '$productImgInfo' }
                    }
                }
            ];
            const orderItems = await OrderModel.aggregateOrderItems(pipeline);


            return res.handler.success(null, { orderDetails, orderItems, countryInfo });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async totalOrders(req, res) {
        try {
            const promises = [];
            const order_status = {
                $in: [
                    ORDER_STATUS_TYPES.RECEIVED,
                    ORDER_STATUS_TYPES.RELEASED,
                    ORDER_STATUS_TYPES.PREPARING,
                    ORDER_STATUS_TYPES.SHIPPED,
                    ORDER_STATUS_TYPES.DELIVERED
                ]
            }

            promises.push(
                OrderModel.countOrders(),
            )

            const daysList = [-6, -5, -4, -3, -2, -1, 0];
            daysList.forEach((d) => {
                promises.push(OrderModel.countOrders({ created_at: { $gte: moment().add(d, "days").format("YYYY-MM-DD") }, order_status: order_status }))
            });

            const [
                count,
                last7Days,
                last6Days,
                last5Days,
                last4Days,
                last3Days,
                last2Days,
                last1Days
            ] = await Promise.all(promises);
            const firstDayOrders = last1Days
            const secondDayOrders = last2Days - last1Days
            const thirdDayOrders = last3Days - last2Days
            const fourthDayOrders = last4Days - last3Days
            const fifthDayOrders = last5Days - last4Days
            const sixthDayOrders = last6Days - last5Days
            const seventhDayOrders = last7Days - last6Days

            const data = [firstDayOrders, secondDayOrders, thirdDayOrders, fourthDayOrders, fifthDayOrders, sixthDayOrders, seventhDayOrders]
            return res.handler.success(null, { count, data })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async draftToCart(req, res) {
        try {
            const userroleid = req.headers.userroleid;

            const { draftId, tenantId, customerUserRoleId } = req.body;
            const cartId = `${tenantId}_${customerUserRoleId}_${userroleid}`
            const draftPopulate = {
                path: 'draftItems',
                select: excludeProjections,
                populate: [
                    {
                        path: 'product_variant_id',
                        select: excludeProjections,
                        populate: [
                            { path: 'parent_id', select: excludeProjections },
                            { path: 'group_value_id', select: excludeProjections },
                            { path: 'variant_value_id', select: excludeProjections }
                        ]
                    },
                ]
            };
            const draft = await OrderModel.findDraft(
                {
                    draft_owner_user_role_id: userroleid,
                    _id: draftId,
                    tenant_id: tenantId,
                    customer_user_role_id: customerUserRoleId
                },
                undefined,
                { populate: draftPopulate }
            );

            const itemsOriginallyHadDeal = [];
            const addedDealsToItems = [];
            const invalidDraftItems = [];

            if (!draft || !draft.draftItems?.length) {
                return res.handler.badRequest('invalid_draft');
            }

            const draftItemIds = [];
            await OrderModel.deleteCartItems({ cart_id: cartId }); // clear existing cart items before moving draft items to cart

            for (let i = 0; i < draft.draftItems.length; i++) {
                const draftItem = draft.draftItems[i];
                draftItemIds.push(draftItem._id);

                const productDetails = draftItem.product_variant_id;
                const parentDetails = productDetails.parent_id;

                let priceMap = productDetails.price_mappings?.find(p => p.master_price_id.toString() === draftItem.master_price_id.toString());

                if (!priceMap || !priceMap.price) {
                    invalidDraftItems.push(productDetails.item_number);
                    continue;
                }


                const { activeProductDealDetails } = OrderModel.getActiveDealInfo(productDetails, priceMap.master_price_id, draft.sales_user_role_id);

                const { cartItemDealInfo, isDealApplied, dealPrice } = await OrderModel.calculateDealPrice(activeProductDealDetails, priceMap.price, draftItem.quantity, productDetails.packaging_map.qty_ctn);

                const { taxCalculations, taxValues } = await OrderModel.generateTaxDetailsForCart(productDetails, dealPrice, priceMap.price);

                let updateInfo = {
                    tenant_id: productDetails.tenant_id,
                    cart_id: cartId,
                    product_variant_id: productDetails._id,
                    variant_id: productDetails?.variant_id,
                    item_number: productDetails.item_number,
                    parent_item_number: parentDetails?.item_number,
                    product_name: productDetails.title || parentDetails.title,
                    product_secondary_name: productDetails.secondary_language_title || parentDetails?.secondary_language_title,
                    variant_name: productDetails?.variant_value_id?.name,
                    variant_secondary_name: productDetails?.variant_value_id?.secondary_language_name,
                    group_secondary_name: productDetails?.group_value_id?.secondary_language_name,
                    group_name: productDetails?.group_value_id?.name,
                    master_price_id: draftItem.master_price_id,

                    item_comment: [],
                    customer_user_role_id: draft.customer_user_role_id,
                    sales_user_role_id: draft.sales_user_role_id,
                    created_by: req.headers.userDetails?._id,
                    update_by: req.headers.userDetails?._id,

                    last_cart_action: CART_ACTIONS.DRAFT_TO_CART,
                    uom_id: draftItem.uom_id,
                    uom_name: draftItem.uom_name,
                    min_qty: productDetails.packaging_map.min_qty,
                    base_price: taxCalculations.basePrice,
                    tax: taxCalculations.calculatedTax,
                    tax_calculation_info: taxValues.tax_details,
                    original_price: priceMap.price,
                    // quantity: overAllQuantity
                    $inc: { quantity: draftItem.quantity, }
                };

                if (isDealApplied) {
                    updateInfo['deal_info'] = cartItemDealInfo;
                }

                if (!draftItem.deal_info && isDealApplied) {
                    addedDealsToItems.push(productDetails.item_number);
                } else if (draftItem.deal_info && !isDealApplied) {
                    itemsOriginallyHadDeal.push(productDetails.item_number);
                }

                const filter = { cart_id: cartId, product_variant_id: productDetails._id, customer_user_role_id: draft.customer_user_role_id };
                await OrderModel.upsertCartItem(filter, updateInfo, { new: true, upsert: true });
            }

            Promise.all([
                draft.deleteOne(),
                OrderModel.deleteDraftItems({ _id: { $in: draftItemIds } })
            ])
            return res.handler.success("successfully_moved_to_cart", { itemsOriginallyHadDeal, addedDealsToItems, invalidDraftItems });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async setAutoEnableCatalogModeForCustomer() {
        const profiler = logger.startTimer()
        logger.info("Started: Customer's auto enable catalog mode job")

        try {
            const tenantsAppSettings = await InternalServiceModel.getTenantsAppSettings({
                filter: {
                    "customer_auto_catalog_mode.enabled": true
                },
                projection: "-_id tenant_id customer_auto_catalog_mode",
                options: {
                    lean: true
                }
            })

            const autoCatalogEmailTasks = []

            for (let i = 0; i < tenantsAppSettings.length; i++) {
                try {
                    const appSettings = tenantsAppSettings[i];
                    const fromDate = moment().subtract(appSettings.customer_auto_catalog_mode.duration, 'months')

                    const logMessage = (message) => {
                        logger.info(`Tenant ID: ${appSettings.tenant_id} -> Message: ${message}`)
                    }

                    const filter = {
                        customer_catalog_mode: {
                            $ne: true
                        },
                        is_deleted: false,
                        tenant_id: appSettings.tenant_id,
                        last_mobile_login_time: {
                            $exists: true
                        },
                        first_mobile_login_time: {
                            $lt: fromDate
                        },
                        $or: [
                            {
                                customer_catalog_mode_disabled_at: {
                                    $exists: false
                                },
                            },
                            {
                                customer_catalog_mode_disabled_at: {
                                    $lt: fromDate,
                                }
                            }
                        ],
                        created_at: {
                            $lt: fromDate
                        }
                    }

                    const customersCount = await InternalServiceModel.getCustomersCount({
                        filter
                    })

                    const limit = 50
                    const totalPages = Math.ceil(customersCount / limit)

                    let taskReport = {
                        updatedCustomerCount: 0,
                        updatedCustomerUserRoleIds: [],
                        updatedCustomerIds: [],
                    }

                    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
                        try {
                            const customers = await InternalServiceModel.getCustomers({
                                filter,
                                projection: "_id customer_id",
                                options: {
                                    lean: true,
                                    sort: {
                                        created_at: 1,
                                    },
                                    skip: limit * (currentPage - 1),
                                    limit
                                },
                            });

                            const customerOrderPromises = customers.map(customer => {
                                return OrderModel.findOrder(
                                    {
                                        created_at: {
                                            $gte: fromDate
                                        },
                                        order_status: {
                                            $nin: [
                                                ORDER_STATUS_TYPES.CANCELLED
                                            ]
                                        },
                                        customer_user_role_id: customer._id,
                                        order_creator_user_role_id: customer._id,
                                        order_app_type: ORDER_APP_TYPE.CUSTOMER_APP
                                    },
                                    "_id customer_user_role_id",
                                    {
                                        lean: true
                                    }
                                )
                            })

                            const customerOrdersResult = await Promise.allSettled(customerOrderPromises)

                            const nonPurchasedCustomer = {}

                            for (let i = 0; i < customers.length; i++) {
                                const customerOrderResult = customerOrdersResult[i]
                                const customer = customers[i];

                                if (customerOrderResult.status === "fulfilled") {
                                    if (!customerOrderResult.value) {
                                        nonPurchasedCustomer[customer._id] = customer.customer_id
                                    }
                                }
                            }

                            const customerUserRoleIds = Object.keys(nonPurchasedCustomer)
                            if (customerUserRoleIds.length) {
                                const updateCustomerResponse = await InternalServiceModel.updateCustomer({
                                    filter: {
                                        _id: {
                                            $in: customerUserRoleIds
                                        }
                                    },
                                    updateFields: {
                                        customer_catalog_mode: true
                                    }
                                })

                                if (updateCustomerResponse.modifiedCount) {
                                    taskReport.updatedCustomerCount += updateCustomerResponse.modifiedCount
                                    taskReport.updatedCustomerUserRoleIds.push(...customerUserRoleIds)
                                    taskReport.updatedCustomerIds.push(...Object.values(nonPurchasedCustomer))
                                }
                            }
                        }
                        catch (customerTaskError) {
                            logger.error(customerTaskError)
                        }
                    }

                    if (taskReport.updatedCustomerCount > 0) {
                        logMessage(`Customer's catalog mode enabled for ${taskReport.updatedCustomerCount} customers`)
                        logMessage(`Customer's catalog mode enabled for customer user role id: ${taskReport.updatedCustomerUserRoleIds.join(', ')}`)

                        autoCatalogEmailTasks.push({
                            customerIds: taskReport.updatedCustomerIds,
                            tenantId: appSettings.tenant_id,
                        })
                    }
                }
                catch (tenantTaskError) {
                    logger.error(tenantTaskError)
                }
            }

            if (autoCatalogEmailTasks.length) {
                const emailSummary = await Promise.allSettled(
                    autoCatalogEmailTasks.map(task => sendCatalogModeEnabledEmail(
                        task.customerIds,
                        task.tenantId
                    ))
                )

                emailSummary.forEach(summary => {
                    if (summary.reason) {
                        logger.error("Error in sending catalog mode enabled email", { errorMessage: summary.reason })
                    }
                })
            }
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "CUSTOMER AUTO ENABLE CATALOG MODE JOB RUN TIME" })
            logger.info(`Completed: Customer's auto enable catalog mode job\n`)
        }
    }

    async checkPreApproved(req, res) {
        try {
            const {
                tenantId,
                externalId,
                orderId
            } = req.body;

            const {
                statuscode,
                data,
                error
            } = await InternalServiceModel.checkCustomersPreApprovedStatus(
                tenantId,
                [externalId]
            )

            if (statuscode === STATUS_CODES.SUCCESS) {
                const pre_approved = data?.[externalId] ?? false

                await OrderModel.updateOrder(
                    {
                        _id: orderId,
                        tenant_id: tenantId
                    },
                    {
                        pre_approved
                    }
                )

                return res.handler.success(data?.message, { pre_approved });
            }
            else {
                return res.handler.custom(
                    statuscode,
                    data?.message,
                    data?.data,
                    error
                )
            }
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = OrderController;

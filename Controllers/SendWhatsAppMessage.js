const InternalServiceModel = new (require("../Models/InternalServiceModel"))();
const OrderModel = new (require("../Models/OrderModel"))();
const MessageBird = new (require('../Configs/messageBird'))();

const {
    ORDER_STATUS_TYPES,
    STATUS_CODES,
    PRIMITIVE_ROLES,
    MESSAGE_REQUEST
} = require("../Configs/constants");

const {
    excludeProjections,
    roundOf,
} = require("../Utils/helpers");

class SendWhatsAppMessage {

    mapTemplateKeyToValue = (parameters = [], order = {}) => {
        if (!parameters?.length) return {};

        const variables = {};
        for (let index = 0; index < parameters.length; index++) {
            const parameter = parameters[index];
            if (parameter.system_key === 'input_field') {
                variables[parameter.integration_key] = order[parameter.integration_key] || "";
            }
            else {
                variables[parameter.integration_key] = order[parameter.system_key] || "";
            }
        }

        return variables;
    }

    getReasonByUserRole = (
        templates = [],
        receiverRole,
        templateId
    ) => {
        if (!templates.length) return {};
        const data = templates.find((template) =>
            template.template_id === templateId &&
            template.role === receiverRole
        );
        return data || {};
    }

    prepareMessageByUserRole = async (
        reason = {},
        receiver = {},
        order = {},
        messageDetails = {},
        configurations = {},
        isReleaseMessage = false
    ) => {
        const USER_TYPE = {
            [PRIMITIVE_ROLES.SALES_PERSON]: 'salesPerson',
            [PRIMITIVE_ROLES.CUSTOMER]: 'customer'
        }

        const holdReasonByRole = this.getReasonByUserRole(
            isReleaseMessage ? reason.release_templates : reason.hold_templates,
            receiver.role,
            messageDetails[USER_TYPE[receiver.role]]?.templateId || ''
        );

        if (holdReasonByRole.template_id) {
            const project = await MessageBird.getProjectDetail(
                holdReasonByRole.template_id,
                configurations
            );


            if (project.status === STATUS_CODES.SUCCESS) {
                if (!project.data) {
                    return {
                        status: STATUS_CODES.NOT_FOUND,
                        role: receiver.role,
                        data: {}
                    };
                }

                const payload = JSON.parse(JSON.stringify(MESSAGE_REQUEST));

                payload.sender.connector.identifierValue = configurations.channel_id;
                payload.receiver.contacts = [
                    {
                        identifierKey: "phonenumber",
                        identifierValue: `${receiver.countryCode}${receiver.mobileNumber}`
                    }
                ]
                payload.template.projectId = project.data.projectId;
                payload.template.locale = project.data.defaultLocale;
                payload.template.version = project.data.id;

                const userInputs = messageDetails[USER_TYPE[receiver.role]]?.inputs || {};

                payload.template.variables = this.mapTemplateKeyToValue(
                    holdReasonByRole.parameters || [],
                    {
                        ...order,
                        ...userInputs
                    },
                );

                return {
                    status: STATUS_CODES.SUCCESS,
                    role: receiver.role,
                    data: payload
                };
            }
            else {
                return project;
            }
        }
        else {
            return {
                status: STATUS_CODES.NOT_FOUND,
                role: receiver.role,
                message:
                    receiver.role === PRIMITIVE_ROLES.SALES_PERSON
                        ? 'sales_person_template_not_configured'
                        : 'customer_template_not_configured',
                data: {}
            }
        }
    }

    sendReasonMessage = async (req, res) => {
        try {
            const {
                orderId,
                holdReasonId,
                tenantId,
                messageDetails = {},
                isReleaseMessage,
                isSendMessage
            } = req.body;

            let holdReasonExcludeProjection = JSON.parse(JSON.stringify(excludeProjections));

            if (isReleaseMessage) {
                holdReasonExcludeProjection.hold_templates = 0;
            } else {
                holdReasonExcludeProjection.release_templates = 0;
            }

            const holdReasonApiParams = {
                filter: {
                    _id: holdReasonId,
                    tenant_id: tenantId
                },
                projection: holdReasonExcludeProjection,
                options: {
                    lean: true,
                    populate: [
                        {
                            path: "integration_credential_id"
                        },
                    ]
                },
            };

            let [order, holdReason] = await Promise.all(
                [
                    OrderModel.findOrder(
                        {
                            _id: orderId,
                            tenant_id: tenantId,
                        },
                        {
                            total_amount: 1,
                            total_tax: 1,
                            customer_user_role_id: 1,
                            sales_user_role_id: 1,
                            order_number: 1,
                            order_remark: 1,
                            order_status: 1,
                            order_status_track: 1,
                            shipping_address: 1,
                            city_name: 1,
                            region_name: 1,
                            external_id: 1,
                            order_hold_reason_id: 1
                        },
                    ),
                    InternalServiceModel.getReason(holdReasonApiParams),
                ]
            );

            if (!order) {
                return res.handler.notFound("order_not_found");
            }

            const userApiParams = {
                filter: {
                    _id: {
                        $in:
                            [
                                new mongoose.Types.ObjectId(order.sales_user_role_id),
                                new mongoose.Types.ObjectId(order.customer_user_role_id)
                            ]
                    },
                },
                projection: "-_id user_id role_id tenant_id collection_name customer_name customer_first_name customer_last_name customer_legal_name",
                options: {
                    lean: true,
                    populate: [
                        {
                            path: "user_id",
                            select: "_id first_name last_name email country_code mobile_number"
                        },
                        {
                            path: "role_id",
                            select: "_id name role_id"
                        },
                    ]
                },
            };

            const receivers = await InternalServiceModel.getUsers(
                userApiParams
            );

            const sendMessage =
                holdReason &&
                holdReason.is_whatsapp_message_enabled &&
                holdReason.is_active &&
                holdReason.integration_credential_id &&
                receivers?.length &&
                isSendMessage;

            let messages = [],
                errorInPreparedMessage = [],
                log = {
                    receivers: {
                        [PRIMITIVE_ROLES.SALES_PERSON]: false,
                        [PRIMITIVE_ROLES.CUSTOMER]: false
                    },
                    message: ""
                },
                sendMessagePromises = [],
                sentMessageIdsPromises = [];

            if (sendMessage) {
                for (let index = 0; index < receivers.length; index++) {
                    const element = receivers[index];

                    let receiver = {
                        countryCode: element.user_id?.country_code || '',
                        mobileNumber: element.user_id?.mobile_number?.toString() || '',
                        role: element.role_id?.name || '',
                    }

                    if (!receiver.countryCode?.charAt(0) === "+") {
                        receiver.countryCode = `+${receiver.countryCode}`;
                    }

                    const mapKey = {
                        ...order?._doc,
                    };

                    const tenantAppSettings = await InternalServiceModel.tenantAppSettings(tenantId, "decimal_points")
                    const { decimal_points = 0 } = tenantAppSettings || {}

                    const orderAmount =
                        (mapKey.total_amount || 0) +
                        (mapKey.total_tax || 0)

                    mapKey.order_amount = roundOf(orderAmount, decimal_points, "string")

                    if (receiver.role === PRIMITIVE_ROLES.SALES_PERSON) {
                        mapKey.first_name = element.user_id?.first_name || '';
                        mapKey.last_name = element.user_id?.last_name || '';
                        mapKey.name = `${element.user_id?.first_name || ""} ${element.user_id?.last_name || ""}`;

                        const customer = receivers.find(ele => ele.role_id?.name === PRIMITIVE_ROLES.CUSTOMER)

                        if (customer) {
                            mapKey.legal_name = customer.customer_legal_name
                        }
                    }

                    if (receiver.role === PRIMITIVE_ROLES.CUSTOMER) {
                        mapKey.first_name = element?.customer_first_name || "";
                        mapKey.last_name = element?.customer_last_name || "";
                        mapKey.name = `${element?.customer_first_name || ""} ${element?.customer_last_name || ""}`;
                        mapKey.legal_name = element?.customer_legal_name
                    }

                    delete mapKey.order_status_track;

                    let preparedMessage = {};
                    try {
                        preparedMessage = await this.prepareMessageByUserRole(
                            holdReason,
                            receiver,
                            mapKey,
                            messageDetails,
                            holdReason.integration_credential_id.configurations,
                            isReleaseMessage
                        );

                        if (preparedMessage.status === STATUS_CODES.SUCCESS) {
                            messages.push({
                                data: JSON.parse(JSON.stringify(preparedMessage.data)),
                                role: receiver.role
                            });
                        }
                        else {
                            errorInPreparedMessage.push(preparedMessage)
                        }
                    }
                    catch (error) {
                        logger.error(error)
                    }
                }

                if (errorInPreparedMessage?.length) {
                    for (let index = 0; index < errorInPreparedMessage.length; index++) {
                        const element = errorInPreparedMessage[index];

                        logger.error("errorInPreparedMessage", {
                            errorMessage: element.message,
                            status: element.status || STATUS_CODES.SERVER_ERROR
                        })
                    }
                }

                for (let index = 0; index < messages.length; index++) {
                    const element = messages[index];

                    logger.info("SEND WHATSAPP MESSAGE :", { data: element?.data })

                    sendMessagePromises.push(
                        MessageBird.sendMessageToReceiver(
                            element?.data || {},
                            holdReason.integration_credential_id.configurations,
                            element?.role
                        )
                    )
                }
                const sentMessages = await Promise.all(sendMessagePromises);

                for (let index = 0; index < sentMessages.length; index++) {
                    const element = sentMessages[index];

                    logger.info("MESSAGE SENDING DETAIL :", { data: element })

                    if (
                        [
                            STATUS_CODES.SUCCESS,
                            STATUS_CODES.CREATED,
                            STATUS_CODES.ACCEPTED
                        ]
                            .includes(element.status)
                    ) {
                        sentMessageIdsPromises.push(
                            MessageBird.getMessageStatus(
                                element?.data?.id || '',
                                holdReason.integration_credential_id.configurations,
                                element?.role
                            )
                        )
                        logger.info(`MESSAGE ACCEPTED FOR THE "${element?.role}"`)
                    }
                    else {
                        logger.error("Error", {
                            errorMessage: element.message,
                            status: element.status
                        })
                    }
                }
            }

            const messageStatusResponse = await Promise.all(sentMessageIdsPromises);

            for (let index = 0; index < messageStatusResponse.length; index++) {
                const element = messageStatusResponse[index];
                try {
                    const data = element?.data || {}
                    if (
                        ['sent', 'processing', 'delivered'].includes(data.status)
                    ) {
                        log.receivers[element?.role] = true;
                    }
                    else {
                        logger.info("messageStatusResponse", {
                            status: data?.status
                        })
                    }
                }
                catch (error) {
                    logger.error(error)
                }
            }

            let status = isReleaseMessage
                ? ORDER_STATUS_TYPES.RELEASED
                : ORDER_STATUS_TYPES.ON_HOLD;

            order.order_status = status;
            order.order_status_track = order.order_status_track.length
                ? order.order_status_track
                : [];

            if (isReleaseMessage) {
                order.order_hold_reason_id = undefined;
                order.whatsapp_message_sent = undefined;
            } else {
                order.order_hold_reason_id = holdReasonId;
                order.whatsapp_message_sent = {
                    sales_person: log.receivers[PRIMITIVE_ROLES.SALES_PERSON],
                    customer: log.receivers[PRIMITIVE_ROLES.CUSTOMER],
                };
            }

            order.order_status_track.push(
                {
                    order_status: status,
                    time: new Date(),
                    user_info: {
                        user_role_id: req.headers.userroleid,
                        name: `${req.headers.userDetails.first_name || ''} ${req.headers.userDetails.last_name || ''}`,
                    },
                    reason_id: holdReasonId
                }
            );

            await order.save();

            if (
                log.receivers[PRIMITIVE_ROLES.SALES_PERSON] &&
                !log.receivers[PRIMITIVE_ROLES.CUSTOMER]
            ) {
                log.message = 'message_successfully_sent_to_sales_person'
            }

            if (
                log.receivers[PRIMITIVE_ROLES.CUSTOMER] &&
                !log.receivers[PRIMITIVE_ROLES.SALES_PERSON]
            ) {
                log.message = 'message_successfully_sent_to_customer'
            }

            if (
                !log.receivers[PRIMITIVE_ROLES.SALES_PERSON] &&
                !log.receivers[PRIMITIVE_ROLES.CUSTOMER]
            ) {
                log.message = 'message_not_sent_to_sales_person_and_customer';
            }

            if (
                log.receivers[PRIMITIVE_ROLES.SALES_PERSON] &&
                log.receivers[PRIMITIVE_ROLES.CUSTOMER]
            ) {
                log.message = 'message_successfully_sent_to_sales_person_and_customer';
            }

            return res.handler.success(log.message);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }
}

module.exports = SendWhatsAppMessage;

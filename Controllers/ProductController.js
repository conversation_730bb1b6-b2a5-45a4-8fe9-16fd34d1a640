const difference = require("lodash.difference");
const { default: mongoose } = require("mongoose");

const FileUpload = require('../Configs/awsUploader').S3Upload;

const {
    PRODUCT_TYPE,
    ENTITY_STATUS,
    FILE_PATH,
    BUCKET_TYPE,
    INCREMENT_PRODUCT_NUMBER_BY,
    FAVORITE_PRODUCT_TYPE,
    NEW_PRODUCT_TYPE,
    ACTIVE_STATUS_ENUM,
    PRODUCT_SORT_BY,
    PRODUCT_SORT_TYPE,
    DEAL_STATUS,
    INVENTORY_PRODUCT_LISTING_SORT_TYPES,
    INTEGRATION_CHANNELS
} = require("../Configs/constants");

const {
    excludeProjections,
} = require('../Utils/helpers');

const OrderModel = new (require("../Models/OrderModel"));
const ProductModel = new (require("../Models/ProductModel"));
const VariantTypeModel = new (require("../Models/VariantTypeModel"));
const ImageModel = new (require("../Models/ImageModel"))();
const MasterDataModel = new (require("../Models/MasterDataModel"))();

const InternalServiceModel = new (require("../Models/InternalServiceModel"));


class ProductController {

    async addProduct(req, res) {
        // using transactions for better data consistency
        let session
        try {
            session = await mongoose.startSession();
            session.startTransaction();

            const productMasterPriceMap = {};
            const variantInventoryMap = new Map();
            const barcodesSet = new Set()

            const body = req.body
            const productDetails = body.product;
            const variantProductsDetails = body.variantProducts;
            const product = await ProductModel.createProduct();

            const userId = req.headers.userDetails?._id

            product.tenant_id = body.tenantId;
            product.item_number = productDetails.itemNumber;
            product.unique_item_number = `${product.tenant_id}_${product.item_number}`;
            product.title = productDetails.title;
            product.secondary_language_title = productDetails.secondaryLanguageTitle;
            product.type = productDetails.type;
            product.brand_id = productDetails.brand;
            product.family_id = productDetails.family;
            product.category_id = productDetails.category;
            product.subcategory_id = productDetails.subCategory;
            product.attributes = productDetails.attributes;
            product.is_active = productDetails.isActive;
            product.is_deleted = false;
            product.description = productDetails.description;
            product.secondary_language_description = productDetails.secondaryLanguageDescription;
            product.attribute_set = productDetails.attributeSet || null;
            product.barcodes = []

            let category;
            if (product.family_id && product.category_id && product.subcategory_id) {
                category = await ProductModel.findCategoryById({ _id: product.subcategory_id }, { _id: 1, product_counter: 1, is_active: 1 });
                if (!category) {
                    return res.handler.notFound("can't_find_sub_cat");
                }
                category.product_counter += 1
                product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
            } else if (product.family_id && product.category_id) {
                category = await ProductModel.findCategoryById({ _id: product.category_id }, { _id: 1, product_counter: 1, is_active: 1 });
                if (!category) {
                    return res.handler.notFound("can't_find_cat")
                }
                category.product_counter += 1
                product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
            } else {
                category = await ProductModel.findCategoryById({ _id: product.family_id }, { _id: 1, product_counter: 1, is_active: 1 });
                if (!category) {
                    return res.handler.notFound("can't_family");
                }
                category.product_counter += 1
                product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
            }
            await category.save({ session: session });
            product.created_by = userId
            product.updated_by = userId

            const proTagsPromises = [];
            for (let i = 0; i < productDetails.tags.length; i++) {
                const tag = productDetails.tags[i];
                proTagsPromises.push(ProductModel.addOrUpdateTag(req.body.tenantId, tag, { session }))
            }
            const savedTags = await Promise.all(proTagsPromises);
            product.tags = savedTags?.map(t => t._id) || [];

            switch (productDetails.type) {
                case NEW_PRODUCT_TYPE.SINGLE:
                    {
                        // barcode adding for single parent
                        await ProductModel.addUpdateBarcodes(body.tenantId, productDetails.barcodes, product._id, { session }, true);
                        product.packaging_map = {
                            uom_id: productDetails.uomMapping.uom,
                            min_qty: productDetails.uomMapping.minQty,
                            qty_ctn: productDetails.uomMapping.qtyCtn
                        };
                        product.price_mappings = productDetails.priceMapping;
                        product.inventory_mappings = productDetails.inventoryMapping;
                        product.tax_id = productDetails.taxId;

                        productDetails.barcodes?.forEach(pdb => {
                            if (barcodesSet.has(pdb)) {
                                const error = new Error();
                                error.name = "barcode_already_exists";
                                error.keyValue = { barcodes: `${body.tenantId}_${pdb}` };
                                throw error;
                            }
                            else {
                                barcodesSet.add(pdb)
                            }
                        })
                        product.barcodes = productDetails.barcodes

                        break;
                    }

                case NEW_PRODUCT_TYPE.PARENT:
                    {
                        //add variant ids and groups ids for product document to create product_variant document's PK (_id)
                        const [variantTypes, groupTypes] = await ProductModel.addVariantsAndGroups(
                            req.body.tenantId,
                            productDetails.variants.values,
                            productDetails.groups?.values,
                            product._id,
                            {
                                session,
                                ordered: true,
                            }
                        );

                        product.variants = {
                            type: productDetails.variants.type,
                            values: variantTypes.map(vt => vt._id)
                        };
                        if (productDetails.groups) {
                            product.groups = {
                                type: productDetails.groups?.type,
                                values: groupTypes.map(gt => gt._id)
                            }
                        }

                        let variantCount = 0;
                        const variantProducts = [];
                        const variantBarcodes = [];

                        for (let i = 0; i < variantProductsDetails.length; i++) {
                            const vProd = variantProductsDetails[i];

                            const variantInfo = variantTypes.find(v => v.name === vProd.variantName);

                            let variantGroupError = null;
                            if (!variantInfo) {
                                variantGroupError = new Error(`variant info is missing for the variant name ${vProd.variantName}`);
                                variantGroupError.name = "invalid_variant_value";
                                variantGroupError.variantInfo = { variantName: vProd.variantName }
                            }

                            const groupInfo = productDetails.groups?.type ? groupTypes.find(v => v.name === vProd.groupName) : null;
                            if (productDetails.groups?.type && (!groupInfo || !groupInfo?._id)) {
                                variantGroupError = new Error(`group info is missing for the variant name ${variantInfo.name} and group type is ${groupInfo.type}`);
                                variantGroupError.name = "invalid_group_value";
                                variantGroupError.groupInfo = { variantName: vProd.variantName, groupName: vProd.groupName }
                            }

                            if (variantGroupError) {
                                throw variantGroupError;
                            }

                            // variant's variant_id key
                            const variant_id = `${product._id}_${variantInfo._id}${groupInfo?._id ? `_${groupInfo?._id}` : ""}`

                            const variantProductInfo = {
                                variant_id: variant_id,
                                tenant_id: req.body.tenantId,
                                parent_id: product._id,
                                variant_value_id: variantInfo._id,
                                // variant_name: vProd.variantName,
                                // group_name: vProd.groupName,
                                group_value_id: groupInfo?._id,
                                item_number: vProd.itemNumber,
                                unique_item_number: `${body.tenantId}_${vProd.itemNumber}`,
                                type: NEW_PRODUCT_TYPE.VARIANT,
                                packaging_map: {
                                    uom_id: vProd.uomMapping.uom,
                                    min_qty: vProd.uomMapping.minQty,
                                    qty_ctn: vProd.uomMapping.qtyCtn
                                },
                                price_mappings: vProd.priceMapping,
                                inventory_mappings: vProd.inventoryMapping,
                                barcodes: vProd.barcodes,
                                variant_order: i + 1,
                                created_by: userId,
                                updated_by: userId,
                                is_active: vProd.isActive,
                                is_deleted: false,
                                tax_id: vProd.taxId,
                            };

                            vProd.barcodes?.forEach(pdb => {
                                if (barcodesSet.has(pdb)) {
                                    const error = new Error();
                                    error.name = "barcode_already_exists";
                                    error.keyValue = { barcodes: `${body.tenantId}_${pdb}` };
                                    throw error;
                                }
                                else {
                                    barcodesSet.add(pdb)
                                }
                            })

                            // const variant = await ProductModel.createVariantProduct(variantProductInfo);
                            const variant = await ProductModel.createProduct(variantProductInfo);
                            variantBarcodes.push({
                                barcodes: vProd.barcodes,
                                variantId: variant._id
                            })

                            if (variantProductInfo.is_active) {
                                product.active_variant_barcodes.push(...vProd.barcodes)
                            }
                            else {
                                product.inactive_variant_barcodes.push(...vProd.barcodes)
                            }

                            variantProducts.push(variant);
                            variantCount++;

                            for (let p = 0; p < vProd.priceMapping?.length; p++) {
                                let varPriceMap = vProd.priceMapping[p];
                                varPriceMap["product_variant_id"] = variant._id; // add the variant info for price-mapping
                                if ((!productMasterPriceMap[varPriceMap.master_price_id]) || ((varPriceMap.price > 0) && (productMasterPriceMap[varPriceMap.master_price_id].price > varPriceMap.price))) {
                                    productMasterPriceMap[varPriceMap.master_price_id] = varPriceMap;
                                }
                            }

                            for (let i = 0; i < vProd.inventoryMapping.length; i++) {
                                const iMap = vProd.inventoryMapping[i];
                                if (variantInventoryMap.has(iMap.branch_id)) {
                                    const inventoryObj = variantInventoryMap.get(iMap.branch_id);
                                    inventoryObj.quantity += iMap.quantity;
                                    variantInventoryMap.set(iMap.branch_id, inventoryObj);
                                } else {
                                    variantInventoryMap.set(iMap.branch_id, iMap);
                                }
                            }

                            if (vProd.isActive) {
                                product.active_variant_item_numbers.push(vProd.itemNumber);
                            }
                            else {
                                product.inactive_variant_item_numbers.push(vProd.itemNumber);
                            }
                        }

                        try {
                            await Promise.all([
                                //Update barcodes
                                ...variantBarcodes.map(
                                    obj => {
                                        return ProductModel.addUpdateBarcodes(body.tenantId, obj.barcodes, obj.variantId, { session }, true)
                                    }
                                ),
                                //Update variant product info
                                ...variantProducts.map(
                                    variant => (
                                        variant.save({ session })
                                    )
                                )
                            ])
                        }
                        catch (e) {
                            const error = new Error(e);

                            if (e?.code === 11000) {
                                error.stack = e.stack
                                error.keyValue = e.keyValue;

                                if ((e?.keyValue?.unique_item_number)) {
                                    error.name = "item_number_already_exists";
                                }
                                else if ((e?.keyValue?.variant_id)) {
                                    error.name = "variant_already_exists";
                                }
                                else if ((e?.keyValue?.barcodes)) {
                                    error.name = "barcode_already_exists";
                                }
                            } else if (e.codeName === "NoSuchTransaction") {
                                error.codeName = "NoSuchTransaction";
                            }
                            throw error;
                        }
                        product.variant_count = variantCount;

                        //logic for adding lowest price for the each price list in pricing_map
                        const mainProductPriceMapping = []
                        for (const key in productMasterPriceMap) {
                            mainProductPriceMapping.push(productMasterPriceMap[key]);
                        }
                        product.price_mappings = mainProductPriceMapping;
                        product.inventory_mappings = Array.from(variantInventoryMap.values());

                        break;
                    }
            }

            try {
                await product.save({ session: session });
            }
            catch (e) {
                const error = new Error(e);

                if (e?.code === 11000) {
                    error.stack = e.stack
                    error.keyValue = e.keyValue;

                    if (e?.keyValue?.unique_item_number) {
                        error.name = "item_number_already_exists"
                    }
                    else if (e?.keyValue?.barcodes) {
                        error.name = "barcode_already_exists";
                    }
                }
                throw error;
            }
            await product.populate({ path: "groups.values", select: "_id name" })
            await session.commitTransaction();
            await session.endSession();

            return res.handler.success("added_product_successfully", product);
        }
        catch (error) {
            await session.abortTransaction();
            await session.endSession({ forceClear: true });

            switch (error.name) {
                case "item_number_already_exists":
                    return res.handler.badRequest("item_number_already_exists", error.keyValue);

                case "barcode_already_exists":
                    return res.handler.badRequest("barcode_already_exists", error.keyValue);

                case "variant_already_exists":
                    return res.handler.badRequest("variant_already_exists", error.keyValue);

                case "invalid_group_value":
                    return res.handler.badRequest("invalid_group_value", error.groupInfo);

                case "invalid_variant_value":
                    return res.handler.badRequest("invalid_variant_value", error.variantInfo)
                default:
                    if (error.codeName === "WriteConflict") {
                        return res.handler.conflict("write_conflict_occurred");
                    }

                    if (error.codeName === "NoSuchTransaction") {
                        return res.handler.custom(STATUS_CODES.UNPROCESSABLE_ENTITY, "transaction_error_occurred", error)
                    }
                    return res.handler.serverError(error, error.metadata)
            }
        }

    }

    async getProductDetails(req, res) {
        try {
            const { productId, tenantId, activeVariants, customerUserRoleId, salesPersonUserRoleId, priceListId } = req.query;
            const filter = { _id: productId, is_deleted: false, tenant_id: tenantId };
            const projection = { created_by: 0, updated_by: 0, __v: 0 };
            const userRoleId = req.headers.userroleid;
            const variantProductFilter = { is_deleted: false };
            const cart_id = (customerUserRoleId
                ? (customerUserRoleId === userRoleId ? `${tenantId}_${customerUserRoleId}` : `${tenantId}_${customerUserRoleId}_${userRoleId}`)
                : null
            );

            activeVariants ? variantProductFilter["is_active"] = true : null;

            const date = moment().utc().toDate();

            const dealMatchObj = {
                deal_from_date: { $lte: date },
                deal_to_date: { $gte: date },
            }

            if (priceListId) {
                dealMatchObj['price_id'] = new mongoose.Types.ObjectId(priceListId);
            }

            const dealPopulate = {
                path: "deal_id",
                select: { deal_type: 1, deal_id: 1, deal_name: 1, secondary_deal_name: 1 },
                match: {
                    deal_status: {
                        $nin: [DEAL_STATUS.PROGRESS, DEAL_STATUS.PAUSED, DEAL_STATUS.CANCELLED],
                    }
                }
            }

            if (salesPersonUserRoleId) {
                dealPopulate['match']['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserRoleId);
            }

            const variantProductPopulate = {
                path: "variantProducts",
                select: projection, match: variantProductFilter,
                options: { sort: { variant_order: 1 } },
                populate: [
                    { path: "variant_value_id", select: { name: 1 } },
                    { path: "group_value_id", select: { name: 1 } },
                    { path: "images", select: projection, options: { sort: { image_number: 1 } } },
                    {
                        path: "product_deal_info",
                        select: {
                            deal_id: 1,
                            discount_type: 1,
                            percent: 1,
                            amount: 1,
                            discounted_price: 1,
                            first_tier: 1,
                            second_tier: 1,
                            third_tier: 1,
                            buy_product: 1,
                            free_product: 1,
                            deal_from_date: 1,
                            deal_to_date: 1
                        },
                        match: dealMatchObj,
                        populate: dealPopulate
                    }
                ],
            };

            const options = {
                populate: [
                    variantProductPopulate,
                    { path: "images", select: projection, options: { sort: { image_number: 1 } }, match: { group_id: null } },
                    { path: "variants.values", select: { name: 1, order: 1 } },
                    { path: "groups.values", select: { name: 1, order: 1 }, populate: [{ path: "groupImages", options: { sort: { image_number: 1 } } }] },
                    { path: "tags", select: projection },
                    { path: "attributes.attribute_id", select: { attribute_name: 1, secondary_language_attribute_name: 1, is_active: 1 } },
                    { path: "attribute_set", select: { attribute_set_name: 1, secondary_language_attribute_set_name: 1, is_active: 1 } },
                    {
                        path: "product_deal_info",
                        select: {
                            deal_id: 1,
                            discount_type: 1,
                            percent: 1,
                            amount: 1,
                            discounted_price: 1,
                            first_tier: 1,
                            second_tier: 1,
                            third_tier: 1,
                            buy_product: 1,
                            free_product: 1,
                            deal_from_date: 1,
                            deal_to_date: 1
                        },
                        match: dealMatchObj,
                        populate: dealPopulate
                    }
                ]
            };

            if (cart_id) {
                const cartProjection = {
                    min_qty: 1,
                    cart_id: 1,
                    product_variant_id: 1,
                    base_price: 1,
                    tax: 1,
                    tax_calculation_info: 1,
                    uom_id: 1,
                    uom_name: 1,
                    variant_id: 1,
                    quantity: 1,
                    original_price: 1,
                    master_price_id: 1,
                    item_comment: 1
                };
                variantProductPopulate.populate.push(
                    { path: "cartItem", select: cartProjection, match: { cart_id } }
                )
                options.populate.push({ path: "cartItem", select: cartProjection, match: { cart_id } })
            }
            const product = await ProductModel.findProduct(filter, projection, options);
            if (!product) {
                return res.handler.notFound("product_not_found");
            }

            const is_favorite = Boolean(await ProductModel.findFavoriteProduct({ user_role_id: userRoleId, tenant_id: tenantId, product_variant_id: product._id }, { _id: 1 }));
            return res.handler.success(null, { ...product.toObject(), is_favorite });
        } catch (error) {
            return res.handler.serverError(error)
        }
    }

    async editProduct(req, res) {
        let session;
        try {
            session = await mongoose.startSession();
            session.startTransaction();

            const itemNumberSet = new Set();
            const barcodesSet = new Set()
            const body = req.body
            const productDetails = body.product;
            const variantProductsDetails = body.variantProducts;
            const { tenantId } = body

            const product = await ProductModel.findProduct({ _id: productDetails.productId, tenant_id: body.tenantId }, null, {
                populate: [
                    { path: "tags" },
                    { path: "variants.values", select: { name: 1 } },
                    { path: "groups.values", select: { name: 1 } },
                ],
                session,
            });

            if (!product) {
                await session.endSession();
                return res.handler.notFound("product_not_found")
            }

            if ((product.type !== productDetails.type) || (product.type === NEW_PRODUCT_TYPE.VARIANT)) {
                await session.endSession();
                return res.handler.conflict("invalid_product_type");
            }
            // Check tenant's product's limit
            if (productDetails.isActive && !product.is_active) {
                const defaultTenantSetting = await MasterDataModel.getTenantInfo(req.body.tenantId, "advance_limit") // Dec 30 product limit validation
                let tenantAdvanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_PRODUCTS');
                const allowProducts = tenantAdvanceLimit.allowance
                const activeProducts = await ProductModel.allowNewValidation(req.body.tenantId)
                if (allowProducts <= activeProducts) {
                    // productDetails.isActive = false;
                    await session.endSession();
                    return res.handler.conflict("product_limit_exceed");
                }
            }
            const userId = req.headers.userDetails?._id

            product.tenant_id = body.tenantId;
            product.item_number = productDetails.itemNumber;
            itemNumberSet.add(productDetails.itemNumber);
            product.unique_item_number = `${product.tenant_id}_${product.item_number}`;
            product.title = productDetails.title;
            product.secondary_language_title = productDetails.secondaryLanguageTitle;
            product.type = productDetails.type;
            product.brand_id = productDetails.brand;
            product.attributes = productDetails.attributes;
            product.is_active = productDetails.isActive;
            product.is_deleted = false;
            product.description = productDetails.description;
            product.secondary_language_description = productDetails.secondaryLanguageDescription;
            product.attribute_set = productDetails.attributeSet || null;
            product.updated_by = userId
            product.tax_id = productDetails.taxId;
            product.attribute_set = productDetails.attributeSet || null;

            let hasCatChanged = false;
            if (product.family_id.toString() !== productDetails.family || (product.category_id?.toString() !== productDetails.category) || (product.subcategory_id?.toString() !== productDetails.subCategory)) {
                hasCatChanged = true;
            }

            product.family_id = productDetails.family;
            product.category_id = productDetails.category;
            product.subcategory_id = productDetails.subCategory;

            if (hasCatChanged) {
                let category;
                if (product.family_id && product.category_id && product.subcategory_id) {
                    category = await ProductModel.findCategoryById({ _id: product.subcategory_id }, { _id: 1, product_counter: 1, is_active: 1 });
                    if (!category) {
                        return res.handler.notFound("can't_find_sub_cat");
                    }
                    category.product_counter += 1
                    product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
                } else if (product.family_id && product.category_id) {
                    category = await ProductModel.findCategoryById({ _id: product.category_id }, { _id: 1, product_counter: 1, is_active: 1 });
                    if (!category) {
                        return res.handler.notFound("can't_find_cat")
                    }
                    category.product_counter += 1
                    product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
                } else {
                    category = await ProductModel.findCategoryById({ _id: product.family_id }, { _id: 1, product_counter: 1, is_active: 1 });
                    if (!category) {
                        return res.handler.notFound("can't_family");
                    }
                    category.product_counter += 1
                    product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
                }
                await category.save({ session });
            }

            const allVariants = []
            const barcodeUpdates = [];

            switch (product.type) {
                case NEW_PRODUCT_TYPE.SINGLE:
                    {
                        product.packaging_map = {
                            uom_id: productDetails.uomMapping.uom,
                            min_qty: productDetails.uomMapping.minQty,
                            qty_ctn: productDetails.uomMapping.qtyCtn
                        };
                        product.price_mappings = productDetails.priceMapping;
                        product.inventory_mappings = productDetails.inventoryMapping;

                        const addedBarcodes = []; // barcode string for creating new barcodes
                        const deletedBarcode = []; // It will have _id of existing barcodes

                        productDetails.barcodes?.forEach(pdb => {
                            if (!product.barcodes?.find(barcode => (barcode === pdb))) {
                                if (barcodesSet.has(pdb)) {
                                    const error = new Error();
                                    error.name = "barcode_already_exists";
                                    error.keyValue = { barcodes: `${body.tenantId}_${pdb}` };
                                    throw error;
                                }
                                else {
                                    addedBarcodes.push(pdb)
                                    barcodesSet.add(pdb)
                                }
                            }
                        });

                        product.barcodes?.forEach(barcode => {
                            if (!productDetails.barcodes?.find(pdb => (barcode === pdb))) {
                                const barcodeId = tenantId + "_" + barcode
                                deletedBarcode.push(barcodeId)
                            }
                        });
                        product.barcodes = productDetails.barcodes

                        if (deletedBarcode.length) {
                            barcodeUpdates.push({
                                isDeleted: true,
                                variantId: product._id,
                                barcodes: deletedBarcode
                            })
                        }
                        if (addedBarcodes.length) {
                            barcodeUpdates.push({
                                variantId: product._id,
                                barcodes: addedBarcodes
                            })
                        }

                        await Promise.all(barcodeUpdates.map(obj => {
                            if (obj.isDeleted) {
                                return ProductModel.deleteBarcodes({ product_variant_id: obj.variantId, _id: { $in: obj.barcodes } }, { session })
                            }
                            else {
                                return ProductModel.addUpdateBarcodes(body.tenantId, obj.barcodes, obj.variantId, { session }, true)
                            }
                        }));

                        break;
                    }

                case NEW_PRODUCT_TYPE.PARENT:
                    {
                        const variantPromises = [];
                        const productVariantIds = []

                        if (Array.isArray(variantProductsDetails)) {
                            variantProductsDetails.forEach(vp => {
                                if (itemNumberSet.has(vp.itemNumber)) {
                                    const error = new Error();
                                    error.name = "item_number_already_exists";
                                    error.keyValue = { unique_item_number: `${body.tenantId}_${vp.itemNumber}` };
                                    throw error;
                                }
                                itemNumberSet.add(vp.itemNumber);
                            })
                        }
                        for (let i = 0; i < variantProductsDetails.length; i++) {
                            const vProd = variantProductsDetails[i];
                            const [productId, variantValueId, groupValueId] = vProd.variantId.split("_");

                            let variantGroupError = null;

                            if (product.id !== productId) {
                                variantGroupError = new Error(`invalid product id in variantId parameter`);
                                variantGroupError.name = "invalid_product_id";
                                variantGroupError.variantInfo = { variantId: vProd.variantId };
                                throw variantGroupError;
                            }
                            if (product.variants.type && (!variantValueId || !product.variants?.values.some(i => i.id === variantValueId))) {
                                variantGroupError = new Error(`variant is missing for the group id in variantId parameter`);
                                variantGroupError.name = "invalid_variant_id";
                                variantGroupError.variantInfo = { variantName: vProd.variantName };
                                throw variantGroupError;
                            }

                            if (product.groups?.type && ((!groupValueId) || (!product.groups?.values.some(i => i.id === groupValueId)))) {
                                variantGroupError = new Error(`variant is missing for the group id in variantId parameter`);
                                variantGroupError.name = "invalid_group_id";
                                variantGroupError.groupInfo = { variantName: vProd.variantName, groupName: vProd.groupName }
                                throw variantGroupError;
                            }

                            const existingVariant = await ProductModel.findProduct(
                                {
                                    parent_id: product._id, variant_id: vProd.variantId
                                },
                                null,
                                {
                                    session,
                                }
                            )

                            let variantInfo
                            if (!existingVariant) {
                                // Fix: Sometimes not getting a variant_count
                                product.variant_count = product.variant_count || 0

                                const variantObj = {
                                    variant_id: vProd.variantId,
                                    variant_value_id: variantValueId,
                                    tenant_id: req.body.tenantId,
                                    parent_id: product._id,
                                    // variant_name: vProd.variantName,
                                    // group_name: vProd.groupName,
                                    group_value_id: groupValueId,
                                    item_number: vProd.itemNumber,
                                    unique_item_number: `${body.tenantId}_${vProd.itemNumber}`,
                                    type: NEW_PRODUCT_TYPE.VARIANT,
                                    packaging_map: {
                                        uom_id: vProd.uomMapping.uom,
                                        min_qty: vProd.uomMapping.minQty,
                                        qty_ctn: vProd.uomMapping.qtyCtn
                                    },
                                    price_mappings: vProd.priceMapping,
                                    inventory_mappings: vProd.inventoryMapping,
                                    barcodes: vProd.barcodes,
                                    variant_order: product.variant_count + 1,
                                    created_by: userId,
                                    updated_by: userId,
                                    is_active: vProd.isActive,
                                    tax_id: vProd.taxId,
                                    is_deleted: false
                                };

                                vProd.barcodes?.forEach(pdb => {
                                    if (barcodesSet.has(pdb)) {
                                        const error = new Error();
                                        error.name = "barcode_already_exists";
                                        error.keyValue = { barcodes: `${body.tenantId}_${pdb}` };
                                        throw error;
                                    }
                                    else {
                                        barcodesSet.add(pdb)
                                    }
                                });

                                variantInfo = await ProductModel.createProduct(variantObj);
                                barcodeUpdates.push({
                                    variantId: variantInfo._id,
                                    barcodes: vProd.barcodes
                                })
                                product.variant_count++;
                            } else {
                                variantInfo = existingVariant;
                                variantInfo.item_number = vProd.itemNumber;
                                variantInfo.unique_item_number = `${body.tenantId}_${vProd.itemNumber}`;
                                variantInfo.packaging_map = {
                                    uom_id: vProd.uomMapping.uom,
                                    min_qty: vProd.uomMapping.minQty,
                                    qty_ctn: vProd.uomMapping.qtyCtn
                                };
                                variantInfo.price_mappings = vProd.priceMapping;
                                variantInfo.inventory_mappings = vProd.inventoryMapping;
                                variantInfo.updated_by = userId;
                                variantInfo.is_active = vProd.isActive;
                                variantInfo.tax_id = vProd.taxId;

                                const addedBarcodes = []; // barcode string for creating new barcodes
                                const deletedBarcode = []; // It will have _id of existing barcodes

                                const variantBarcodes = [
                                    ...vProd.barcodes,
                                    ...variantInfo.barcodes,
                                ]

                                variantBarcodes.forEach(barcode => {
                                    const barcodeExist = variantInfo.barcodes.some(code => {
                                        return barcode === code
                                    })

                                    const barcodeRemains = vProd.barcodes.some(code => {
                                        return barcode === code
                                    })

                                    if (barcodeRemains) {
                                        if (!barcodeExist) {
                                            if (barcodesSet.has(barcode)) {
                                                const error = new Error();
                                                error.name = "barcode_already_exists";
                                                error.keyValue = { barcodes: `${body.tenantId}_${barcode}` };
                                                throw error;
                                            }

                                            addedBarcodes.push(barcode);
                                        }
                                        
                                        barcodesSet.add(barcode)
                                    }
                                    else {
                                        if (barcodeExist) {
                                            deletedBarcode.push(tenantId + "_" + barcode);
                                        }
                                    }
                                })

                                variantInfo.barcodes = vProd.barcodes;

                                if (deletedBarcode.length) {
                                    barcodeUpdates.push({
                                        isDeleted: true,
                                        variantId: variantInfo._id,
                                        barcodes: deletedBarcode
                                    })
                                }
                                if (addedBarcodes.length) {
                                    barcodeUpdates.push({
                                        variantId: variantInfo._id,
                                        barcodes: addedBarcodes
                                    })
                                }
                            }
                            allVariants.push(variantInfo)
                            variantPromises.push(variantInfo)
                            productVariantIds.push(variantInfo._id)
                        }

                        try {
                            if (barcodesSet.size) {
                                const existingBarcodes = await ProductModel.getAllBarCodes(
                                    {
                                        "tenant_id": tenantId,
                                        "product_variant_id": {
                                            $nin: productVariantIds,
                                        },
                                        "is_active": true,
                                        "barcode": {
                                            "$in": [...barcodesSet]
                                        },
                                    },
                                    "_id",
                                    {
                                        lean: true
                                    }
                                )

                                if (existingBarcodes.length) {
                                    const totalBarCodeIds = existingBarcodes.map(element => element._id)

                                    const error = new Error();
                                    error.code = STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE
                                    error.name = "barcode_already_exists";
                                    error.keyValue = { barcodes: totalBarCodeIds };
                                    throw error;
                                }
                            }

                            await Promise.all([
                                //Update barcodes
                                ...barcodeUpdates.map(
                                    obj => {
                                        if (obj.isDeleted) {
                                            return ProductModel.deleteBarcodes({ product_variant_id: obj.variantId, _id: { $in: obj.barcodes } }, { session })
                                        }
                                        else {
                                            return ProductModel.addUpdateBarcodes(body.tenantId, obj.barcodes, obj.variantId, { session })
                                        }
                                    }
                                ),
                                //Update variant product info
                                ...variantPromises.map(
                                    variantInfo => (
                                        variantInfo.save({ session })
                                    )
                                )
                            ])
                        }
                        catch (e) {
                            const error = new Error(e);

                            if (e?.code === 11000) {
                                error.stack = e.stack
                                error.keyValue = e.keyValue;

                                if ((e?.keyValue?.unique_item_number)) {
                                    error.name = "item_number_already_exists";
                                }
                                else if ((e?.keyValue?.variant_id)) {
                                    error.name = "variant_already_exists";
                                }
                                else if (e?.keyValue?.barcodes) {
                                    error.name = "barcode_already_exists";
                                }
                            }
                            else if (e.codeName === "NoSuchTransaction") {
                                error.codeName = "NoSuchTransaction";
                            }
                            throw error;
                        }
                        break;
                    }
            }

            // ----------- UPDATING TAG COUNT START ----------- //
            productDetails.convertedTags = [];
            for (let i = 0; i < productDetails.tags.length; i++) {
                const tag = productDetails.tags[i];
                if (tag?.hasOwnProperty("name")) {
                    productDetails.convertedTags.push(tag.name);
                } else if (typeof tag === "string") {
                    productDetails.convertedTags.push(tag);
                }
            }

            const deletedTags = product.tags.filter(t => (productDetails.convertedTags.findIndex(pt =>
                pt === t.name) === -1)).map(t => t._id.toString()); //

            const addedTags = productDetails.convertedTags.filter(t => (product.tags.findIndex(pt =>
                pt.name === t) === -1)); // Array[String]

            if (Array.isArray(addedTags) && addedTags.length) {
                for (let i = 0; i < addedTags.length; i++) {
                    let tag = addedTags[i];
                    tag = await ProductModel.addOrUpdateTag(req.body.tenantId, tag, { session });
                    product.tags.push(tag._id);
                }
            }

            if (Array.isArray(deletedTags)) {
                let tag = await ProductModel.decreaseTagCount(req.body.tenantId, deletedTags, { session });
                product.tags = product.tags.filter(pt => {
                    return !deletedTags.includes(pt._id.toString());
                });
            }
            // ----------- UPDATING TAG COUNT END ----------- //



            try {
                await product.save({ session: session });
            } catch (e) {
                const error = new Error(e);
                if (e?.code === 11000) {
                    error.stack = e.stack
                    error.keyValue = e.keyValue;

                    if (e?.keyValue?.unique_item_number) {
                        error.name = "item_number_already_exists"
                    }
                    else if (e?.keyValue?.barcodes) {
                        error.name = "barcode_already_exists";
                    }
                }
                throw error;
            }

            if (product.type === NEW_PRODUCT_TYPE.PARENT) {
                const areSomeActive = allVariants.some(variant => variant.is_active === true)
                const areAllInActive = allVariants.every(variant => variant.is_active === false)

                if (areSomeActive) {
                    product.is_active = true
                }
                else if (areAllInActive) {
                    product.is_active = false
                }

                await ProductModel.generateMappingForParent(product, allVariants);
                await product.save();
            }
            await session.commitTransaction();
            await session.endSession();

            return res.handler.success("changes_saved");
        }
        catch (error) {
            await session.abortTransaction();
            await session.endSession();
            switch (error.name) {
                case "item_number_already_exists":
                    return res.handler.badRequest("item_number_already_exists", error.keyValue);

                case "barcode_already_exists":
                    return res.handler.badRequest("barcode_already_exists", error.keyValue);

                case "invalid_product_id":
                    return res.handler.badRequest(error.name, error.variantInfo);

                case "invalid_group_id":
                    return res.handler.badRequest(error.name, error.groupInfo);

                case "invalid_variant_id":
                    return res.handler.badRequest(error.name, error.variantInfo)

                case "variant_already_exists":
                    return res.handler.badRequest("variant_already_exists", error.keyValue);
                default:
                    if (error.codeName === "WriteConflict") {
                        return res.handler.conflict("write_conflict_occurred");
                    }
                    return res.handler.serverError(error)
            }
        }
    }

    async listProducts(req, res) {
        try {
            const {
                tenantId,
                searchKey,
                type,

                sortBy,
                sortType,

                page,
                perPage,
                withVariants
            } = req.query;

            // ------- SEARCH INDEX STAGE ------- //
            const searchFilters = [
                {
                    "range": {
                        "path": "tenant_id",
                        "gte": tenantId,
                        "lte": tenantId,
                    }
                },
                {
                    "equals": {
                        "path": "is_deleted",
                        "value": false,
                    }
                },
                {
                    "queryString": {
                        "defaultPath": "type",
                        "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                    }
                }
            ]

            if (type) {
                searchFilters.push(
                    {
                        "equals": {
                            "path": "is_active",
                            "value": ACTIVE_STATUS_ENUM.ACTIVE === type,
                        }
                    },
                )
            }

            const searchStage = {
                "$search": {
                    "index": "product_v2_search",
                    "returnStoredSource": false,
                    "compound": {
                        "filter": searchFilters,
                    },
                },
            }

            if (searchKey) {
                const shouldMatches = [
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "title"
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "secondary_language_title",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "item_number",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "barcodes",
                        },
                    },
                ]

                const textQueryPathList = [
                    "title",
                    "secondary_language_title",
                    "item_number",
                    "barcodes",
                ]

                if (type) {
                    let fieldPath = "active_variant_item_numbers"
                    let barcodeFieldPath = "active_variant_barcodes"

                    if (type === ACTIVE_STATUS_ENUM.INACTIVE) {
                        fieldPath = "inactive_variant_item_numbers"
                        barcodeFieldPath = "inactive_variant_barcodes"
                    }

                    shouldMatches.push(
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": fieldPath,
                            },
                        },
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": barcodeFieldPath,
                            },
                        },
                    )
                    textQueryPathList.push(
                        fieldPath,
                        barcodeFieldPath
                    )
                }
                else {
                    shouldMatches.push(
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": "active_variant_item_numbers",
                            },
                        },
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": "inactive_variant_item_numbers",
                            },
                        },
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": "active_variant_barcodes",
                            },
                        },
                        {
                            "autocomplete": {
                                "query": searchKey,
                                "path": "inactive_variant_barcodes",
                            },
                        },
                    )

                    textQueryPathList.push(
                        "active_variant_item_numbers",
                        "inactive_variant_item_numbers",
                        "active_variant_barcodes",
                        "inactive_variant_barcodes",
                    )
                }

                shouldMatches.push({
                    "text": {
                        "query": searchKey,
                        "path": textQueryPathList,
                    }
                })

                searchStage["$search"]["compound"]["should"] = shouldMatches;
                searchStage["$search"]["compound"]["minimumShouldMatch"] = 1
            }
            // ------- SEARCH INDEX STAGE ------- //



            const pipeline = [searchStage];
            const offset = (page - 1) * perPage;
            if (sortBy && sortType) {

                let sort_by = {
                    $sort: {}
                }

                pipeline.push(
                    {
                        '$addFields': {
                            sort_title: { $toLower: '$title' },
                            sort_item_number: { $toLower: '$item_number' },
                        }
                    }
                )
                let sortOption;
                if (sortBy === PRODUCT_SORT_BY.TITLE) {
                    sortOption = 'sort_title'
                } else if (sortBy === PRODUCT_SORT_BY.ITEM_NUMBER) {
                    sortOption = 'sort_item_number'
                }

                sort_by["$sort"][sortOption] = sortType === PRODUCT_SORT_TYPE.ASC ? 1 : -1

                pipeline.push(sort_by,
                    {
                        '$project': {
                            sort_title: 0,
                            sort_item_number: 0
                        }
                    }
                );
            } else {
                const sort = {
                    $sort: {
                        _id: -1
                    }
                };
                pipeline.push(sort)
            }

            pipeline.push(
                {
                    $facet: {
                        product: [{ $skip: offset }, { $limit: perPage }],
                        count: [{ $count: "count" }]
                    }
                },
            );
            if (withVariants) {
                pipeline.push(
                    {
                        $unwind: "$product"
                    },
                    {
                        $lookup: {
                            from: "master_brands",
                            localField: "product.brand_id",
                            foreignField: "_id",
                            as: "product.brand_id",
                            pipeline: [
                                {
                                    $project: {
                                        brand_name: 1,
                                    }
                                },
                            ]
                        }
                    },
                    {
                        $lookup: {
                            from: "images_2.0",
                            as: "product.cover_image",
                            foreignField: "product_variant_id",
                            localField: "product._id",
                            pipeline: [
                                {
                                    $match: {
                                        group_id: null
                                    }
                                },
                                {
                                    $project: {
                                        _id: 1,
                                        image_name: 1,
                                        s3_url: 1,
                                        image_number: 1,
                                        updated_at: 1
                                    }
                                },
                                {
                                    $sort: { "image_number": 1 }
                                },
                                {
                                    $limit: 1
                                }
                            ]
                        }
                    },
                    {
                        $lookup: {
                            from: "categories",
                            localField: "product.family_id",
                            foreignField: "_id",
                            as: "product.family_id",
                            pipeline: [
                                {
                                    $project: {
                                        category_name: 1,
                                    }
                                },
                            ]
                        }
                    },
                    {
                        $lookup: {
                            from: "categories",
                            localField: "product.category_id",
                            foreignField: "_id",
                            as: "product.category_id",
                            pipeline: [
                                {
                                    $project: {
                                        category_name: 1,
                                    }
                                },
                            ]
                        }
                    },
                    {
                        $lookup: {
                            from: "categories",
                            localField: "product.subcategory_id",
                            foreignField: "_id",
                            as: "product.subcategory_id",
                            pipeline: [
                                {
                                    $project: {
                                        category_name: 1,
                                    }
                                },
                            ]
                        }
                    },
                    {
                        $lookup: {
                            from: "products_2.0",
                            as: "product.product_variants",
                            foreignField: "parent_id",
                            localField: "product._id",
                            pipeline: [
                                {
                                    $match: {
                                        is_deleted: false,
                                    }
                                },
                                {
                                    $lookup: {
                                        from: "images_2.0",
                                        as: "cover_image",
                                        localField: "_id",
                                        foreignField: "product_variant_id",
                                        pipeline: [
                                            {
                                                $project: {
                                                    _id: 1,
                                                    image_name: 1,
                                                    s3_url: 1,
                                                    image_number: 1,
                                                    updated_at: 1
                                                }
                                            },
                                            {
                                                $sort: { "image_number": 1 }
                                            },
                                            {
                                                $limit: 1
                                            }
                                        ]
                                    }
                                },
                                {
                                    $lookup: {
                                        from: "images_2.0",
                                        foreignField: "group_id",
                                        localField: "group_value_id",
                                        as: "group_cover_image",
                                        let: { group_id: "$group_value_id" },
                                        pipeline: [
                                            {
                                                $match: {
                                                    group_id: { $ne: null },
                                                    $expr: {
                                                        $eq: ["$group_id", "$$group_id"]
                                                    }
                                                }
                                            },
                                            {
                                                $project: {
                                                    _id: 1,
                                                    image_name: 1,
                                                    s3_url: 1,
                                                    image_number: 1,
                                                    updated_at: 1
                                                }
                                            },
                                            {
                                                $sort: { "image_number": 1 }
                                            },
                                            {
                                                $limit: 1
                                            }
                                        ]
                                    }
                                },
                                {
                                    $lookup: {
                                        from: "variant_types_2.0",
                                        localField: "variant_value_id",
                                        foreignField: "_id",
                                        as: "variant_value_id",
                                        pipeline: [
                                            {
                                                $project: {
                                                    name: 1
                                                }
                                            }
                                        ]
                                    }
                                },
                                {
                                    $lookup: {
                                        from: "variant_types_2.0",
                                        localField: "group_value_id",
                                        foreignField: "_id",
                                        as: "group_value_id",
                                        pipeline: [
                                            {
                                                $project: {
                                                    name: 1
                                                }
                                            }
                                        ]
                                    }
                                },
                                {
                                    $addFields: {
                                        "cover_image": { $first: "$cover_image" },
                                        group_cover_image: { $first: "$group_cover_image" },
                                        "group_value_id": { $first: "$group_value_id" },
                                        "variant_value_id": { $first: "$variant_value_id" }
                                    }
                                },
                                {
                                    $sort: {
                                        variant_order: 1
                                    }
                                }
                            ]
                        }
                    },
                    {
                        $addFields: {
                            "product.family_id": { $first: "$product.family_id" },
                            "product.category_id": { $first: "$product.category_id" },
                            "product.subcategory_id": { $first: "$product.subcategory_id" },
                            "product.brand_id": { $first: "$product.brand_id" },
                            "product.cover_image": { $first: "$product.cover_image" },
                            "count": { $first: "$count" }
                        }
                    }
                )
            } else if (!withVariants) {
                pipeline.push(
                    {
                        $unwind: "$count"
                    }
                );
            }
            const data = await ProductModel.productAggregation(pipeline)

            const count = data[0]?.count?.count || 0;
            let list = []

            if (!withVariants) {
                list = data[0]?.product || [];
            }
            else {
                list = data.map(d => d.product);
            }

            const productList = {
                count,
                list
            }
            return res.handler.success(null, productList);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async searchProducts(req, res) {
        try {
            const list = await ProductModel.searchProducts(req)
            return res.handler.success(null, list)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteProducts(req, res) {
        let session
        try {
            session = await mongoose.startSession();
            session.startTransaction();
            const { productVariantIds, tenantId } = req.query;
            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);
            //....
            const productVariantDocs = await ProductModel.findProducts(
                { _id: { $in: productVariantIds }, tenant_id: tenantId, is_deleted: false },
                null, // projection
                {
                    populate: [
                        {
                            path: "variantProducts",
                            match: { is_deleted: false },
                            populate: [
                                {
                                    path: "images",
                                    select: "_id"
                                }
                            ]
                        },
                        {
                            path: "images",
                            select: { created_by: 0, updated_at: 0, created_at: 0, updated_at: 0, s3_url: 0, group_id: 0 }
                        },
                        {
                            path: "tags",
                            select: "_id name attached_count"
                        }
                    ],
                    session
                }
            );
            const parentIdSet = new Set();
            const deletedParents = new Set();
            const deletedVariants = new Set();

            const imagePromises = [];
            const imgDocs = [];

            const variantTypeIds = [];

            const productDocPromises = [];
            const deletedProductVariantIds = []; // for deleting it from drafts and carts
            for (let i = 0; i < productVariantDocs.length; i++) {
                const prodDoc = productVariantDocs[i];

                switch (prodDoc.type) {
                    case NEW_PRODUCT_TYPE.VARIANT:
                        {
                            if (deletedVariants.has(prodDoc.id)) {
                                // already deleted as its parent might have deleted
                                continue;
                            }
                            if (!deletedParents.has(prodDoc.parent_id.toString())) {
                                parentIdSet.add(prodDoc.parent_id.toString());
                            }
                            deletedVariants.add(prodDoc.id);

                            if (prodDoc.images?.length) {
                                prodDoc.images.forEach(i => imgDocs.push(i));
                            }

                            deletedProductVariantIds.push(prodDoc._id);

                            productDocPromises.push(
                                ProductModel.updateProduct(
                                    { _id: prodDoc._id },
                                    {
                                        unique_item_number: prodDoc._id.toString(),
                                        is_deleted: true,
                                        is_active: false,
                                        $unset: { variant_id: "" },
                                    },
                                    { session }
                                )
                            );
                            break;
                        }

                    case NEW_PRODUCT_TYPE.PARENT:
                        {
                            deletedParents.add(prodDoc.id);
                            if (parentIdSet.has(prodDoc.id)) parentIdSet.delete(prodDoc.id);
                            if (prodDoc.tags?.length) {
                                await ProductModel.decreaseTagCount(tenantId, prodDoc.tags.map(t => t._id), { session });
                            }

                            if (prodDoc.images?.length) {
                                prodDoc.images.forEach(i => imgDocs.push(i))
                            }

                            for (let v = 0; v < prodDoc.variantProducts?.length; v++) {
                                const vProd = prodDoc.variantProducts[v];
                                if (deletedVariants.has(vProd.id)) {
                                    continue; // deleted earlier from case of VARIANT type
                                }

                                deletedVariants.add(vProd.id);

                                if (vProd.images?.length) {
                                    vProd.images.forEach(img => imgDocs.push(img));
                                }

                                deletedProductVariantIds.push(vProd._id);

                                productDocPromises.push(
                                    ProductModel.updateProduct(
                                        { _id: vProd._id },
                                        {
                                            unique_item_number: vProd._id.toString(),
                                            is_deleted: true,
                                            is_active: false,
                                            $unset: { variant_id: "" },
                                        },
                                        { session }
                                    )
                                );
                            }

                            if (prodDoc.variants?.type && prodDoc.variants?.values?.length) {
                                prodDoc.variants?.values?.forEach(vt => variantTypeIds.push(vt));
                            }

                            if (prodDoc.groups?.type && prodDoc.groups?.values?.length) {
                                prodDoc.groups?.values?.forEach(vt => variantTypeIds.push(vt));
                            }

                            deletedProductVariantIds.push(prodDoc._id); // incase, PARENT became part of a draft or cart

                            productDocPromises.push(
                                ProductModel.updateProduct(
                                    { _id: prodDoc._id },
                                    {
                                        unique_item_number: prodDoc._id.toString(),
                                        is_deleted: true,
                                        is_active: false,
                                    },
                                    { session }
                                )
                            );
                            break;
                        }

                    case NEW_PRODUCT_TYPE.SINGLE:
                        {
                            await ProductModel.decreaseTagCount(tenantId, prodDoc.tags.map(t => t._id), { session });

                            if (prodDoc.images?.length) {
                                prodDoc.images.forEach(i => imgDocs.push(i));
                            }

                            deletedProductVariantIds.push(prodDoc._id);
                            productDocPromises.push(ProductModel.updateProduct({ _id: prodDoc._id }, { unique_item_number: prodDoc._id.toString(), is_deleted: true }, { session }));
                            break;
                        }

                    default:
                        break;
                }
            }

            await ImageModel.deleteImages({ _id: { $in: imgDocs?.map(i => i._id) } }, { session })
            await Promise.all(productDocPromises); // need to reflect all the updates to the DB first then process PARENT type docs for price_mappings and inventory_mappings

            await Promise.all([
                ProductModel.deleteVariantTypes({ _id: { $in: variantTypeIds } }, { session }),
                ProductModel.deleteBarcodes({ product_variant_id: { $in: deletedProductVariantIds } }, { session }),
                ProductModel.deleteFavoriteProducts({ product_variant_id: { $in: deletedProductVariantIds, }, tenant_id: tenantId }, { session }),
                Promise.all(imagePromises),
                OrderModel.deleteCartItems({ product_variant_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
                OrderModel.deleteDraftItems({ product_variant_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
                OrderModel.deleteDealProductItems({ product_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
            ]);
            await session.commitTransaction();
            await ProductModel.deleteS3Images(tenantId, imgDocs, imagePromises, publicS3);
            const parentProductIds = Array.from(parentIdSet);

            const parentProducts = await ProductModel.findProducts(
                {
                    _id: { $in: parentProductIds },
                    type: NEW_PRODUCT_TYPE.PARENT,
                    is_deleted: false,
                },
                {
                    _id: 1,
                    type: 1,
                },
                {
                    populate: [
                        {
                            path: "variantProducts",
                            select: {
                                "price_mappings": 1,
                                "inventory_mappings": 1,
                                "_id": 1,
                                "item_number": 1,
                                "is_deleted": 1,
                                "is_active": 1,
                                "is_restocked": 1,
                                "barcodes": 1
                            },
                            match: {
                                "is_deleted": false,
                            }
                        }
                    ]
                }
            );

            try {
                const updateParentProducts = []

                for (let i = 0; i < parentProducts.length; i++) {
                    const parentDoc = parentProducts[i];

                    await ProductModel.generateMappingForParent(parentDoc, parentDoc.variantProducts)
                    updateParentProducts.push(parentDoc)
                }

                await Promise.allSettled(
                    updateParentProducts.map(
                        parentProduct => parentProduct.save()
                    )
                )
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error in deleteProducts for generateMappingForParent"
                })
            }

            await session.endSession({ forceClear: true });
            return res.handler.success("deleted_products_successfully");
        }
        catch (error) {
            await session.abortTransaction();
            await session.endSession({ forceClear: true });
            switch (error.name) {
                default:
                    if (error.codeName === "WriteConflict") {
                        return res.handler.conflict("write_conflict_occurred");
                    }

                    if (error.codeName === "NoSuchTransaction") {
                        return res.handler.custom(STATUS_CODES.UNPROCESSABLE_ENTITY, "transaction_error_occurred", error)
                    }
                    return res.handler.serverError(error)
            }
        }
    }

    async updateProductsStatus(req, res) {
        try {
            const {
                tenantId,
                type,
                singleProductIds = [],
                variantProductIds = []
            } = req.body;

            let is_active;

            if (type === ENTITY_STATUS.ACTIVE) {
                is_active = true;
            }
            else if (type === ENTITY_STATUS.INACTIVE) {
                is_active = false;
            }

            const updateBulkStatus = async (ids) => {
                return ProductModel.updateProducts(
                    {
                        _id: { $in: ids },
                        tenant_id: tenantId,
                    },
                    {
                        is_active
                    }
                )
            }
            const unProcessedSingleIds = []
            const unProcessedVariantIds = []

            if (singleProductIds.length) {
                const res = await updateBulkStatus(singleProductIds)

                if (!res.modifiedCount) {
                    unProcessedSingleIds.push(...singleProductIds)
                }
            }

            if (variantProductIds.length) {

                for (let index = 0; index < variantProductIds.length; index++) {
                    const vProduct = variantProductIds[index]

                    const res = await updateBulkStatus(vProduct.updateIds)

                    if (!res.modifiedCount) {
                        unProcessedVariantIds.push(...vProduct.updateIds)
                        continue;
                    }

                    /** Update parent product */
                    const parentProduct = await ProductModel.findProduct(
                        {
                            _id: vProduct.parentId
                        },
                        "_id type"
                    );

                    if (!parentProduct) {
                        logger.info(`In updateProductsStatus: Parent product ${vProduct.parentId} not found!`)
                        unProcessedVariantIds.push(...vProduct.updateIds)
                        continue;
                    }

                    const allVariants = await ProductModel.findProducts(
                        {
                            "parent_id": vProduct.parentId,
                            "is_deleted": false,
                        },
                        "is_active price_mappings inventory_mappings item_number is_restocked barcodes",
                    );

                    if (!allVariants?.length) {
                        logger.info(`In updateProductsStatus: Parent product's ${vProduct.parentId} variants not found`)
                        unProcessedVariantIds.push(...vProduct.updateIds)
                        continue;
                    }

                    const areSomeActive = allVariants.some(variant => variant.is_active === true)
                    const areAllInActive = allVariants.every(variant => variant.is_active === false)

                    if (areSomeActive) {
                        parentProduct.is_active = true
                    }
                    else if (areAllInActive) {
                        parentProduct.is_active = false
                    }

                    await ProductModel.generateMappingForParent(parentProduct, allVariants);
                    await parentProduct.save()
                }
            }

            if (!singleProductIds.length && !variantProductIds.length) {
                return res.handler.badRequest("no_products_ids_provided")
            }
            return res.handler.success("changes_saved", {
                unProcessedSingleIds,
                unProcessedVariantIds,
            });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async checkExistingItemNumber(req, res) {
        try {
            const { itemNumber, tenantId } = req.query;

            const productDetails = await ProductModel.findProduct({ unique_item_number: `${tenantId}_${itemNumber}`, is_deleted: false }, { _id: 1, item_number: 1, unique_item_number: 1, parent_id: 1, type: 1, variant_id: 1 });

            return res.handler.success(null, productDetails);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async inventoryProductList(req, res) {
        try {
            const {
                priceId,
                branchId,
                page,
                perPage,
                searchKey = "",
                tenantId,
                sortBy,
                sortType,
                status,
                quantityRange,
            } = req.query;

            const searchFilters = [
                {
                    "range": {
                        "path": "tenant_id",
                        "gte": tenantId,
                        "lte": tenantId,
                    }
                },
                {
                    "equals": {
                        "path": "is_deleted",
                        "value": false,
                    }
                },
                {
                    "queryString": {
                        "defaultPath": "type",
                        "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                    }
                }
            ];

            if (status) {
                searchFilters.push({
                    "equals": {
                        "path": "is_active",
                        "value": status === ACTIVE_STATUS_ENUM.ACTIVE,
                    }
                })
            }

            if (priceId) {
                searchFilters.push({
                    "embeddedDocument": {
                        "path": "price_mappings",
                        "operator": {
                            "compound": {
                                "filter": [
                                    {
                                        "equals": {
                                            "path": "price_mappings.master_price_id",
                                            "value": new mongoose.Types.ObjectId(priceId),
                                        }
                                    },
                                ]
                            }
                        }
                    }
                })
            }

            const {
                from,
                to
            } = quantityRange || {};

            const inventorySearchFilter = []

            if (inventorySearchFilter.length) {
                searchFilters.push({
                    "embeddedDocument": {
                        "path": "inventory_mappings",
                        "operator": {
                            "compound": {
                                "filter": inventorySearchFilter,
                            }
                        }
                    }
                })
            }

            const searchStage = {
                "$search": {
                    "index": "product_v2_search",
                    "returnStoredSource": false,
                    "compound": {
                        "filter": searchFilters,
                    },
                },
            }

            if (searchKey) {
                const shouldMatches = [
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "title",
                            "fuzzy": {
                                "maxEdits": 1
                            },
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "secondary_language_title",
                            "fuzzy": {
                                "maxEdits": 1
                            },
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "item_number",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "barcodes",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "active_variant_barcodes",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "inactive_variant_barcodes",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "active_variant_item_numbers",
                        },
                    },
                    {
                        "autocomplete": {
                            "query": searchKey,
                            "path": "inactive_variant_item_numbers",
                        },
                    },
                    {
                        "text": {
                            "query": searchKey,
                            "path": ["title", "secondary_language_title"],
                            "fuzzy": {
                                "maxEdits": 1
                            },
                        }
                    },
                    {
                        "text": {
                            "query": searchKey,
                            "path": [
                                "item_number",
                                "barcodes",
                                "active_variant_barcodes",
                                "inactive_variant_barcodes",
                                "active_variant_item_numbers",
                                "inactive_variant_item_numbers",
                            ],
                        }
                    },
                ]

                searchStage["$search"]["compound"]["should"] = shouldMatches
                searchStage["$search"]["compound"]["minimumShouldMatch"] = 1
            }

            const pipeline = [searchStage];

            pipeline.push(
                {
                    $sort: {
                        _id: -1
                    }
                },
                {
                    $project: {
                        type: 1,
                        price_mappings: 1,
                        inventory_mappings: 1,
                        secondary_language_title: 1,
                        title: 1,
                        item_number: 1,
                        active_variant_item_numbers: 1,
                        inactive_variant_item_numbers: 1,
                        is_active: 1
                    }
                },
                {
                    $lookup: {
                        from: "products_2.0",
                        localField: "_id",
                        foreignField: "parent_id",
                        as: "product_variants",
                        pipeline: [
                            {
                                $match: {
                                    is_deleted: false,
                                }
                            },
                            {
                                $lookup: {
                                    from: "images_2.0",
                                    localField: "_id",
                                    foreignField: "product_variant_id",
                                    as: "cover_image",
                                    pipeline: [
                                        {
                                            $project: {
                                                _id: 1,
                                                image_name: 1,
                                                s3_url: 1,
                                                image_number: 1,
                                            }
                                        },
                                        {
                                            $sort: { "image_number": 1 }
                                        },
                                        {
                                            $limit: 1
                                        }
                                    ]
                                }
                            },
                            {
                                $lookup: {
                                    from: "images_2.0",
                                    localField: "parent_id",
                                    foreignField: "product_variant_id",
                                    as: "group_cover_image",
                                    let: { group_id: "$group_value_id" },
                                    pipeline: [
                                        {
                                            $match: {
                                                group_id: { $ne: null },
                                                $expr: { $eq: ["$group_id", "$$group_id"] }
                                            }
                                        },
                                        {
                                            $project: {
                                                _id: 1,
                                                image_name: 1,
                                                s3_url: 1,
                                                image_number: 1,
                                            }
                                        },
                                        {
                                            $sort: { "image_number": 1 }
                                        },
                                        {
                                            $limit: 1
                                        }
                                    ]
                                }
                            },
                            {
                                $lookup: {
                                    from: "variant_types_2.0",
                                    localField: "variant_value_id",
                                    foreignField: "_id",
                                    as: "variant_value_info",
                                    pipeline: [
                                        {
                                            $project: {
                                                name: 1
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                $lookup: {
                                    from: "variant_types_2.0",
                                    localField: "group_value_id",
                                    foreignField: "_id",
                                    as: "group_value_info",
                                    pipeline: [
                                        {
                                            $project: {
                                                name: 1
                                            }
                                        }
                                    ]
                                }
                            },
                            {
                                $addFields: {
                                    "cover_image": { $first: "$cover_image" },
                                    group_cover_image: { $first: "$group_cover_image" },
                                    variant_value_info: { $first: "$variant_value_info" },
                                    group_value_info: { $first: "$group_value_info" }
                                }
                            },
                            {
                                $project: {
                                    cover_image: 1,
                                    group_cover_image: 1,
                                    variant_value_info: 1,
                                    group_value_info: 1,
                                    type: 1,
                                    item_number: 1,
                                    price_mappings: 1,
                                    inventory_mappings: 1,
                                    variant_value_id: 1,
                                    group_value_id: 1,
                                    is_active: 1
                                }
                            }
                        ]
                    }
                },
                {
                    $unwind: {
                        path: '$product_variants',
                        preserveNullAndEmptyArrays: true
                    }
                },
            );

            let PARENT_PRODUCT = { type: NEW_PRODUCT_TYPE.PARENT };
            let SINGLE_PRODUCT = { type: NEW_PRODUCT_TYPE.SINGLE };

            const ELEMENT = {
                $elemMatch: {
                    ...(from && to ?
                        {
                            quantity: {
                                $gte: +from,
                                $lte: +to
                            }
                        } :
                        {}),
                    ...(branchId ?
                        {
                            branch_id: branchId
                        } :
                        {}),
                },
            }

            const elementLength = Object.keys(ELEMENT.$elemMatch)?.length > 0;

            PARENT_PRODUCT = {
                ...(elementLength ?
                    {
                        ...PARENT_PRODUCT,
                        $and: [
                            {
                                "product_variants.inventory_mappings": { ...ELEMENT },
                            },
                        ],
                    } :
                    {})
            }

            SINGLE_PRODUCT = {
                ...(elementLength ?
                    {
                        ...SINGLE_PRODUCT,
                        $and: [
                            {
                                "inventory_mappings": { ...ELEMENT },
                            },
                        ],
                    } :
                    {})
            }

            if (Object.keys(PARENT_PRODUCT)?.length > 1 || Object.keys(SINGLE_PRODUCT)?.length > 1) {
                pipeline.push({
                    $match: {
                        $or: [
                            PARENT_PRODUCT,
                            SINGLE_PRODUCT
                        ]
                    }
                })
            }

            switch (sortType) {
                case INVENTORY_PRODUCT_LISTING_SORT_TYPES.INVENTORY:
                    {
                        if (branchId) {
                            pipeline.push(
                                {
                                    $addFields: {
                                        inventory: {
                                            $cond: {
                                                if: { $eq: ["$type", NEW_PRODUCT_TYPE.PARENT] },
                                                then: "$product_variants.inventory_mappings",
                                                else: "$inventory_mappings"
                                            }
                                        }
                                    }
                                },
                                {
                                    $addFields: {
                                        inventory: {
                                            $filter: {
                                                input: '$inventory',
                                                as: 'inv',
                                                cond: { $eq: ["$$inv.branch_id", branchId] }
                                            }
                                        }
                                    }
                                },
                                {
                                    $sort: {
                                        'inventory.quantity': sortBy
                                    }
                                }
                            )
                        }
                        break;
                    }
            }

            pipeline.push(
                {
                    $facet: {
                        product: [
                            { $skip: (page - 1) * perPage },
                            { $limit: perPage }
                        ],
                        count: [{ $count: "count" }]
                    }
                },
                {
                    $unwind: {
                        path: '$product',
                    }
                },
                {
                    $lookup: {
                        from: 'images_2.0',
                        localField: 'product._id',
                        foreignField: 'product_variant_id',
                        as: 'product.cover_image',
                        pipeline: [
                            {
                                $match: { group_id: null }
                            },
                            {
                                $project: {
                                    "image_number": 1,
                                    'image_name': 1,
                                    's3_url': 1
                                }
                            }, {
                                $sort: {
                                    'image_number': 1
                                }
                            }, {
                                $limit: 1
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        'product.cover_image': {
                            $first: '$product.cover_image'
                        },
                        count: {
                            $first: '$count.count'
                        }
                    }
                },
            );

            const data = await ProductModel.productAggregation(pipeline);
            const result = { list: [], count: 0 };

            result.list = data[0] ? data.map(d => d.product) : [];
            result.count = data[0]?.count ? data[0].count : 0;
            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async rewardProductList(req, res) {
        try {
            const rewardProducts = await InternalServiceModel.getRewardProgramProducts({
                filter: {
                    tenant_id: req.query.tenantId,
                    is_deleted: false,
                    product_variant_id: { $exists: true }
                },
                projection: "-_id product_variant_id",
                options: {
                    lean: true
                }
            })
            const products = await ProductModel.rewardProductList(req.query, rewardProducts)

            return res.handler.success(null, products)
        } catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateVariantGroupInfo(req, res) {
        let session;
        try {
            session = await mongoose.startSession();
            session.startTransaction();
            const { tenantId, productId, variants, groupType, groups } = req.body;
            const deletedProductVariantIds = []; // for deleting it from drafts and carts
            const populate = [
                { path: "variantProducts", populate: [{ path: "images" }] },
                { path: "variants.values" },
                { path: "groups.values" }
            ]
            const product = await ProductModel.findProduct({ _id: productId, tenant_id: tenantId }, null, { session, populate });

            if (!product) {
                await session.endSession({ forceClear: true })
                return res.handler.notFound("product_not_found")
            }

            if (
                [
                    NEW_PRODUCT_TYPE.SINGLE,
                    NEW_PRODUCT_TYPE.VARIANT,
                ].includes(product.type)
            ) {
                await session.endSession({ forceClear: true })
                return res.handler.badRequest("invalid_product_type")
            }

            let hasAddedGroup = false;
            const deletedImages = [];
            const deletedVariantProducts = [];

            const deleteVariantPromises = [];
            if (!product.groups?.type && groupType) hasAddedGroup = true;

            // generate addedVariants, deletedVariants, (with barcodes and images(variant))
            const remainingVariantValues = []
            const deletedVariantTypes = []
            const addedVariantTypes = []
            let variantsValueCounter = 0

            product.variants.values.forEach(v => {
                if (variants.includes(v.name)) {
                    remainingVariantValues.push(v.name)

                    if (v.order > variantsValueCounter) {
                        variantsValueCounter = v.order
                    }
                }
                else {
                    deletedVariantTypes.push(v._id)
                }
            })

            variants.forEach(v => {
                const existingValue = remainingVariantValues.find(rv => rv === v)

                if (!existingValue) {
                    addedVariantTypes.push({
                        name: v,
                        order: ++variantsValueCounter,
                    })
                }
            })

            await ProductModel.deleteVariantTypes({ _id: { $in: deletedVariantTypes } }, { session });

            if (hasAddedGroup) {
                // delete all the variant products
                product.variantProducts.forEach(vProd => {
                    deletedProductVariantIds.push(vProd._id);

                    deleteVariantPromises.push(
                        ProductModel.updateProduct(
                            { _id: vProd._id },
                            {
                                unique_item_number: vProd._id.toString(),
                                is_deleted: true,
                                is_active: false,
                                $unset: { variant_id: "" },
                            },
                            { session }
                        )
                    )
                    // gather images to delete them form s3 and DB.
                    vProd.images?.forEach(i => deletedImages.push(i));
                });

                // create newGroups (with barcodes and images(variant)) variant_types collection
                const [variantTypes, groupTypes] = await ProductModel.addVariantsAndGroups(
                    tenantId,
                    addedVariantTypes,
                    groups,
                    product._id,
                    {
                        session,
                        ordered: true,
                    }
                );

                product.depopulate("variants.values");
                product.depopulate("groups.values");

                // update the PARENT product with variants and groups.
                product.variants.values = product.variants.values.filter(v => !deletedVariantTypes.includes(v));
                product.groups = { type: "", values: [] };
                product.groups.type = groupType;
                variantTypes.forEach(vt => {
                    product.variants.values.push(vt._id);
                });
                groupTypes.forEach(vt => {
                    product.groups.values.push(vt._id);
                });
            }
            else {
                // create addedGroups and deletedGroups (with barcodes and images(variant))
                let deletedGroupTypes = [];
                let addedGroupTypes = [];
                if (product.groups?.type && !groupType) { // all the existing groupTypes are deleted

                    product.variantProducts.forEach(vProd => deletedVariantProducts.push(vProd));

                    const [variantTypes, _] = await ProductModel.addVariantsAndGroups(
                        tenantId,
                        addedVariantTypes,
                        [],
                        product._id,
                        {
                            session,
                            ordered: true,
                        }
                    );

                    product.depopulate("variants.values");
                    product.depopulate("groups.values");
                    product.groups.values.forEach(v => deletedGroupTypes.push(v));
                    product.variants.values = product.variants.values.filter(v => !deletedVariantTypes.includes(v));
                    variantTypes.forEach(vt => {
                        product.variants.values.push(vt._id);
                    });
                    product.groups = undefined;

                    await ProductModel.deleteVariantTypes({ _id: { $in: deletedGroupTypes } }, { session });
                }
                else if (product.groups?.type && groupType) { // check for added and deleted groupTypes
                    const remainingGroupValues = []
                    let groupsValueCounter = 0

                    product.groups.values.forEach(v => {
                        if (groups.includes(v.name)) {
                            remainingGroupValues.push(v.name)

                            if (v.order > groupsValueCounter) {
                                groupsValueCounter = v.order
                            }
                        }
                        else {
                            deletedGroupTypes.push(v._id)
                        }
                    })

                    groups.forEach(v => {
                        const existingValue = remainingGroupValues.find(rv => rv === v)

                        if (!existingValue) {
                            addedGroupTypes.push({
                                name: v,
                                order: ++groupsValueCounter,
                            })
                        }
                    })

                    product.variantProducts.forEach(vProd => {
                        let delVProd = deletedVariantTypes.find(v => v === vProd.variant_value_id.toString());
                        if (delVProd) {
                            deletedVariantProducts.push(vProd);
                        } else {
                            delVProd = deletedGroupTypes.find(v => v === vProd.group_value_id.toString());
                            delVProd ? deletedVariantProducts.push(vProd) : null
                        }
                    });

                    const [variantTypes, groupTypes] = await ProductModel.addVariantsAndGroups(
                        tenantId,
                        addedVariantTypes,
                        addedGroupTypes,
                        product._id,
                        {
                            session,
                            ordered: true,
                        }
                    );

                    product.depopulate("variants.values");
                    product.depopulate("groups.values");

                    product.groups.type = groupType;
                    product.variants.values = product.variants.values.filter(v => !deletedVariantTypes.includes(v));
                    product.groups.values = product.groups.values.filter(v => !deletedGroupTypes.includes(v));
                    variantTypes.forEach(vt => {
                        product.variants.values.push(vt._id);
                    });
                    groupTypes.forEach(gt => {
                        product.groups.values.push(gt._id);
                    });

                    await ProductModel.deleteVariantTypes({ _id: { $in: deletedGroupTypes } }, { session });
                }
                else if (!product.groups?.type && !groupType) { // only check for added & deleted variantTypes

                    const [variantTypes, _] = await ProductModel.addVariantsAndGroups(
                        tenantId,
                        addedVariantTypes,
                        [],
                        product._id,
                        {
                            session,
                            ordered: true,
                        }
                    );

                    product.depopulate("variants.values");
                    product.variantProducts.forEach(vProd => {
                        let delVProd = deletedVariantTypes.find(v => v === vProd.variant_value_id.toString());
                        if (delVProd) {
                            deletedVariantProducts.push(vProd);
                        }
                    })

                    // update the PARENT product with variants and groups.
                    product.variants.values = product.variants.values.filter(v => !deletedVariantTypes.includes(v));
                    variantTypes.forEach(vt => {
                        product.variants.values.push(vt._id);
                    });
                }

                deletedVariantProducts.forEach(vProd => {
                    // gather barcodes and images to delete in deletedImages later delete them
                    vProd.images.forEach(i => { deletedImages.push(i) });

                    deletedProductVariantIds.push(vProd._id);

                    deleteVariantPromises.push(
                        ProductModel.updateProduct(
                            { _id: vProd._id },
                            {
                                unique_item_number: vProd._id.toString(),
                                is_deleted: true,
                                is_active: false,
                                $unset: { variant_id: "" },
                            },
                            { session }
                        )
                    );
                });

                //fetch group images to delete them
                const deleteGrpImgs = await ImageModel.getImages({ product_variant_id: product._id, group_id: { $in: deletedGroupTypes } });
                deleteGrpImgs.forEach(i => deletedImages.push(i));
            }

            //commit the transactions
            await Promise.all(deleteVariantPromises);
            await Promise.all([
                ImageModel.deleteImages({ _id: { $in: deletedImages.map(i => i._id) } }, { session }),
                ProductModel.deleteBarcodes({ product_variant_id: { $in: deletedProductVariantIds } }, { session }),
                OrderModel.deleteCartItems({ product_variant_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
                OrderModel.deleteDraftItems({ product_variant_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
                OrderModel.deleteDealProductItems({ product_id: { $in: deletedProductVariantIds }, tenant_id: tenantId }, { session }),
            ]);
            await product.save();
            await session.commitTransaction();

            try {
                const imagePromises = [];
                const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

                // re-calculate inventory mappings, price_mappings and variant_item_numbers for PARENT
                await ProductModel.generateMappingForParent(product);

                if (deletedImages.length) {
                    await ProductModel.deleteS3Images(tenantId, deletedImages, imagePromises, publicS3);
                }
                product.save();
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error in updateVariantGroupInfo either from 'generateMappingForParent' or 'deleteS3Images'"
                })
            }
            return res.handler.success("changes_saved");
        }
        catch (error) {
            await session.abortTransaction();
            await session.endSession({ forceClear: true });
            return res.handler.serverError(error);
        }
    }

    async updateVariantTypeInfo(req, res) {
        try {
            const {
                tenantId,
                productId,
                variantTypeId,
                name
            } = req.body

            await VariantTypeModel.updateVariantType(
                {
                    _id: variantTypeId,
                    tenant_id: tenantId,
                    product_id: productId,
                },
                {
                    name
                },
                {
                    runValidators: true
                }
            )

            return res.handler.success("variant_type_updated");
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async changeVariantOrder(req, res) {
        try {
            const { tenantId, productVariantIds } = req.body;

            const variantProducts = await ProductModel.findProducts({ _id: { $in: productVariantIds }, tenant_id: tenantId }, { variant_order: 1, type: 1, parent_id: 1 });

            if (variantProducts.length < 2
                || !variantProducts.every(v => v.type === NEW_PRODUCT_TYPE.VARIANT)
                || variantProducts[0].parent_id.toString() !== variantProducts[1].parent_id.toString()
            ) {
                return res.handler.badRequest("invalid_variant_products");
            }
            const temp = variantProducts[0].variant_order;
            variantProducts[0].variant_order = variantProducts[1].variant_order;
            variantProducts[1].variant_order = temp;

            await Promise.all([
                variantProducts[0].save(),
                variantProducts[1].save()
            ]);
            return res.handler.success("changes_saved");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async productCount(req, res) {
        try {
            const { tenantId, statusType } = req.query;
            const data = { productCount: 0 };

            const promises = [];
            promises.push(ProductModel.countProductOfTenant(tenantId, statusType));
            // promises.push(ProductModel.getTenantInfo(tenantId, "advance_limit"))
            const [count] = await Promise.all(promises);


            // data.allowProducts = defaultTenantSetting.advance_limit.find(data => data.key === TENANT_ADVANCE_LIMIT_KEYS.NUMBER_OF_PRODUCTS)?.allowance || 0;
            data.productCount = count
            return res.handler.success(null, data);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async barcodeDetails(req, res) {
        try {
            const { tenantId, barcode } = req.query;
            const query = { _id: `${tenantId}_${barcode}` }
            const populate = [
                { path: 'productDetails', select: { type: 1, parent_id: 1, price_mappings: 1, inventory_mappings: 1, variant_id: 1, is_deleted: 1, is_active: 1 } }
            ]
            const barcodeDetails = await ProductModel.getBarcode(query, { created_at: 0, updated_at: 0 }, { populate });

            return res.handler.success(null, barcodeDetails);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async favoriteProduct(req, res) {
        try {
            const { ids, tenantId, type } = req.body;
            const userRoleId = req.headers.userroleid;

            const products = await ProductModel.getProductsWithPopulation({ _id: { $in: ids }, type: { $in: [NEW_PRODUCT_TYPE.PARENT, NEW_PRODUCT_TYPE.SINGLE] } }, { _id: 1 });

            if (!products.length) {
                return res.handler.notFound("products_not_found")
            }
            const productIds = products.map(p => p.id)
            const invalidProductIds = difference(ids, productIds);

            if (type === FAVORITE_PRODUCT_TYPE.FAVORITE) {
                const favProducts = await ProductModel.findFavoriteProducts(
                    { user_role_id: userRoleId, tenant_id: tenantId, product_variant_id: { $in: productIds } },
                    '-_id product_variant_id')

                const favProductIds = favProducts.map(fp => fp.product_variant_id.toString());

                const toFavProductIds = difference(productIds, favProductIds);

                if (!toFavProductIds.length) {
                    return res.handler.conflict("products_already_favorite", { invalidProductIds })
                }

                const promises = [];
                for (let i = 0; i < toFavProductIds.length; i++) {
                    const element = toFavProductIds[i];
                    promises.push(ProductModel.upsertFavoriteProduct(userRoleId, element, tenantId))
                }

                await Promise.all(promises);
                return res.handler.success("products_favorite_success", { invalidProductIds })
            } else if (type === FAVORITE_PRODUCT_TYPE.UNFAVORITE) {
                await ProductModel.deleteFavoriteProducts({ user_role_id: userRoleId, tenant_id: tenantId, product_variant_id: { $in: productIds } })

                return res.handler.success("products_favorite_success");
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    // TODO: add out of stock product parameter
    async favoriteProductList(req, res) {
        try {
            const userRoleId = req.headers.userroleid;
            const { tenantId, favoriteProductId, perPage, salesPersonUserRoleId, priceListId, hideOutOfStock, branchId } = req.query;

            const pipeline = []

            const date = moment().utc().toDate();

            const dealLookUpMatchObj = {
                deal_from_date: { $lte: date },
                deal_to_date: { $gte: date },
            }

            if (priceListId) {
                dealLookUpMatchObj['price_id'] = new mongoose.Types.ObjectId(priceListId);
            }

            const dealMatch = {
                tenant_id: tenantId,
                deal_status: {
                    $nin: [DEAL_STATUS.PROGRESS, DEAL_STATUS.PAUSED, DEAL_STATUS.CANCELLED],
                }
            }

            if (salesPersonUserRoleId) {
                dealMatch['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserRoleId)
            }

            const dealPipeline = [
                {
                    $match: dealMatch
                },
                {
                    $project: {
                        deal_id: 1,
                        deal_type: 1,
                        deal_name: 1,
                        secondary_deal_name: 1,
                        deal_from_date: 1,
                        deal_to_date: 1,
                    }
                }
            ]

            pipeline.push(
                {
                    '$match': {
                        'user_role_id': new mongoose.Types.ObjectId(userRoleId),
                        'tenant_id': tenantId
                    }
                },
                {
                    '$sort': {
                        'created_at': -1
                    }
                },
                {
                    $lookup: {
                        from: 'products_2.0',
                        localField: 'product_variant_id',
                        foreignField: '_id',
                        as: 'product_variant_id',
                        pipeline: [
                            {
                                $project: {
                                    variant_count: 1,
                                    variants: 1,
                                    title: 1,
                                    secondary_language_title: 1,
                                    inventory_mappings: 1,
                                    price_mappings: 1,
                                    item_number: 1,
                                    type: 1,
                                    is_deleted: 1,
                                    is_active: 1
                                }
                            }
                        ]
                    }
                },
            );

            if (hideOutOfStock === "true" && branchId) {
                pipeline.push(
                    {
                        $match: {
                            'product_variant_id.inventory_mappings': {
                                "$elemMatch": {
                                    "branch_id": branchId,
                                    "quantity": { "$gt": 0 }
                                }
                            }
                        }
                    },
                )
            }

            pipeline.push(
                {
                    $lookup: {
                        from: 'images_2.0',
                        localField: 'product_variant_id._id',
                        foreignField: 'product_variant_id',
                        as: 'cover_image',
                        pipeline: [
                            {
                                $match: {
                                    group_id: null
                                }
                            },
                            {
                                $project: {
                                    'image_name': 1,
                                    's3_url': 1,
                                    updated_at: 1
                                }
                            }, {
                                $sort: {
                                    'image_name': 1
                                }
                            }, {
                                $limit: 1
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        'cover_image': {
                            $first: '$cover_image'
                        },
                        'product_variant_id': {
                            $first: '$product_variant_id'
                        }
                    }
                },
                {
                    $match: {
                        'product_variant_id.is_deleted': false,
                        'product_variant_id.is_active': true
                    }
                },
                {
                    $lookup: {
                        from: "deal_products",
                        localField: "product_variant_id._id",
                        foreignField: "product_id",
                        as: "product_deal_info",
                        pipeline: [
                            {
                                $match: dealLookUpMatchObj
                            },
                            {
                                $lookup: {
                                    from: "deals",
                                    localField: "deal_id",
                                    foreignField: "_id",
                                    as: "deal_id",
                                    pipeline: dealPipeline
                                }
                            },
                            {
                                $addFields: {
                                    "deal_id": { $first: "$deal_id" },
                                }
                            },
                            {
                                $match: { deal_id: { $ne: null } }
                            },
                            {
                                $project: {
                                    deal_id: 1,
                                    discount_type: 1,
                                    percent: 1,
                                    amount: 1,
                                    discounted_price: 1,
                                    first_tier: 1,
                                    second_tier: 1,
                                    third_tier: 1,
                                    buy_product: 1,
                                    free_product: 1,
                                    deal_from_date: 1,
                                    deal_to_date: 1
                                }
                            }
                        ]
                    }
                },
                {
                    $lookup: {
                        from: "deal_products",
                        localField: "product_variant_id._id",
                        foreignField: "parent_id",
                        as: "parent_deals",
                        pipeline: [
                            {
                                $match: dealLookUpMatchObj
                            },
                            {
                                $lookup: {
                                    from: "deals",
                                    localField: "deal_id",
                                    foreignField: "_id",
                                    as: "deal_id",
                                    pipeline: dealPipeline
                                }
                            },
                            {
                                $addFields: {
                                    "deal_id": { $first: "$deal_id" },
                                }
                            },
                            {
                                $match: { deal_id: { $ne: null } }
                            },
                            {
                                $project: {
                                    deal_id: 1,
                                    discount_type: 1,
                                    percent: 1,
                                    amount: 1,
                                    discounted_price: 1,
                                    first_tier: 1,
                                    second_tier: 1,
                                    third_tier: 1,
                                    buy_product: 1,
                                    free_product: 1,
                                    deal_from_date: 1,
                                    deal_to_date: 1
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        product_deal_info: {
                            $first: "$product_deal_info",
                        },
                        parent_deal_size: {
                            $size: "$parent_deals"
                        },
                        parent_deals: {
                            $first: "$parent_deals",
                        },
                    }
                },
                {
                    $project: {
                        user_role_id: 1,
                        product_variant_id: 1,
                        cover_image: 1,
                        product_deal_info: 1,
                        variant_deal_info: {
                            $cond: [{ $eq: ['$parent_deal_size', 1] }, '$parent_deals', null]
                        },
                        product_deal_info: 1,
                        multipleDeal: {
                            $cond: [{ $gt: ['$parent_deal_size', 1] }, true, false
                            ]
                        }
                    }
                },
            )

            if (favoriteProductId) {
                pipeline.push(
                    {
                        '$facet': {
                            'list': [{ $match: { _id: { $lt: new mongoose.Types.ObjectId(favoriteProductId) } } }, { '$limit': perPage }],
                            'count': [{ '$count': "count" }]
                        }
                    }
                )
            } else {
                pipeline.push(
                    {
                        '$facet': {
                            'list': [{ '$limit': perPage }],
                            'count': [{ '$count': "count" }]
                        }
                    }
                )
            }

            pipeline.push(
                {
                    '$unwind': {
                        'path': '$count',
                    }
                }
            )

            const data = await ProductModel.aggregateFavoriteProduct(pipeline);
            const result = { list: [], count: 0 };
            result.list = data[0]?.list ? data[0]?.list : []
            result.count = data[0]?.count?.count ? data[0].count.count : 0
            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTagList(req, res) {
        try {
            const {
                tenantId,
                perPage,
                searchKey
            } = req.query;

            const match = {
                tenant_id: tenantId,
                attached_count: {
                    $gt: 0
                }
            }

            if (searchKey) {
                match.name = new RegExp(searchKey, "i")
            }

            const list = await ProductModel.findTags(
                match,
                {
                    name: 1,
                    tenant_id: 1,
                    name: 1,
                    attached_count: 1,
                },
                {
                    sort: {
                        attached_count: -1
                    },
                    limit: perPage
                }
            );
            return res.handler.success(null, list);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeProductType(req, res) {

        let session;
        try {
            session = await mongoose.startSession();
            session.startTransaction();

            const {
                tenantId,
                productVariantId,
                typeChangeTo,
                variantId,
                groupId,
                parentId,
            } = req.body;

            const productPopulate = [
                {
                    path: "parent_id",
                    select: excludeProjections
                },
                {
                    path: "variant_value_id",
                    select: { name: 1 }
                },
                {
                    path: "group_value_id",
                    select: { name: 1 }
                }
            ]

            const product = await ProductModel.findProduct(
                {
                    _id: productVariantId,
                    tenant_id: tenantId,
                    is_deleted: false,
                },
                undefined,
                {
                    populate: productPopulate,
                    session
                }
            );

            let parentProduct;

            if (!product) {
                await session.endSession();
                return res.handler.success("product_not_found");
            }

            switch (typeChangeTo) {
                case NEW_PRODUCT_TYPE.SINGLE:
                    {
                        if (
                            [
                                NEW_PRODUCT_TYPE.SINGLE,
                                NEW_PRODUCT_TYPE.PARENT,
                            ].includes(product.type)
                        ) {
                            await session.endSession();
                            return res.handler.success("invalid_product_type");
                        }

                        product.type = NEW_PRODUCT_TYPE.SINGLE;
                        product.title = `${product.parent_id.title} - ${product.group_value_id?.name ? `${product.group_value_id.name} / ` : ""}${product.variant_value_id.name}`;
                        product.secondary_language_title = product.parent_id.secondary_language_title;
                        product.brand_id = product.parent_id.brand_id
                        product.family_id = product.parent_id.family_id;
                        product.category_id = product.parent_id.category_id;
                        product.subcategory_id = product.parent_id.subcategory_id;
                        product.tax_id = product.parent_id.tax_id;
                        product.description = product.parent_id.description;
                        product.secondary_language_description = product.parent_id.secondary_language_description;
                        product.attribute_set = product.parent_id.attribute_set;
                        product.attributes = product.parent_id.attributes
                        product.tags = product.parent_id.tags;

                        product.variant_id = undefined;
                        product.variant_order = undefined;

                        /**
                         * For safer side removing below keys if they exists.
                         * Mostly these keys will be found in parent product only.
                         */
                        product.variants = undefined
                        product.groups = undefined
                        product.variant_count = undefined
                        product.active_variant_item_numbers = undefined
                        product.inactive_variant_item_numbers = undefined

                        let category;
                        let categoryDbField = ""

                        if (product.parent_id.subcategory_id) {
                            categoryDbField = "subcategory_id"
                        }
                        else if (product.parent_id.category_id) {
                            categoryDbField = "category_id"
                        }
                        else if (product.parent_id.family_id) {
                            categoryDbField = "family_id"
                        }

                        if (categoryDbField) {
                            await product.parent_id.populate({
                                path: categoryDbField,
                                select: {
                                    product_counter: 1
                                },
                                options: {
                                    session
                                }
                            })

                            category = product.parent_id[categoryDbField];
                            category.product_counter += 1
                            product.product_order = category.product_counter * INCREMENT_PRODUCT_NUMBER_BY;
                        }

                        parentProduct = product.parent_id;
                        product.depopulate("parent_id");
                        product.depopulate("group_value_id");
                        product.depopulate("variant_value_id");
                        product.group_value_id = undefined;
                        product.variant_value_id = undefined;
                        product.parent_id = undefined;

                        const updateTagPromise = ProductModel.updateTags(
                            {
                                _id: { $in: product.tags }
                            },
                            {
                                $inc: { attached_count: 1 }
                            },
                            {
                                session
                            }
                        )

                        const updateDealProductPromise = ProductModel.updateDealProducts(
                            {
                                product_id: product._id
                            },
                            {
                                $unset: { parent_id: "" }
                            },
                            {
                                session
                            }
                        )

                        await Promise.all([
                            product.save(),
                            category?.save(),
                            updateTagPromise,
                            updateDealProductPromise,
                        ]);
                        break;
                    }

                /* case NEW_PRODUCT_TYPE.VARIANT:
                    {
                        parentProduct = await ProductModel.findProduct(
                            { _id: parentId, is_deleted: false, type: NEW_PRODUCT_TYPE.PARENT },
                            { created_at: 0, updated_at: 0 },
                            { session }
                        );

                        if (!parentProduct) {
                            await session.endSession();
                            return res.handler.success("invalid_parent_product");
                        }
                        const [variantValue, groupValue] = await Promise.all([
                            ProductModel.getVariantType({ _id: variantId, type: PRODUCT_VARIANT_TYPES.VARIANT_VALUE }, { _id: 1 }),
                            ProductModel.getVariantType({ _id: groupId, type: PRODUCT_VARIANT_TYPES.GROUP_VALUE }),
                        ])
                        if (!parentProduct.variants.values.includes(variantId) || !variantValue) {
                            await session.endSession();
                            return res.handler.success("invalid_variant_id");
                        }

                        if (Array.isArray(parentProduct.groups?.values)
                            && (!parentProduct.groups.values.includes(groupId) || !(groupValue))) {
                            await session.endSession();
                            return res.handler.success("invalid_group_id");
                        }

                        if (product.type === NEW_PRODUCT_TYPE.VARIANT) {
                            await session.endSession();
                            return res.handler.success("invalid_product_type");
                        }

                        product.variant_id = `${parentProduct._id}_${variantId}${groupValue ? `_${groupValue._id}` : ""}`;

                        product.type = NEW_PRODUCT_TYPE.VARIANT;
                        product.title = undefined;
                        product.secondary_language_title = undefined;
                        product.brand_id = undefined;
                        product.family_id = undefined;
                        product.category_id = undefined;
                        product.subcategory_id = undefined;
                        product.attribute_set = undefined;
                        product.attributes = undefined
                        const productTags = product.tags;
                        product.tags = undefined;
                        product.product_order = undefined;

                        product.parent_id = parentProduct._id;

                        product.variant_value_id = variantValue._id;
                        product.group_value_id = groupValue._id;

                        parentProduct.variant_count += 1;
                        product.variant_order = parentProduct.variant_count;

                        await Promise.all([
                            product.save(),
                            ProductModel.updateTags({ _id: { $in: productTags }, attached_count: { $gt: 0 } }, { $inc: { attached_count: -1 } }, { session }),
                            ProductModel.updateDealProducts({ product_id: product._id }, { $set: { parent_id: parentProduct._id } }, { session })
                        ]);
                        break;
                    } */
            }

            /**
             * generateMapping of price_mappings, inventory_mappings, and variant_item_numbers
             * for parent product and update same on parent product
             */
            await ProductModel.generateMappingForParent(parentProduct);

            await parentProduct.save();
            await session.commitTransaction();
            await session.endSession();

            return res.handler.success("updated_edited")
        }
        catch (error) {
            await session.abortTransaction();
            await session.endSession({ forceClear: true });

            switch (error.code) {
                case 11000:
                    if (error?.keyValue?.variant_id) {
                        return res.handler.conflict("variant_already_exists")
                    }
                    else if ((e?.keyValue?.unique_item_number)) {
                        return res.handler.conflict("item_number_already_exists")
                    }
                    break;

                default:
                    return res.handler.serverError(error);
            }
        }
    }

    async totalProductCount(req, res) {
        try {
            const { tenantId } = req.query;

            const searchFilters = [
                {
                    "equals": {
                        "path": "is_active",
                        "value": true,
                    }
                },
                {
                    "equals": {
                        "path": "is_deleted",
                        "value": false,
                    }
                },
                {
                    "queryString": {
                        "defaultPath": "type",
                        "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                    }
                }
            ]
            const query = { is_active: true, is_deleted: false, type: { $in: [NEW_PRODUCT_TYPE.PARENT, NEW_PRODUCT_TYPE.SINGLE] } };
            if (tenantId) {
                query['tenant_id'] = tenantId;
                searchFilters.push(
                    {
                        "range": {
                            "path": "tenant_id",
                            "gte": parseInt(tenantId),
                            "lte": parseInt(tenantId),
                        }
                    }
                )
            }
            const searchStage = {
                "$search": {
                    "index": "product_v2_search",
                    "returnStoredSource": false,
                    "compound": {
                        "filter": searchFilters,
                    },
                },
            }
            const pipeline = [
                searchStage,
                {
                    $count: 'count'
                }
            ]
            // const totalProductCount = await ProductModel.countProducts(query);
            const result = await ProductModel.productAggregation(pipeline);

            const totalProductCount = result[0] ? result[0].count : 0;
            return res.handler.success(null, totalProductCount)
        } catch (error) {
            return res.handler.serverError(error)
        }
    }

}

module.exports = ProductController;

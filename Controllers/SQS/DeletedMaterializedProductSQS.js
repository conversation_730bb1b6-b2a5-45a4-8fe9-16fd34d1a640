const DeletedMaterializedProductQueue = require("../../SQS/DeletedMaterializedProductQueue")
const DeletedMaterializedProductSqsService = require("../../Services/sqs/DeletedMaterializedProductSqsService")

const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))

class DeletedMaterializedProductSQS {

    handleMessageBatch = async (messages) => {
        try {
            if (!messages.length) {
                return
            }

            const messageGroupMap = new Map()
            const documentIdMap = new Map()
            const deleteMsgEntriesMap = new Map()
            const updateArray = []

            // Generates messages mapping by `MessageGroupId`
            for (let i = 0; i < messages.length; i++) {
                const {
                    MessageId,
                    ReceiptHandle,
                    Body,
                    Attributes: {
                        MessageGroupId,
                    } = {},
                } = messages[i]

                const message = {
                    receiptHandle: ReceiptHandle,
                    messageGroupId: MessageGroupId,
                    messageBody: JSON.parse(Body)
                }

                const existingDocMap = documentIdMap.get(message.messageBody.documentId)
                const docMapData = existingDocMap || []

                docMapData.push({
                    MessageId,
                    ReceiptHandle,
                })
                documentIdMap.set(message.messageBody.documentId, docMapData)

                let bulkMessage = []

                if (messageGroupMap.has(MessageGroupId)) {
                    bulkMessage = messageGroupMap.get(MessageGroupId)
                }
                bulkMessage.push(message)
                messageGroupMap.set(MessageGroupId, bulkMessage)
            }

            DeletedMaterializedProductSqsService.handleMsgByGroup(messageGroupMap, updateArray)
            await DeletedMaterializedProductSqsService.upsertDeletedMaterializedProducts(documentIdMap, deleteMsgEntriesMap, updateArray)


            await MaterializedProductHelper.deleteMessages(
                deleteMsgEntriesMap,
                DeletedMaterializedProductQueue.deleteMessageBatch,
                "DeletedMaterializedProductSQS/handleMessageBatch",
            )
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "Error: DeletedMaterializedProductSQS/handleMessageBatch",
                messages
            })
        }
    }
}

module.exports = new DeletedMaterializedProductSQS()

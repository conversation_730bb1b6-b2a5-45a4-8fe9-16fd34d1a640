
const MaterializedProductQueue = new (require("../../SQS/MaterializedProductQueue"))
const MaterializedProductModel = new (require("../../Models/materialized_product/MaterializedProductModel"))
const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))

const {
    MONGODB,
    NEW_PRODUCT_TYPE,
} = require("../../Configs/constants")

class ProductSQS {

    handleMessageBatch = async (messages) => {
        try {
            if (!messages.length) {
                return
            }

            const messageGroupMap = new Map()
            const documentIdMap = new Map()

            const insertOpMsgs = {
                singleProductMsgs: [], // In a single batch, if `Parent` type product msgs come then it'll also handle along with `Single`
                variantProductMsgs: [], // In a single batch of the same messageGroup, if `update` op msgs come then it'll also handle along with `insert`
            }

            const updateOpMsgs = {
                singleProductMsgs: [],
            }
            let deleteMsgEntriesMap = new Map()

            const categoryIdSet = new Set()
            const brandIdSet = new Set()
            const uomIdSet = new Set()
            const variantTypesIdSet = new Set()
            const attributeIdSet = new Set()

            // Generates messages mapping by `MessageGroupId`
            for (let i = 0; i < messages.length; i++) {
                const {
                    MessageId,
                    ReceiptHandle,
                    Body,
                    Attributes: {
                        MessageGroupId,
                    } = {},
                } = messages[i]

                const message = {
                    receiptHandle: ReceiptHandle,
                    messageGroupId: MessageGroupId,
                    messageBody: JSON.parse(Body)
                }

                const existingDocMap = documentIdMap.get(message.messageBody.documentId)
                const docMapData = existingDocMap || []

                docMapData.push({
                    MessageId,
                    ReceiptHandle,
                })
                documentIdMap.set(message.messageBody.documentId, docMapData)

                MaterializedProductHelper.prepareDataMapping({
                    messageBody: message.messageBody,
                    categoryIdSet,
                    brandIdSet,
                    uomIdSet,
                    variantTypesIdSet,
                    attributeIdSet,
                })

                let bulkMessage = []

                if (messageGroupMap.has(MessageGroupId)) {
                    bulkMessage = messageGroupMap.get(MessageGroupId)
                }
                bulkMessage.push(message)
                messageGroupMap.set(MessageGroupId, bulkMessage)
            }

            this.distributeMsgByGroup(
                messageGroupMap,
                insertOpMsgs,
                updateOpMsgs,
            )

            const hasInsertMsgs = insertOpMsgs.singleProductMsgs.length || insertOpMsgs.variantProductMsgs.length
            const hasUpdateMsgs = updateOpMsgs.singleProductMsgs.length
            let mappingData

            if (hasInsertMsgs || hasUpdateMsgs) {
                mappingData = await MaterializedProductHelper.getMappingData({
                    categoryIdSet,
                    brandIdSet,
                    uomIdSet,
                    variantTypesIdSet,
                    attributeIdSet,
                })
            }

            if (insertOpMsgs.singleProductMsgs.length) {
                await MaterializedProductModel.addBulkSingleProduct(
                    insertOpMsgs.singleProductMsgs,
                    mappingData,
                    documentIdMap,
                    deleteMsgEntriesMap,
                )
            }

            if (insertOpMsgs.variantProductMsgs.length) {
                await MaterializedProductModel.addBulkVariantProduct(
                    insertOpMsgs.variantProductMsgs,
                    mappingData,
                    documentIdMap,
                    deleteMsgEntriesMap,
                )
            }

            if (updateOpMsgs.singleProductMsgs.length) {
                await MaterializedProductModel.updateBulkSingleProduct(
                    updateOpMsgs.singleProductMsgs,
                    mappingData,
                    documentIdMap,
                    deleteMsgEntriesMap,
                )
            }

            if (deleteMsgEntriesMap.size) {
                try {
                    const res = await MaterializedProductQueue.deleteMessageBatch(Array.from(deleteMsgEntriesMap.values()))

                    if (res.Successful?.length) {
                        logger.info(`ProductSQS/handleMessageBatch. Successfully deleted msgs of: ${res.Successful.map(item => item.Id).join(", ")}`)
                    }
                    if (res.Failed?.length) {
                        logger.error("Error: ProductSQS/handleMessageBatch deleteMessageBatch", {
                            failed: res.Failed
                        })
                    }
                }
                catch (error) {
                    logger.error(error, {
                        errorMessage: "Error: ProductSQS/handleMessageBatch deleteMessageBatch",
                        messages
                    })
                }
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "Error: ProductSQS/handleMessageBatch",
                messages
            })
        }
    }

    distributeMsgByGroup = (
        messageGroupMap,
        insertOpMsgs,
        updateOpMsgs,
    ) => {
        messageGroupMap.forEach((bulkMessage, messageGroupId, map) => {
            const [, , productType] = messageGroupId.split("_")

            const isSingleProduct = productType === NEW_PRODUCT_TYPE.SINGLE
            const isParentProduct = productType === NEW_PRODUCT_TYPE.PARENT

            if (bulkMessage.length === 1) {
                /** Handle single message case */
                const {
                    messageBody: {
                        operationType,
                    },
                } = bulkMessage[0]

                if (operationType === MONGODB.OPERATION_TYPE.INSERT) {
                    if (isSingleProduct) {
                        insertOpMsgs.singleProductMsgs.push(bulkMessage[0])
                    }
                    else {
                        insertOpMsgs.variantProductMsgs.push(bulkMessage[0])
                    }
                }
                else if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
                    if (isSingleProduct) {
                        updateOpMsgs.singleProductMsgs.push(bulkMessage[0])
                    }
                    else {
                        insertOpMsgs.variantProductMsgs.push(bulkMessage[0])
                    }
                }
                return // Early exit for single message
            }

            /** Handle multiple messages case */
            const totalMessageCount = bulkMessage.length

            let insertOpCount = 0
            let updateOpCount = 0

            let variantInsertCount = 0

            let singleUpdateCount = 0
            let parentUpdateCount = 0

            for (let index = 0; index < bulkMessage.length; index++) {
                const {
                    messageBody: {
                        operationType,
                    },
                } = bulkMessage[index]

                if (operationType === MONGODB.OPERATION_TYPE.INSERT) {
                    insertOpCount++

                    if (isParentProduct) {
                        variantInsertCount++
                    }
                }
                else if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
                    updateOpCount++

                    if (isSingleProduct) {
                        singleUpdateCount++
                    }
                    else {
                        parentUpdateCount++
                    }
                }
            }

            if (totalMessageCount === insertOpCount) {
                insertOpMsgs.variantProductMsgs.push(...bulkMessage)
            }
            else if (totalMessageCount === updateOpCount) {
                if (singleUpdateCount === totalMessageCount) {
                    updateOpMsgs.singleProductMsgs.push(...bulkMessage)
                }
                else {
                    insertOpMsgs.variantProductMsgs.push(...bulkMessage)
                }
            }
            else {
                if (insertOpCount) {
                    insertOpMsgs.variantProductMsgs.push(...bulkMessage)
                }
                else if (updateOpCount) {
                    if (isSingleProduct) {
                        updateOpMsgs.singleProductMsgs.push(...bulkMessage)
                    }
                    else {
                        insertOpMsgs.variantProductMsgs.push(...bulkMessage)
                    }
                }
            }
        })
    }
}

module.exports = ProductSQS

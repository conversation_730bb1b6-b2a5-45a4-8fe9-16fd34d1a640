const DeleteImageQueue = require("../../SQS/DeleteImageQueue")
const DeleteImageSqsService = require("../../Services/sqs/DeleteImageSqsService")

const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))

class DeleteImageSQS {

    handleMessageBatch = async (messages) => {
        try {
            if (!messages.length) {
                return
            }

            const messageGroupMap = new Map()
            const documentIdMap = new Map()
            const deleteMsgEntriesMap = new Map()
            const updateArray = []

            // Generates messages mapping by `MessageGroupId`
            for (let i = 0; i < messages.length; i++) {
                const {
                    MessageId,
                    ReceiptHandle,
                    Body,
                    Attributes: {
                        MessageGroupId,
                    } = {},
                } = messages[i]

                const message = {
                    receiptHandle: ReceiptHandle,
                    messageGroupId: MessageGroupId,
                    messageBody: JSON.parse(Body)
                }

                const existingDocMap = documentIdMap.get(message.messageBody.documentId)
                const docMapData = existingDocMap || []

                docMapData.push({
                    MessageId,
                    ReceiptHandle,
                })
                documentIdMap.set(message.messageBody.documentId, docMapData)

                let bulkMessage = []

                if (messageGroupMap.has(MessageGroupId)) {
                    bulkMessage = messageGroupMap.get(MessageGroupId)
                }
                bulkMessage.push(message)
                messageGroupMap.set(MessageGroupId, bulkMessage)
            }

            DeleteImageSqsService.handleMsgByGroup(messageGroupMap, updateArray)
            await DeleteImageSqsService.upsertDeletedImages(documentIdMap, deleteMsgEntriesMap, updateArray)

            await MaterializedProductHelper.deleteMessages(
                deleteMsgEntriesMap,
                DeleteImageQueue.deleteMessageBatch,
                "DeleteImageSQS/handleMessageBatch",
            )
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "Error: DeleteImageSQS/handleMessageBatch",
                messages
            })
        }
    }
}

module.exports = new DeleteImageSQS()

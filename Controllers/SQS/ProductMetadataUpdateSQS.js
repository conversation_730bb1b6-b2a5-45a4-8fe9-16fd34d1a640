
const ProductMetadataUpdateQueue = new (require("../../SQS/ProductMetadataUpdateQueue"))
const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))
const ProductMetadataUpdateSqsService = new (require("../../Services/sqs/ProductMetadataUpdateSqsService"))

class ProductMetadataUpdateSQS {

    handleMessageBatch = async (messages) => {
        try {
            if (!messages.length) {
                return
            }

            const messageGroupMap = new Map()
            const documentIdMap = new Map()
            const deleteMsgEntriesMap = new Map()

            const metadataMessages = {
                categoryMsgs: [],
                brandMsgs: [],
                attributeMsgs: [],
                variantMsgs: [],
                groupMsgs: [],
            }

            // Generates messages mapping by `MessageGroupId`
            for (let i = 0; i < messages.length; i++) {
                const {
                    MessageId,
                    ReceiptHandle,
                    Body,
                    Attributes: {
                        MessageGroupId,
                    } = {},
                } = messages[i]

                const message = {
                    receiptHandle: ReceiptHandle,
                    messageGroupId: MessageGroupId,
                    messageBody: JSON.parse(Body)
                }

                const existingDocMap = documentIdMap.get(message.messageBody.documentId)
                const docMapData = existingDocMap || []

                docMapData.push({
                    MessageId,
                    ReceiptHandle,
                })
                documentIdMap.set(message.messageBody.documentId, docMapData)

                let bulkMessage = []

                if (messageGroupMap.has(MessageGroupId)) {
                    bulkMessage = messageGroupMap.get(MessageGroupId)
                }
                bulkMessage.push(message)
                messageGroupMap.set(MessageGroupId, bulkMessage)
            }

            ProductMetadataUpdateSqsService.handleMsgByGroup(messageGroupMap, metadataMessages)
            await ProductMetadataUpdateSqsService.updateMetadata(documentIdMap, deleteMsgEntriesMap, metadataMessages)

            await MaterializedProductHelper.deleteMessages(
                deleteMsgEntriesMap,
                ProductMetadataUpdateQueue.deleteMessageBatch,
                "ProductMetadataUpdateSQS/handleMessageBatch",
            )
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "Error: ProductMetadataUpdateSQS/handleMessageBatch",
                messages
            })
        }
    }
}

module.exports = ProductMetadataUpdateSQS

const ProductModel = new (require("../Models/ProductModel"))();
const ImageModel = new (require("../Models/ImageModel"))();
const InternalServiceModel = new (require("../Models/InternalServiceModel"));

const ImageGetService = new (require("../Services/images/ImageGetService"))()

const FileUpload = require('../Configs/awsUploader').S3Upload;

const {
    FILE_PATH,
    BUCKET_TYPE,
    IMAGE_LISTING,
    PRODUCT_VARIANT_TYPES,
    IMAGE_TYPE,
    NEW_PRODUCT_TYPE,
    VARIANT_TYPE_ENUM,
    DATA_SHEET,
    STATUS_CODES,
    VALUES,
} = require('../Configs/constants');

const {
    toLeanOption,
} = require("../Utils/helpers");

class ImageController {

    async addImage(req, res) {
        try {
            const imageName = req.body.imageName;

            const start = imageName.lastIndexOf("_P") + 2;
            const end = imageName.lastIndexOf(".");

            req.body.imageNumber = parseInt(imageName.substring(start, end));

            if (isNaN(req.body.imageNumber)) {
                return res.handler.badRequest("invalid_image_name");
            }

            const imageDetail = await ImageModel.addUpdateImage(req.body, req.headers)

            return res.handler.success("updated_product_image", imageDetail)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getUploadSignature(req, res) {
        try {
            const { tenantId, imageName, type } = req.body;

            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);
            const pathName = `${FILE_PATH.PRODUCT_IMAGE.WEB}/${tenantId}`
            const responseData = {
                signedUrl: "",
                s3Url: "",
                imageName: "",
            };

            if (type === IMAGE_TYPE.GROUP_IMAGE) {
                const groupId = imageName.split("_"); // {{product_id}}_{{group_id}}_P{number}.imgExt
                if (!groupId[0] || !groupId[1]) {
                    return res.handler.badRequest("invalid_image_name")
                }
                const existGroupValueId = await ProductModel.getVariantType({ _id: groupId[1], product_id: groupId[0], type: PRODUCT_VARIANT_TYPES.GROUP_VALUE, is_deleted: false }, { created_at: 0, updated_at: 0, is_deleted: 0 })
                if (!existGroupValueId) {
                    return res.handler.notFound("group_not_found")
                }
                responseData["groupId"] = groupId[1];
                responseData["productVariantId"] = existGroupValueId.product_id;
                responseData["signedUrl"] = await publicS3.getSignedUrl(pathName, imageName);
                responseData["s3Url"] = process.env.AWS_PUBLIC_BUCKET_BASE_URL + pathName + "/" + imageName;
                responseData["imageName"] = imageName;
            } else {
                const index = imageName.lastIndexOf("_")
                if (index === -1) {
                    return res.handler.notFound("image_product_association_not_found")
                }
                const itemNumber = imageName.substring(0, index)

                const produceVariantDetails = await ProductModel.findProduct({ unique_item_number: `${tenantId}_${itemNumber}`, is_deleted: false });
                if (!produceVariantDetails || !produceVariantDetails?._id) {
                    return res.handler.notFound("image_product_association_not_found")
                }

                const imageOrderWithExt = imageName.substring(index);

                responseData['productVariantId'] = produceVariantDetails._id;
                const originalName = `${produceVariantDetails._id}${imageOrderWithExt}`;
                responseData["signedUrl"] = await publicS3.getSignedUrl(pathName, originalName);
                responseData["s3Url"] = process.env.AWS_PUBLIC_BUCKET_BASE_URL + pathName + "/" + originalName;
                responseData["imageName"] = originalName;
            }

            return res.handler.success("upload_url_get_success", responseData);
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    listImages = async (req, res) => {
        try {
            const body = req.query;
            let pipeline;
            let data;
            const result = { list: [], count: 0 }

            if (body.imageType === IMAGE_LISTING.ALL || body.imageType === IMAGE_LISTING.NEW) {
                pipeline = await this.generatePipelineForImageList(body, req);
                data = await ImageModel.aggregateImages(pipeline);

                result.list = data.map(d => d.images);
                result.count = data[0]?.count?.count || 0;
            }
            else {
                pipeline = await this.generateNeedsImagesPipeline(body);
                data = await ProductModel.productAggregation(pipeline);

                result.list = data[0]?.list ? data[0]?.list : [];
                result.count = data[0]?.count?.count ? data[0]?.count?.count : 0
            }
            return res.handler.success(null, result);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteImages(req, res) {
        try {
            const {
                imageIds,
                tenantId,
            } = req.query

            const filter = {
                _id: { $in: imageIds },
                tenant_id: tenantId,
            }

            const images = await ImageModel.getImages(
                filter,
                {
                    "image_name": 1,
                },
                toLeanOption,
            )

            if (!images.length) {
                return res.handler.notFound("image_not_found")
            }
            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

            const response = await Promise.allSettled([
                ProductModel.deleteS3ImageList(tenantId, images, publicS3),
                ImageModel.deleteImages(filter)
            ])
            return res.handler.success("deleted_product_images", response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async swapImages(req, res) {
        try {
            const { imageIds, tenantId } = req.body
            if (imageIds[0] === imageIds[1]) {
                return res.handler.badRequest("same_image_ids")
            }
            const [img1, img2] = await ImageModel.getImages({ _id: { $in: imageIds }, tenant_id: tenantId });
            if (!img1 || !img2) {
                return res.handler.notFound("image_not_found")
            }

            /**
             *  @caution 🚩🚩🚩🚩🚩🚩🚩🚩🚩🚩
             * Here only web image is being swapped,
             * not others (like tablet, mobile, logo, thumbnail, mini thumbnail).
             *
             * Those images are automatically being swapped by our AWS lambda function,
             * as it is trigged by PUT event, when we've swapped into web folder.
             *
             * so don't make any changes to other type of images here,
             * as it can cause issues in the AWS lambda's trigger (can run multiple time).
             */
            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

            const pathName = `${FILE_PATH.PRODUCT_IMAGE.WEB}/${tenantId}`
            const img1S3Key = pathName + "/" + img1.image_name;
            const img2S3Key = pathName + "/" + img2.image_name;
            const img1ReadStream = publicS3.getReadStreamOfObject(img1S3Key);
            const img2ReadStream = publicS3.getReadStreamOfObject(img2S3Key);
            const { writeStream: updatedImg1UpStream, uploadFinished: img1UploadFinish } = publicS3.writeStreamToS3(img2S3Key);
            const { writeStream: updatedImg2UpStream, uploadFinished: img2UploadFinish } = publicS3.writeStreamToS3(img1S3Key);

            img1ReadStream.pipe(updatedImg1UpStream);
            img2ReadStream.pipe(updatedImg2UpStream);

            const promises = [img1UploadFinish, img2UploadFinish];
            img1.updated_at = new Date();
            img2.updated_at = new Date();

            img1.updated_by = req.headers.userDetails._id
            img2.updated_by = req.headers.userDetails._id

            promises.push(img1.save());
            promises.push(img2.save());
            await Promise.all(promises);

            return res.handler.success("image_swapped");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    generatePipelineForImageList = async (body, req) => {
        let { tenantId, searchKey = "", page = 0, perPage = 10, imageType = "" } = body;

        page = parseInt(page);
        perPage = parseInt(perPage);
        const offset = (page - 1) * perPage;

        const pipeline = [];
        const $match = {
            tenant_id: tenantId
        }

        if (imageType === IMAGE_LISTING.NEW) {
            const settings = await InternalServiceModel.tenantAppSettings(tenantId, "consider_new_item")
            const days = "consider_new_item" in settings
                ? settings.consider_new_item
                : 30

            $match['created_at'] = {
                $gte: moment().subtract(days, "days").toDate()
            }
        }

        pipeline.push({ $match });

        const parentPipeLine = [
            {
                '$lookup': {
                    'from': "products_2.0",
                    'localField': "parent_id",
                    'foreignField': "_id",
                    'as': "parentDetails",
                    'pipeline': [
                        {
                            '$project': {
                                title: 1,
                                item_number: 1,
                                secondary_language_title: 1,
                                type: 1,
                            }
                        }
                    ]
                }
            },
            {
                '$lookup': {
                    'from': "variant_types_2.0",
                    'localField': "variant_value_id",
                    'foreignField': "_id",
                    'as': "variant_value_id",
                    'pipeline': [
                        {
                            '$project': { 'name': 1 }
                        }
                    ]
                }
            },
            {
                '$lookup': {
                    'from': "variant_types_2.0",
                    'localField': "group_value_id",
                    'foreignField': "_id",
                    'as': "group_value_id",
                    'pipeline': [
                        {
                            '$project': { 'name': 1 }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    variant_value_id: { $first: "$variant_value_id" },
                    group_value_id: { $first: "$group_value_id" },
                    parentDetails: { $first: "$parentDetails" }
                }
            },
            {
                $project: {
                    title: 1,
                    item_number: 1,
                    secondary_language_title: 1,
                    type: 1,
                    variant_value_id: 1,
                    group_value_id: 1,
                    parentDetails: 1
                }
            }
        ]

        if (searchKey) {
            pipeline.push(
                {
                    // Will fetch SINGLE, PARENT & VARIANT type product (group images will not be matched because it does not store product_variant_id)
                    $lookup: {
                        from: "products_2.0",
                        foreignField: "_id",
                        localField: "product_variant_id",
                        as: "productDetails",
                        pipeline: parentPipeLine
                    }
                },
                {
                    $addFields: {
                        productDetails: { $first: "$productDetails" },
                    }
                },
                {
                    $match: {
                        $or: [
                            { "productDetails.title": { $regex: searchKey, $options: "i" } },
                            { "productDetails.item_number": { $regex: searchKey, $options: "i" } },
                            { "productDetails.productDetails.item_number": { $regex: searchKey, $options: "i" } }
                        ]
                    }
                }
            )
        }

        pipeline.push(
            {
                $sort: { "created_at": -1 }
            },
            {
                $facet: {
                    images: [{ $skip: offset }, { $limit: perPage }],
                    count: [{ $count: "count" }]
                }
            },
            {
                $addFields: { "count": { $first: "$count" } }
            },
            {
                $unwind: { path: "$images" }
            }
        )


        if (!searchKey) {
            pipeline.push(
                {
                    $lookup: {
                        from: "products_2.0",
                        localField: "images.product_variant_id",
                        foreignField: "_id",
                        as: "images.productDetails",
                        pipeline: parentPipeLine,
                    }
                },
                {
                    $addFields: {
                        "images.productDetails": { $first: "$images.productDetails" }
                    },
                }
            )

        }

        return pipeline;
    }

    async generateNeedsImagesPipeline(body, forCount) {
        const { tenantId, searchKey = "", page = 0, perPage = 10 } = body;

        const offset = (page - 1) * perPage;

        const searchFilters = [
            {
                "range": {
                    "path": "tenant_id",
                    "gte": tenantId,
                    "lte": tenantId,
                }
            },
            {
                "equals": {
                    "path": "is_deleted",
                    "value": false,
                }
            },
            {
                "queryString": {
                    "defaultPath": "type",
                    "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                }
            }
        ];

        const shouldMatches = [
            {
                "autocomplete": {
                    "query": searchKey,
                    "path": "title"
                },
            },
            {
                "autocomplete": {
                    "query": searchKey,
                    "path": "secondary_language_title",
                },
            },
            {
                "autocomplete": {
                    "query": searchKey,
                    "path": "item_number",
                },
            },
            {
                "autocomplete": {
                    "query": searchKey,
                    "path": "active_variant_item_numbers",
                },
            },
            {
                "autocomplete": {
                    "query": searchKey,
                    "path": "inactive_variant_item_numbers",
                },
            },
            {
                "text": {
                    "query": searchKey,
                    "path": [
                        "title",
                        "secondary_language_title",
                        "item_number",
                        "active_variant_item_numbers",
                        "inactive_variant_item_numbers",
                    ],
                }
            },
        ];

        const searchStage = {
            "$search": {
                "index": "product_v2_search",
                "returnStoredSource": false,
                "compound": {
                    "filter": searchFilters,
                },
            },
        }

        if (!forCount && searchKey) {
            searchStage["$search"]["compound"]["should"] = shouldMatches;
            searchStage["$search"]["compound"]["minimumShouldMatch"] = 1
        }

        const productVariantsMatchObj = {
            is_deleted: false
        }

        if (!forCount) {
            productVariantsMatchObj["$expr"] = { $eq: ['$$variantType', VARIANT_TYPE_ENUM.COLOR] }
        }

        const pipeline = [
            searchStage,
            {
                $sort: {
                    created_at: -1
                }
            },
            {
                $lookup: {
                    from: 'products_2.0',
                    'let': {
                        variantType: '$variants.type'
                    },
                    foreignField: 'parent_id',
                    localField: '_id',
                    as: 'product_variants',
                    pipeline: [
                        {
                            $match: productVariantsMatchObj
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'variant_value_id',
                                foreignField: '_id',
                                as: 'variant_value_id',
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'group_value_id',
                                foreignField: '_id',
                                as: 'group_value_id',
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'images_2.0',
                                localField: '_id',
                                foreignField: 'product_variant_id',
                                as: 'variantImages',
                                pipeline: [
                                    {
                                        $match: {
                                            group_id: null
                                        }
                                    },
                                    {
                                        $limit: 1
                                    },
                                    {
                                        $count: 'count'
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                variantImages: {
                                    $first: '$variantImages'
                                },
                                variant_name: {
                                    $first: '$variant_value_id.name'
                                },
                                group_name: {
                                    $first: '$group_value_id.name'
                                }
                            }
                        },
                        {
                            $match: {
                                variantImages: null
                            }
                        }
                    ]
                }
            }
        ]

        pipeline.push(
            {
                $addFields: {
                    product_variant_count: {
                        $size: '$product_variants'
                    }
                }
            },
            {
                $lookup: {
                    from: 'images_2.0',
                    localField: '_id',
                    foreignField: 'product_variant_id',
                    as: 'images',
                    pipeline: [
                        {
                            $match: {
                                group_id: null
                            }
                        },
                        {
                            $facet: {
                                mainProductImage: [
                                    {
                                        $project: {
                                            image_number: 1,
                                            image_name: 1,
                                            s3_url: 1
                                        }
                                    },
                                    {
                                        $sort: {
                                            image_number: 1
                                        }
                                    },
                                    {
                                        $limit: 1
                                    }
                                ],
                                count: [
                                    {
                                        $count: 'count'
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                mainProductImage: {
                                    $first: '$mainProductImage'
                                },
                                count: {
                                    $first: '$count'
                                }
                            }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    image: {
                        $first: '$images'
                    }
                }
            }
        )

        if (forCount) {
            pipeline.push(
                {
                    $unwind: {
                        path: '$product_variants',
                        preserveNullAndEmptyArrays: true
                    }
                }
            )
        }

        pipeline.push(
            {
                $match: {
                    $or: [
                        {
                            type: NEW_PRODUCT_TYPE.SINGLE,
                            'image.count': null
                        },
                        {
                            type: NEW_PRODUCT_TYPE.PARENT,
                            $or: [
                                {
                                    product_variant_count: {
                                        $gt: 0
                                    }
                                },
                                {
                                    'image.count': null
                                }
                            ]
                        }
                    ]
                }
            },
            {
                $project: {
                    title: 1,
                    item_number: 1,
                    image: 1,
                    type: 1,
                    'product_variants._id': 1,
                    'product_variants.item_number': 1,
                    'product_variants.variant_name': 1,
                    'product_variants.group_name': 1
                }
            }
        )

        if (forCount) {
            pipeline.push(
                {
                    $count: "count"
                }
            )

            return pipeline
        }

        pipeline.push(
            {
                $facet: {
                    list: [
                        {
                            $skip: offset
                        },
                        {
                            $limit: perPage
                        }
                    ],
                    count: [
                        {
                            $count: 'count'
                        }
                    ]
                }
            },
            {
                $unwind: {
                    path: '$count'
                }
            }
        )

        return pipeline
    }

    async imagesMatch(req, res) {
        try {
            const body = req.query;
            const pipeline = await this.generateNeedsImagesPipeline(body, true);
            const needImageProductsCount = await ProductModel.productAggregation(pipeline);
            const needImageProducts = await ImageModel.linkedTenantProductImage(body);
            needImageProducts["processedCount"] = needImageProductsCount.length ? needImageProductsCount[0].count : 0;
            return res.handler.success(null, needImageProducts);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async imagesMatchAction(req, res) {
        try {
            const body = req.body;
            let productImages;
            let existProcess;
            let notCopyImages = [];

            existProcess = await ImageModel.existProcess(body.tenantId, body.linkedTenantId)
            if (!existProcess) {
                existProcess = await ImageModel.addImageProcess(body.tenantId, body.linkedTenantId)
                await existProcess.save();
            } else {
                const data = moment().add(-5, 'minutes').utc()
                if (existProcess.created_at > data) {
                    return res.handler.conflict('process_working');
                } else {
                    existProcess.created_at = moment().utc()
                    await existProcess.save();
                }
            }

            if (body.type === DATA_SHEET.APPROVE_TYPE.ALL) {
                const product = await ImageModel.linkedTenantProductImage(body)

                for (let index = 0; index < product?.length; index++) {
                    if (product[index].linkedTenantProductId) {
                        productImages = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: product[index].linkedTenantProductId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 }) // { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 } { tenant_id: 0, product_id: 0, variant_id: 0, created_at: 0, updated_at: 0, created_by: 0, updated_by: 0, __v: 0 }
                    } else if (product[index].linkedTenantVariantId) {
                        const getProduct = await ProductModel.findProduct({ _id: product[index].parentId, is_deleted: false }, { 'variants.type': 1 })
                        var parentImage = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: product[index].linkedParentId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 })
                        if (getProduct.variants.type != "Size") {
                            productImages = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: product[index].linkedTenantVariantId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 }) // { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 } { tenant_id: 0, product_id: 0, variant_id: 0, created_at: 0, updated_at: 0, created_by: 0, updated_by: 0, __v: 0 }
                        }
                    }

                    const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

                    // linked tenant parent products copy images
                    for (let i = 0; i < parentImage?.length; i++) {
                        const productIndex = parentImage[i].image_name.lastIndexOf('_P')
                        const newImageName = product[index].parentId + parentImage[i].image_name.substring(productIndex)
                        const copyImage = await fileUpload.copyImage('web/', body.tenantId, body.linkedTenantId, parentImage[i].image_name, newImageName)  //will change copImage Bucket destination path remove last + '/'
                        const details = {
                            tenantId: body.tenantId,
                            imageName: newImageName,
                            imageSize: parentImage[i].image_size,
                            productVariantId: product[index].parentId,
                            s3Url: VALUES.awsPublicBucketBaseURL + VALUES.awsBucketName + 'product/' + 'web/' + body.tenantId + '/' + newImageName,
                            imageNumber: parentImage[i].image_number
                        }

                        if (copyImage.status === STATUS_CODES.SUCCESS) {
                            await ImageModel.addUpdateImage(details, req.headers)
                        } else {
                            notCopyImages.push(parentImage[i].image_name)
                        }
                    }

                    // linked tenant products copy images
                    for (let i = 0; i < productImages?.length; i++) {
                        const productIndex = productImages[i].image_name.lastIndexOf('_P')
                        const newImageName = (product[index].variantId ? product[index].variantId : product[index].productId) + productImages[i].image_name.substring(productIndex)
                        const copyImage = await fileUpload.copyImage('web/', body.tenantId, body.linkedTenantId, productImages[i].image_name, newImageName) //will change copImage Bucket destination path remove last + '/'
                        const details = {
                            tenantId: body.tenantId,
                            imageName: newImageName,
                            imageSize: productImages[i].image_size,
                            productVariantId: product[index].variantId ? product[index].variantId : product[index].productId,
                            s3Url: VALUES.awsPublicBucketBaseURL + VALUES.awsBucketName + 'product/' + 'web/' + body.tenantId + '/' + newImageName,
                            imageNumber: productImages[i].image_number
                        }

                        if (copyImage.status === STATUS_CODES.SUCCESS) {
                            await ImageModel.addUpdateImage(details, req.headers)

                        } else {
                            notCopyImages.push(productImages[i].image_name)
                        }
                    }
                }

                await existProcess.deleteOne();
                return res.handler.success("all_copy_images");

            } else if (body.type === DATA_SHEET.APPROVE_TYPE.SELECTED) {

                for (let index = 0; index < body.product?.length; index++) {

                    if (body.product[index].linkedTenantProductId) {
                        productImages = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: body.product[index].linkedTenantProductId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 }) // { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 } { tenant_id: 0, product_id: 0, variant_id: 0, created_at: 0, updated_at: 0, created_by: 0, updated_by: 0, __v: 0 }
                    } else if (body.product[index].linkedTenantVariantId) {
                        const getProduct = await ProductModel.findProduct({ _id: body.product[index].parentId, is_deleted: false }, { 'variants.type': 1 })
                        var parentImage = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: body.product[index].linkedParentId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 })
                        if (getProduct.variants.type != "Size") {
                            productImages = await ImageModel.linkedProductImages({ tenant_id: body.linkedTenantId, product_variant_id: body.product[index].linkedTenantVariantId, group_id: null }, { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 }) // { image_name: 1, image_number: 1, image_size: 1, s3_url: 1 } { tenant_id: 0, product_id: 0, variant_id: 0, created_at: 0, updated_at: 0, created_by: 0, updated_by: 0, __v: 0 }
                        }
                    }

                    const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

                    // linked tenant parent products copy images
                    for (let i = 0; i < parentImage?.length; i++) {
                        const productIndex = parentImage[i].image_name.lastIndexOf('_P')
                        const newImageName = body.product[index].parentId + parentImage[i].image_name.substring(productIndex)
                        const copyImage = await fileUpload.copyImage('web/', body.tenantId, body.linkedTenantId, parentImage[i].image_name, newImageName) //will change copImage Bucket destination path remove last + '/'
                        const details = {
                            tenantId: body.tenantId,
                            imageName: newImageName,
                            imageSize: parentImage[i].image_size,
                            productVariantId: body.product[index].parentId,
                            s3Url: VALUES.awsPublicBucketBaseURL + VALUES.awsBucketName + 'product/' + 'web/' + body.tenantId + '/' + newImageName,
                            imageNumber: parentImage[i].image_number
                        }

                        if (copyImage.status === STATUS_CODES.SUCCESS) {
                            await ImageModel.addUpdateImage(details, req.headers)
                        } else {
                            notCopyImages.push(parentImage[i].image_name)
                        }
                    }

                    // linked tenant products copy images
                    for (let i = 0; i < productImages?.length; i++) {
                        const productIndex = productImages[i].image_name.lastIndexOf('_P')
                        const newImageName = (body.product[index].variantId ? body.product[index].variantId : body.product[index].productId) + productImages[i].image_name.substring(productIndex)
                        const copyImage = await fileUpload.copyImage('web/', body.tenantId, body.linkedTenantId, productImages[i].image_name, newImageName) //will change copImage Bucket destination path remove last + '/'
                        const details = {
                            tenantId: body.tenantId,
                            imageName: newImageName,
                            imageSize: productImages[i].image_size,
                            productVariantId: body.product[index].variantId ? body.product[index].variantId : body.product[index].productId,
                            s3Url: VALUES.awsPublicBucketBaseURL + VALUES.awsBucketName + 'product/' + 'web/' + body.tenantId + '/' + newImageName,
                            imageNumber: productImages[i].image_number
                        }

                        if (copyImage.status === STATUS_CODES.SUCCESS) {
                            await ImageModel.addUpdateImage(details, req.headers)
                        } else {
                            notCopyImages.push(productImages[i].image_name)
                        }
                    }
                }

                await existProcess.deleteOne();
                return res.handler.success("selected_copy_images");
            }

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    /**
     * 
     * @param {*} req 
     * @param {*} res 
     * @returns Array of objects with itemNumber & imageUrl
     * @description Designed for Faris' amazon_db sheet only. Getting imageUrls only for single and variant products
     */
    getImagesByItemNumbers = async (req, res) => {
        try {
            const {
                tenantId,
                itemNumbers,
            } = req.body

            const products = await ImageGetService.fetchProductsByItmNum(tenantId, itemNumbers)
            if (!products.length) {
                return res.handler.notFound("product_not_found")
            }

            const imageList = await ImageGetService.getImagesByItemNumbers(products, tenantId)
            return res.handler.success(null, imageList)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = ImageController;

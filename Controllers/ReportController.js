const axios = require("axios")
const bwipJs = require("bwip-js")
const isEmpty = require('lodash.isempty')

const OrderModel = new (require("../Models/OrderModel"))()
const MasterDataModel = new (require("../Models/MasterDataModel"))()
const InternalServiceModel = new (require("../Models/InternalServiceModel"))()

const {
    VALUES,
    REPORTS,
    ORDER_ITEM_LISTING_TYPES,
    DEAL_TYPE,
    DISCOUNT_TYPE,
} = require("../Configs/constants")

const {
    arrayBufferToBase64,
    formatAmount,
    toLeanOption,
} = require("../Utils/helpers")

class ReportController {

    async generateShippingLabelPayload(req, payload) {
        const { reportData } = req.body

        const {
            logo,
            orderId,
            date,
            numberOfCopies,
            from,
            to,
        } = reportData

        const {
            tenantLegalName,
            streetAddress,
            region,
            city,
            mobileNumber,
            phoneNumber = ""
        } = from

        const {
            customerLegalName,
            externalId,
            shippingRegion,
            shippingCity,
            shippingMobileNumber,
        } = to

        const labels = Array.from({ length: numberOfCopies }, (v, i) => i + 1)

        const [
            qrCodeBuffer,
            barcodeBuffer,
        ] =
            await Promise.all([
                /* generates qrCodeBuffer */
                bwipJs.toBuffer({
                    bcid: "qrcode",
                    text: orderId,
                    textxalign: "center",
                    scale: 2,
                    paddingwidth: 5,
                    paddingtop: 5,
                    paddingbottom: 4,
                }),

                /* generates barcodeBuffer */
                bwipJs.toBuffer({
                    bcid: "code128",
                    text: orderId,
                })
            ])

        const qrCode = "data:image/png;base64," + arrayBufferToBase64(qrCodeBuffer)
        const barcode = "data:image/png;base64," + arrayBufferToBase64(barcodeBuffer)

        payload["data"] = {
            logo,
            orderId,
            date,
            labels,
            qrCode,
            barcode,
            "from": {
                tenantLegalName,
                streetAddress,
                region,
                city,
                mobileNumber,
                phoneNumber,
            },
            "to": {
                customerLegalName,
                externalId,
                "region": shippingRegion,
                "city": shippingCity,
                "mobileNumber": shippingMobileNumber,
            },
        }
        return payload
    }

    async generateOrderDetailPayload(req, payload) {
        const {
            tenantId,
            orderId,
            currency,
            timezone,
        } = req.body.reportData

        const [
            tenantInfo,
            shippingLabelInfo,
            tenantAppSettings,
            orderDetail,
            orderItems,
        ] =
            await Promise.all([
                /* 1. Fetch tenantInfo */
                MasterDataModel.getTenantInfo(
                    tenantId,
                    `
                        -_id
                        legal_name
                        street_name
                    `,
                    toLeanOption,
                    [
                        {
                            "path": "region",
                            "select": "-_id name"
                        },
                        {
                            "path": "city",
                            "select": "-_id name"
                        },
                    ],
                ),

                /* 2. Fetch shippingLabelInfo */
                InternalServiceModel.getShippingLabel({
                    "tenantId": tenantId,
                    "projection": "-_id tenant_id shipping_label_logo",
                    "options": toLeanOption,
                }),

                /* 3. Fetch tenantAppSettings */
                InternalServiceModel.tenantAppSettings(tenantId, "decimal_points"),

                /* 4. Fetch orderDetail */
                OrderModel.findOrder(
                    { _id: orderId },
                    `
                        -_id
                        order_number
                        customer_user_role_id
                        customer_legal_name
                        customer_primary_contact_name
                        external_id
                        tax_calculation_info
                        total_amount
                        total_tax
                        created_at
                    `,
                    toLeanOption,
                ),

                /* 5. Fetch orderItems */
                OrderModel.getOrder({
                    "query": {
                        tenantId,
                        orderId,
                        orderItemListingType: ORDER_ITEM_LISTING_TYPES.ALL,
                        orderItemProjection: [
                            "product_variant_id",
                            "product_name",
                            "variant_name",
                            "group_name",
                            "original_price",
                            "quantity",
                            "uom_name",
                            "item_number",
                            "deal_info",
                        ]
                    }
                }),
            ])

        const customerInfo = await InternalServiceModel.getCustomer({
            "_id": orderDetail.customer_user_role_id,
            "projection": "-_id tenant_id collection_name customer_name",
            "options": { lean: true },
            "populate": [
                {
                    path: "user_id",
                    select: "_id mobile_number"
                },
            ]
        })

        const { decimal_points = 0 } = tenantAppSettings || {}

        const order_date =
            momentTimezone(orderDetail.created_at)
                .tz(timezone)
                .format("DD/MM/YYYY")

        const order_sub_total = formatAmount(orderDetail.total_amount, decimal_points, currency)
        const total_tax = formatAmount(orderDetail.total_tax, decimal_points, currency)
        const order_total = formatAmount((orderDetail.total_amount + orderDetail.total_tax), decimal_points, currency)

        const totalTaxes = []
        if (!isEmpty(orderDetail.tax_calculation_info)) {
            const taxes = Object.values(orderDetail.tax_calculation_info)

            taxes.forEach(tax => {
                const { type, tax_name, calculated_tax, group_taxes } = tax

                if (type === "SINGLE") {
                    totalTaxes.push({
                        tax_name,
                        total_tax: formatAmount(calculated_tax, decimal_points, currency)
                    })
                }
                else if (type === "GROUP") {
                    const groupTaxes = Object.values(group_taxes)
                    const totalGroupTaxes = []

                    groupTaxes.forEach(tax => {
                        const { tax_name, calculated_tax } = tax
                        totalGroupTaxes.push({
                            tax_name,
                            total_tax: formatAmount(calculated_tax, decimal_points, currency)
                        })
                    })

                    totalTaxes.push({
                        tax_name,
                        total_tax: formatAmount(calculated_tax, decimal_points, currency),
                        group_taxes: totalGroupTaxes,
                    })
                }
            })
        }

        delete orderDetail.customer_user_role_id
        delete orderDetail.created_at
        delete orderDetail.tax_calculation_info
        delete orderDetail.total_amount

        const order = {
            ...orderDetail,
            order_date,
            total_taxes: totalTaxes,
            customer_primary_mobile_number: customerInfo?.user_id?.mobile_number,

            order_sub_total,
            total_tax,
            order_total,
        }

        const orders_items = []

        orderItems.list.forEach(item => {
            const {
                group_cover_image,
                cover_image,
                parent_cover_image,
            } = item

            const {
                product_name,
                variant_name,
                group_name,
                original_price,
                quantity,
                uom_name,
                item_number,
                deal_info,
            } = item.order_items

            let item_image =
                cover_image?.s3_url ||
                group_cover_image?.s3_url ||
                parent_cover_image?.s3_url

            if (item_image) {
                item_image = item_image.replace("/web/", "/logo/")
            }
            let item_name = product_name

            if (variant_name) {
                item_name +=
                    " - " + (
                        group_name
                            ? group_name + " / "
                            : ""
                    )
                    + variant_name
            }

            const raw_item_price = original_price
            let item_qty = quantity
            let item_price = formatAmount(raw_item_price, decimal_points)
            let item_subtotal = formatAmount(item_qty * raw_item_price, decimal_points)

            let deal_label = ""
            let deal_type = ""
            let item_original_price = ""

            if (deal_info) {
                const {
                    deal_type: dealType,
                    discount_type,
                    percent,
                    amount,
                    bulk_price,
                    discounted_price,
                    buy_product,
                    free_product,
                } = deal_info

                const calculatePriceAndSubTotal = (price) => {
                    item_price = formatAmount(price, decimal_points)
                    item_subtotal = formatAmount(item_qty * price, decimal_points)
                }

                switch (dealType) {
                    case DEAL_TYPE.BULK_PRICING: {
                        deal_label = "Bulk Pricing"
                        item_original_price = item_price

                        calculatePriceAndSubTotal(bulk_price)
                        break;
                    }

                    case DEAL_TYPE.DISCOUNT: {
                        deal_label = `${formatAmount(percent, decimal_points)}% off`
                        item_original_price = item_price

                        if (discount_type === DISCOUNT_TYPE.AMOUNT) {
                            deal_label = `${formatAmount(amount, decimal_points, currency)} off`
                        }

                        calculatePriceAndSubTotal(discounted_price)
                        break;
                    }

                    case DEAL_TYPE.BUY_X_AND_GET_Y: {
                        /** Calculate BUY_X_AND_GET_Y item */
                        deal_label = `${buy_product}+${free_product}`

                        calculatePriceAndSubTotal(raw_item_price)

                        orders_items.push({
                            item_image,
                            item_number,
                            item_name,
                            item_qty,
                            item_original_price,
                            item_price,
                            item_subtotal,
                            deal_label,
                            deal_type,
                            uom_name,
                        })

                        /** Calculate GIFT item */
                        deal_label = "Gift"
                        deal_type = "GIFT"
                        item_image = VALUES.giftIcon
                        item_qty = 1
                        item_original_price = item_price

                        calculatePriceAndSubTotal(0)
                        break;
                    }

                    default:
                        break;
                }
            }

            orders_items.push({
                item_image,
                item_number,
                item_name,
                item_qty,
                item_original_price,
                item_price,
                item_subtotal,
                deal_label,
                deal_type,
                uom_name,
            })
        })

        payload["data"] = {
            logo: shippingLabelInfo.shipping_label_logo,
            tenant: {
                legal_name: tenantInfo.legal_name,
                shipping_address: tenantInfo.street_name,
                region_name: tenantInfo.region.name,
                city_name: tenantInfo.city.name,
            },
            order,
            orders_items,
        }
        return payload
    }

    getReportPayload(req) {
        const { reportType } = req.body

        const payload = {
            "template": {
                "name": REPORTS.TEMPLATE[reportType],
            },
            "data": {}
        }

        if (reportType === REPORTS.REPORT_TYPE.SHIPPING_LABEL) {
            return this.generateShippingLabelPayload(req, payload)
        }
        else if (reportType === REPORTS.REPORT_TYPE.ORDER_DETAIL) {
            return this.generateOrderDetailPayload(req, payload)
        }
    }

    generateReport = async (req, res) => {
        try {
            const payload = await this.getReportPayload(req)
            const token = Buffer.from(`${VALUES.jsReportUsername}:${VALUES.jsReportPassword}`, "utf-8").toString("base64")

            const response = await axios({
                url: VALUES.jsReportBaseURL,
                responseType: 'arraybuffer',
                method: 'post',
                headers: {
                    "Authorization": `Basic ${token}`,
                    "Accept": "application/pdf",
                },
                data: payload
            })

            const base64String = arrayBufferToBase64(response.data)
            return res.handler.success(null, base64String)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = ReportController

const MasterDataModel = new (require("../Models/MasterDataModel"))()

const { MASTER_DATA } = require("../Configs/ApiType")
const { MASTER_DATA_ENTITIES, ENTITY_STATUS } = require("../Configs/constants")

class MasterDataController {
    async addAttribute(req, res) {
        const entity = MasterDataModel.getEntityName(req.route.path)
        const entityLowerCase = entity.toLowerCase()

        try {
            const apiType = MASTER_DATA[entity].ADD
            const existingAttribute = await MasterDataModel.getAttribute(req, apiType)

            if (existingAttribute?._id) {
                return res.handler.conflict(`${entityLowerCase}_name_exists`)
            }
            const attribute = await MasterDataModel.addAttribute(req, apiType)

            return res.handler.success(`${entityLowerCase}_add_success`, attribute)
        }
        catch (error) {
            if (error.code === 11000) {
                return res.handler.conflict(`${entityLowerCase}_name_exists`, undefined, error)
            }
            else {
                return res.handler.serverError(error)
            }
        }
    }

    async editAttribute(req, res) {
        try {
            const entity = MasterDataModel.getEntityName(req.route.path)
            const entityLowerCase = entity.toLowerCase()
            const apiType = MASTER_DATA[entity].EDIT

            let attribute = await MasterDataModel.getAttributeById(req.body._id, apiType)

            if (!attribute?._id || attribute?.is_deleted) {
                return res.handler.notFound(`${entityLowerCase}_not_found`)
            }
            const existingAttribute = await MasterDataModel.getAttribute(req, apiType)

            if (
                existingAttribute?._id &&
                !existingAttribute._id.equals(attribute._id)
            ) {
                return res.handler.conflict(`${entityLowerCase}_name_exists`)
            }
            attribute = await MasterDataModel.editAttribute(req, apiType, attribute)

            return res.handler.success(`${entityLowerCase}_edit_success`, attribute)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getAttributeList(req, res) {
        try {
            const entity = MasterDataModel.getEntityName(req.route.path)
            const entityLowerCase = entity.toLowerCase()
            const apiType = MASTER_DATA[entity].LIST

            const list = await MasterDataModel.getAttributeList(req, apiType)
            return res.handler.success(`${entityLowerCase}_list_success`, list)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async deleteAttribute(req, res) {
        try {
            const entity = MasterDataModel.getEntityName(req.route.path)
            const entityLowerCase = entity.toLowerCase()
            const apiType = MASTER_DATA[entity].DELETE
            const updateField = { "is_deleted": true }

            const response = await MasterDataModel.updateAttributes(req, updateField, apiType)

            if (response?.modifiedCount) {
                return res.handler.success(`${entityLowerCase}_delete_success`)
            }
            return res.handler.success(`${entityLowerCase}_delete_unsuccess`)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateAttributes(req, res) {
        try {
            const { isActive, entity } = req.body
            const apiType = MASTER_DATA[entity].EDIT
            const updateField = { "is_active": isActive }

            const response = await MasterDataModel.updateAttributes(req, updateField, apiType)

            if (response?.modifiedCount) {
                return res.handler.success(`${entity.toLowerCase()}_update_status_success`)
            }
            return res.handler.success(`${entity.toLowerCase()}_update_status_unsuccess`)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateAttributeAssociation(req, res) {
        try {
            const entity = MASTER_DATA_ENTITIES.ATTRIBUTE_SET
            const apiType = MASTER_DATA[entity].EDIT

            let attributeSet = await MasterDataModel.getAttributeById(req.body.attributeSetId, apiType)

            if (!attributeSet?._id || attributeSet?.is_deleted) {
                return res.handler.notFound(`${entity.toLowerCase()}_not_found`)
            }
            const response = await MasterDataModel.updateAttributeAssociation(req)

            if (response?.modifiedCount) {
                return res.handler.success("attribute_association_update_success")
            }
            return res.handler.success("attribute_association_update_unsuccess")
        }
        catch (error) {
            return res.handler.serverError(err)
        }
    }

    async getAssociatedAttributes(req, res) {
        try {
            const attributeSet = await MasterDataModel
                .getAttributeById(
                    req.query._id,
                    MASTER_DATA[MASTER_DATA_ENTITIES.ATTRIBUTE_SET].LIST
                )

            if (!attributeSet?._id || attributeSet?.is_deleted) {
                return res
                    .handler
                    .notFound(`${MASTER_DATA_ENTITIES.ATTRIBUTE_SET.toLowerCase()}_not_found`)
            }

            if (!attributeSet?.attribute_ids?.length) {
                return res
                    .handler
                    .notFound(`${MASTER_DATA_ENTITIES.ASSOCIATED_ATTRIBUTES.toLowerCase()}_not_found`)
            }

            const list = await MasterDataModel
                .getAttributeList(
                    req,
                    MASTER_DATA[MASTER_DATA_ENTITIES.ASSOCIATED_ATTRIBUTES].LIST,
                    attributeSet.attribute_ids
                )
            return res.handler.success("associated_attribute_list_success", list)
        }
        catch (error) {
            return res.handler.serverError(err)
        }
    }

    async masterPriceList(req, res) {
        try {
            const { tenantId, status = ENTITY_STATUS.ACTIVE } = req.query

            const list = await MasterDataModel.masterPriceList(tenantId, status)
            return res.handler.success("attribute_list_success", list)
        } catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = MasterDataController

const express = require('express')
const router = express.Router()

const ConfigurationValidator = require("../Middleware/validators/ConfigurationValidator");
const AuthenticateUser = require('../Middleware/authentication').authentication
const ConfigurationController = new (require("../Controllers/ConfigurationController"))();
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route('/tax')
    .post(
        ConfigurationValidator.addAndEditTax,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX]: [ACTIONS.CREATE, ACTIONS.EDIT],
        }),
        ConfigurationController.addAndEditTax
    )
    .put(
        ConfigurationValidator.changeTaxStatus,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX]: ACTIONS.EDIT
        }),
        ConfigurationController.changeTaxStatus
    )
    .get(
        ConfigurationValidator.taxList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX]: ACTIONS.VIEW
        }),
        ConfigurationController.taxList
    )
    .delete(
        ConfigurationValidator.deleteTax,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX]: ACTIONS.DELETE
        }),
        ConfigurationController.deleteTax
    )

router.route('/master-tax')
    .post(
        ConfigurationValidator.masterTaxSetting,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX_SETTING]: ACTIONS.EDIT
        }),
        ConfigurationController.masterTaxSetting
    )
    .get(
        ConfigurationValidator.getMasterTaxSetting,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX_SETTING]: ACTIONS.VIEW
        }),
        ConfigurationController.getMasterTaxSetting
    )

module.exports = router;

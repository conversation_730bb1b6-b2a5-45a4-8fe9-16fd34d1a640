const express = require('express')
const router = express.Router();

const OrderController = new (require("../Controllers/OrderController"));
const OrderReportController = new (require('../Controllers/OrderReportController'));

const OrderValidators = require("../Middleware/validators/OrderValidator");

const AuthenticateUser = require('../Middleware/authentication').authentication
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route("/")
    .post(
        OrderValidators.placeOrder,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.CREATE
        }),
        OrderController.placeOrder
    )
    .get(
        OrderValidators.getOrder,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.getOrder
    )
    .put(
        OrderValidators.updateOrderValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.EDIT
        }),
        OrderController.updateCustomerWithOrders
    )

router.route("/orders")
    .get(
        OrderValidators.listOrders,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.listOrders
    )
    .put(
        OrderValidators.updateOrderStatus,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.EDIT
        }),
        OrderController.updateOrderStatus
    )

router.route("/orderStats")
    .get(
        OrderValidators.getOrderStats,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.getOrderStats
    )

router.route("/dashboardSummary")
    .get(
        OrderValidators.getDashboardSummary,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.getDashboardSummary
    )

router.route("/orderReport")
    .get(
        OrderValidators.getOrderReportList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderReportController.getOrderReportList
    )

module.exports = router

const express = require('express')
const router = express.Router();

const OrderController = new (require("../Controllers/OrderController"));
const OrderExportController = new (require('../Controllers/OrderExportController'));

const SendWhatsAppMessageController = new (require("../Controllers/SendWhatsAppMessage"))();

const OrderValidators = require("../Middleware/validators/OrderValidator");
const SendWhatsAppMessageValidator = require("../Middleware/validators/SendWhatsAppMessageValidator");

const AuthenticateUser = require('../Middleware/authentication').authentication
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route('/cart')
    .post(
        OrderValidators.cartActions,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: [
                ACTIONS.CREATE,
                ACTIONS.VIEW,
                ACTIONS.EDIT,
            ]
        }),
        OrderController.cartActions
    )
    .get(
        OrderValidators.cartDetails,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.cartDetails
    )
    .delete(
        OrderValidators.deleteCartItems,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.DELETE
        }),
        OrderController.deleteCartItems
    )

router.route("/itemCount")
    .get(
        OrderValidators.getCartItemCount,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.getCartItemCount
    )

router.route("/draft")
    .post(
        OrderValidators.createDraft,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.CREATE
        }),
        OrderController.createDraft
    )
    .get(
        OrderValidators.draftDetails,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.draftDetails
    )

router.route("/drafts")
    .get(
        OrderValidators.draftListing,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        OrderController.draftListing
    )
    .delete(
        OrderValidators.deleteDrafts,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.DELETE
        }),
        OrderController.deleteDrafts
    )

router.route("/checkOrderUsers")
    .get(
        OrderValidators.checkOrderUsers,
        AuthenticateUser,
        /* verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }), */
        OrderController.checkOrderUsers
    )

router.route("/draftToCart")
    .post(
        OrderValidators.draftToCart,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.CREATE
        }),
        OrderController.draftToCart
    )

router.route("/sendReasonMessage")
    .post(
        SendWhatsAppMessageValidator.sendReasonMessage,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT,
            ]
        }),
        SendWhatsAppMessageController.sendReasonMessage
    )

router.route("/checkPreApproved")
    .put(
        OrderValidators.checkPreApproved,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.EDIT
        }),
        OrderController.checkPreApproved
    )

router.route("/exportTask")
    .post(
        OrderValidators.exportOrders,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET_ORDERS]: ACTIONS.VIEW
        }),
        OrderExportController.exportOrders
    )

router.route("/status")
    .get(
        OrderValidators.getOrderStatusList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET_ORDERS]: ACTIONS.VIEW
        }),
        OrderExportController.getOrderStatusList
    )

module.exports = router;

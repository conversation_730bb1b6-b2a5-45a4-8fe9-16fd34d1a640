const express = require('express')
const router = express.Router()

const CategoryValidator = require("../Middleware/validators/CategoryValidator");
const AuthenticateUser = require('../Middleware/authentication').authentication
const CategoryController = new (require("../Controllers/CategoryController"))();
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');


router.route('/')
    .post(
        CategoryValidator.addAndUpdateCategory,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.CREATE
        }),
        CategoryController.addAndUpdateCategory
    )
    .get(
        CategoryValidator.categoryList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
        }),
        CategoryController.categoryList
    )
    .put(
        CategoryValidator.sequenceCategory,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.EDIT
        }),
        CategoryController.sequenceCategory
    )
    .delete(
        CategoryValidator.deleteCategory,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.DELETE
        }),
        CategoryController.deleteCategory
    )

router.route('/get-category')
    .get(
        CategoryValidator.getCategory,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW
        }),
        CategoryController.getCategory
    )
    .post(
        CategoryValidator.categoryListValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW
        }),
        CategoryController.getAllCategoryList
    )

router.route('/allProductList')
    .get(
        CategoryValidator.allCategoryProductList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        CategoryController.allCategoryProductList
    )

router.route('/categoryProductList')
    .get(
        CategoryValidator.productList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        CategoryController.productList
    )
    .post(
        CategoryValidator.productSequence,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        CategoryController.productSequence
    )

router.route('/dashboard/topCategory')
    .get(
        CategoryValidator.topCategory,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW
        }),
        CategoryController.topCategory
    )

module.exports = router;

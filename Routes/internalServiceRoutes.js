const express = require('express');
const router = express.Router()

const { InternalServiceMiddleware } = require('../Middleware/InternalServiceMiddleware');

const DataSheetValidator = require("../Middleware/validators/DataSheetValidator");
const { tenantIdValidator } = require('../Middleware/validators/CommonValidator');

const InternalServiceController = new (require("../Controllers/InternalServiceController"))();
const ConfigurationController = new (require("../Controllers/ConfigurationController"))();
const OrderController = new (require("../Controllers/OrderController"))
const MasterDataController = new (require("../Controllers/MasterDataController"))()
const DataSheetController = new (require('../Controllers/DataSheetController'))();

const ProductController = new (require("../Controllers/ProductController"))();

router.route("/configuration/tax")
    .post(
        tenantIdValidator,
        InternalServiceMiddleware,
        ConfigurationController.masterTaxSetting
    )

router.route("/dashboard/productCount")
    .get(
        ProductController.totalProductCount
    )

router.route("/dashboard/orderCount")
    .get(
        OrderController.totalOrders
    )

router.route("/dashboard/salesState")
    .post(
        OrderController.getSalesState
    )

router.route("/tenant/masterPriceList")
    .get(
        MasterDataController.masterPriceList
    )

router.route("/tenant/orderStatus")
    .post(
        OrderController.getOrderStats
    )

router.route("/dataSheet")
    .get(
        DataSheetValidator.getDataSheetValidator,
        InternalServiceMiddleware,
        DataSheetController.getDataSheet
    )
    .put(
        DataSheetValidator.updateDataSheetValidator,
        InternalServiceMiddleware,
        DataSheetController.updateDataSheet.bind(DataSheetController)
    )

router.route("/order")
    .get(
        InternalServiceController.getOrderDetails
    )

router.route("/orders")
    .get(
        InternalServiceController.getOrdersList
    )
    .put(
        InternalServiceController.updateOrders
    )

module.exports = router;

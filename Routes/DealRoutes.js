const express = require('express')
const router = express.Router()

const {
	PERMISSION_MODULES,
	ACTIONS
} = require('../Configs/constants');

const DealValidator = require("../Middleware/validators/DealValidator")
const DealController = new (require("../Controllers/DealController.js"));
const AuthenticateUser = require('../Middleware/authentication').authentication
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

router.route("/")
	.post(
		DealValidator.createDeal,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.CREATE
		}),
		DealController.createDeal
	)
	.put(
		DealValidator.updateDeal,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.EDIT
		}),
		DealController.updateDeal
	)
	.get(
		DealValidator.dealList,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.dealList
	)
	.delete(
		DealValidator.dealDelete,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.DELETE
		}),
		DealController.deleteDeals
	)
	.patch(
		DealValidator.dealStatus,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.EDIT
		}),
		DealController.updateDealStatus
	)

router.route("/detail")
	.get(
		DealValidator.dealDetail,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.getDealDetail
	)

router.route("/productList")
	.get(
		DealValidator.productList,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.productList
	)

router.route("/dealProduct")
	.post(
		DealValidator.addDealProduct,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.CREATE
		}),
		DealController.addDealProduct
)
	.get(
		DealValidator.dealProductList,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.dealProductList
	)

router.route("/updateProductList")
	.delete(
		DealValidator.deletedDealProduct,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.DELETE
		}),
		DealController.deleteDealProducts
	)
	.post(
		DealValidator.productStatus,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.EDIT
		}),
		DealController.updateDealProductStatus
	)
	.put(
		DealValidator.changeDealProductSequence,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.EDIT
		}),
		DealController.changeDealProductSequence
	)
	.get(
		DealValidator.nextDealProductList,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.nextDealProductList
	)

router.route('/dealsProductList')
	.get(
		DealValidator.dealsProductList,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.getRunningDealsProductList
	)
	.post(
		DealValidator.productListValidator,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.getProductListByItemNumbers
	)

router.route("/checkValidItems")
	.post(
		DealValidator.checkValidItemsValidator,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.checkValidItems
	)

router.route('/dealStatistics')
	.post(
		DealValidator.updateDealStatistics,
		AuthenticateUser,
		verifyPermission({
			[PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
		}),
		DealController.updateDealStatistics
	)

/*
router.route("/scripts")
	.post(DealController.scripts)
*/

module.exports = router;

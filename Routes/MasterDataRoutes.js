const express = require('express')
const router = express.Router()

const AuthenticateUser = require('../Middleware/authentication').authentication
const MasterDataController = new (require("../Controllers/MasterDataController"))()
const CommonValidator = require('../Middleware/validators/CommonValidator')
const MasterDataValidator = require('../Middleware/validators/master_data/MasterDataValidator')
const MasterAttributeValidator = require('../Middleware/validators/master_data/MasterAttributeValidator')
const MasterAttributeSetValidator = require('../Middleware/validators/master_data/MasterAttributeSetValidator')
const MasterBrandValidator = require('../Middleware/validators/master_data/MasterBrandValidator')
const MasterUnitValidator = require('../Middleware/validators/master_data/MasterUnitValidator')
const MasterPriceValidator = require('../Middleware/validators/master_data/MasterPriceValidator')

const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route("/attribute")
    .post(
        MasterAttributeValidator.addAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE]: ACTIONS.CREATE
        }),
        MasterDataController.addAttribute
    )
    .put(
        MasterAttributeValidator.editAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE]: ACTIONS.EDIT
        }),
        MasterDataController.editAttribute
    )
    .get(
        CommonValidator.getAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.VIEW
        }),
        MasterDataController.getAttributeList
    )
    .delete(
        CommonValidator.deleteAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE]: ACTIONS.DELETE
        }),
        MasterDataController.deleteAttribute
    )

router.route("/updateStatus")
    .put(
        MasterDataValidator.updateStatusValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA]: ACTIONS.EDIT
        }),
        MasterDataController.updateAttributes
    )

router.route("/attributeSet")
    .post(
        MasterAttributeSetValidator.addAttributeSetValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.CREATE
        }),
        MasterDataController.addAttribute
    )
    .put(
        MasterAttributeSetValidator.editAttributeSetValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.EDIT
        }),
        MasterDataController.editAttribute
    )
    .get(
        CommonValidator.getAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.VIEW
        }),
        MasterDataController.getAttributeList
    )
    .delete(
        CommonValidator.deleteAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.DELETE
        }),
        MasterDataController.deleteAttribute
    )

router.route("/brand")
    .post(
        MasterBrandValidator.addBrandValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_BRAND]: ACTIONS.CREATE
        }),
        MasterDataController.addAttribute
    )
    .put(
        MasterBrandValidator.editBrandValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_BRAND]: ACTIONS.EDIT
        }),
        MasterDataController.editAttribute
    )
    .get(
        CommonValidator.getAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_BRAND]: ACTIONS.VIEW
        }),
        MasterDataController.getAttributeList
    )
    .delete(
        CommonValidator.deleteAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_BRAND]: ACTIONS.DELETE
        }),
        MasterDataController.deleteAttribute
    )

router.route("/unit")
    .post(
        MasterUnitValidator.addUnitValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_UOM]: ACTIONS.CREATE
        }),
        MasterDataController.addAttribute
    )
    .put(
        MasterUnitValidator.editUnitValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_UOM]: ACTIONS.EDIT
        }),
        MasterDataController.editAttribute
    )
    .get(
        CommonValidator.getAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_UOM]: ACTIONS.VIEW,
        }),
        MasterDataController.getAttributeList
    )
    .delete(
        CommonValidator.deleteAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_UOM]: ACTIONS.DELETE
        }),
        MasterDataController.deleteAttribute
    )

router.route("/price")
    .post(
        MasterPriceValidator.addPriceValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_PRICE_LIST]: ACTIONS.CREATE
        }),
        MasterDataController.addAttribute
    )
    .put(
        MasterPriceValidator.editPriceValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_PRICE_LIST]: ACTIONS.EDIT
        }),
        MasterDataController.editAttribute
    )
    .get(
        CommonValidator.getAttributeValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.INVENTORY]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_PRICE_LIST]: ACTIONS.VIEW
        }),
        MasterDataController.getAttributeList
    )
    .delete(
        CommonValidator.deleteAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_PRICE_LIST]: ACTIONS.DELETE
        }),
        MasterDataController.deleteAttribute
    )

router.route("/attributeAssociation")
    .put(
        MasterAttributeSetValidator.updateAttributeAssociationValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.EDIT
        }),
        MasterDataController.updateAttributeAssociation
    )
    .get(
        CommonValidator.getAttributeValidator,
        MasterAttributeSetValidator.getAssociatedAttributesValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_ATTRIBUTE_SET]: ACTIONS.VIEW
        }),
        MasterDataController.getAssociatedAttributes
    )

module.exports = router;

const express = require('express')
const router = express.Router()

const categoryRoutes = require("./CategoryRoutes");
const MasterDataRoutes = require("./MasterDataRoutes");
const ProductRoutes = require("./ProductRoutes");
const ConfigurationRoutes = require("./ConfigurationRoutes");
const OrderRoutes = require("./OrderRoutes")
const internalServiceRoutes = require("./internalServiceRoutes");
const ReportRoutes = require("./ReportRoutes");
const DealRoutes = require("./DealRoutes");
const DataSyncRoutes = require("./DataSyncRoutes");

const ImageRoutes = require("./ImageRoutes");
const Order2Routes = require("./OrderRoutes2");

router.get("/", (req, res) => {
    res.status(STATUS_CODES.SUCCESS)
        .send("Welcome to " + process.env.PROJECT_NAME)
});

//V2 routes
router.use("/productV2", ProductRoutes)
router.use("/imageV2", ImageRoutes)
router.use("/orderV2", Order2Routes)

router.use("/masterData", MasterDataRoutes);
router.use("/category", categoryRoutes);
router.use("/configuration", ConfigurationRoutes);
router.use("/order", OrderRoutes)
router.use("/internalProductService", internalServiceRoutes);
router.use("/report", ReportRoutes)
router.use("/deal", DealRoutes)
router.use("/dataSync", DataSyncRoutes);

module.exports = router

const express = require('express')
const router = express.Router();


const ProductValidator = require("../Middleware/validators/ProductValidator");
const AuthenticateUser = require('../Middleware/authentication').authentication;
const ProductController = new (require("../Controllers/ProductController"))();
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route("/")
    .post(
        ProductValidator.addProduct,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.CREATE
        }),
        ProductController.addProduct
    )
    .get(
        ProductValidator.getProductDetails,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.getProductDetails
    )
    .put(
        ProductValidator.editProduct,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        ProductController.editProduct
    )

router.route("/products")
    .get(
        ProductValidator.listProducts,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.listProducts
    )
    .delete(
        ProductValidator.deleteProducts,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.DELETE
        }),
        ProductController.deleteProducts
    )
    .put(
        ProductValidator.updateProductsStatus,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        ProductController.updateProductsStatus
    )

router.route("/search")
    .get(
        ProductValidator.searchProductValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.searchProducts
    )

router.route("/existItemNumber")
    .get(
        ProductValidator.checkExistingItemNumber,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.checkExistingItemNumber
    )

router.route("/inventoryProductList")
    .get(
        ProductValidator.inventoryProductList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.inventoryProductList
    )

router.route("/rewardProductList")
    .get(
        ProductValidator.rewardProductList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT,
            ]
        }),
        ProductController.rewardProductList
    )

router.route("/updateVariantGroupInfo")
    .put(
        ProductValidator.updateVariantGroupInfo,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        ProductController.updateVariantGroupInfo
    )

router.route("/variantType")
    .put(
        ProductValidator.updateVariantTypeInfo,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        ProductController.updateVariantTypeInfo
    )

router.route("/changeVariantOrder")
    .put(
        ProductValidator.changeVariantOrder,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: [ACTIONS.CREATE, ACTIONS.EDIT]
        }),
        ProductController.changeVariantOrder
    )

router.route("/productCount")
    .get(
        ProductValidator.productCount,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.productCount
    )

router.route("/barcodeDetails")
    .get(
        ProductValidator.barcodeDetails,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.barcodeDetails
    )

router.route("/favoriteProduct")
    .post(
        ProductValidator.favoriteProduct,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.favoriteProduct
    )
    .get(
        ProductValidator.favoriteProductList,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.favoriteProductList
    )

router.route("/tags")
    .get(
        ProductValidator.getTagsValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        ProductController.getTagList
    )

router.route("/changeProductType")
    .put(
        ProductValidator.changeProductType,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT
        }),
        ProductController.changeProductType
    )

/* router.route("/scripts")
    .post(ProductDataSheetModel.addBarcodeToDatabase) */

module.exports = router;

const express = require("express")
const router = express.Router()

const CategoryValidator = require("../../Middleware/validators/CategoryValidator")
const AuthenticateUser = require("../../Middleware/authentication").authentication
const CategoryController = new (require("../../Controllers/v2/CategoryController"))()
const verifyPermission = require("../../Middleware/verifyPermission").verifyPermission

const { PERMISSION_MODULES, ACTIONS } = require("../../Configs/constants")

router.route("/get-category")
    .post(
        CategoryValidator.categoryListValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW
        }),
        CategoryController.getAllCategoryList
    )

module.exports = router

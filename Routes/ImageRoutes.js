const express = require('express')
const router = express.Router();

const ImageValidator = require("../Middleware/validators/ImageValidator");
const AuthenticateUser = require('../Middleware/authentication').authentication;
const ImageController = new (require("../Controllers/ImageController"))();
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');


router.route("/")
    .post(
        ImageValidator.addImageValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.CREATE,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.CREATE,
        }),
        ImageController.addImage
    )
    .get(
        ImageValidator.listImages,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
        }),
        ImageController.listImages
    )
    .delete(
        ImageValidator.deleteImages,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.DELETE,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.DELETE,
        }),
        ImageController.deleteImages
    )

router.route('/getUploadSignature')
    .post(
        ImageValidator.uploadSignatureValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT,
            ],
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT,
        }),
        ImageController.getUploadSignature
    )


router.route('/swapProductImages')
    .post(
        ImageValidator.swapProductImages,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.EDIT,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.EDIT,
        }),
        ImageController.swapImages
    )

router.route('/imagesMatch')
    .get(
        ImageValidator.imagesMatch,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.VIEW
        }),
        ImageController.imagesMatch.bind(ImageController)
    )
    .post(
        ImageValidator.imagesMatchAction,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.CREATE
        }),
        ImageController.imagesMatchAction
    )

router.route("/imagesByItemNumbers")
    .post(
        ImageValidator.getImagesByItemNumbers,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.VIEW
        }),
        ImageController.getImagesByItemNumbers
    )

module.exports = router;

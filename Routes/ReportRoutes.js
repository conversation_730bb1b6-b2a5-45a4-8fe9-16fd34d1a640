const express = require('express')
const router = express.Router()

const ReportValidator = require("../Middleware/validators/ReportValidator")
const ReportController = new (require("../Controllers/ReportController.js"));
const AuthenticateUser = require('../Middleware/authentication').authentication
const verifyPermission = require('../Middleware/verifyPermission').verifyPermission;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route("/")
    .post(
        ReportValidator.getReportValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
        }),
        ReportController.generateReport
    )

module.exports = router;

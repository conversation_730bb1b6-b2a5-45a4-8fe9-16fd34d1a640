/**
 *  This will create active & inactive variant item numbers mapping and variant_count in parent product.
 */

scripts = async (req, res) => {
    try {
        const parentProducts = await ProductModel.findProducts(
            {
                "_id": { "$in": ["6466f8c88d1b30bd5a032038", "64897a4eef859ba6c5b78a77"] },
                "type": NEW_PRODUCT_TYPE.PARENT,
                "is_deleted": false,
            },
            {
                "_id": 1,
                "type": 1,
            },
            {
                "populate": [
                    {
                        "path": "variantProducts",
                        "match": {
                            "is_deleted": false,
                        },
                        "select": {
                            "price_mappings": 1,
                            "inventory_mappings": 1,
                            "_id": 1,
                            "item_number": 1,
                            "is_deleted": 1,
                            "is_active": 1,
                        },
                    }
                ]
            }
        )

        let successProductCount = 0
        let errorProductCount = 0
        let totalProductCount = 0

        try {
            const updateParentProducts = []

            for (let i = 0; i < parentProducts.length; i++) {
                const parentDoc = parentProducts[i];
                const allVariantProducts = parentDoc.variantProducts || []
                const activeVariantItemNumbers = []
                const inactiveVariantItemNumbers = []

                allVariantProducts.forEach(variantProd => {
                    if (variantProd.is_active === true) {
                        activeVariantItemNumbers.push(variantProd.item_number)
                    }
                    else if (variantProd.is_active === false) {
                        inactiveVariantItemNumbers.push(variantProd.item_number)
                    }
                })

                parentDoc.variant_item_numbers = undefined
                parentDoc.active_variant_item_numbers = activeVariantItemNumbers
                parentDoc.inactive_variant_item_numbers = inactiveVariantItemNumbers
                parentDoc.variant_count = allVariantProducts.length || 0

                updateParentProducts.push(parentDoc)
            }
            totalProductCount = updateParentProducts.length

            const results = await Promise.allSettled(
                updateParentProducts.map(
                    parentProduct => parentProduct.save()
                )
            )

            results.forEach(result => {
                if (result.status === "fulfilled") {
                    successProductCount++
                }
                else if (result.status === "rejected") {
                    errorProductCount++
                }
            })
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "Error generateMappingForParent"
            })
        }

        return res.handler.success(null, {
            successProductCount,
            errorProductCount,
        })
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}

/**
 * Once above script is ran then verify mapping,
 * of active & inactive variant_item_numbers in parent
 *
 * After that unset(remove) variant_item_numbers's key
 * from SINGLE and VARIANT type products.
 */

scripts = async (req, res) => {
    try {
        const response = await ProductModel.updateProducts(
            {
                /* _id: {
                    $in: [
                        "643528b1d9329fbdc93492c1",
                        "64364883e9fce6aa030b6204",
                        "64353aa4ef53d11d1b701d18"
                    ]
                } */
            },
            {
                "$unset": { "variant_item_numbers": "" }
            },
        )
        return res.handler.success(null, response)
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}

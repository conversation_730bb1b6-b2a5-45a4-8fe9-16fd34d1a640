const barcodes = db["product_2.0_barcodes"].find()

const data = {}

barcodes.forEach(barcode => {
    const productId = barcode.product_variant_id.toString()

    if (!data[productId]) {
        data[productId] = {
            barcodes: []
        }
    }

    data[productId].barcodes.push(barcode.barcode)
})

const products = db["products_2.0"].find(
    {
        _id: {
            $in: Object.keys(data).map(i => ObjectId(i))
        },
        type: "VARIANT",
    },
    {
        parent_id: 1,
        is_active: 1,
    }
)

products.forEach(product => {
    const productId = product.parent_id.toString()

    if (!data[productId]) {
        data[productId] = {
            active_variant_barcodes: [],
            inactive_variant_barcodes: [],
        }
    }

    if (product.is_active) {
        data[productId].active_variant_barcodes.push(...(data[product._id.toString()].barcodes))
    }
    else {
        data[productId].inactive_variant_barcodes.push(...(data[product._id.toString()].barcodes))
    }
})

Object.keys(data).forEach(key => {
    db["products_2.0"].updateOne(
        {
            _id: ObjectId(key)
        },
        {
            $set: data[key]
        }
    )
})

const orders = db.orders.find({ tenant_id: 1002, created_at: { $lt: ISODate('2023-05-19T18:57:00.890+00:00') } });


orders.forEach(o => {
    db.order_items.deleteMany({ order_id: o._id });
})

db.orders.deleteMany({ tenant_id: 1002, created_at: { $lt: ISODate('2023-05-19T18:57:00.890+00:00') } });

// ============================================================================================================ //

const Orders = db.orders.find({ tenant_id: 1001, created_at: { $lt: ISODate('2023-05-18T19:21:13.275+00:00') } });

Orders.forEach(o => {
    db.order_items.deleteMany({ order_id: o._id });
})

db.orders.deleteMany({ tenant_id: 1001, created_at: { $lt: ISODate('2023-05-18T19:21:13.275+00:00') } });





const oldOrSStats = db.old_order_sales_statistics.find();

oldOrSStats.forEach(o => {
    db.order_sales_statistics.updateOne({ _id: o._id },
        {
            $set: { tenant_id: o.tenant_id, created_at: ISODate(), updated_at: ISODate() },
            $inc: { accepted_order_count: o.accepted_order_count, __v: 1, total_sales: o.total_sales, total_tax: o.total_tax }
        },
        { upsert: true }
    )
})

// ============================================================================================================ //

const catStat = db.old_category_statistics.find({});

catStat.forEach(c => {
    db.category_statistics.insertOne(c)
})


// ============================================================================================================ //

const salesPerDay = db.old_sales_statistics_per_days.find({});

salesPerDay.forEach(c => {
    db.sales_statistics_per_days.insertOne(c)
})


// ============================================================================================================ //

const orders = db.orders.find({}, { order_number: 1, tenant_id: 1, _id: 1 });

orders.forEach(o => {
    db.orders.updateOne({ _id: o._id }, { $set: { unique_order_number: o.tenant_id + o.order_number } });
})
// MongoDB Shell Script to add compound index for cart_items_2.0 collection
// This script optimizes queries that filter by cart_id and sort by created_at in descending order

// Connect to the database (update the connection string as needed)
// db = connect("mongodb://localhost:27017/hawak-product-backend");

// Switch to the appropriate database
// use hawak-product-backend;

// Add compound index for cart_items_2.0 collection
// This index optimizes queries like: findCartItems({ cart_id: cartId }, undefined, { "sort": { "created_at": -1 } })

print("Adding compound index to cart_items_2.0 collection...");

try {
    // Create the compound index
    const result = db.cart_items_2_0.createIndex(
        {
            "cart_id": 1,
            "created_at": -1
        },
        {
            name: "cart_id_created_at_desc",
            background: true,
            unique: false
        }
    );

    print("Index created successfully!");
    print("Index name: " + result);

    // Verify the index was created
    const indexes = db.cart_items_2_0.getIndexes();
    print("\nCurrent indexes on cart_items_2_0 collection:");
    indexes.forEach(function (index) {
        print("- " + index.name + ": " + JSON.stringify(index.key));
    });

} catch (error) {
    print("Error creating index: " + error.message);
}

print("\nScript completed."); 

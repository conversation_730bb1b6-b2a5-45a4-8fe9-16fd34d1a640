/**
 * @description Run this api from ProductDataSheetModel.js file
 */

addBarcodeToDatabase = async (req, res) => {
    try {
        const { tenant_id, data } = req.body
        const errors = []
        const validations = []

        const itemNumbers = data.map(ele => (tenant_id + "_" + ele.item_number))
        const barcodes = data.map(ele => ele.barcodes).join(",")

        /** Check if there is any valid product or not */
        const products = await ProductModel.findProducts(
            {
                unique_item_number: {
                    $in: itemNumbers,
                },
                type: {
                    $in: [
                        NEW_PRODUCT_TYPE.SINGLE,
                        NEW_PRODUCT_TYPE.VARIANT,
                    ]
                }
            },
            {
                item_number: 1,
            },
            {
                lean: true,
            }
        )

        if (!products.length) {
            return res.handler.notFound("products_not_found")
        }
        const productIdAndSkuMap = {}

        products.forEach(product => {
            productIdAndSkuMap[product.item_number] = product._id
        })

        /** Check if barcode exists in same or different product or not */
        await this.checkValidBarcode(tenant_id, barcodes, validations)

        if (validations.length) {
            return res.handler.conflict(null, { validations })
        }
        const updateBarcodePromise = []

        for (let index = 0; index < data.length; index++) {
            const {
                item_number,
                barcodes,
            } = data[index];

            const sanitizedBarCodes =
                barcodes
                    .split(",")
                    .map(element => element.trim())

            const uniqBarCodes = uniq(sanitizedBarCodes)
            const productId = productIdAndSkuMap[item_number]

            if (productId) {
                updateBarcodePromise.push(
                    ProductModel.addUpdateBarcodes(
                        tenant_id,
                        uniqBarCodes,
                        productId,
                    )
                )
            }
            else {
                errors.push(data[index])
            }
        }
        const promiseRes = await Promise.allSettled(updateBarcodePromise)

        return res.handler.success(null, { promiseRes, validations, errors })
    }
    catch (error) {
        logger.error(error)
    }
}


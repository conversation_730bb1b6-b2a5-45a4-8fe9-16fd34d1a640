/**
 * This will search and hard delete products from database.
 * Also, if any image is remaining to delete then it'll delete.
 * 
 * Note: We're soft deleting products and hard deleting
 * other related data from deleteProducts api
 */

scripts = async (req, res) => {
    try {
        const productsIds = [
            "64bf727a9fb31165f0fc40ad",
        ]

        const deleteProducts = await ProductModel.deleteProducts({
            _id: {
                $in: productsIds
            }
        })

        const imageDocs = [
            {
                image_name: "64bf727a9fb31165f0fc40a7_P1.jpeg",
                tenant_id: 1001,
            },
        ]

        const imagePromises = []

        const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);
        ProductModel.deleteS3Images(imageDocs, imagePromises, publicS3);
        const response = await Promise.allSettled(imagePromises)

        return res.handler.success(null, response)

        const itemNumbers = [
            "6004-004",
            "M0472",
        ]

        const products = await ProductModel.findProducts(
            {
                item_number: {
                    $in: itemNumbers
                },
                tenant_id: 1001,
            },
            {
                title: 1,
                type: 1,
                item_number: 1,
                is_active: 1,
                is_deleted: 1,
                parent_id: 1,
            },
            toLeanOption
        )
        const productIds = products.map(product => product._id)

        return res.handler.success(null,
            {
                productIds,
                productCount: products.length,
                products,
            }
        )
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}

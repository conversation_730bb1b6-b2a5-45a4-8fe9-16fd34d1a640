/**
 *  This will re-calculate price mapping, inventory mapping and 
 *  variant item numbers in parent product.
 */

scripts = async (req, res) => {
    try {
        const parents = await ProductModel.findProducts(
            {
                "type": NEW_PRODUCT_TYPE.PARENT,
                "is_deleted": false,
            },
            {
                "_id": 1,
            }
        )

        const products = []

        for (let index = 0; index < parents.length; index++) {
            const product = parents[index];

            const productMasterPriceMap = {};
            const variantInventoryMap = new Map();
            const variantItemNumbers = [];

            const variantProducts = await ProductModel.findProducts(
                {
                    "parent_id": product._id,
                    "is_deleted": false,
                    "is_active": true,
                },
                {
                    "price_mappings": 1,
                    "inventory_mappings": 1,
                    "item_number": 1,
                }
            )

            variantProducts?.forEach(variantProd => {
                // generating price mapping object for parent
                variantItemNumbers.push(variantProd.item_number);

                for (let p = 0; p < variantProd.price_mappings?.length; p++) {
                    let varPriceMap = variantProd.price_mappings[p];
                    varPriceMap["product_variant_id"] = variantProd._id; // add the variant info for price-mapping

                    if (
                        varPriceMap.price > 0 && (
                            (!productMasterPriceMap[varPriceMap.master_price_id]) ||
                            (productMasterPriceMap[varPriceMap.master_price_id].price > varPriceMap.price)
                        )
                    ) {
                        productMasterPriceMap[varPriceMap.master_price_id] = varPriceMap;
                    }
                }

                // generating inventory mapping object for parent
                for (let i = 0; i < variantProd.inventory_mappings.length; i++) {
                    const iMap = variantProd.inventory_mappings[i];

                    if (variantInventoryMap.has(iMap.branch_id)) {
                        const inventoryObj = variantInventoryMap.get(iMap.branch_id);
                        inventoryObj.quantity += iMap.quantity;
                        variantInventoryMap.set(iMap.branch_id, inventoryObj);
                    } else {
                        variantInventoryMap.set(iMap.branch_id, iMap);
                    }
                }
            })

            product.price_mappings = Object.values(productMasterPriceMap)
            product.inventory_mappings = Array.from(variantInventoryMap.values());
            product.variant_item_numbers = variantItemNumbers;

            product.save()

            products.push(product._id)
        }

        return res.handler.success(null, {
            products
        })
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}

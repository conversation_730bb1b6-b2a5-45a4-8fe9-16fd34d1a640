/**
 * @description This will create below fields in existing order items.
 * variant_id
 * parent_id
 * variant_value_id
 * group_value_id
 */

scripts = async (req, res) => {
    try {
        const orderItems = await OrderModel.findOrderItems({}, "product_variant_id", toLeanOption)
        const productVariantIds = orderItems.map(item => item.product_variant_id.toString())
        const uniqueProductIds = [...new Set(productVariantIds)]

        const products = await ProductModel.findProducts(
            {
                _id: { $in: uniqueProductIds },
                type: "VARIANT"
            },
            "variant_id parent_id variant_value_id group_value_id"
        )

        const results = await Promise.allSettled(
            products.map(product => {
                const {
                    _id,
                    variant_id,
                    parent_id,
                    variant_value_id,
                    group_value_id,
                } = product

                return OrderModel.updateOrderItems(
                    {
                        product_variant_id: _id,
                    },
                    {
                        variant_id,
                        parent_id,
                        variant_value_id,
                        group_value_id,
                    },
                )
            })
        )
        let modifiedOrderItemsCount = 0

        results.forEach(result => {
            if (result.status === "fulfilled") {
                modifiedOrderItemsCount += result.value.modifiedCount
            }
        })

        return res.handler.success(null, {
            count: products.length,
            modifiedOrderItemsCount,
            results,
        })
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}

/**
 * @description This will create sort_order field in existing order items order-wise.
 */

scripts = async (req, res) => {
    try {
        const orders = await OrderModel.findOrders({
            /* _id: {
                $in: [
                    "64631a0659123a1e6687d733",
                    "646247b407508c773569c518"
                ]
            } */
        })
        const items = []

        for (let index = 0; index < orders.length; index++) {
            const order = orders[index];

            const orderItems = await OrderModel.findOrderItems(
                {
                    order_id: order._id
                },
                "_id order_id",
                {
                    "sort": {
                        "created_at": -1,
                    }
                }
            )

            if (orderItems.length) {
                orderItems.forEach((item, index) => {
                    item.sort_order = index + 1
                    items.push(item)
                })
            }
        }
        let results = null

        if (items.length) {
            results = await Promise.allSettled(
                items.map(item => {
                    item.save()
                })
            )
        }

        return res.handler.success(null, {
            results,
        })
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}


/* eslint-env node */
/**
 * @description Script to add tenant_id field for tax configuration records that don't have it
 * This script specifically targets records with group_id that should inherit tenant_id from their parent
 */

const TaxConfigurationSchema = require("../Database/Schemas/configuration/tax_configurations");
const { toLeanOption } = require("../Utils/helpers");

const addTenantIdToTaxConfigurations = async () => {
    try {
        logger.info('Starting tenant_id migration for tax configurations...');

        // Find all records that have group_id but no tenant_id
        const recordsWithoutTenantId = await TaxConfigurationSchema.find(
            {
                group_id: { $exists: true, $ne: null },
                $or: [
                    { tenant_id: { $exists: false } },
                    { tenant_id: null },
                    { tenant_id: { $type: "undefined" } }
                ]
            },
            {
                group_id: 1,
                tenant_id: 1,
            },
            toLeanOption,
        );

        logger.info(`Found ${recordsWithoutTenantId.length} records without tenant_id`, { recordsWithoutTenantId });

        if (recordsWithoutTenantId.length === 0) {
            logger.info('No records found that need tenant_id migration');
            return;
        }

        // Extract all unique group_ids to fetch parent records in bulk
        const groupIds = [...new Set(recordsWithoutTenantId.map(record => record.group_id))];
        logger.info(`Found ${groupIds.length} unique group_ids to process`, { groupIds });

        // Fetch all parent records in bulk
        const parentRecords = await TaxConfigurationSchema.find(
            {
                _id: { $in: groupIds }
            },
            {
                tenant_id: 1
            },
            toLeanOption,
        );

        // Create a map of group_id to tenant_id for quick lookup
        const groupIdToTenantIdMap = {};
        parentRecords.forEach(parent => {
            if (parent.tenant_id) {
                groupIdToTenantIdMap[parent._id.toString()] = parent.tenant_id;
            }
        });

        logger.info(`Found ${Object.keys(groupIdToTenantIdMap).length} parent records with tenant_id`, { groupIdToTenantIdMap });

        // Prepare bulk update operations
        const bulkOps = [];
        let updatedCount = 0;
        let errorCount = 0;

        for (const record of recordsWithoutTenantId) {
            const groupIdStr = record.group_id.toString();
            const tenantId = groupIdToTenantIdMap[groupIdStr];

            if (tenantId) {
                bulkOps.push({
                    updateOne: {
                        filter: { _id: record._id },
                        update: {
                            $set: {
                                tenant_id: tenantId,
                                updated_at: new Date()
                            }
                        }
                    }
                });
                updatedCount++;
            }
            else {
                logger.info(`Warning: Parent record not found or has no tenant_id for group_id: ${record.group_id}`);
                errorCount++;
            }
        }

        // Execute bulk update if there are operations to perform
        if (bulkOps.length > 0) {
            logger.info(`Executing bulk update for ${bulkOps.length} records...`, { bulkOps });
            const bulkResult = await TaxConfigurationSchema.bulkWrite(bulkOps);
            logger.info(`Bulk update completed. Modified: ${bulkResult.modifiedCount}`, { bulkResult });
        }

        logger.info("Counts", { updatedCount, errorCount });

        // Also check for records without group_id that might be missing tenant_id
        const recordsWithoutGroupIdAndTenantId = await TaxConfigurationSchema.find(
            {
                group_id: { $exists: false },
                $or: [
                    { tenant_id: { $exists: false } },
                    { tenant_id: null },
                    { tenant_id: { $type: "undefined" } }
                ]
            },
            {
                type: 1,
                tax_name: 1,
            },
            toLeanOption,
        );

        logger.info(`Found ${recordsWithoutGroupIdAndTenantId.length} records without group_id and tenant_id`, { recordsWithoutGroupIdAndTenantId });

        // For records without group_id, we need to determine tenant_id from other sources
        // This might require business logic or manual intervention
        if (recordsWithoutGroupIdAndTenantId.length > 0) {
            logger.info('Records without group_id that need tenant_id:', { recordsWithoutGroupIdAndTenantId });
            recordsWithoutGroupIdAndTenantId.forEach(record => {
                logger.info(`- Record ID: ${record._id}, Type: ${record.type}, Tax Name: ${record.tax_name}`);
            });
        }

        logger.info('\n=== Migration Summary ===');
        logger.info(`Records without group_id needing manual review: ${recordsWithoutGroupIdAndTenantId.length}`);

        // Verify the migration
        const remainingRecordsWithoutTenantId = await TaxConfigurationSchema.find(
            {
                group_id: { $exists: true, $ne: null },
                $or: [
                    { tenant_id: { $exists: false } },
                    { tenant_id: null },
                    { tenant_id: { $type: "undefined" } }
                ]
            },
            undefined,
            toLeanOption,
        );
        logger.info(`\nVerification: ${remainingRecordsWithoutTenantId.length} records still missing tenant_id`, { remainingRecordsWithoutTenantId });
    }
    catch (error) {
        logger.error('Migration failed:', error);
    }
};

module.exports = { addTenantIdToTaxConfigurations }; 

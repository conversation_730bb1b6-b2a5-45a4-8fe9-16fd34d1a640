const idsMapping = []

const mappingData = db.orders.find(
    {
        order_status_track: { $size: 3 },
        'order_status_track.0.order_status': 'PENDING',
        'order_status_track.2.order_status': 'CANCELLED'
    },
    {
        total_amount: 1,
        total_tax: 1,
        tenant_id: 1,
        customer_user_role_id: 1,
        sales_user_role_id: 1,
    }
)

mappingData.forEach(data => {
    idsMapping.push(
        {
            customer_user_role_id: data.customer_user_role_id,
            tenant_id: data.tenant_id
        },
        {
            sales_user_role_id: data.sales_user_role_id,
            tenant_id: data.tenant_id,
        },
    )
})

idsMapping.forEach(idMapping => {
    const key = Object.keys(idMapping)[0]

    const orders = db.orders.find(
        {
            [key]: idMapping[key],
            tenant_id: idMapping.tenant_id,
            order_status: {
                $in: [
                    "RECEIVED",
                    "PREPARING",
                    "SHIPPED",
                    "DELIVERED",
                ]
            }
        },
        {
            total_amount: 1,
            total_tax: 1,
            tenant_id: 1,
            customer_user_role_id: 1,
            sales_user_role_id: 1,
        }
    ).toArray()

    const accepted_order_count = orders.length
    let total_sales = 0
    let total_tax = 0

    orders.forEach(order => {
        total_sales += order.total_amount
        total_tax += order.total_tax
    })

    printjson("key_data", {
        [key]: idMapping[key],
        accepted_order_count,
        total_sales,
        total_tax,
    })

    const result = db.order_sales_statistics.findOneAndUpdate(
        {
            _id: idMapping[key]
        },
        {
            $set: {
                accepted_order_count: accepted_order_count,
                total_sales: total_sales,
                total_tax: total_tax,
                updated_at: ISODate()
            },
            $inc: {
                __v: 1
            }
        },
        {
            upsert: true,
            returnNewDocument: true
        }
    )
    printjson("result_", result)
})

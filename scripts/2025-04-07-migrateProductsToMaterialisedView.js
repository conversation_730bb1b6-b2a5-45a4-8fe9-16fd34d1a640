
module.exports = async () => {
    logger.info("STARTED: migrateProductsToMaterialisedView")
    const profiler = logger.startTimer()
    try {
        const ProductModel = new (require('../Models/ProductModel'))()
        const InternalServiceModel = new (require('../Models/InternalServiceModel'))()
        const MaterializedProductHelper = new (require('../Models/materialized_product/MaterializedProductHelper'))()
        const MaterializedProductModelMethods = new (require('../Models/materialized_product/MaterializedProductModelMethods'))()

        const { addToSet, stringifyObjectId } = require('../Utils/helpers')
        const { NEW_PRODUCT_TYPE } = require('../Configs/constants')

        const categoryIdSet = new Set()
        const brandIdSet = new Set()
        const uomIdSet = new Set()
        const variantTypesIdSet = new Set()
        const attributeIdSet = new Set()

        const productMap = {
            [NEW_PRODUCT_TYPE.SINGLE]: [],
            [NEW_PRODUCT_TYPE.PARENT]: {},
        }

        const tenantsProfiler = logger.startTimer()
        const tenants = await InternalServiceModel.getTenants({
            filter: {
                is_active: true,
                is_deleted: false,
            },
            projection: "_id",
            options: {
                "lean": true,
            }
        })
        const limit = 200

        tenantsProfiler.done({
            message: "Fetched_Tenants",
            count: tenants.length,
        })

        const filters = []
        const totalProducts = []

        for (const tenant of tenants) {
            let filter = { // TODO: Need to delete this filter index once work is done
                "tenant_id": tenant._id,
                "is_deleted": false,
            }
            let lastId = null

            const productsProfiler = logger.startTimer()

            while (true) {
                if (lastId) {
                    filter["_id"] = {
                        "$gt": lastId,
                    }
                }
                filters.push(filter)

                const products = await ProductModel.findProducts(
                    filter,
                    undefined,
                    {
                        "sort": { "_id": 1 },
                        limit,
                        "lean": true,
                    }
                )
                // logger.info("🚀 ~ module.exports= ~ products:", products.length)

                if (products.length === 0) {
                    break
                }
                lastId = products[products.length - 1]._id
                // logger.info("🚀 ~ module.exports= ~ lastId:", { lastId })

                products.forEach(product => {
                    totalProducts.push(product)

                    const {
                        family_id,
                        category_id,
                        subcategory_id,
                        brand_id,
                        packaging_map: {
                            uom_id,
                        } = {},
                        attributes = [],
                        variants,
                        groups,
                        variant_value_id,
                        group_value_id,

                        _id,
                        type,
                        parent_id,
                    } = product

                    addToSet(categoryIdSet, stringifyObjectId(family_id))
                    addToSet(categoryIdSet, stringifyObjectId(category_id))
                    addToSet(categoryIdSet, stringifyObjectId(subcategory_id))

                    addToSet(brandIdSet, stringifyObjectId(brand_id))
                    addToSet(uomIdSet, stringifyObjectId(uom_id))

                    addToSet(variantTypesIdSet, variants?.values)
                    addToSet(variantTypesIdSet, groups?.values)
                    addToSet(variantTypesIdSet, stringifyObjectId(variant_value_id))
                    addToSet(variantTypesIdSet, stringifyObjectId(group_value_id))

                    if (attributes?.length) {
                        attributes.forEach(attribute => {
                            attributeIdSet.add(stringifyObjectId(attribute.attribute_id))
                        })
                    }

                    if (type === NEW_PRODUCT_TYPE.SINGLE) {
                        productMap[NEW_PRODUCT_TYPE.SINGLE].push(product)
                    }
                    else if (type === NEW_PRODUCT_TYPE.PARENT) {
                        const variants = productMap[NEW_PRODUCT_TYPE.PARENT]?.[_id]?.variants || []

                        productMap[NEW_PRODUCT_TYPE.PARENT][_id] = {
                            parentProduct: product,
                            variants,
                        }
                    }
                    else if (type === NEW_PRODUCT_TYPE.VARIANT) {
                        if (productMap[NEW_PRODUCT_TYPE.PARENT][parent_id]) {
                            productMap[NEW_PRODUCT_TYPE.PARENT][parent_id].variants.push(product)
                        }
                        else {
                            productMap[NEW_PRODUCT_TYPE.PARENT][parent_id] = {
                                parentProduct: null,
                                variants: [product],
                            }
                        }
                    }
                })
                // break

                if (products.length < limit) {
                    break
                }
            }

            productsProfiler.done({
                message: `Fetched_Products for tenant ${tenant._id}`,
            })
        }

        /* logger.info("Fetched filters", {
            filters,
            preparedSets: {
                categoryIdSet: Array.from(categoryIdSet),
                brandIdSet: Array.from(brandIdSet),
                uomIdSet: Array.from(uomIdSet),
                variantTypesIdSet: Array.from(variantTypesIdSet),
                attributeIdSet: Array.from(attributeIdSet),
            }
        }) */

        const mappingDataProfiler = logger.startTimer()
        const mappingData = await MaterializedProductHelper.getMappingData({
            categoryIdSet,
            brandIdSet,
            uomIdSet,
            variantTypesIdSet,
            attributeIdSet,
        })
        mappingDataProfiler.done({
            message: "Fetched_MappingData",
        })

        const docs = []

        productMap[NEW_PRODUCT_TYPE.SINGLE].forEach(product => {
            const doc = MaterializedProductHelper.generateSingleParentDoc(product, mappingData)
            docs.push(doc)
        })

        Object.entries(productMap[NEW_PRODUCT_TYPE.PARENT]).map(([key, value]) => {
            const { parentProduct, variants = [] } = value
            if (!parentProduct) { return }

            const parentProductDoc = MaterializedProductHelper.generateSingleParentDoc(parentProduct, mappingData)
            const productVariants = []

            if (variants.length) {
                variants.forEach(variant => {
                    const variantDoc = MaterializedProductHelper.generateVariantDoc(variant, mappingData)
                    productVariants.push(variantDoc)
                })

                parentProductDoc.product_variants = productVariants
                parentProductDoc.product_variants.sort(
                    (v1, v2) => v1.variant_order - v2.variant_order // sorts `variant order` in ascending order
                )
            }
            else {
                parentProductDoc.product_variants = []
            }
            docs.push(parentProductDoc)
        })
        logger.info("🚀 ~ migrateProductsToMaterialisedView ~ docs:", { docs })

        const successfulDocs = []
        const failedDocs = []

        try {
            const result = await MaterializedProductModelMethods.createProducts(
                docs,
                {
                    "ordered": false, // will insert all the documents it can and report errors later
                    "aggregateErrors": true, // Aggregate Errors instead of throwing the first one that occurs. Default: false
                }
            )

            result.forEach(doc => {
                if (doc instanceof Error) {
                    failedDocs.push(doc)
                }
                else {
                    successfulDocs.push(doc)
                }
            })
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: (API_ERROR) migrateProductsToMaterialisedView`,
                messages
            })
        }
        finally {
            logger.info("API_RESULT", {
                successfulDocs: successfulDocs.map(doc => doc._id),
                failedDocs,
            })
        }
    }
    catch (error) {
        logger.error(error)
    }
    finally {
        profiler.done({
            message: "Product Migration",
        })
    }
}

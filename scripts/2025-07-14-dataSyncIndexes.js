// ================================ categories Indexes ================================

db.categories.createIndex(
    {
        tenant_id: 1,
        is_deleted: 1,
        is_active: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "categories_updated_init_sync_idx",
        background: true
    }
);

db.categories.createIndex(
    {
        tenant_id: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "categories_updated_sync_idx",
        background: true
    }
);

// ================================ Products Indexes ================================

db.mv_products.createIndex(
    {
        // Equality fields
        tenant_id: 1,
        is_active: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "mv_products_updated_sync_idx" }
);

// ================================ Images Indexes ================================

db["images_2.0"].createIndex(
    {
        tenant_id: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "images_updated_sync_idx",
        background: true
    }
);

// ================================ Favorite Products Indexes ================================

db["favorite_products_2.0"].createIndex(
    {
        tenant_id: 1,
        user_role_id: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "favorite_products_updated_sync_idx",
        background: true
    }
);

// ================================ Deals Indexes ================================

db["deals"].createIndex(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,

        // Range fields
        deal_status: 1,
    },
    { name: "deals_updated_sync_idx" }
);

// ================================ Deal Products Indexes ================================

db["deal_products"].createIndex(
    {
        // Equality fields
        tenant_id: 1,
        is_active: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,

        // Range fields
        deal_id: 1,
    },
    { name: "deal_products_updated_sync_idx" }
);

// ================================ Cart Items Indexes ================================

db["cart_items_2.0"].createIndex(
    {
        tenant_id: 1,
        cart_id: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "tenant_cart_updated_id_optimized",
        background: true
    }
);

db["cart_items_2.0"].createIndex(
    {
        cart_id: 1,
        tenant_id: 1,
        updated_at: 1,
        _id: 1,
    },
    {
        name: "cart_tenant_updated_id_regex_first",
        background: true
    }
);

db["cart_items_2.0"].createIndex(
    {
        tenant_id: 1,
        cart_id: 1,
        _id: 1,
        updated_at: 1,
    },
    {
        name: "tenant_cart_id_updated_or_optimized",
        background: true
    }
);

db["cart_items_2.0"].createIndex(
    {
        cart_id: "text"
    },
    {
        name: "cart_id_text_search",
        background: true
    }
);

// ================================ Orders Indexes ================================

db["orders"].createIndex(
    {
        // Equality fields
        tenant_id: 1,
        order_creator_user_role_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,

        // Range fields
        created_at: 1,
    },
    { name: "orders_updated_sync_idx" }
);

// ================================ Deleted Images Indexes ================================

db["deleted_images"].createIndex(
    {
        created_at: 1
    },
    { name: "deleted_images_cleanup_idx", background: true }
);

db["deleted_images"].createIndex(
    {
        tenant_id: 1,
        updated_at: 1,
        _id: 1,
    },
    { name: "deleted_images_updated_sync_idx", background: true }
);

// ================================ Deleted Materialized Products Indexes ================================

db["deleted_mv_products"].createIndex(
    {
        created_at: 1
    },
    { name: "deleted_mv_products_cleanup_idx", background: true }
);

db["deleted_mv_products"].createIndex(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "deleted_mv_products_updated_sync_idx" }
)

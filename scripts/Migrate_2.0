//Run script in two phases

//Phase 1
db.draft_items.drop()
db.drafts.drop()
db.favorite_products.drop()
db.images.drop()
db.old_category_statistics.drop()
db.old_order_reports.drop()
db.old_order_sales_statistics.drop()
db.old_sales_statistics_per_days.drop()
db.order_cart_items.drop()
db.order_carts.drop()
db.product_barcodes.drop()
db.product_tags.drop()
db.product_variants.drop()
db.products.drop()
db.tenant_order_items.drop()
db.tenant_orders.drop()
db.tenant_product_items.drop()
db.variant_types.drop()

//Phase 2
db["cart_items_2.0"].renameCollection("cart_items")
db["carts_2.0"].renameCollection("carts", true)
db["draft_items_2.0"].renameCollection("draft_items", true)
db["drafts_2.0"].renameCollection("drafts", true)
db["favorite_products_2.0"].renameCollection("favorite_products", true)
db["images_2.0"].renameCollection("images", true)
db["product_2.0_barcodes"].renameCollection("product_barcodes", true)
db["product_2.0_tags"].renameCollection("product_tags", true)
db["products_2.0"].renameCollection("products", true)
db["variant_types_2.0"].renameCollection("variant_types", true)
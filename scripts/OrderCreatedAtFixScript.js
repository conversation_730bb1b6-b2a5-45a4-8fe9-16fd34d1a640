scripts = async (req, res) => {
    try {
        const orders = await OrderModel.aggregateOrders(
            [
                {
                    $addFields:
                    {
                        order_status_changed_at: {
                            $first: "$order_status_track.time",
                        },
                    },
                },
                {
                    $project:
                    {
                        tenant_id: 1,
                        created_at: 1,
                        order_status_changed_at: 1,
                    },
                },
                {
                    $sort: {
                        created_at: 1,
                    }
                }
            ]
        )

        const checkOrders = []

        orders.forEach(order => {
            if (new Date(order.order_status_changed_at) < new Date(order.created_at)) {
                const difference = moment(order.created_at).diff(order.order_status_changed_at, "minutes")

                if (difference > 1) {
                    checkOrders.push({ _id: order._id, created_at: order.order_status_changed_at })
                }
            }
        })
        return res.handler.success(null, checkOrders);
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}

array.forEach(order => {
    db.orders.updateOne(
        { _id: ObjectId(order._id) },
        { $set: { created_at: ISODate(order.created_at) } }
    )
})

/**
 * @description This will create/update "discounted_price" inside deal products
 */

scripts = async (req, res) => {
    try {
        const deals = await DealModel.getDeals(
            {
                deal_type: "DISCOUNT"
            },
            {
                _id: 1,
            },
            {
                lean: true
            }
        )

        const dealProducts = await Promise.allSettled(
            deals.map(
                deal => DealModel.aggregateDealProducts([
                    {
                        $match: {
                            deal_id: deal._id
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            deal_id: 1,
                            price_id: 1,
                            product_id: 1,
                            discount_type: 1,
                            percent: 1,
                            amount: 1,
                        }
                    },
                    {
                        $lookup: {
                            from: "products",
                            // from: "product_2.0",
                            localField: "product_id",
                            foreignField: "_id",
                            let: { master_price_id: "$price_id" },
                            as: "product_detail",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        price_mappings: {
                                            $filter: {
                                                input: "$price_mappings",
                                                as: "price_mapping",
                                                cond: {
                                                    $eq: ["$$price_mapping.master_price_id", "$$master_price_id"]
                                                }
                                            }
                                        }
                                    }
                                },
                                {
                                    $addFields: {
                                        price_mappings: {
                                            $first: "$price_mappings"
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    {
                        $addFields: {
                            product_detail: {
                                $first: "$product_detail"
                            }
                        }
                    },
                ])
            )
        )

        const updateData = []
        let totalFilledDealProducts = 0
        let totalEmptyDealProducts = 0
        let totalErrorDealProducts = 0
        let dealProductsHavingNoAmount = 0

        for (let index = 0; index < dealProducts.length; index++) {
            const {
                status,
                value
            } = dealProducts[index];

            if (status === "rejected") {
                totalErrorDealProducts++
                continue
            }

            if (!value.length) {
                totalEmptyDealProducts++
                continue
            }

            for (let dealProductIndex = 0; dealProductIndex < value.length; dealProductIndex++) {
                totalFilledDealProducts++
                const {
                    _id,
                    deal_id,
                    product_id,
                    price_id,
                    amount,
                    product_detail: {
                        price_mappings: {
                            price,
                        } = {}
                    } = {}
                } = value[dealProductIndex];

                if (amount) {
                    const discounted_price = Number(((price - amount) || 0).toFixed(2))

                    if (!discounted_price) {
                        console.log(`price: ${price} amount: ${amount}`)
                    }

                    updateData.push({
                        filter: {
                            _id,
                            deal_id,
                            product_id,
                            price_id,
                        },
                        update: {
                            discounted_price,
                        },
                        restData: value[dealProductIndex]
                    })
                }
                else {
                    dealProductsHavingNoAmount++
                }
            }
        }

        const updateRes = await Promise.allSettled(
            updateData.map(doc => {
                DealModel.updateDealProduct(doc.filter, doc.update)
            })
        )

        return res.handler.success(null, {
            totalDeals: deals.length,
            totalDealProducts: dealProducts.length,
            totalFilledDealProducts,
            totalEmptyDealProducts,
            totalErrorDealProducts,
            totalUpdateData: updateData.length,
            dealProductsHavingNoAmount,
            updateData,
            updateRes,
        })
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}

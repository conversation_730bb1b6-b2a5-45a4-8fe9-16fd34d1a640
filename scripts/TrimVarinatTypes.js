const VariantTypesSchema = require("../Database/Schemas/product/variant_types_2.0")

scripts = async (req, res) => {
    try {
        await VariantTypesSchema.updateMany({},
            [
                {
                    "$set": {
                        "name": {
                            "$trim": {
                                "input": "$name"
                            }
                        }
                    }
                }
            ])

        return res.handler.success()
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}

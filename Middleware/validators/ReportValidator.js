const { body } = require('express-validator')
const { REPORTS } = require('../../Configs/constants')

const {
    headerValidator,
} = require("./CommonValidator")

exports.getReportValidator = [
    ...headerValidator,

    body("reportType", "Please provide report type.")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(REPORTS.REPORT_TYPE))
        .withMessage(`Please provide report type ${Object.values(REPORTS.REPORT_TYPE).join(" or ")}`),

    body("reportData", "Please provide valid report data.")
        .isObject(),

    /** ----------------- SHIPPING_LABEL report's validations ----------------- */

    body("reportData.logo", "Please provide valid shipping logo.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
    )
        .trim()
        .notEmpty(),

    body("reportData.orderId", "Please provide valid order id.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.date", "Please provide valid date.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.numberOfCopies", "Please provide valid number of copies.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .toInt()
        .isInt(),

    body("reportData.from.tenantLegalName", "Please provide valid tenant legal name.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.from.streetAddress", "Please provide valid from street address.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.from.region", "Please provide valid from region.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.from.city", "Please provide valid from city.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.from.mobileNumber", "Please provide valid from mobile number.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.from.phoneNumber", "Please provide valid from mobile number.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .optional()
        .trim()
        .notEmpty(),

    body("reportData.to.customerLegalName", "Please provide valid customer legal name.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.to.externalId", "Please provide valid externalId.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .optional()
        .trim()
        .notEmpty(),

    body("reportData.to.shippingRegion", "Please provide valid shipping region.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.to.shippingCity", "Please provide valid shipping city.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    body("reportData.to.shippingMobileNumber", "Please provide valid shipping mobile number.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.SHIPPING_LABEL)
        )
        .trim()
        .notEmpty(),

    /** ----------------- ORDER_DETAIL report's validations ----------------- */

    body("reportData.tenantId")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.ORDER_DETAIL)
        )
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please send valid tenant id."),

    body("reportData.orderId", "Please provide valid order id.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.ORDER_DETAIL)
        )
        .isMongoId(),

    body("reportData.timezone", "Please provide valid timezone.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.ORDER_DETAIL)
        )
        .trim()
        .notEmpty(),

    body("reportData.currency", "Please provide valid currency.")
        .if(
            body("reportType").equals(REPORTS.REPORT_TYPE.ORDER_DETAIL)
        )
        .trim()
        .notEmpty(),
]

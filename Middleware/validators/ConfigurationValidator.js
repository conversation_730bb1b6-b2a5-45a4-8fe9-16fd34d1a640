const { header, body, query } = require('express-validator');
const { VALUES, ENTITY_STATUS } = require('../../Configs/constants');

exports.addAndEditTax = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    body('tenantId', 'Please provide tenant id').isInt({ min: 1000 }).withMessage('Please provide tenant valid'),
    body('taxId', 'Please provide tax id').optional().trim().notEmpty().isMongoId(),
    body('type').isIn(Object.values(VALUES.TAX_TYPE)).withMessage(`Please provide type as in ${Object.values(VALUES.TAX_TYPE).join(", ")}`),
    body('taxName', 'Please enter valid tax name').isArray({min: 1}),
    body('taxCalculation').isIn(Object.values(VALUES.TAX_CALCULATION)).withMessage(`Please provide tax calculation as in ${Object.values(VALUES.TAX_CALCULATION).join(", ")}`).isArray({min: 1}),
    body('taxRate', 'Please enter valid tax rate').isArray({min: 1}),
    body('status', 'Please provide is status').optional().trim().notEmpty().isBoolean()
];

exports.changeTaxStatus = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    query('taxId', 'Please provide tax id').trim().notEmpty().isMongoId(),
    query("status", "Please provide status").trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`value should be in ${Object.values(ENTITY_STATUS).join(", ")}`)
];

exports.taxList = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    query('tenantId', 'Please provide tenant id').isInt({ min: 1000 }).withMessage('Please provide tenant valid'),
    query('taxId', 'Please provide tax id').optional().trim().notEmpty().isMongoId(),
    query("status", "Please provide status").optional().trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`value should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
    query('textSetting').optional().trim().notEmpty().toBoolean()
];

exports.deleteTax = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    query('taxId', 'Please provide tax id').trim().notEmpty().isMongoId()
];

exports.masterTaxSetting = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    body('tenantId', 'Please provide tenant id').isInt({ min: 1000 }).withMessage('Please provide tenant valid'),
    body('enableTax', 'Please provide enable tax').trim().notEmpty().isBoolean(),
    body('price').isIn(Object.values(VALUES.MASTER_PRICE)).withMessage(`Please provide price as in ${Object.values(VALUES.MASTER_PRICE).join(", ")}`),
    body('defaultTax', 'Please provide default tax').optional().trim().notEmpty().isMongoId(),
    body('universalTax', 'Please provide universal tax').trim().notEmpty().isBoolean()
];

exports.getMasterTaxSetting = [
   header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header('authorization', 'Please provide authorization').trim().notEmpty(),
    header("userroleid", ).trim().notEmpty(),
    query('tenantId', 'Please provide tenant id').isInt({ min: 1000 }).withMessage('Please provide tenant valid'),
];

const { body } = require('express-validator');
const {
    headerValidator,
    tenantIdBodyValidator
} = require("./CommonValidator");

exports.sendReasonMessage = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("orderId", "Please provide valid 'orderId'").isMongoId(),

    body("holdReasonId", "Please provide valid 'holdReasonId'").isMongoId(),

    body("isReleaseMessage", "Please send 'isReleaseMessage' value either true or false.")
        .isBoolean(),

    body("isSendMessage", "Please send 'isSendMessage' value either true or false.")
        .isBoolean(),

    body("messageDetails", "Please provide 'messageDetails' with its value.")
        .optional()
        .isObject(),

    body("messageDetails.salesPerson", "Please provide 'salesPerson' in 'messageDetails'")
        .optional()
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        }),

    body("messageDetails.salesPerson.templateId", "Please provide 'templateId' in 'messageDetails.salesPerson'")
        .optional()
        .trim()
        .notEmpty(),

    body("messageDetails.salesPerson.language", "Please provide 'language' in 'messageDetails.salesPerson'")
        .optional()
        .trim()
        .notEmpty(),

    body("messageDetails.salesPerson.inputs", "Please provide 'inputs' in 'messageDetails.salesPerson'")
        .optional()
        .isObject(),

    body("messageDetails.customer", "Please provide 'customer' in 'messageDetails'")
        .optional()
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        }),

    body("messageDetails.customer.templateId", "Please provide 'templateId' in 'messageDetails.customer'")
        .optional()
        .trim()
        .notEmpty(),

    body("messageDetails.customer.language", "Please provide 'language' in 'messageDetails.customer'")
        .optional()
        .trim()
        .notEmpty(),

    body("messageDetails.customer.inputs", "Please provide 'inputs' in 'messageDetails.customer'")
        .optional()
        .isObject(),
]

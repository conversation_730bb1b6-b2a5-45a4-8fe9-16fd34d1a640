const { body, query } = require('express-validator');

const {
    headerValidator,
    pageValidator,
    perPageValidator,
    searchKeyValidator,
    timezoneBodyValidator
} = require("./CommonValidator");

const {
    CART_ACTIONS,
    ORDER_PUNCH_DEVICE_OS,
    ORDER_PUNCH_DEVICE_TYPES,
    ORDER_APP_TYPE,
    REGEX,
    ORDER_STATUS_TYPES,
    ORDER_ITEM_LISTING_TYPES,
    DURATION_PERIOD_OPTIONS,
    PORTAL_TYPE,
    ORDER_EXPORT
} = require("../../Configs/constants");

const validator = require("validator")

const tenantIdBodyValidator = [
    body('tenantId', "Please provide valid tenant id").isInt({ min: 1000 }).toInt()
];

const tenantIdQueryValidator = [
    query('tenantId', "Please provide valid tenant id").isInt({ min: 1000 }).toInt()
]

exports.placeOrder = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("customerUserRoleId", "Please provide valid customer user role id").isMongoId(),
    body("customerName", "Please provide valid customer name").trim().notEmpty(),
    body("customerPrimaryContactName", "Please provide valid customer primary contact name").trim().notEmpty(),
    body("salesPersonRoleId", "Please provide valid sales person user role id").isMongoId(),
    body("salePersonName", "Please provide valid salesperson name").trim().notEmpty(),
    body("branchId", "Please provide valid branch id").isMongoId(),
    body("customer_legal_name", "Please provide valid customer legal name").trim().notEmpty(),

    body("orderPunchDeviceType", "Please provide valid device type").isIn(Object.values(ORDER_PUNCH_DEVICE_TYPES)),
    body("orderPunchDeviceOs", "Please provide valid device os").isIn(Object.values(ORDER_PUNCH_DEVICE_OS)),
    body("orderAppType", "Please provide valid order app type").isIn(Object.values(ORDER_APP_TYPE)),

    body("orderRemark", "Please provide 'orderRemark'").optional().trim().notEmpty(),

    // Shipping related fields' validation
    body("ShippingAddress", "Please provide shipping address")
        .optional()
        .trim(),
    // .notEmpty(),

    body("cityId", "Please provide valid city id")
        .optional(),
    // .isMongoId(),

    body("regionId", "Please provide valid region id")
        .optional(),
    // .isMongoId(),

    body("shippingMobileNumber", "Please provide shipping mobile number")
        .optional()
        .trim(),
    // .notEmpty(),

    body("shippingCountryCode", "Please provide valid shipping country code")
        .optional(),
    // .custom((value) => REGEX.COUNTRY_CODE.test(value)),

    body("regionName", "Please provide valid region name")
        .optional()
        .trim(),
    // .notEmpty(),

    body("cityName", "Please provide city name")
        .optional()
        .trim(),
    // .notEmpty(),

    body("shippingCoordinates", "Please provide valid shipping coordinate object")
        .optional(),
    // .isObject(),

    body("shippingCoordinates.lat", "Please provide valid shipping lat")
        .optional()
        .trim(),
    // .notEmpty(),

    body("shippingCoordinates.lng", "Please provide valid shipping lng")
        .optional()
        .trim(),
    // .notEmpty(),

    // body("externalId", "please provide external id").trim().notEmpty(),
];

exports.getOrder = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("orderId", "Please provide valid order id").trim().notEmpty().isMongoId(),
    query("perPage").optional().isInt({ min: 1 }).toInt(),
    query("page").optional().isInt({ min: 1 }).toInt(),
    query("orderDetails").optional().trim().notEmpty().toBoolean(),
    query("orderItemListingType", "Please provide valid order item listing types").optional().isIn(Object.values(ORDER_ITEM_LISTING_TYPES)),
    query("branchId", "Please provide valid branch id").optional().isMongoId()
]

exports.updateOrderStatus = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("orderIds", "Please provide valid order id").isArray({ min: 1 }).custom((value, { req, location, path }) => {
        if (Array.isArray(value)) {
            for (let i = 0; i < value.length; i++) {
                if (!validator.isMongoId(value[i])) return false;
            }
        }
        return true
    }),
    body("orderStatus", "Please provide valid order status").isIn(Object.values(ORDER_STATUS_TYPES))
];

exports.getOrderStats = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("userRoleId", "Please provide valid user role id").isMongoId()
];

exports.getDashboardSummary = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("branchId", "Please provide valid branch id").trim().notEmpty().isMongoId()
];

exports.listOrders = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("branchId", "Please provide valid branch id").optional().isMongoId(),
    query("orderStatus", "Please provide valid order status").optional().isIn(Object.values(ORDER_STATUS_TYPES)),
    query("perPage").if(query("onlyCount").not().equals("true")).isInt({ min: 1 }).toInt(),
    query("page").if(query("onlyCount").not().equals("true")).isInt({ min: 1 }).toInt(),
    query("orderAppType", "Please provide valid app type").optional().isIn(Object.values(ORDER_APP_TYPE)),
    query("salesPersonRoleId", "Please provide valid 'salesPersonRoleId'").if(query("orderAppType").equals(ORDER_APP_TYPE.SALES_APP)).isMongoId(),
    query("customerUserRoleId", "Please provide valid 'customerUserRoleId'").if(query("orderAppType").equals(ORDER_APP_TYPE.CUSTOMER_APP)).isMongoId(),
    query("duration").optional().isInt({ min: 1 }).toInt(),
    query("durationPeriod", "PLease provide valid duration period").optional().isIn(Object.values(DURATION_PERIOD_OPTIONS)),
    query("portalAppTypeFilter", "Please provide valid portalAppTypeFilter").optional().isIn(Object.values(ORDER_APP_TYPE)),
    query("masterPriceId", "please provide valid master price id").optional().isMongoId(),

    // FOR fetching order count of particular status type orders only...
    query("orderCountStatusType", "Please provide valid orderCountStatusType").if(query("onlyCount").equals("true")).isIn(Object.values(ORDER_STATUS_TYPES)),
    query("onlyCount", "Please provide valid value of onlyCount").optional().isBoolean().toBoolean(),
];


exports.moveDraftToCart = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("customerUserRoleId", "Please provide valid customer role id").isMongoId(),
    query("salesPersonRoleId", "Please provide valid salesperson role id").isMongoId(),
    query("draftId", "please provide valid draft id").isMongoId(),
    // query("masterPriceId").isMongoId()
];

exports.updateOrderValidator = [
    ...headerValidator,

    body("customerUserRoleId", "Please provide valid customer user role id.")
        .isMongoId(),

    body("updateInformation", "Please provide valid update information.")
        .isObject(),

    body("updateInformation.shippingMobileNumber", "Please provide valid shippingMobileNumber in updateInformation.")
        .trim()
        .notEmpty()
        .toInt(),
]

exports.getOrderReportList = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...pageValidator,
    ...perPageValidator,

    query("startDate", "Please provide valid 'startDate'")
        .trim()
        .notEmpty(),

    query("userRoleIds", "Please provide valid 'userRoleIds")
        .isArray({ min: 1 })
        .bail()
        .custom((value, { req, location, path }) => {
            const hasInvalidId = value.some((val) => !mongoose.Types.ObjectId.isValid(val));
            return !hasInvalidId;
        }),

    query("endDate", "Please provide valid 'endDate'")
        .trim()
        .notEmpty()
        .bail()
        .custom((value, { req }) => {
            const startDate = new Date(req.query.startDate)
            const endDate = new Date(value)

            if (startDate > endDate) {
                throw new Error(`Start date (${req.query.startDate}) should be less than or equal to end date (${value})`)
            }
            return true
        }),

    query("timezone", "Please provide valid 'timezone'")
        .trim()
        .notEmpty()
];

exports.cartActions = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("actionType", "Please provide valid action type")
        .isIn([
            CART_ACTIONS.ADD_ITEM_COMMENT,
            CART_ACTIONS.ADD_ITEM_TO_CART,
            CART_ACTIONS.EDIT_ITEM_PRICE,
            CART_ACTIONS.REMOVE_FROM_CART,
            CART_ACTIONS.UPDATE_QUANTITY
        ]),
    body("cartItemId", "Please provide valid cart item id").if(body("actionType")
        .isIn([
            // CART_ACTIONS.UPDATE_QUANTITY, ( frontend developer request, do it from product and variant id)
            // CART_ACTIONS.REMOVE_FROM_CART, ( frontend developer request, do it from product and variant id)
            CART_ACTIONS.ADD_ITEM_COMMENT,
            CART_ACTIONS.EDIT_ITEM_PRICE,
        ])).isMongoId(),
    body("customerUserRoleId", "Please provide valid customer user role id").isMongoId(),
    body("salesPersonRoleId", "Please provide valid sales person user role id").isMongoId(),

    body("productVariantId", "Please provide valid product id").if(body("actionType")
        .isIn([
            CART_ACTIONS.UPDATE_QUANTITY,
            CART_ACTIONS.ADD_ITEM_COMMENT,
            CART_ACTIONS.EDIT_ITEM_PRICE,
            CART_ACTIONS.ADD_ITEM_TO_CART
        ])).isMongoId(),
    // body("variantId", "Please provide valid variant id").optional().trim().notEmpty(),

    body("quantity", "Please provide valid quantity").if(body("actionType")
        .isIn([
            CART_ACTIONS.ADD_ITEM_TO_CART,
            CART_ACTIONS.UPDATE_QUANTITY
        ])).isInt({ min: 1 }).toInt(),
    body("uomId", "please provide valid uom").if(body("actionType").equals(CART_ACTIONS.ADD_ITEM_TO_CART)).isMongoId(),
    body("uomName", "Please provide valid uom name").if(body("actionType").equals(CART_ACTIONS.ADD_ITEM_TO_CART)).trim().notEmpty(),

    // body("basePrice", "Please provide valid base price").custom((value, { req, path, location }) => {
    //     const actionType = req.body.actionType;
    //     if ((actionType === CART_ACTIONS.ADD_ITEM_TO_CART || actionType === CART_ACTIONS.EDIT_ITEM_PRICE)
    //         && (isNaN(value) || value <= 0)) return false;

    //     return true;
    // }),
    body("masterPriceId", "Please provide master price id").if(body("actionType").equals(CART_ACTIONS.ADD_ITEM_TO_CART)).isMongoId(),
    // body("tax", "Please provide valid tax price").custom((value, { req, path, location }) => {
    //     const actionType = req.body.actionType;
    //     if ((actionType === CART_ACTIONS.ADD_ITEM_TO_CART || actionType === CART_ACTIONS.EDIT_ITEM_PRICE)
    //         && (isNaN(value) || value < 0)) return false;

    //     return true;
    // }),
    body("itemComment", "Please provide item comment").if(body("actionType").isIn([
        CART_ACTIONS.EDIT_ITEM_PRICE,
        CART_ACTIONS.ADD_ITEM_COMMENT
    ])).trim().notEmpty(),
    body("minQty", "invalid min qty").if(body("actionType").equals(CART_ACTIONS.ADD_ITEM_TO_CART)).isInt({ min: 1 }),
]

exports.cartDetails = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("customerUserRoleId", "Please provide valid customer user role id").isMongoId(),
];

exports.deleteCartItems = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("customerUserRoleId", "Please provide valid customer user role id").isMongoId(),
    query("cartItemIds", "Please provide valid cart item ids").isArray({ min: 1 }).custom(values => Array.isArray(values) && values.every(v => validator.isMongoId(v))),
];

exports.getCartItemCount = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("customerUserRoleId", "User role id is required.").isMongoId()
];

exports.createDraft = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("customerUserRoleId", "Please provide valid customer user role id").isMongoId(),
    body("salesPersonRoleId", "Please provide valid sales person user role id").isMongoId(),
    body("customerName", "Please provide valid customer name").trim().notEmpty(),
    body("customerPrimaryContactName", "Please provide valid customer primary contact name").trim().notEmpty(),
    body("customerId").optional().trim().notEmpty(),
    body("externalId").trim()
];

exports.draftDetails = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("draftId").isMongoId(),
]

exports.draftListing = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("customerUserRoleId").optional().isMongoId(),
    query("perPage").isInt({ min: 1 }).toInt(),
    query("page").isInt({ min: 1 }).toInt(),
    query("duration").optional().isInt({ min: 1 }).toInt(),
    query("durationPeriod", "PLease provide valid duration period").optional().isIn(Object.values(DURATION_PERIOD_OPTIONS))
];

exports.deleteDrafts = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("draftIds", "Please provide valid draft ids").isArray({ min: 1 }).custom((value) => Array.isArray(value) && value.every(v => validator.isMongoId(v))),
    // query("salesPersonRoleId", "Please provide salesperson id").isMongoId(),
];


exports.checkOrderUsers = [
    query("orderId", "Please provide valid order id").isMongoId(),
    query("token", "Please provide valid token id").isMongoId(),
];

exports.draftToCart = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("draftId", "Please provide valid draft id").isMongoId(),
    body("customerUserRoleId", "please provide valid customer user role id").isMongoId(),
];

exports.checkPreApproved = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("externalId", "Please provide external id")
        .trim()
        .notEmpty(),

    body("orderId", "Please provide valid order id")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
];

exports.exportOrders = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...timezoneBodyValidator,

    body("startDate", "Please provide valid 'startDate'")
        .trim()
        .notEmpty(),

    body("endDate", "Please provide valid 'endDate'")
        .trim()
        .notEmpty()
        .bail()
        .custom((value, { req }) => {
            const startDate = new Date(req.body.startDate)
            const endDate = new Date(value)

            if (startDate > endDate) {
                throw new Error(`Start date (${req.body.startDate}) should be less than or equal to end date (${value})`)
            }

            let differenceInMonths = moment(endDate).diff(moment(startDate), 'months', true)

            if (differenceInMonths > 3) {
                throw new Error(`Date range should not exceed more than 3 months`)
            }

            return true
        }),

    body("orderStatusList", "Please provide orderStatusList")
        .isArray({ min: 1 }),

    body("orderStatusList.*", "Please provide valid orderStatusList")
        .trim()
        .isIn(Object.keys(ORDER_EXPORT.ORDER_STATUS_LIST))
        .withMessage(
            `Please provide orderStatusList within ${Object.keys(ORDER_EXPORT.ORDER_STATUS_LIST)}`
        ),
]

exports.getOrderStatusList = [
    ...headerValidator
]

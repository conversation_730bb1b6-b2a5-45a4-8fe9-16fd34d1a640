const path = require("path")
const { body, query } = require('express-validator');

const {
    VALID_IMAGES_EXTS,
    IMAGE_LISTING,
    IMAGE_TYPE,
    DATA_SHEET } = require('../../Configs/constants');

const {
    headerValidator,
    tenantIdValidator,
    tenantIdQueryValidator,
    pageValidator,
    perPageValidator,
    tenantIdBodyValidator,
    searchKeyValidator,
} = require("./CommonValidator");

const validator = require("validator")

exports.imageNameValidator = [
    body("imageName", 'Please provide valid image name.')
        .trim()
        .notEmpty()
        .custom(value => {
            const ext = path.extname(value)

            if (!VALID_IMAGES_EXTS.includes(ext)) {
                return false
            }
            return true
        })
        .withMessage(`Image extension must be in ${Object.values(VALID_IMAGES_EXTS).join(", ")}`),
]


exports.addImageValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...this.imageNameValidator,

    body("imageSize", "Please provide valid image size.")
        .isInt(),

    body("s3Url", "Please provide valid s3 url.")
        .trim()
        .notEmpty(),
    body("productVariantId", "Please provide productId").isMongoId(),
    body("groupId", "please provide group id.").optional().isMongoId()

];

exports.listImages = [
    ...headerValidator,
    ...searchKeyValidator,

    query("tenantId", "Please provide tenant id").toInt().isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    query("imageType").isIn(Object.values(IMAGE_LISTING)).withMessage(`value should be in ${Object.values(IMAGE_LISTING).join(", ")}`),
    query("page", "Please provide page.").optional().toInt().isInt({ min: 1 }),
    query("perPage", "Please provide per page limit.").optional().toInt().isInt({ min: 1 }),
]

exports.deleteImages = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("imageIds", "Please provide valid 'imageIds'(min 1)")
        .isArray({ min: 1 }),

    query("imageIds.*", "Please provide valid 'imageIds'")
        .isMongoId()
]

exports.uploadSignatureValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...this.imageNameValidator,
    body("type").optional().isIn(Object.values(IMAGE_TYPE)).withMessage(`value should be in ${Object.values(IMAGE_TYPE).join(", ")}`)
];

exports.swapProductImages = [
    ...headerValidator,
    ...tenantIdValidator,
    body("imageIds", "Please provide valid image ids").isArray({ min: 2, max: 2 }).custom((values) => values.every(i => validator.isMongoId(i))),
]

exports.imagesMatch = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...pageValidator,
    ...perPageValidator,
    ...searchKeyValidator,

    query("linkedTenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please send valid linked tenant id."),
]

exports.imagesMatchAction = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("linkedTenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please send valid linked tenant id."),
    body("type", "please provide type").isIn(Object.values(DATA_SHEET.APPROVE_TYPE)).withMessage(`type value should be in ${Object.values(DATA_SHEET.APPROVE_TYPE).join(", ")}`),
    body("product", "please provide valid product details").optional().isArray(),
    body("product.*.productId", "Please provide product id").optional().isMongoId(),
    body("product.*.linkedTenantProductId", "please provide linked tenant product id.").optional().isMongoId(),
    body("product.*.variantId", "Please provide variant id").optional(),
    body("product.*.parentId", "Please provide parent id").optional(),
    body("product.*.linkedTenantVariantId", "please provide linked tenant variant id.").optional(),
    body("product.*.linkedParentId", "please provide linked tenant parent id.").optional()
]

exports.getImagesByItemNumbers = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("itemNumbers", "Please provide valid 'itemNumbers'(i.e. min 1)")
        .isArray({ min: 1 }),

    body("itemNumbers.*")
        .trim()
]

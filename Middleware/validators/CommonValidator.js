const { header, body, query, buildCheckFunction } = require('express-validator')

const { ENTITY_STATUS } = require('../../Configs/constants')
const checkBodyAndQuery = buildCheckFunction(["body", "query"])

exports.headerValidator = [
    header("authorization", "Please provide authorization.")
        .trim()
        .notEmpty(),

    header('deviceToken', 'Device token is required.')
        .trim()
        .notEmpty(),

    header("userroleid", "User role id is required.")
        .trim()
        .notEmpty(),
]

exports.tenantIdValidator = [
    checkBodyAndQuery("tenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please send valid tenant id."),
];

exports.tenantIdBodyValidator = [
    body("tenantId", "Please provide 'tenantId'")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),
]

exports.tenantIdQueryValidator = [
    query("tenantId", "Please provide 'tenantId'")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),
]

exports.pageValidator = [
    query("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageValidator = [
    query("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.pageBodyValidator = [
    body("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageBodyValidator = [
    body("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.isActiveValidator = [
    body("isActive", "Please send isActive value either true or false.")
        .isBoolean()
]

exports.searchKeyValidator = [
    query("searchKey")
        .trim()
        .custom((value) => {
            if (value && value.toString().length < 3) {
                return false;
            }
            return true;
        })
        .withMessage("Minimum search value must be 3 characters long."),
];

exports.getAttributeValidator = [
    ...this.headerValidator,
    ...this.tenantIdValidator,
    ...this.pageValidator,
    ...this.perPageValidator,
    ...this.searchKeyValidator,

    query("status")
        .trim()
        .isIn(Object.values(ENTITY_STATUS))
        .withMessage(
            `Please provide value within ${Object.values(ENTITY_STATUS)}`
        ),
];

exports.idsValidator = [
    body("ids", "Please send ids in array.")
        .isArray(),
]

exports.deleteAttributesValidator = [
    ...this.headerValidator,
    ...this.tenantIdValidator,
    ...this.idsValidator,
]

exports.timezoneBodyValidator = [
    body("timezone", "Please provide valid timezone")
        .trim()
        .notEmpty(),
]

exports.lastSyncedAtQueryValidator = [
    query("lastSyncedAt", "Please provide valid 'lastSyncedAt' date")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'lastSyncedAt'"),
]

exports.isInitialSyncQueryValidator = [
    query("isInitialSync", "Please provide valid 'isInitialSync'")
        .optional()
        .isBoolean()
        .withMessage("'isInitialSync' must be a boolean value"),
]

exports.cursorStringIdQueryValidator = [
    query("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
]

exports.cursorMongoIdQueryValidator = [
    query("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid 'cursor' as MongoDB ObjectId"),
]

exports.lastSyncedAtBodyValidator = [
    body("lastSyncedAt", "Please provide valid 'lastSyncedAt' date")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'lastSyncedAt'"),
]

exports.isInitialSyncBodyValidator = [
    body("isInitialSync", "Please provide valid 'isInitialSync'")
        .optional()
        .isBoolean()
        .withMessage("'isInitialSync' must be a boolean value"),
]

exports.cursorMongoIdBodyValidator = [
    body("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid 'cursor' as MongoDB ObjectId"),
]

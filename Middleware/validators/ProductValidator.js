const { header, body, query } = require('express-validator');
const isEmpty = require('lodash.isempty');

const validator = require("validator")

const { NEW_PRODUCT_TYPE, SELECTED_ATTRIBUTES_SET_TYPE, PRODUCT_SORT_BY, PRODUCT_SORT_TYPE, ACTIVE_STATUS_ENUM, PRODUCT_SEARCH_TYPES, ENTITY_STATUS, VARIANT_TYPE_ENUM, FAVORITE_PRODUCT_TYPE, INVENTORY_PRODUCT_LISTING_SORT_TYPES, PRODUCT_VARIANT_TYPES, PRODUCT_TYPE } = require('../../Configs/constants');

const { headerValidator, tenantIdValidator, pageValidator, perPageValidator, tenantIdQueryValidator, tenantIdBodyValidator, searchKeyValidator } = require("./CommonValidator");

const commonAuthValidators = [
    ...headerValidator,
    header("userroleid",).trim().notEmpty(),
];

exports.addProduct = [
    ...commonAuthValidators,
    body("tenantId").toInt().isInt({ min: 1000 }),
    body("product", "please provide valid product details").isObject(),
    body("product.isActive").isBoolean(),
    body("product.title", "Please provide valid title").trim().notEmpty(),
    body("product.itemNumber", "Please provide valid itemNumber").trim().notEmpty(),
    body("product.type", "Please provide valid type").isIn([NEW_PRODUCT_TYPE.PARENT, NEW_PRODUCT_TYPE.SINGLE]),
    body("product.barcodes", "Please provide valid barcodes").isArray(),
    body("product.barcodes.*", "Barcode must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),
    body("product.tags", "Please provide valid barcodes").isArray(),
    body("product.tags.*", "Tags must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),
    body("product.secondaryLanguageTitle").optional().trim(),
    body("product.description", "Please provide product description").optional().trim().notEmpty(),
    body("product.secondaryLanguageDescription", "Please provide product description in secondary language").optional().trim().notEmpty(),
    body("product.brand", "please provide brand").isMongoId(),
    body("product.family", "please provide category").optional().isMongoId(),
    body("product.category", "please provide category").optional().isMongoId(),
    body("product.subCategory", "please provide subCategory").optional().isMongoId(),
    body("product.attributes", "Please provide attributes array").isArray(),
    body("product.attributes.*.attribute_id", "missing attribute id").isMongoId(),
    body("product.attributes.*.type", "missing attribute value").optional().isIn(Object.values(SELECTED_ATTRIBUTES_SET_TYPE)),
    // body("product.attributes.*.value", "missing attribute value").trim().notEmpty(),
    body("product.attributeSet", "Please provide valid value of attribute set id").optional().isMongoId(),
    // SINGLE TYPE PRODUCTS
    body("product.uomMapping.uom", "Please provide uom for SINGLE product type").custom((value, { req, path, location }) => {
        if (req.body.product.type === NEW_PRODUCT_TYPE.SINGLE && !validator.isMongoId(value)) return false

        return true;
    }),
    body("product.uomMapping.qtyCtn", "Please provide qtyCtn for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).optional().isInt({ min: 0 }),
    body("product.uomMapping.minQty", "Please provide minQty for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isInt({ min: 0 }),
    body("product.priceMapping", "price mapping missing in product").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isArray({ min: 0 }),
    body("product.priceMapping.*.master_price_id", "Please provide master_price_id for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.priceMapping.*.price", "Please provide price for SINGLE product type").custom((value, { req, path, location }) => {
        if (req.body.product.type === NEW_PRODUCT_TYPE.SINGLE && (isNaN(value) || value < 0)) return false;

        return true
    }),

    body("product.inventoryMapping", "Inventory array is missing").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isArray({ min: 0 }),
    body("product.inventoryMapping.*.branch_id", "Please provide branch id").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.inventoryMapping.*.warehouse_id", "Please provide warehouse id").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.inventoryMapping.*.quantity", "Please provide quantity").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isInt({ min: 0 }).toInt(),

    // VARIANT TYPE PRODUCTS
    body("product.variants", "variants object missing for VARIANT type product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isObject(),
    body("product.variants.type", "Invalid variant type value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isIn(Object.values(VARIANT_TYPE_ENUM)),
    body("product.variants.values", "Invalid variant values value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("product.groups", "invalid groups object").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isObject(),
    body("product.groups.type", "invalid groups type value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().trim().notEmpty(),
    body("product.groups.values", "invalid groups values").if(body("product.values").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isArray(),

    body("variantProducts", "Please provide valid array of variantProducts").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isArray(),
    body("variantProducts.*.itemNumber", "invalid item number of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(),
    body("variantProducts.*.variantName", "invalid name of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(),
    body("variantProducts.*.groupName", "invalid group name of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().trim(),
    body("variantProducts.*.isActive", "invalid value of isActive").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isBoolean(),
    body("variantProducts.*.barcodes", "Please provide valid barcodes of variant").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("variantProducts.*.barcodes.*", "Barcodes of variant must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),

    body("variantProducts.*.uomMapping.uom", "Please provide valid uom of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(),
    body("variantProducts.*.uomMapping.qtyCtn", "Please provide valid qtyCtn of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isInt({ min: 0 }),
    body("variantProducts.*.uomMapping.minQty", "Please provide valid minQty of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isInt({ min: 0 }),

    body("variantProducts.*.priceMapping", "Please provide valid array of variantProducts' priceMapping").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("variantProducts.*.priceMapping.*.master_price_id", "Please provide valid value of master_price_id").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.priceMapping.*.price", "Please provide valid value of price").custom((value, { req, path, location }) => {

        if (req.body.product.type === NEW_PRODUCT_TYPE.PARENT && (isNaN(value) || value < 0)) return false;

        return true;
    }),

    body("variantProducts.*.inventoryMapping.*.branch_id", "Please provide valid value of branch_id").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.inventoryMapping.*.warehouse_id", "Please provide valid value of warehouse_id").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.inventoryMapping.*.quantity", "Please provide valid value of quantity").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isInt({ min: 0 }).toInt(),
];

exports.getProductDetails = [
    ...commonAuthValidators,
    query("productId", "Please provide valid product id").isMongoId(),
    query("tenantId", "Please provide valid tenant id").toInt().isInt({ min: 1000 }),
    query("customerUserRoleId", "Please provide valid customer user role id").optional().isMongoId(),
    query("activeVariants", "Please provide valid value of activeVariants").optional().isBoolean().toBoolean(),
    query("salesPersonUserRoleId", "Please provide valid sales person user role id").optional().isMongoId()
];

exports.editProduct = [
    ...commonAuthValidators,
    body("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
    body("product", "please provide valid product details").isObject(),
    body("product.isActive", "Please provide valid isActive").isBoolean(),
    body("product.productId", "Please provide valid productId").isMongoId(),
    body("product.title", "Please provide valid title").trim().notEmpty(),
    body("product.type", "Please provide valid type").isIn([NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.PARENT]),
    body("product.itemNumber", "Please provide valid itemNumber").trim().notEmpty(),
    // body("product.addedBarcodes", "Please provide valid barcodes").isArray(), // This contains only newly added barcodes name strings
    // body("product.deletedBarcodes", "Please provide array of deletedBarcodes").isArray(), // deleted barcode PKs
    // body("product.addedTags", "Please provide array of addedTags").isArray(),
    // body("product.deletedTags", "Please provide array of deletedTags").isArray(), // deleted tags PKs
    body("product.barcodes", "Please provide valid barcodes").optional().isArray(),
    body("product.barcodes.*", "Barcode must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),

    body("product.tags", "Please provide valid tags").isArray(),
    body("product.tags.*", "Tags must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),
    body("product.secondaryLanguageTitle", "please provide product description in secondary language").optional().trim(),
    body("product.description", "please provide description").optional().trim().notEmpty(),
    body("product.secondaryLanguageDescription", "please provide secondaryLanguageDescription").optional().trim().notEmpty(),
    body("product.brand", "please provide brand").isMongoId(),
    body("product.family", "please provide category").optional().isMongoId(),
    body("product.category", "please provide category").optional().isMongoId(),
    body("product.subCategory", "please provide subCategory").optional().isMongoId(),
    body("product.attributes", "please provide array of attributes").isArray(),
    body("product.attributes.*.attribute_id", "please provide valid value of attribute_id").isMongoId(),
    // body("product.attributes.*.value", "please provide valid value of attribute-value").trim().notEmpty(),
    body("product.attributes.*.type", "missing attribute value").optional().isIn(Object.values(SELECTED_ATTRIBUTES_SET_TYPE)),
    body("product.attributeSet", "Please provide valid value of attribute set id").optional().isMongoId(),

    // SINGLE TYPE PRODUCTS
    body("product.uomMapping.uom", "Please provide uom for SINGLE product type").custom((value, { req, path, location }) => {
        if (req.body.product.type === NEW_PRODUCT_TYPE.SINGLE && !validator.isMongoId(value)) return false

        return true
    }),
    body("product.uomMapping.qtyCtn", "Please provide qtyCtn for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).optional().isInt({ min: 0 }),
    body("product.uomMapping.minQty", "Please provide minQty for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isInt({ min: 0 }),
    body("product.priceMapping", "Please provide array of price mappings").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isArray({ min: 0 }),
    body("product.priceMapping.*.master_price_id", "Please provide master_price_id for SINGLE product type").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.priceMapping.*.price", "Please provide price for SINGLE product type").custom((value, { req, path, location }) => {
        if (req.body.product.type === NEW_PRODUCT_TYPE.SINGLE && (isNaN(value) || value < 0)) return false;
        return true
    }),

    body("product.inventoryMapping", "Please provide array of inventoryMapping").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isArray({ min: 1 }),
    body("product.inventoryMapping.*.branch_id", "Please provide branch id").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.inventoryMapping.*.warehouse_id", "Please provide warehouse id").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isMongoId(),
    body("product.inventoryMapping.*.quantity", "Please provide quantity").if(body("product.type").equals(NEW_PRODUCT_TYPE.SINGLE)).isInt({ min: 0 }).toInt(),

    // VARIANT TYPE PRODUCTS
    // body("product.variants", "variants object missing for VARIANT type product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isObject(),
    // body("product.variants.type", "Invalid variant type value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isIn(Object.values(VARIANT_TYPE_ENUM)),
    // body("product.variants.values", "Invalid variant values value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    // body("product.groups", "invalid groups object").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isObject(),
    // body("product.groups.type", "invalid groups type value").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().trim().notEmpty(),
    // body("product.groups.values", "invalid groups values").if(body("product.values").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isArray(),

    body("variantProducts", "Please provide array of variantProducts").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("variantProducts.*.variantId", "Please provide valid value of variantId").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(), // product-id_variant-id_group-id
    // body("variantProducts.*.variantValueId", "Please provide valid value of variantId").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    // body("variantProducts.*.groupValueId", "Please provide valid value of groupId").if(body("product.type").optional().equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    // body("variantProducts.*.variantName", "Invalid name of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.VARIANT)).trim().notEmpty(),
    // body("variantProducts.*.groupName", "Invalid group name of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.VARIANT)).optional().trim(),
    body("variantProducts.*.isActive", "Please provide valid value of isActive").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isBoolean(),
    body("variantProducts.*.barcodes", "Please provide barcodes of variant").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("variantProducts.*.barcodes.*", "Barcodes of variant must be at least 3 characters long")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        }),

    body("variantProducts.*.uomMapping.uom", "Please provide valid uom").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(),
    body("variantProducts.*.uomMapping.qtyCtn", "Please provide valid qtyCtn").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).optional().isInt({ min: 0 }),
    body("variantProducts.*.uomMapping.minQty", "Please provide valid minQty").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isInt({ min: 0 }),

    body("variantProducts.*.priceMapping", "please provide price mapping array").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isArray(),
    body("variantProducts.*.priceMapping.*.master_price_id", "please provide valid master_price_id of variant").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.priceMapping.*.price").custom((value, { req, path, location }) => {
        if (req.body.product.type === NEW_PRODUCT_TYPE.PARENT && (isNaN(value) || value < 0)) return false;
        return true;
    }).withMessage("please provide valid price of variant"),

    body("variantProducts.*.inventoryMapping.*.branch_id", "Please provide valid value of branch_id").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.inventoryMapping.*.warehouse_id", "Please provide valid value of warehouse_id").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isMongoId(),
    body("variantProducts.*.inventoryMapping.*.quantity", "Please provide valid value of quantity").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).isInt({ min: 0 }).toInt(),
    body("variantProducts.*.itemNumber", "invalid item number of variant product").if(body("product.type").equals(NEW_PRODUCT_TYPE.PARENT)).trim().notEmpty(),
];

exports.listProducts = [
    ...commonAuthValidators,
    ...searchKeyValidator,

    query("tenantId", "Please provide valid tenant id").toInt().isInt({ min: 1000 }),
    query("type", "Please provide valid type value").optional().isIn(Object.values(ACTIVE_STATUS_ENUM)),
    query("page", "Please provide valid page value").isInt({ min: 1 }).toInt(),
    query("perPage", "Please provide valid perPage value").isInt({ min: 1 }).toInt(),
    query("withVariants", "please provide valid withVariants value").optional().isBoolean().toBoolean(),
    query("sortBy").optional().isIn(Object.values(PRODUCT_SORT_BY)).withMessage(`Please provide sortBy as in ${Object.values(PRODUCT_SORT_BY).join(", ")}`),
    query("sortType").optional().if(query("sortBy")).isIn(Object.values(PRODUCT_SORT_TYPE)).withMessage(`Please provide sortType as in ${Object.values(PRODUCT_SORT_TYPE).join(", ")}`),
];

exports.hideOutOfStockQueryValidator = [
    query("hideOutOfStock")
        .optional()
        .exists()
        .withMessage("Please provide valid 'hideOutOfStock'")
        .bail()
        .isBoolean()
        .withMessage("Please provide hide out of stock's value in boolean")
        .toBoolean(),
]

exports.branchIdQueryValidator = [
    query("branchId", "Please provide a valid 'branchId'")
        .if(
            query("hideOutOfStock")
                .equals("true")
        )
        .exists()
        .bail()
        .trim()
        .notEmpty(),
]

exports.searchProductValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...pageValidator,
    ...perPageValidator,
    ...this.hideOutOfStockQueryValidator,
    ...this.branchIdQueryValidator,

    query("searchType", `searchType must be ${Object.values(PRODUCT_SEARCH_TYPES).join(" or ")}`)
        .if(
            query("searchType")
                .exists()
        )
        .trim()
        .isIn(Object.values(PRODUCT_SEARCH_TYPES)),

    query("searchKey", "Please provide 'searchKey' with its value.")
        .if(
            query("searchType")
                .isIn([PRODUCT_SEARCH_TYPES.AUTO_COMPLETE, PRODUCT_SEARCH_TYPES.SEARCH])
        )
        .exists()
        .bail()
        .trim()
        .isLength({ min: 3 })
        .withMessage("Minimum search value must be 3 characters long."),

    query("priceListId", "Please provide valid price list id.")
        .trim()
        .isMongoId(),

    query("isPrimaryLanguage")
        .optional()
        .exists()
        .withMessage("Please provide valid 'isPrimaryLanguage'.")
        .bail()
        .isBoolean()
        .withMessage("Please provide primary language's value in boolean.")
        .toBoolean(),

    query("filters", "Please provide 'filters' with its value.")
        .if(
            query("searchType")
                .equals(PRODUCT_SEARCH_TYPES.FILTER)
        )
        .exists()
        .bail()
        .trim()
        .notEmpty()
        .bail()
        .customSanitizer(value => {
            return JSON.parse(value)
        })
        .custom(value => {
            const noFilters = Object.values(value).every(ele => isEmpty(ele))
            return !noFilters
        }),

    query("salesPersonUserRoleId", "Please provide valid sales person user role id")
        .optional()
        .isMongoId()

]

exports.deleteProducts = [
    ...commonAuthValidators,
    ...tenantIdQueryValidator,

    query("productVariantIds", "Please provide valid 'productVariantIds'(i.e.min 1)")
        .isArray({ min: 1 }),

    query("productVariantIds.*", "Please provide valid 'productVariantIds'")
        .isMongoId()
];

exports.updateProductsStatus = [
    ...commonAuthValidators,
    ...tenantIdBodyValidator,

    body("type")
        .trim()
        .isIn([ENTITY_STATUS.ACTIVE, ENTITY_STATUS.INACTIVE]),

    body("singleProductIds", "Please provide valid 'singleProductIds'")
        .optional()
        .isArray({ min: 1 })
        .bail()
        .custom(values => values.every(pId => validator.isMongoId(pId))),

    body("variantProductIds.*.parentId", "Please provide 'parentId' in 'variantProductIds' array")
        .if(body("variantProductIds").isArray({ min: 1 }))
        .isMongoId(),

    body("variantProductIds.*.updateIds", "Please provide valid 'updateIds' in 'variantProductIds' array")
        .if(body("variantProductIds").isArray({ min: 1 }))
        .isArray({ min: 1 })
        .bail()
        .custom(values => values.every(pId => validator.isMongoId(pId))),
];

exports.checkExistingItemNumber = [
    ...commonAuthValidators,
    query("itemNumber", "Please provide valid product ids").trim().notEmpty(),
    query("tenantId", "Please provide valid tenant id").toInt().isInt({ min: 1000 }),
];

exports.inventoryProductList = [
    ...commonAuthValidators,
    ...tenantIdQueryValidator,
    ...perPageValidator,
    ...pageValidator,
    ...searchKeyValidator,

    query("priceId", "Please provide valid price id").optional().trim().notEmpty().isMongoId(),
    query("branchId", "Please provide valid branch id").optional().trim().notEmpty().isMongoId(),
    query("sortType", "Please provide valid sort by value").optional().isIn(Object.values(INVENTORY_PRODUCT_LISTING_SORT_TYPES)),
    query('sortBy', "please provide valid sort by").if(query("sortType").notEmpty()).toInt().isIn([-1, 1]),
    query("status", "Please provide status")
        .optional()
        .isIn(Object.values(ACTIVE_STATUS_ENUM))
        .withMessage(`'status' must be ${Object.values(ACTIVE_STATUS_ENUM).join(" or ")}`),

    query("quantityRange", "Please provide 'quantityRange' with its value.")
        .optional()
        .customSanitizer(value => {
            if (value) {
                return JSON.parse(value)
            }
            return {};
        })
        .custom(value => {
            if (typeof value === "object" && Object.keys(value).length > 0) {
                if (!value?.from) {
                    throw new Error("Please provide 'from' in 'quantityRange'")
                }

                if (!value?.to) {
                    throw new Error("Please provide 'to' in 'quantityRange'")
                }
            }
            else {
                throw new Error("Please provide 'quantityRange' with its value.")
            }
            return true;
        }),
];

exports.rewardProductList = [
    ...headerValidator,
    ...tenantIdValidator,
    ...pageValidator,
    ...perPageValidator,
    ...searchKeyValidator
]

exports.updateVariantGroupInfo = [
    ...headerValidator,
    ...tenantIdValidator,

    body("productId", "Please provide valid 'productId'")
        .isMongoId(),

    body("variants", "Please provide valid 'variants'")
        .isArray({ min: 1 })
        .withMessage("Please provide value(i.e. min 1) in 'variants'"),

    body("variants.*").trim(),

    body("groupType", "Please provide valid 'groupType'")
        .if(
            body("groups")
                .isArray()
        )
        .trim()
        .notEmpty(),

    body("groups", "Please provide valid 'groups'")
        .if(
            body("groupType")
                .trim()
                .notEmpty()
        )
        .isArray({ min: 1 })
        .withMessage("Please provide value(i.e. min 1) in 'groups'"),

    body("groups.*").trim(),
];

exports.updateVariantTypeInfo = [
    ...commonAuthValidators,
    ...tenantIdBodyValidator,

    body("productId", "Please provide valid productId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    body("variantTypeId", "Please provide valid variantTypeId")
        .trim()
        .notEmpty(),

    body("name", "Please provide valid name")
        .trim()
        .notEmpty(),
]

exports.changeVariantOrder = [
    ...commonAuthValidators,
    body("tenantId").isInt({ min: 1000 }).toInt(),
    body("productVariantIds", "Please provide valid values").isArray({ min: 2, max: 2 }).custom(values => Array.isArray(values) && values.every(pId => validator.isMongoId(pId)))
];


exports.productCount = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("statusType", "Please provide type").optional().isIn(Object.values(ACTIVE_STATUS_ENUM))
];


exports.barcodeDetails = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("barcode", 'Please provide barcode').trim().notEmpty()
];

exports.favoriteProduct = [
    ...headerValidator,
    body("tenantId").isInt({ min: 1000 }).toInt(),
    body("ids", "Please provide valid product ids.").isArray().custom(values => Array.isArray(values) && values.every(pId => validator.isMongoId(pId))),
    body("type", "Please provide valid type")
        .isIn(Object.values(FAVORITE_PRODUCT_TYPE))
        .withMessage(`value should be in ${Object.values(FAVORITE_PRODUCT_TYPE).join(", ")}`)
];

exports.favoriteProductList = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
    query("favoriteProductId", 'Please provide last product id').optional().trim().notEmpty().isMongoId()
]

exports.getTagsValidator = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,
    query("perPage", "Please provide per page limit.").toInt().isInt({ min: 1 }),
];

const isTypeChangeToVariant =
    body("typeChangeTo")
        .equals(NEW_PRODUCT_TYPE.VARIANT)

exports.changeProductType = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("productVariantId", "Please provide valid 'productVariantId'")
        .isMongoId(),

    body("typeChangeTo", "Please provide valid 'typeChangeTo'")
        .isIn([NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.VARIANT]),

    body("parentId", "Please provide valid 'parentId'")
        .if(isTypeChangeToVariant)
        .isMongoId(),

    body("variantId", "Please provide valid 'variantId'")
        .if(isTypeChangeToVariant)
        .isMongoId(),

    body("groupId", "Please provide valid 'groupId'")
        .if(isTypeChangeToVariant)
        .optional()
        .isMongoId(),
];

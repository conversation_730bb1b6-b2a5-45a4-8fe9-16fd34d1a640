const { body } = require('express-validator');

const {
    headerValidator,

    tenantIdQueryValidator,
    tenantIdBodyValidator,

    lastSyncedAtQueryValidator,
    lastSyncedAtBodyValidator,

    cursorMongoIdQueryValidator,
    cursorMongoIdBodyValidator,

    isInitialSyncQueryValidator,

    perPageValidator,
    perPageBodyValidator,
} = require('./CommonValidator');

exports.getMasterData = [
    ...headerValidator,
    ...tenantIdQueryValidator,
];

exports.getDataValidator = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...lastSyncedAtQueryValidator,
    ...cursorMongoIdQueryValidator,
    ...perPageValidator,
];

exports.getBodyDataValidator = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...lastSyncedAtBodyValidator,
    ...cursorMongoIdBodyValidator,
    ...perPageBodyValidator,
];

exports.getCommonDataValidator = [
    ...this.getDataValidator,
    ...isInitialSyncQueryValidator,
];

exports.getDealProductListValidator = [
    ...this.getBodyDataValidator,

    body("dealIds", "Please provide valid 'dealIds'(i.e.min 1) in array")
        .isArray({ min: 1 }),

    body("dealIds.*", "Please provide valid 'dealIds'")
        .isMongoId()
        .withMessage("Please provide valid 'dealIds' as MongoDB ObjectId"),
];

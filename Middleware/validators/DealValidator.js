const { body, query } = require('express-validator');
const { DEAL_TYPE, DISCOUNT_TYPE, DEAL_STATUS, DEAL_SORT_BY, PRODUCT_SORT_TYPE, DEAL_SORT_TYPE } = require("../../Configs/constants");
const {
	headerValidator,
	tenantIdBodyValidator,
	tenantIdQueryValidator,
	pageValidator,
	perPageValidator,
	searchKeyValidator,
	pageBodyValidator,
	perPageBodyValidator,
} = require("./CommonValidator");

const {
	hideOutOfStockQueryValidator,
	branchIdQueryValidator,
} = require('./ProductValidator');

const dealIdValidator = [
	body("dealId", "Please provide valid deal id")
		.trim()
		.isMongoId()
]

const priceIdValidator = [
	body("priceId", "Please provide valid price id")
		.trim()
		.isMongoId()
]

const itemNumbersValidator = [
	body("itemNumbers", "Please provide valid itemNumbers")
		.isArray({ min: 1 })
		.bail()
		.customSanitizer(value => {
			return [...new Set(value)]
		})
]

exports.createDeal = [
	...headerValidator,
	...tenantIdBodyValidator,
	...priceIdValidator,

	body("dealType", "Please provide deal type")
		.isIn(Object.values(DEAL_TYPE))
		.withMessage(
			`Please provide status as in ${Object.values(DEAL_TYPE).join(", ")}`,
		),
];

exports.updateDeal = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealName", "Please provide deal name").trim().notEmpty(),
	body("secondaryDealName", "Please provide secondary deal name").trim().notEmpty(),
	body("dealFromDate", "Please provide deal from date").trim().notEmpty(),
	body("dealToDate", "Please provide deal to date").trim().notEmpty(),
	body("salesPersonId", "Please provide sales person ids").isArray().isMongoId(),
	body("products", "Please provide deal products").optional().isArray(),
	body("products.*.dealType", "Please provide deal type")
		.isIn(Object.values(DEAL_TYPE))
		.withMessage(
			`Please provide deal type as in ${Object.values(DEAL_TYPE).join(", ")}`,
		)
		.trim()
		.notEmpty(),

	// ------ Discount related fields' validation ------
	body("products.*.discountType", "Please provide discount type")
		.if(body("products.*.dealType").equals(DEAL_TYPE.DISCOUNT))
		.isIn(Object.values(DISCOUNT_TYPE))
		.withMessage(
			`Please provide discount type as in ${Object.values(DISCOUNT_TYPE).join(
				", ",
			)}`,
		)
		.trim()
		.notEmpty(),

	body("products.*.percent", "Please provide percent")
		.if(body("products.*.dealType").equals(DEAL_TYPE.DISCOUNT))
		.trim()
		.notEmpty(),

	body("products.*.amount", "Please provide amount")
		.if(body("products.*.dealType").equals(DEAL_TYPE.DISCOUNT))
		.trim()
		.notEmpty(),

	body("products.*.discountedPrice", "Please provide discountedPrice in 'products.*.discountedPrice'")
		.if(body("products.*.dealType").equals(DEAL_TYPE.DISCOUNT))
		.isFloat({ min: 1 })
		.withMessage("Please provide valid 'discountedPrice'(i.e. min 1) in 'products.*.discountedPrice'"),

	body("products.*.firstTier", "Please provide first tier")
		.if(body("products.*.dealType").equals(DEAL_TYPE.BULK_PRICING))
		.isObject(),
	body("products.*.secondTier", "Please provide second tier")
		.if(body("products.*.dealType").equals(DEAL_TYPE.BULK_PRICING))
		.isObject(),
	body("products.*.thirdTier", "Please provide third tier")
		.if(body("products.*.dealType").equals(DEAL_TYPE.BULK_PRICING))
		.isObject(),
	body("products.*.buyProduct", "Please provide buy product")
		.if(body("products.*.dealType").equals(DEAL_TYPE.BUY_X_AND_GET_Y))
		.trim()
		.notEmpty(),
	body("products.*.freeProduct", "Please provide free product")
		.if(body("products.*.dealType").equals(DEAL_TYPE.BUY_X_AND_GET_Y))
		.trim()
		.notEmpty(),
	body("dealProductSequence", "Please provide deal products sequence").optional().isArray(),
	body("dealSortType", "Please provide dealSortType")
		.isIn(Object.values(DEAL_SORT_TYPE))
		.withMessage(
			`Please provide dealSortType as in ${Object.values(DEAL_SORT_TYPE).join(", ")}`,
		)
		.trim()
		.notEmpty(),
];

exports.listValidator = [
	...headerValidator,
	...tenantIdQueryValidator,
	...pageValidator,
	...perPageValidator,
	...searchKeyValidator,
];

exports.dealList = [
	...this.listValidator,

	query("priceId", "Please provide valid priceId")
		.optional()
		.trim()
		.notEmpty()
		.bail()
		.isMongoId(),

	query("dealType", "Please provide valid 'dealType'")
		.optional()
		.trim()
		.notEmpty()
		.bail()
		.isIn(Object.values(DEAL_TYPE))
		.withMessage(`Please provide dealType ${Object.values(DEAL_TYPE).join(" or ")}`),

	query("dealStatus", "Please provide valid 'dealStatus'")
		.optional()
		.trim()
		.notEmpty()
		.bail()
		.isIn(Object.values(DEAL_STATUS))
		.withMessage(`Please provide dealStatus ${Object.values(DEAL_STATUS).join(" or ")}`),
];


exports.dealDetail = [
	...headerValidator,
	query("dealId", "Please provide deal id").trim().notEmpty().isMongoId(),
];

exports.productList = [
	...this.listValidator,
	query("dealFromDate", "Please provide deal from date").trim().notEmpty(),
	query("dealToDate", "Please provide deal to date").trim().notEmpty(),
	query("priceId", "Please provide price id").trim().notEmpty().isMongoId(),
	query("dealId", "Please provide deal id").trim().notEmpty().isMongoId(),
];

exports.addDealProduct = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealFromDate", "Please provide deal from date").trim().notEmpty(),
	body("dealToDate", "Please provide deal to date").trim().notEmpty(),
	body("products", "Please provide product id").isArray({ min: 1 }),
];

exports.dealProductList = [
	...this.listValidator,
	query("dealId", "Please provide deal id").trim().notEmpty().isMongoId(),
	query("priceId", "Please provide price id").trim().notEmpty().isMongoId(),
	query("sortBy").optional().isIn(Object.values(DEAL_SORT_BY)).withMessage(`Please provide sortBy as in ${Object.values(DEAL_SORT_BY).join(", ")}`),
	query("sortType").optional().if(query("sortBy")).isIn(Object.values(PRODUCT_SORT_TYPE)).withMessage(`Please provide sortType as in ${Object.values(PRODUCT_SORT_TYPE).join(", ")}`),
];

exports.dealDelete = [
	...headerValidator,
	...tenantIdBodyValidator,
	body("dealIds", "Please provide deal id").isArray({ min: 1 }),
];

exports.dealStatus = [
	...headerValidator,
	...tenantIdBodyValidator,
	body("dealIds", "Please provide deal id").isArray({ min: 1 }),
	body("status", "Please provide proper deal status")
		.trim()
		.notEmpty()
		.toUpperCase()
		.isIn([
			DEAL_STATUS.CANCELLED,
			DEAL_STATUS.PAUSED,
			DEAL_STATUS.SCHEDULED,
			DEAL_STATUS.ARCHIVED,
		]),
];

exports.dealsProductList = [
	...headerValidator,
	...tenantIdQueryValidator,
	...hideOutOfStockQueryValidator,
	...branchIdQueryValidator,

	query("dealType", "Please provide deal type")
		.optional()
		.isIn(Object.values(DEAL_TYPE))
		.withMessage(
			`Please provide deal type as in ${Object.values(DEAL_TYPE).join(", ")}`,
		),

	query("productLimit", "Please provide 'productLimit'")
		.optional()
		.toInt()
		.isInt({ min: 10 })
		.withMessage("Please provide valid(i.e. min 10) 'productLimit'"),

	query("salesPersonUserId", "Please provide 'salesPersonUserId'")
		.optional()
		.trim()
		.notEmpty()
		.bail()
		.isMongoId()
		.withMessage("Please provide valid 'salesPersonUserId'"),

	query("priceId", "Please provide 'priceId'")
		.trim()
		.notEmpty()
		.bail()
		.isMongoId()
		.withMessage("Please provide valid 'priceId'"),
];

exports.deletedDealProduct = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealProductId", "Please provide deal product id").isArray({ min: 1 }),
]
exports.productStatus = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealProductId", "Please provide deal product id").isArray({ min: 1 }),
	body("status", "Please provide status").trim().notEmpty(),
]

exports.changeDealProductSequence = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealProductId", "Please provide deal product id").trim().notEmpty().isMongoId(),
	body("dealProductSequence", "Please provide deal product sequence").trim().notEmpty()
]

exports.nextDealProductList = [
	...headerValidator,
	query("dealId", "Please provide valid dealId").trim().isMongoId(),
	query("dealProductSequence", "Please provide deal dealProductSequence").trim().notEmpty(),
	query("nextDealProductCount", "Please provide deal nextDealProductCount").toInt().trim().notEmpty()
]

exports.checkValidItemsValidator = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,
	...priceIdValidator,
	...itemNumbersValidator,
]

exports.productListValidator = [
	...headerValidator,
	...tenantIdBodyValidator,
	...priceIdValidator,
	...pageBodyValidator,
	...perPageBodyValidator,
	...itemNumbersValidator,
];


exports.updateDealStatistics = [
	...headerValidator,
	...tenantIdBodyValidator,
	...dealIdValidator,

	body("dealProductId", "Please provide valid dealProductId")
		.isMongoId(),

	body("customerUserRoleId", "Please provide valid 'customerUserRoleId'")
		.optional()
		.isMongoId(),
]

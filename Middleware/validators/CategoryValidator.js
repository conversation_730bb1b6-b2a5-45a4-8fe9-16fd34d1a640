const { body, query } = require('express-validator');
const isEmpty = require('lodash.isempty');
const validator = require("validator")

const {
    VALUES,
    ENTITY_STATUS,
} = require('../../Configs/constants');

const {
    headerValidator,
    tenantIdValidator,
    pageValidator,
    perPageValidator,
    tenantIdQueryValidator,
    searchKeyValidator,
} = require('./CommonValidator');

exports.addAndUpdateCategory = [
    ...headerValidator,
    ...tenantIdValidator,
    body('categoryId', 'Please provide category id').optional().trim().notEmpty().isMongoId(),
    body('categoryName', 'Please enter valid category name').trim().notEmpty(),
    body('secondaryCategoryName', 'Please enter valid secondary category name').trim().notEmpty(),
    body("type").isIn(Object.values(VALUES.category)).withMessage(`Please provide type as in ${Object.values(VALUES.category).join(", ")}`),
    body('parentFamilyId', 'Please provide parent family id').optional().trim().notEmpty().isMongoId(),
    body('parentCategoryId', 'Please provide parent category id').optional().trim().notEmpty().isMongoId(),
    body('isActive', 'Please provide is active').trim().notEmpty().isBoolean(),
];

exports.categoryList = [
    ...headerValidator,
    ...tenantIdValidator,
    ...searchKeyValidator,

    query("type").isIn(Object.values(VALUES.category)).withMessage(`Please provide type as in ${Object.values(VALUES.category).join(", ")}`),
    query('categoryId', 'Please provide category id').optional().isMongoId(),
    query("status").isIn(Object.values(ENTITY_STATUS)).withMessage(`Please provide type as in ${Object.values(ENTITY_STATUS).join(", ")}`),
    query('imageName', 'Please provide image name')
        .optional()
        .trim()
        .notEmpty(),

];

exports.sequenceCategory = [
    ...headerValidator,
    body("type").isIn(Object.values(VALUES.category)).withMessage(`Please provide type as in ${Object.values(VALUES.category).join(", ")}`),
    body('categoryId', 'Please provide category id').isArray()
];

exports.deleteCategory = [
    ...headerValidator,
    ...tenantIdValidator,
    body("type").isIn(Object.values(VALUES.category)).withMessage(`Please provide type as in ${Object.values(VALUES.category).join(", ")}`),
    body('familyId', 'Please provide family id').trim().notEmpty().isMongoId(),
    body('categoryId', 'Please provide category id').optional().trim().notEmpty().isMongoId(),
    body('subCategoryId', 'Please provide sub category id').optional().trim().notEmpty().isMongoId(),
    body('newFamilyId', 'Please provide new family id').optional().notEmpty().isMongoId(),
    body('newCategoryId', 'Please provide new category id').optional().trim().notEmpty().isMongoId(),
    body('newSubCategoryId', 'Please provide new sub category id').optional().trim().notEmpty().isMongoId()
]

exports.getCategory = [
    ...headerValidator,
    query('categoryId', 'Please provide category id').trim().notEmpty().isMongoId()
];

exports.categoryListValidator = [
    ...headerValidator,
    ...tenantIdValidator,

    body("priceListId", "Please provide valid price list id.")
        .trim()
        .isMongoId(),

    body("filters", "Please provide 'filters' with its value.")
        .optional()
        .exists()
        .bail()
        .isObject()
        .withMessage("'filters' must be an object.")
        .bail()
        .custom(value => {
            const noFilters = Object.values(value).every(ele => isEmpty(ele))
            return !noFilters
        }),

    body("familyIds", "Please provide valid 'familyIds'")
        .optional()
        .isArray({ min: 1 })
        .bail()
        .custom(values => values.every(id => validator.isMongoId(id)))
        .bail()
        .customSanitizer(values => {
            return values.map(id => new mongoose.Types.ObjectId(id))
        }),
]

exports.allCategoryProductList = [
    ...headerValidator,
    ...tenantIdValidator,
    ...pageValidator,
    ...perPageValidator,

    query('familyId', 'Please provide family id').optional().trim().notEmpty().isMongoId(),
    query('status').isIn(Object.values(ENTITY_STATUS)).withMessage(`Please provide status as in ${Object.values(ENTITY_STATUS).join(", ")}`),
];

exports.productList = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...pageValidator,
    ...perPageValidator,

    query('familyId', "Please provide valid 'familyId'")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    query('categoryId', "Please provide valid 'categoryId'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    query('subCategoryId', "Please provide valid 'subCategoryId'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    query('status').isIn(Object.values(ENTITY_STATUS)).withMessage(`Please provide status as in ${Object.values(ENTITY_STATUS).join(", ")}`),

    query("filters", "Please provide 'filters' with its value.")
        .optional()
        .exists()
        .bail()
        .trim()
        .notEmpty()
        .bail()
        .customSanitizer(value => {
            return JSON.parse(value)
        })
        .custom(value => {
            const noFilters = Object.values(value).every(ele => isEmpty(ele))
            return !noFilters
        }),

    query("salesPersonUserRoleId", "Please provide valid salesPersonUserRoleId")
        .optional()
        .isMongoId()
];

exports.productSequence = [
    ...headerValidator,

    body('productId', 'Please provide valid productId')
        .trim()
        .isMongoId(),

    body('productSequence', 'Please provide valid productSequence')
        .trim()
        .notEmpty()
];

exports.topCategory = [
    ...headerValidator,
    ...tenantIdQueryValidator
];
const { body, query } = require('express-validator');
const { DATA_SHEET } = require('../../Configs/constants');
const { headerValidator, tenantIdValidator, tenantIdQueryValidator, tenantIdBodyValidator } = require('./CommonValidator');

exports.dataTypeValidator = [
    body("dataType", "Please provide data type")
        .isIn(Object.values(DATA_SHEET.DATA_TYPE))
        .withMessage(`Please provide dataType ${Object.values(DATA_SHEET.DATA_TYPE).join(" or ")}`)
]

exports.operationTypeValidator = [
    body("operationType", "Please provide operation type")
        .isIn(Object.values(DATA_SHEET.OPERATION_TYPE))
        .withMessage(`Please provide operationType ${Object.values(DATA_SHEET.OPERATION_TYPE).join(" or ")}`)
]

exports.fileNameValidator = [
    body("fileName", "Please provide file name")
        .trim()
        .notEmpty()
]

exports.dataSheet = [
    ...headerValidator,
    ...tenantIdValidator,
    body("dataType", "Please provide data type").isIn(Object.values(DATA_SHEET.DATA_TYPE)).withMessage(`dataType value should be in ${Object.values(DATA_SHEET.DATA_TYPE).join(", ")}`),
    body("operationType", "Please provide operation type").equals(DATA_SHEET.OPERATION_TYPE.UPDATE).withMessage('operationType value should be UPDATE')
]

exports.getDataSheetValidator = [
    ...tenantIdQueryValidator,

    query("dataType", "Please provide data type")
        .isIn(Object.values(DATA_SHEET.DATA_TYPE))
        .withMessage(`Please provide dataType ${Object.values(DATA_SHEET.DATA_TYPE).join(" or ")}`),

    query("operationType", "Please provide operation type")
        .isIn(Object.values(DATA_SHEET.OPERATION_TYPE))
        .withMessage(`Please provide operationType ${Object.values(DATA_SHEET.OPERATION_TYPE).join(" or ")}`),

    query("apiVersion", "please provide valid apiVersion")
        .optional()
        .toInt()
]


exports.updateDataSheetValidator = [
    ...tenantIdBodyValidator,
    ...this.fileNameValidator,
    ...this.dataTypeValidator,
    ...this.operationTypeValidator,

    body("type", "Please provide data sheet type")
        .isIn(Object.values(DATA_SHEET.TYPE))
        .withMessage(`Please provide operationType ${Object.values(DATA_SHEET.TYPE).join(" or ")}`),

    body("importData", "Please provide valid importData value")
        .optional()
        .isBoolean()
        .withMessage("Please provide importData value in boolean.")
        .toBoolean(),

    body("fileId", "Please provide valid fileId")
        .if(
            body("importData").equals(true)
        )
        .trim()
        .notEmpty()
        .isMongoId(),

    body("approveType", "Please provide valid approve type")
        .if(
            body("importData").equals(true)
        )
        .isIn(Object.values(DATA_SHEET.APPROVE_TYPE))
        .withMessage(`Please provide approveType ${Object.values(DATA_SHEET.APPROVE_TYPE).join(" or ")}`),

    body("selectRow", "Please provide valid rows")
        .if(
            body("approveType").equals(DATA_SHEET.APPROVE_TYPE.SELECTED)
        )
        .isArray(),
]

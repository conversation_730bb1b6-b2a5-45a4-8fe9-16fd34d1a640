const { body } = require('express-validator')

const {
    headerV<PERSON><PERSON><PERSON>,
    tenantIdValidator,
    isActiveValidator,
} = require('../CommonValidator')

exports.addAttributeValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...isActiveValidator,

    body("attributeName", "Please provide attribute name.")
        .trim()
        .notEmpty(),

    body("secondaryLanguageAttributeName", "Please provide secondary language's attribute name.")
        .trim()
        .notEmpty(),
]

exports.editAttributeValidator = [
    ...this.addAttributeValidator,

    body("_id", "Please provide valid attribute id.")
        .trim()
        .notEmpty()
        .isMongoId(),
]

const { body, query } = require('express-validator')

const {
    headerValidator,
    tenantIdValidator,
    isActiveValidator,
} = require('../CommonValidator')

exports.addAttributeSetValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...isActiveValidator,

    body("attributeSetName", "Please provide attribute set name.")
        .trim()
        .notEmpty(),

    body("secondaryLanguageAttributeSetName", "Please provide secondary language's attribute set name.")
        .trim()
        .notEmpty(),
]

exports.editAttributeSetValidator = [
    ...this.addAttributeSetValidator,

    body("_id", "Please provide valid attribute set id.")
        .trim()
        .notEmpty()
        .isMongoId(),
]

exports.updateAttributeAssociationValidator = [
    ...headerValidator,
    ...tenantIdValidator,

    body("attributeSetId", "Please provide valid attribute set id.")
        .trim()
        .notEmpty()
        .isMongoId(),

    body("attributeIds", "Please send attribute ids in array.")
        .isArray({max: 20, min: 1})
        .withMessage("Please send attribute id(s).")
]

exports.getAssociatedAttributesValidator = [
    query("_id", "Please provide valid attribute set id.")
        .trim()
        .notEmpty()
        .isMongoId(),
];

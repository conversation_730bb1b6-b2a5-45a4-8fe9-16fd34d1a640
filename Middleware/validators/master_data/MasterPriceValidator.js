const { body } = require('express-validator')

const {
    headerV<PERSON><PERSON><PERSON>,
    tenantIdValidator,
    isActiveValidator,
} = require('../CommonValidator')

exports.addPriceValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...isActiveValidator,

    body("priceName", "Please provide price name.")
        .trim()
        .notEmpty(),

    body("secondaryLanguagePriceName", "Please provide secondary language's price name.")
        .trim()
        .notEmpty(),

    body("isDefault", "Please provide isDefault")
        .isBoolean()
]

exports.editPriceValidator = [
    ...this.addPriceValidator,

    body("_id", "Please provide valid price id.")
        .trim()
        .notEmpty()
        .isMongoId(),
]

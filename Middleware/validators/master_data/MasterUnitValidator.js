const { body } = require('express-validator')

const {
    headerV<PERSON><PERSON><PERSON>,
    tenantIdValidator,
    isActiveValidator,
} = require('../CommonValidator')

exports.addUnitValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...isActiveValidator,

    body("unitName", "Please provide unit name.")
        .trim()
        .notEmpty(),

    body("secondaryLanguageUnitName", "Please provide secondary language's unit name.")
        .trim()
        .notEmpty(),
]

exports.editUnitValidator = [
    ...this.addUnitValidator,

    body("_id", "Please provide valid unit id.")
        .trim()
        .notEmpty()
        .isMongoId(),
]

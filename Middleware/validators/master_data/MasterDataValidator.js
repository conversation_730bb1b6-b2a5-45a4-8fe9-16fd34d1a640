const { body } = require('express-validator')

const { MASTER_DATA_ENTITIES } = require('../../../Configs/constants')
const { headerValidator, tenantIdValidator, idsValidator, isActiveValidator } = require('../CommonValidator')

exports.entityValidator = [
    body("entity")
        .trim()
        .isIn(Object.values(MASTER_DATA_ENTITIES))
        .withMessage(`Please provide value within ${Object.values(MASTER_DATA_ENTITIES)}`),
]

exports.updateStatusValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...idsValidator,
    ...this.entityValidator,
    ...isActiveValidator,
]

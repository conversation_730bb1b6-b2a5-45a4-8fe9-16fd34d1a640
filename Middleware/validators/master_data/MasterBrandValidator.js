const { body } = require('express-validator')

const {
    headerValida<PERSON>,
    tenantIdValidator,
    isActiveValidator,
} = require('../CommonValidator')

exports.addBrandValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    ...isActiveValidator,

    body("brandName", "Please provide brand name.")
        .trim()
        .notEmpty(),

    body("secondaryLanguageBrandName", "Please provide secondary language's brand name.")
        .trim()
        .notEmpty(),
]

exports.editBrandValidator = [
    ...this.addBrandValidator,

    body("_id", "Please provide valid brand id.")
        .trim()
        .notEmpty()
        .isMongoId(),
]

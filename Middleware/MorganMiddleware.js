const morgan = require("morgan")

const whiteListBodyURLs = [
    "POST /productService/order"
]

// Custom token in Morgan to log request body
morgan.token("body", (req, res) => {
    const {
        method,
        originalUrl,
        body,
        headers: {
            userroleid,
            userDetails,
            devicetoken,
            devicetype,
            portalType,
            deviceTypeDetected,
            deviceaccesstype,
            version,
            build,
        } = {},
    } = req

    return whiteListBodyURLs.includes(`${method} ${originalUrl}`)
        ? JSON.stringify({
            headers: {
                userroleid,
                userDetails,
                devicetoken,
                devicetype,
                portalType,
                deviceTypeDetected,
                deviceaccesstype,
                version,
                build,
            },
            body,
        })
        : undefined
})

const skipLog = function (req, res) {
    if (
        req.url.includes(".log") ||
        req.originalUrl === "/" ||
        [
            "/robots.txt",
            "/favicon.ico"
        ].includes(req.url)
    ) {
        return true
    }
    return false
}

// Function to set up middleware
const morganMiddleware = (app) => {
    // Setup Morgan with custom format
    app.use(
        morgan(
            "\n Morgan => Req: :status :method :url | ResTime: :response-time ms | TotalResTime: :total-time ms | ReqBody: :body \n",
            {
                stream: {
                    // Configure Morgan to use our custom logger with the http severity
                    write: (message) => logger.http(message.trim()),
                },
                skip: skipLog,
            }
        )
    )
}

module.exports = morganMiddleware

const { validationResult } = require('express-validator')

exports.InternalServiceMiddleware = (request, response, next) => {

    const errors = validationResult(request).formatWith(({ msg }) => msg)

    if (!errors.isEmpty())
        return response.handler.badRequest(
            undefined,
            errors.array()
        );

    request.headers.userDetails = JSON.parse(request.headers.userdetails);

    return next();
}
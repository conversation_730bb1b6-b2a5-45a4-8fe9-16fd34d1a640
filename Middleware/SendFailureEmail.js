const { sendEmail } = require("../Configs/Mailer")

const {
    ORDER_APP_TYPE,
    EMAIL_ID,
    VALUES,
} = require("../Configs/constants")

exports.default = async (req, errors) => {
    try {
        const {
            originalUrl,
            method,
            headers: {
                userroleid,
                userDetails: {
                    _id,
                    first_name,
                    last_name,
                    email,
                    mobile_number,
                    country_code,
                    is_active,
                    is_deleted,
                } = {},
                devicetoken,
                devicetype,
                portalType,
                deviceTypeDetected,
                deviceaccesstype,
                version,
                build,
            } = {},
            body: {
                tenantId,
                orderAppType,
                customer_legal_name,
            },
        } = req

        if (
            ["/productService/order", "/productService/order/placeOrderFromDraft"].includes(originalUrl) &&
            !VALUES.IS_DEV_ENV &&
            method === "POST"
        ) {
            const isPlaceOrderFromDraft = originalUrl.includes("placeOrderFromDraft")
            const orderCreatorUserRoleId = userroleid
            const userFirstName = first_name || ""
            const userLastName = last_name || ""

            const orderCreatorName =
                orderAppType === ORDER_APP_TYPE.CUSTOMER_APP && !isPlaceOrderFromDraft
                    ? customer_legal_name
                    : (
                        (userFirstName || userLastName) &&
                        `${userFirstName} ${userLastName}`
                    )

            const payload = {
                "from": EMAIL_ID.NOTIFICATION,
                // "to": EMAIL_ID.BRIJESH,
                "to": EMAIL_ID.FARIS,
                "cc": [
                    EMAIL_ID.YOUSEF,
                    EMAIL_ID.YASHASHWINI,
                    EMAIL_ID.INDRAJEET,
                    EMAIL_ID.BRIJESH,
                    EMAIL_ID.RAJAN,
                ],
                "subject": `Failed To Place Order`,
                "data": {
                    "html": `
                        Failed to place order${isPlaceOrderFromDraft ? " from the draft" : ""}.
                        <br/><br/>

                        <b> Environment: </b> ${VALUES.ENVIRONMENT}
                        <br/><br/>

                        <b> Tenant Id: </b> ${tenantId || "N/A"}
                        <br/><br/>

                        <b> API: </b> ${method} ${originalUrl}
                        <br/><br/>

                        <b> API Request Headers: </b>
                        <pre> ${JSON.stringify(
                        {
                            userDetails: {
                                _id,
                                first_name,
                                last_name,
                                email,
                                mobile_number,
                                country_code,
                                is_active,
                                is_deleted,
                            },
                            devicetoken,
                            devicetype,
                            portalType,
                            deviceTypeDetected,
                            deviceaccesstype,
                            version,
                            build,
                        },
                        undefined,
                        4
                    )}
                        </pre>

                        <b> API Request Body: </b>
                        <pre> ${JSON.stringify(
                        {
                            orderCreatorName,
                            orderCreatorUserRoleId,
                            ...req.body,
                        },
                        undefined,
                        4
                    )}
                        </pre>

                        <b> Reason: </b> ${errors}
                    `
                }
            }
            await sendEmail(payload)
        }
    }
    catch (error) {
        logger.error(error, {
            errorMessage: "Error in sending place order failure email"
        })
    }
}

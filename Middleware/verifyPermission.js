const {
    PRIMITIVE_ROLES
} = require("../Configs/constants");

exports.verifyPermission = (permission) => async (req, res, next) => {
    try {
        const userRoleDetails = req.headers.userRoleDetails;
        if (!userRoleDetails)
            return res.handler.forbidden('access_denied');

        const userPermissions = userRoleDetails.permission;
        const tenantDetail = req.headers.tenantDetail;

        const moduleServicePermissions = tenantDetail?.services.reduce((data, service) => {
            if (permission[service.key] !== undefined) {
                data[service.key] = service.permission
            }

            return data
        }, {}) ?? {}

        let canAccess = false

        Object.keys(permission).forEach(module => {
            const actions = Array.isArray(permission[module]) ? permission[module] : [permission[module]]

            actions.forEach(action => {
                const tenantHasModuleAccess = moduleServicePermissions[module]?.[action] ?? true;
                const userHasModuleAccess = userPermissions[module]?.[action] ?? false;

                if (tenantHasModuleAccess && userHasModuleAccess) {
                    canAccess = true
                }
            })
        })

        if (canAccess) {
            return next();
        } 
        else {
            return res.handler.forbidden('access_denied');
        }
    } 
    catch (error) {
        return res.handler.serverError(error);
    }
};

{"name": "hawak-product-backend", "version": "1.0.0", "description": "product service", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "createAPIDoc": "node Doc/create.js", "updateAPIDoc": "node Doc/update-postman-json.js", "prepare": "husky", "commitlint": "commitlint --edit"}, "dependencies": {"@sendgrid/mail": "^7.7.0", "aws-sdk": "^2.616.0", "axios": "^0.19.2", "bwip-js": "^3.2.2", "cors": "^2.8.5", "cron": "^1.8.2", "dotenv": "^8.2.0", "exceljs": "^4.3.0", "express": "^4.17.1", "express-validator": "^6.4.0", "lodash.difference": "^4.5.0", "lodash.isempty": "^4.4.0", "lodash.mapkeys": "^4.6.0", "lodash.snakecase": "^4.1.1", "lodash.startcase": "^4.4.0", "lodash.uniq": "^4.5.0", "moment": "^2.24.0", "moment-timezone": "^0.5.40", "mongoose": "^8.5.3", "morgan": "^1.10.0", "postman-to-openapi": "^2.9.0", "sqs-consumer": "^5.8.0", "swagger-ui-express": "^4.6.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^19.2.1", "@commitlint/config-conventional": "^19.1.0", "@eslint/js": "^9.30.1", "eslint": "^9.30.1", "globals": "^16.3.0", "husky": "^9.0.11", "nodemon": "^2.0.20"}}
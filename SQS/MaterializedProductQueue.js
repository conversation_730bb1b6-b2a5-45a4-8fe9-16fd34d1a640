const AWS = require("aws-sdk")

const { Consumer } = require("sqs-consumer")

const { VALUES } = require("../Configs/constants")

class MaterializedProductQueue {

    constructor() {
        this.sqs = new AWS.SQS(
            {
                accessKeyId: process.env.AWS_ACCESS_KEY,
                secretAccessKey: process.env.AWS_SECRET_KEY,
                region: process.env.AWS_REGION,
            }
        )
        this.queueUrl = process.env.MATERIALIZED_PRODUCT_SQS_URL
    }

    sendMessageToSQS = ({ MessageBody, MessageGroupId }) => {
        return this.sqs.sendMessage(
            {
                QueueUrl: this.queueUrl,
                MessageBody: JSON.stringify(MessageBody),
                MessageGroupId,
            }
        ).promise()
    }

    consumeMessageFromSQS = (cb) => {
        logger.info("📞 CALLED: MaterializedProductQueue/consumeMessageFromSQS")

        if (!VALUES.IS_APP_RUNNING_ON_SERVER) {
            logger.info("❌ APP_NOT_RUNNING_ON_SERVER: MaterializedProductQueue/consumeMessageFromSQS")
            return
        }

        const consumer = Consumer.create({
            queueUrl: this.queueUrl,
            sqs: this.sqs,
            attributeNames: ["All"],
            batchSize: 10, // The number of messages to request from SQS when polling (default 1, max 10)
            shouldDeleteMessages: false, // Default to true, if you don't want this package to delete messages from sqs, set this to false.
            handleMessageBatch: cb,
        })

        consumer.on("error", (err) => {
            logger.error(err, {
                errorMessage: "consumer: MaterializedProductQueue/consumeMessageFromSQS"
            })
        })

        consumer.on("processing_error", (err) => {
            logger.error(err, {
                errorMessage: "consumer: (processing_error) MaterializedProductQueue/consumeMessageFromSQS"
            })
        })

        consumer.on("timeout_error", (err) => {
            logger.error(err, {
                errorMessage: "consumer: (timeout_error) MaterializedProductQueue/consumeMessageFromSQS"
            })
        })

        consumer.start()
        logger.info("✅ INITIALIZED: MaterializedProductQueue/consumeMessageFromSQS")
    }

    deleteMessageBatch = (Entries) => {
        return this.sqs.deleteMessageBatch(
            {
                QueueUrl: this.queueUrl,
                Entries,
            }
        ).promise()
    }
}

module.exports = MaterializedProductQueue

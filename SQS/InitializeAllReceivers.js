const { initializeDealStatReceiver } = require('./DealStatisticsQueue');
const { initializeOrderExportReceiver } = require('./OrderExportQueue');
const { initializeOrderExportDLQ } = require('./OrderExportDLQ');

initializeDealStatReceiver()
initializeOrderExportReceiver()
initializeOrderExportDLQ()

/**
 * @description Initialize MaterializedProduct's consumer
 */
const MaterializedProductQueue = new (require("./MaterializedProductQueue"))
const ProductSQS = new (require("../Controllers/SQS/ProductSQS"))

MaterializedProductQueue.consumeMessageFromSQS(ProductSQS.handleMessageBatch)


/**
 * @description Initialize Product Metadata Update's consumer
 */
const ProductMetadataUpdateQueue = new (require("./ProductMetadataUpdateQueue"))
const ProductMetadataUpdateSQS = new (require("../Controllers/SQS/ProductMetadataUpdateSQS"))

ProductMetadataUpdateQueue.consumeMessageFromSQS(ProductMetadataUpdateSQS.handleMessageBatch)

/**
 * @description Initialize Delete Image's consumer
 */
const DeleteImageSQS = require("../Controllers/SQS/DeleteImageSQS")
const DeleteImageQueue = require("./DeleteImageQueue")

DeleteImageQueue.consumeMessageFromSQS(DeleteImageSQS.handleMessageBatch)

/**
 * @description Initialize Deleted Materialized Product's consumer
 */
const DeletedMaterializedProductSQS = require("../Controllers/SQS/DeletedMaterializedProductSQS")
const DeletedMaterializedProductQueue = require("./DeletedMaterializedProductQueue")

DeletedMaterializedProductQueue.consumeMessageFromSQS(DeletedMaterializedProductSQS.handleMessageBatch)

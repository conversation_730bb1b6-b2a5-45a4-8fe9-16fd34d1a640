const AWS = require('aws-sdk');
const { Consumer } = require('sqs-consumer');

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const { VALUES } = require('../Configs/constants');

/**
 * this function will be active only in pm2 environment
 * @param {JSON} messageBody it must have camelCase
 * @param {*} tenantId it should be string or number
 * @returns {Promise<void>}
 */
const sendDealStatMessageToSQS = function (messageBody, tenantId) {
    return new Promise(
        function (resolve, reject) {
            if (VALUES.IS_APP_RUNNING_ON_SERVER) // send message to AWS SQS only when it is deployed on server
                return sqs.sendMessage(
                    {
                        QueueUrl: process.env.DEAL_STATISTICS_SQS_URL,
                        MessageBody: JSON.stringify(messageBody),
                        MessageGroupId: tenantId?.toString(),
                        // MessageAttributes: {}
                    },
                    function (error, data) {
                        if (error) {
                            logger.error(error)
                            reject(error);
                        }
                        else {
                            resolve(data);
                        }
                    }
                );

            logger.info("NOT RUNNING IN PM2 CONFIG");
            resolve();
        })
};

const initializeDealStatReceiver = function () {
    logger.info("📞 CALLED: DealStatisticsQueue/initializeDealStatReceiver");

    if (VALUES.IS_APP_RUNNING_ON_SERVER) {
        const DealModel = new (require("../Models/DealModel"))()

        const dealStatReceiver = Consumer.create({
            queueUrl: process.env.DEAL_STATISTICS_SQS_URL,
            sqs,
            // messageAttributeNames: ["dealProduct"],
            pollingWaitTimeMs: 2500,
            handleMessageBatch: async function (messages) {
                // PERFORM OPERATION ON THE RECEIVED MESSAGES
                const updateStatPromises = [];
                for (let i = 0; i < messages.length; i++) {
                    const message = messages[i];
                    updateStatPromises.push(DealModel.updateDealStatistics(message))
                }
                try {
                    const receivedMessages = await Promise.allSettled(updateStatPromises);
                    return receivedMessages.filter(m => m.status !== "rejected").map(m => m.value);
                }
                catch (error) {
                    logger.error(error, {
                        errorMessage: "ERROR: COULD NOT PROCESS MESSAGES:"
                    })
                }
            },
            visibilityTimeout: 30,
            batchSize: 10,

        });
        dealStatReceiver.on('error', (err) => {
            logger.error(err)
        });

        dealStatReceiver.on('processing_error', (err) => {
            logger.error(err)
        });

        dealStatReceiver.on('timeout_error', (err) => {
            logger.error(err)
        });

        dealStatReceiver.start();
        logger.info('✅ INITIALIZED: DealStatisticsQueue/initializeDealStatReceiver');
        return;
    }

    logger.info("❌ APP_NOT_RUNNING_ON_SERVER: DealStatisticsQueue/initializeDealStatReceiver")
};

module.exports = {
    initializeDealStatReceiver,
    sendDealStatMessageToSQS,
};

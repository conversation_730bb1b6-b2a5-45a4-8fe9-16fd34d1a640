const AWS = require('aws-sdk');
const { Consumer } = require('sqs-consumer');

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const sentOrderExportMessageToSQS = (task) => {
    return sqs.sendMessage(
        {
            QueueUrl: process.env.ORDER_EXPORT_SQS_URL,
            MessageBody: JSON.stringify(task),
        }
    ).promise()
}

const initializeOrderExportReceiver = () => {
    const OrderExportController = new (require('../Controllers/OrderExportController'))()

    const consumer = Consumer.create({
        queueUrl: process.env.ORDER_EXPORT_SQS_URL,
        sqs,
        handleMessageBatch: async (messages) => {
            if (messages.length) {
                const tasks = messages.map(message => {
                    const exportTask = JSON.parse(message.Body)
                    return OrderExportController.startOrderExportTask(exportTask)
                })

                const results = await Promise.allSettled(tasks)
                results.forEach(result => {
                    if (result.reason) {
                        logger.error(result.reason)
                    }
                })
            }
        },
        batchSize: 5,
    })

    consumer.on('error', (err) => {
        logger.error(err)
    });

    consumer.on('processing_error', (err) => {
        logger.error(err)
    });

    consumer.on('timeout_error', (err) => {
        logger.error(err)
    });

    consumer.start();
}

module.exports = {
    initializeOrderExportReceiver,
    sentOrderExportMessageToSQS
};

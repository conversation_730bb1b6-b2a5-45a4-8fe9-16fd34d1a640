const AWS = require('aws-sdk');
const { Consumer } = require('sqs-consumer');

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const initializeOrderExportDLQ = () => {
    const OrderExportController = new (require('../Controllers/OrderExportController'))()

    const consumer = Consumer.create({
        queueUrl: process.env.ORDER_EXPORT_DLQ_URL,
        sqs,
        handleMessageBatch: async (messages) => {
            if (messages.length) {
                const tasks = messages.map(message => {
                    const exportTask = JSON.parse(message.Body)
                    return OrderExportController.handleOrderExportDLQ(exportTask)
                })

                const results = await Promise.allSettled(tasks)
                results.forEach(result => {
                    if (result.reason) {
                        logger.error(result.reason)
                    }
                })
            }
        },
        batchSize: 5,
    })

    consumer.on('error', (err) => {
        logger.error(err)
    });

    consumer.on('processing_error', (err) => {
        logger.error(err)
    });

    consumer.on('timeout_error', (err) => {
        logger.error(err)
    });

    consumer.start();
}

module.exports = {
    initializeOrderExportDLQ,
};

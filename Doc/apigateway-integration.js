require('dotenv').config();

const stripTrailingSlash = (str) => {
	return str.endsWith('/') ?
		str.slice(0, -1) :
		str;
};

module.exports = (apiPath, method) => {
	const uri = stripTrailingSlash(process.env.BASE_URL) + apiPath;

	return {
		"x-amazon-apigateway-integration": {
			"httpMethod": method.toUpperCase(),
			"uri": uri,
			"responses": {
				"default": {
					"statusCode": "200"
				}
			},
			"passthroughBehavior": "when_no_match",
			"type": "http_proxy"
		}
	}
}
{"openapi": "3.0.0", "info": {"title": "dev_user_service", "description": "development environment server", "version": "2023-01-10T13:01:57.038Z"}, "servers": [{"url": "https://dev-product-api.hawak.io/", "description": "development environment server"}], "tags": [{"name": "Master Data"}, {"name": "Master Data > Attribute"}, {"name": "Master Data > Attribute Set"}, {"name": "Master Data > Brand"}, {"name": "Master Data > Unit"}, {"name": "Master Data > Price"}, {"name": "Master Data > Attribute Association"}, {"name": "Images"}, {"name": "Category"}, {"name": "Category > Sort product list"}, {"name": "Products"}, {"name": "Products > variant Product"}, {"name": "Products > Tenant Allow Validation"}, {"name": "Products > Favorite Product"}, {"name": "Configuration"}, {"name": "Orders"}, {"name": "Orders > Cart"}], "paths": {"/productService/masterData/attribute": {"post": {"tags": ["Master Data > Attribute"], "summary": "Add Attribute", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "attributeName": "Earbuds", "secondaryLanguageAttributeName": "Earbudsss"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Attribute"], "summary": "Edit Attribute", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "637489cf4dfc9600126081ed", "isActive": true, "tenantId": 1131, "attributeName": "Helmet kings", "secondaryLanguageAttributeName": "Earbuddds"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute"], "summary": "Get Attribute List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Attribute"], "summary": "Delete Attribute", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/attributeSet": {"post": {"tags": ["Master Data > Attribute Set"], "summary": "Add Attribute Set", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "attributeSetName": "Office Products", "secondaryLanguageAttributeSetName": "office chiz"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Attribute Set"], "summary": "Edit Attribute Set", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "63724673f46f0cb0dc66fef5", "isActive": true, "tenantId": 1131, "attributeSetName": "Power Tools", "secondaryLanguageAttributeSetName": "urja samgri tools"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute Set"], "summary": "Get Attribute Set List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "gri"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Attribute Set"], "summary": "Delete Attribute Set", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/brand": {"post": {"tags": ["Master Data > Brand"], "summary": "Add Brand", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "brandName": "Dell", "secondaryLanguageBrandName": "del"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Brand"], "summary": "Edit Brand", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "637331be585729637044fb5c", "isActive": true, "tenantId": 1131, "brandName": "philips", "secondaryLanguageBrandName": "philips t"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Brand"], "summary": "Get Brand List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "2"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Brand"], "summary": "Delete Brand", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/unit": {"post": {"tags": ["Master Data > Unit"], "summary": "Add Unit", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "unitName": "indiUnit", "secondaryLanguageUnitName": "indi"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Unit"], "summary": "Edit Unit", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "6373522431cd87bd062f72e2", "isActive": true, "tenantId": 1131, "unitName": "KG", "secondaryLanguageUnitName": "ke giii"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Unit"], "summary": "Get Unit List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Unit"], "summary": "Delete Unit", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/price": {"post": {"tags": ["Master Data > Price"], "summary": "Add Price", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": false, "tenantId": 1131, "priceName": "dfdhfbdjcbdcjb", "secondaryLanguagePriceName": "test4"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Price"], "summary": "Edit Price", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "6374bd481f6d6bdec112ee96", "isActive": "true", "tenantId": 1131, "priceName": "test4", "secondaryLanguagePriceName": "wholesale pricee"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Price"], "summary": "Get Price List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Price"], "summary": "Delete Price", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/attributeAssociation": {"put": {"tags": ["Master Data > Attribute Association"], "summary": "Update Attribute Association", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "attributeSetId": "63724673f46f0cb0dc66fef5", "attributeIds": ["6374a9ed4dfc96001260858c", "6371d602f4d7553be184b791", "6371de646c6c8e71cec916b3", "sdsd"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute Association"], "summary": "Get Attribute Association List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "_id", "in": "query", "schema": {"type": "string"}, "example": "63731436e604224cd8124845"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/updateStatus": {"put": {"tags": ["Master Data"], "summary": "Update Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"entity": "PRICE", "tenantId": 1131, "isActive": false, "ids": ["6373538e31cd87bd062f72ec", "6374bf41cc15a0e922008be2"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageUpload": {"post": {"tags": ["Images"], "summary": "Add & Update Image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1531, "imageName": "63b500587291d2ea86c328d4_P2.jpeg", "imageSize": 1578, "s3Url": "https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1531/63b500587291d2ea86c328d4_P2.jpeg", "productId": "637df7223c114064c1a6f743"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Images"], "summary": "List of Images", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "imageType", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "sdajkd"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Images"], "summary": "Delete image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"imageId": ["636b6480835d286a8804b00b", "636b64ce853b8a6b64c449d8"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageUpload/getUploadSignature": {"post": {"tags": ["Images"], "summary": "Get Upload Signature", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1133, "imageName": "3-101-96-85211_P20.png"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/allProductList": {"get": {"tags": ["Category > Sort product list"], "summary": "All Category Product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "familyId", "in": "query", "schema": {"type": "string"}, "description": "family category id", "example": "6388485d7ef5eb001232304f"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "incrementLimit", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/categoryProductList": {"get": {"tags": ["Category > Sort product list"], "summary": "Category Product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "familyId", "in": "query", "schema": {"type": "string"}, "example": "6388485d7ef5eb001232304f"}, {"name": "categoryId", "in": "query", "schema": {"type": "string"}, "example": "6392e9c5ed866b0011dbdc88"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "appType", "in": "query", "schema": {"type": "string"}, "example": "NATIVE"}, {"name": "priceListId", "in": "query", "schema": {"type": "string"}, "example": "6374bd481f6d6bdec112ee96"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Category > Sort product list"], "summary": "Product sequnce", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"productId": "63848d4d69a85508d065d179", "productSequence": 1001}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category": {"post": {"tags": ["Category"], "summary": "Add and Edit category", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"categoryId": "636c8b35e4f5e5558c72f670", "categoryName": "NE<PERSON> family", "secondaryCategoryName": "NEW Secondary lang name", "tenantId": 1035, "type": "FAMILY", "isActive": true, "parentFamilyId": "636bae633faa4a5020118c0f", "parentCategoryId": "636cc289ace2943700645843"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Category"], "summary": "Change category sequnce", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"categoryId": ["636bae633faa4a5020118c0f", "637484cb8dd3c800120bd3cf", "6373668ad35c5b0012db9c19", "637484be8dd3c800120bd3cc", "637366d6d35c5b0012db9c1f", "637484768dd3c800120bd3b9", "6373674dd35c5b0012db9c23", "6374852b8dd3c800120bd3e7", "637484ab8dd3c800120bd3c5", "637484858dd3c800120bd3bd", "637485018dd3c800120bd3d9", "6373676cd35c5b0012db9c27", "637485118dd3c800120bd3e0", "63771137afe939001295298a", "6377676951ecdd00121aa744", "6377681751ecdd00121aa773"], "type": "FAMILY"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Category"], "summary": "Get  category list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1035"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "FAMILY/CATEGORY/SUBCATEGORY", "example": "CATEGORY"}, {"name": "categoryId", "in": "query", "schema": {"type": "string"}, "description": "if type = CATEGORY and SUBCATEGORY", "example": "636bae633faa4a5020118c0f"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "description": "ALL/ACTIVE/INACTIVE", "example": "ALL"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Category"], "summary": "Delete category", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/get-category": {"get": {"tags": ["Category"], "summary": "Get category", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "categoryType", "in": "query", "schema": {"type": "string"}, "example": "FAMILY"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Category"], "summary": "Get category List (With Product Count)", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "priceListId": "6374bd481f6d6bdec112ee96"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/changeVariantOrder": {"put": {"tags": ["Products > variant Product"], "summary": "swap variant products", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"variantIds": []}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/productVariants": {"get": {"tags": ["Products > variant Product"], "summary": "variant products of pro", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "productId", "in": "query", "schema": {"type": "string"}, "example": "637df77a3c114064c1a6f75f"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/productVariant": {"delete": {"tags": ["Products > variant Product"], "summary": "delete variant product", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "variantId", "in": "query", "schema": {"type": "string"}, "example": "63ab35c39101c8001209ed19_63ab35c39101c8001209ed1e"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1133"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/allowNewValidation": {"get": {"tags": ["Products > Tenant Allow Validation"], "summary": "Tenant active product", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1133"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "PRODUCTS"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/favoriteProduct": {"post": {"tags": ["Products > Favorite Product"], "summary": "Favorite product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "type": "FAVORITE", "ids": ["63a2a1c65495220012144b66"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Products > Favorite Product"], "summary": "Favorite product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "5"}, {"name": "favoriteProductId", "in": "query", "schema": {"type": "string"}, "description": "last product id", "example": "63b542568901ecaabc69fb02"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product": {"post": {"tags": ["Products"], "summary": "Add product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "product": {"isActive": true, "attributes": [], "title": "MacTest !@!@", "secondaryLanguageTitle": "MacTest !@!@", "itemNumber": "3-101-96-85q11112w", "barcodes": [], "type": "SINGLE", "brand": "637ca66d84b9540012d6ac30", "family": "6375c5be421cf4001212f453", "tags": [], "uomMapping": {"uom": "637c995784b9540012d6aa2a", "qtyCtn": "1", "minQty": "1"}, "inventoryMapping": [{"warehouse_id": "636ccd34fc3d2d01fc93c89f", "branch_id": "6333f1fb67e853cb742142a2", "quantity": "01"}, {"warehouse_id": "63b4270747da2100129b5b60", "branch_id": "63b4270747da2100129b5b5e", "quantity": "01"}], "priceMapping": [{"price": "01.01", "master_price_id": "637c997484b9540012d6aa3f"}, {"price": "01.01", "master_price_id": "639035510eb78c00125dd91c"}], "taxId": null}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Products"], "summary": "product details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "productId", "in": "query", "schema": {"type": "string"}, "example": "63aebf861ba6a0e7ecc8d029"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Products"], "summary": "edit product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "product": {"productId": "63b53ce69bf774b1f21ff292", "barcodes": [], "isActive": true, "title": "Battery Operated Personal Hand Fan 2", "secondaryLanguageTitle": "Battery Operated Personal Hand Fan 2", "itemNumber": "BOP1", "type": "VARIANT", "attributes": [], "brand": "6399795d281ecb0012eb2127", "family": "6374b3170db8250012325d0c", "tags": [], "taxId": "639987c6281ecb0012eb2232"}, "variantProducts": [{"variantId": "63b53ce69bf774b1f21ff292_63b53ce69bf774b1f21ff298_63b53ce69bf774b1f21ff29a", "variantName": "50 mm", "isActive": true, "itemNumber": "I0001", "uomMapping": {"uom": "637c9d7884b9540012d6aadc", "minQty": 20}, "priceMapping": [{"price": "1", "master_price_id": "637c68644f63130012f43d11"}, {"price": "1", "master_price_id": "637c687c4f63130012f43d15"}, {"price": "1", "master_price_id": "637c68884f63130012f43d19"}, {"price": "1", "master_price_id": "637c997484b9540012d6aa3f"}, {"price": "1", "master_price_id": "637ca6cdfdd8d54d78b89ff1"}, {"price": "1", "master_price_id": "637d9c3ee1bcfc001292df01"}, {"price": "1", "master_price_id": "639035510eb78c00125dd91c"}, {"price": "1", "master_price_id": "6399e7bdf26e8900120562e2"}, {"price": "1", "master_price_id": "63abfc9c4fac7e00122119fc"}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63b4270747da2100129b5b5e", "warehouse_id": "63b4270747da2100129b5b60", "quantity": 0}], "barcodes": [], "groupName": "Normal"}, {"variantId": "63b53ce69bf774b1f21ff292_63b53ce69bf774b1f21ff298_63b53ce69bf774b1f21ff29b", "variantName": "50 mm", "isActive": true, "itemNumber": "I0002", "uomMapping": {"uom": "637c9d7884b9540012d6aadc", "minQty": 20}, "priceMapping": [{"price": "1", "master_price_id": "637c68644f63130012f43d11"}, {"price": "1", "master_price_id": "637c687c4f63130012f43d15"}, {"price": "1", "master_price_id": "637c68884f63130012f43d19"}, {"price": "1", "master_price_id": "637c997484b9540012d6aa3f"}, {"price": "1", "master_price_id": "637ca6cdfdd8d54d78b89ff1"}, {"price": "1", "master_price_id": "637d9c3ee1bcfc001292df01"}, {"price": "1", "master_price_id": "639035510eb78c00125dd91c"}, {"price": "1", "master_price_id": "6399e7bdf26e8900120562e2"}, {"price": "1", "master_price_id": "63abfc9c4fac7e00122119fc"}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63b4270747da2100129b5b5e", "warehouse_id": "63b4270747da2100129b5b60", "quantity": 0}], "barcodes": [], "groupName": "Medium"}, {"variantId": "63b53ce69bf774b1f21ff292_63b53ce69bf774b1f21ff299_63b53ce69bf774b1f21ff29a", "variantName": "100mm", "isActive": true, "itemNumber": "I0003", "uomMapping": {"uom": "637c9d7884b9540012d6aadc", "minQty": 20}, "priceMapping": [{"price": "1", "master_price_id": "637c68644f63130012f43d11"}, {"price": "1", "master_price_id": "637c687c4f63130012f43d15"}, {"price": "1", "master_price_id": "637c68884f63130012f43d19"}, {"price": "1", "master_price_id": "637c997484b9540012d6aa3f"}, {"price": "1", "master_price_id": "637ca6cdfdd8d54d78b89ff1"}, {"price": "1", "master_price_id": "637d9c3ee1bcfc001292df01"}, {"price": "1", "master_price_id": "639035510eb78c00125dd91c"}, {"price": "1", "master_price_id": "6399e7bdf26e8900120562e2"}, {"price": "1", "master_price_id": "63abfc9c4fac7e00122119fc"}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63b4270747da2100129b5b5e", "warehouse_id": "63b4270747da2100129b5b60", "quantity": 0}], "barcodes": [], "groupName": "Normal"}, {"variantId": "63b53ce69bf774b1f21ff292_63b53ce69bf774b1f21ff299_63b53ce69bf774b1f21ff29b", "variantName": "100mm", "isActive": true, "itemNumber": "I0004", "uomMapping": {"uom": "637c9d7884b9540012d6aadc", "minQty": 20}, "priceMapping": [{"price": "1", "master_price_id": "637c68644f63130012f43d11"}, {"price": "1", "master_price_id": "637c687c4f63130012f43d15"}, {"price": "1", "master_price_id": "637c68884f63130012f43d19"}, {"price": "1", "master_price_id": "637c997484b9540012d6aa3f"}, {"price": "1", "master_price_id": "637ca6cdfdd8d54d78b89ff1"}, {"price": "1", "master_price_id": "637d9c3ee1bcfc001292df01"}, {"price": "1", "master_price_id": "639035510eb78c00125dd91c"}, {"price": "1", "master_price_id": "6399e7bdf26e8900120562e2"}, {"price": "1", "master_price_id": "63abfc9c4fac7e00122119fc"}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63b4270747da2100129b5b5e", "warehouse_id": "63b4270747da2100129b5b60", "quantity": 0}], "barcodes": [], "groupName": "Medium"}]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Products"], "summary": "Delete product API", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1133"}, {"name": "productId", "in": "query", "schema": {"type": "string"}, "example": "6388537b947a634c16d942bf"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/products": {"get": {"tags": ["Products"], "summary": "product listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "4"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "withV<PERSON>ts", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Products"], "summary": "update multiple product/variant-product status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "productIds": ["637df7223c114064c1a6f743", "637df76a3c114064c1a6f752"], "variantProductIds": ["637df7223c114064c1a6f743_637df7243c114064c1a6f748_637df7243c114064c1a6f74a"], "type": "INACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/tags": {"get": {"tags": ["Products"], "summary": "Tag List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/search": {"get": {"tags": ["Products"], "summary": "Search Product", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "ger"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "searchType", "in": "query", "schema": {"type": "string"}, "description": "AUTO_COMPLETE / SEARCH / FILTER", "example": "FILTER"}, {"name": "priceListId", "in": "query", "schema": {"type": "string"}, "example": "6374bd481f6d6bdec112ee96"}, {"name": "filters", "in": "query", "schema": {"type": "string"}, "example": "{\"productType\":\"New_PRODUCTS\",\"brands\":[],\"priceRange\":{\"from\":\"10\", \"to\": \"100\"},\"tags\":[]}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/existItemNumber": {"get": {"tags": ["Products"], "summary": "check existing item number", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1073"}, {"name": "itemNumber", "in": "query", "schema": {"type": "string"}, "example": "K700103"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/update-variant-group": {"put": {"tags": ["Products"], "summary": "update variant and group list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "addedVariants": ["blue"], "deletedIds": ["63b56b19fa4d93d83a7d9381"], "productId": "63b56b19fa4d93d83a7d937a", "addedGroups": []}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageUpload/swapProductImages": {"post": {"tags": ["Products"], "summary": "Swap images", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"imageIds": ["63ad7afc251da20a3613554d", "63ad7afc251da20a36135550"], "tenantId": 1114}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/product/inventoryProductList": {"get": {"tags": ["Products"], "summary": "Inventory product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "priceId", "in": "query", "schema": {"type": "string"}, "example": "636bae633faa4a5020118c0f"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "636bae633faa4a5020118c0f"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "K7000013"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/configuration/tax": {"post": {"tags": ["Configuration"], "summary": "Add and Edit Tax", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1035, "type": "GROUP", "groupName": "AAGST + XXGST", "taxName": ["aaGST", "xxGST"], "taxCalculation": ["PERCENTAGE", "FLAT_VALUE"], "taxRate": ["15", "25"], "status": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Configuration"], "summary": "Get Tax and Taxlist", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1035"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "637e20c1cb50a66e083241ef"}, {"name": "groupId", "in": "query", "schema": {"type": "string"}, "example": "637e20c1cb50a66e083241ef"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "description": "ALL/ACTIVE/INACTIVE ", "example": "ALL"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Configuration"], "summary": "Detele Tax", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "637e20c1cb50a66e083241ef"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Configuration"], "summary": "Change Tax Status", "requestBody": {"content": {}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "637df93b47430c10c44469a0"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "description": "ACTIVE / INACTIVE", "example": "INACTIVE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/configuration/master-tax": {"post": {"tags": ["Configuration"], "summary": "Update Master Tax Setting", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1035, "enableTax": true, "price": "INCLUDE", "defaultTax": "637df93b47430c10c44469a0", "universalTax": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Configuration"], "summary": "Get Master Tax Setting", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1035"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/cart": {"post": {"tags": ["Orders > Cart"], "summary": "(Add/Update/delete) item to Cart", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"masterPriceId": "637c687c4f63130012f43d15", "basePrice": 10, "tax": 1, "quantity": 10, "cartItemId": "63bb9ed1461922059d50eb85", "uomId": "637c995784b9540012d6aa2a", "uomName": "UOM", "minQty": 1, "productId": "63b80b9f016331c4c6d32990", "salesPersonRoleId": "634ff4f82645ba00119e6a54", "customerUserRoleId": "639c2d6d5359ee0012333664", "tenantId": 1114, "actionType": "ADD_ITEM_TO_CART"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Orders > Cart"], "summary": "get cart details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "639c2d6d5359ee0012333664"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Orders > Cart"], "summary": "Clear Cart", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId1114", "in": "query", "schema": {"type": "string"}}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "639c2d6d5359ee0012333664"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/": {"post": {"tags": ["Orders"], "summary": "Place order", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "salesPersonRoleId": "634ff4f82645ba00119e6a54", "customerUserRoleId": "639c2d6d5359ee0012333664", "customerName": "Testing name", "salePersonName": "Testing sales person name", "branchId": "6333f1fb67e853cb742142a2", "orderPunchDeviceType": "MOBILE", "orderPunchDeviceOs": "IOS", "orderAppType": "SALES_APP", "orderRemark": "Testing remarks", "ShippingAddress": "Testing Address of the customer or selected address", "cityId": "63244af322e3eb003821e8ba", "regionId": "631ae5355eece300129f92da", "shippingMobileNumber": 9685635896, "shippingCountryCode": "+91", "regionName": "Testing region", "cityName": "Testing cityName", "shippingCoordinates": {"lat": 20.121, "lng": 72}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}}}
{"info": {"_postman_id": "e80de330-841e-4bb5-bd92-739caf98b8f9", "name": "Hawak-Product-Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24458531", "_collection_link": "https://lunar-flare-246963.postman.co/workspace/Hawak-Backend-Devs~838d78ef-c166-4c7b-9ee7-d04bc82e657e/collection/23115891-e80de330-841e-4bb5-bd92-739caf98b8f9?action=share&creator=24458531&source=collection_link"}, "item": [{"name": "Master Data", "item": [{"name": "Attribute", "item": [{"name": "Add Attribute", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeName\": \"Bottghle\",\n    \"secondaryLanguageAttributeName\": \"Boghttle\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}, {"name": "Edit Attribute", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63c8eba84b8de14dd4400595\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeName\": \"Helmegt kings\",\n    \"secondaryLanguageAttributeName\": \"Earbuddds\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}, {"name": "Get Attribute List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute?tenantId=1131&status=ALL&searchKey=&perPage=20&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "20"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Attribute", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"6371d602f4d7553be184b791\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}]}, {"name": "Attribute Set", "item": [{"name": "Add Attribute Set", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeSetName\": \"Office Products\",\n    \"secondaryLanguageAttributeSetName\":\"office chiz\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}, {"name": "Edit Attribute Set", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63724673f46f0cb0dc66fef5\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeSetName\": \"Power Tools\",\n    \"secondaryLanguageAttributeSetName\": \"urja samgri tools\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}, {"name": "Get Attribute Set List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet?tenantId=1131&status=ALL&searchKey=gri&perPage=10&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": "gri"}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Attribute Set", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63724673f46f0cb0dc66fef5\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}]}, {"name": "Brand", "item": [{"name": "Add Brand", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"brandName\": \"Dell\",\n    \"secondaryLanguageBrandName\": \"del\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}, {"name": "Edit Brand", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"637331be585729637044fb5c\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"brandName\": \"philips\",\n    \"secondaryLanguageBrandName\": \"philips t\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}, {"name": "Get Brand List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand?tenantId=1131&status=ALL&searchKey=&perPage=2&page=2", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "2"}, {"key": "page", "value": "2"}]}}, "response": []}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63724673f46f0cb0dc66fef5\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}]}, {"name": "Unit", "item": [{"name": "Add Unit", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"unitName\": \"indiUnit\",\n    \"secondaryLanguageUnitName\": \"indi\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}, {"name": "Edit Unit", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"6373522431cd87bd062f72e2\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"unitName\": \"KG\",\n    \"secondaryLanguageUnitName\": \"ke giii\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}, {"name": "Get Unit List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit?tenantId=1131&status=ACTIVE&searchKey=&perPage=10&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ACTIVE"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Unit", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"6373522431cd87bd062f72e2\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}]}, {"name": "Price", "item": [{"name": "Add Price", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": false,\n    \"tenantId\": 1131,\n    \"priceName\": \"Battery Capacity\",\n    \"secondaryLanguagePriceName\": \"Second Capacity\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}, {"name": "Edit Price", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63c7ee8bf692a06e910ef5f8\",\n    \"isActive\": \"true\",\n    \"tenantId\": 1131,\n    \"priceName\": \"capacity\",\n    \"secondaryLanguagePriceName\": \"capacity\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}, {"name": "Get Price List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price?tenantId=1131&status=ALL&searchKey=&perPage=200&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "200"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Price", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63c7f1cae87e65931cc1f34f\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}]}, {"name": "Attribute Association", "item": [{"name": "Update Attribute Association", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"attributeSetId\": \"63724673f46f0cb0dc66fef5\",\n    \"attributeIds\": [\"6374a9ed4dfc96001260858c\", \"6371d602f4d7553be184b791\",\"6371de646c6c8e71cec916b3\",\n    \"sdsd\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeAssociation", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeAssociation"]}}, "response": []}, {"name": "Get Attribute Association List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeAssociation?tenantId=1131&status=ACTIVE&searchKey=&perPage=10&page=1&_id=63731436e604224cd8124845", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeAssociation"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ACTIVE"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}, {"key": "_id", "value": "63731436e604224cd8124845"}]}}, "response": []}]}, {"name": "Update Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"entity\": \"PRICE\",\n    \"tenantId\": 1131,\n    \"isActive\": false,\n    \"ids\": [\n        \"6373538e31cd87bd062f72ec\",\n        \"6374bf41cc15a0e922008be2\"\n    ]   \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/updateStatus", "host": ["{{base_url}}"], "path": ["productService", "masterData", "updateStatus"]}}, "response": []}]}, {"name": "Images", "item": [{"name": "Add & Update Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"63bbeb1501320c53f449ce9e_63bbeb1501320c53f449cea6_P1.JPEG\",\n    \"imageSize\": 1114,\n    \"s3Url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/63bbeb1501320c53f449ce9e_63bbeb1501320c53f449cea6_P1.JPEG\",\n    \"productId\": \"63bbeb1501320c53f449ce9e\",\n    \"groupId\": \"63bbeb1501320c53f449cea6\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageUpload", "host": ["{{base_url}}"], "path": ["productService", "imageUpload"]}}, "response": []}, {"name": "List of Images", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageUpload?page=1&perPage=10&tenantId=1114&imageType=NEED_IMAGES&searchKey=18th", "host": ["{{base_url}}"], "path": ["productService", "imageUpload"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1114"}, {"key": "imageType", "value": "NEED_IMAGES"}, {"key": "search<PERSON>ey", "value": "18th"}]}}, "response": []}, {"name": "Get Upload Signature", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"63bbeb1501320c53f449ce9e_63bbeb1501320c53f449cea6_P1.JPEG\",\n    \"type\": \"GROUP_IMAGE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageUpload/getUploadSignature", "host": ["{{base_url}}"], "path": ["productService", "imageUpload", "getUploadSignature"]}}, "response": []}, {"name": "Delete image", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"imageId\" : [\"636b6480835d286a8804b00b\",\"636b64ce853b8a6b64c449d8\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageUpload", "host": ["{{base_url}}"], "path": ["productService", "imageUpload"], "query": [{"key": "imageId", "value": "63689c6802425511bc93fe06", "disabled": true}]}}, "response": []}, {"name": "Product and variant id signature", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"63ad858895e713001116ad59_P1.jpeg\",\n    \"type\": \"PRODUCT\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageUpload/productIdImageSignature", "host": ["{{base_url}}"], "path": ["productService", "imageUpload", "productIdImageSignature"]}}, "response": []}, {"name": "Get image match product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageUpload/imagesMatch?page=1&perPage=15&tenantId=1114&linkedTenantId=1119&searchKey=testing_prod", "host": ["{{base_url}}"], "path": ["productService", "imageUpload", "imagesMatch"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "15"}, {"key": "tenantId", "value": "1114"}, {"key": "linkedTenantId", "value": "1119"}, {"key": "search<PERSON>ey", "value": "testing_prod"}]}}, "response": []}, {"name": "Image match action", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"linkedTenantId\": 1119,\n    \"type\": \"SELECTED\",  // ALL or SELECTED\n    \"product\": [\n        {\n            \"productId\": \"63f5b1be478135a96edd68dd\",\n            \"linkedTenantProductId\": \"63ad1a8d43ff4500122d20c6\"\n        },\n        {\n            \"variantId\": \"63f34b2e53ff820a78b4d090_63f34b2e53ff820a78b4d098_63f34b2e53ff820a78b4d09b\",\n            \"linkedTenantVariantId\": \"63b02100ecf078002c555491_642bb8f8a5ac388d302e9e1c_642bb8f8a5ac388d302e9e1d\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageUpload/imagesMatch", "host": ["{{base_url}}"], "path": ["productService", "imageUpload", "imagesMatch"]}}, "response": []}]}, {"name": "Products", "item": [{"name": "variant Product", "item": [{"name": "swap variant products", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"variantIds\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product/changeVariantOrder", "host": ["{{base_url}}"], "path": ["productService", "product", "changeVariantOrder"]}}, "response": []}, {"name": "variant products of pro", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/productVariants?productId=637df77a3c114064c1a6f75f", "host": ["{{base_url}}"], "path": ["productService", "product", "productVariants"], "query": [{"key": "productId", "value": "637df77a3c114064c1a6f75f"}]}}, "response": []}, {"name": "delete variant product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/productVariant?variantId=63ab35c39101c8001209ed19_63ab35c39101c8001209ed1e&tenantId=1133", "host": ["{{base_url}}"], "path": ["productService", "product", "productVariant"], "query": [{"key": "variantId", "value": "63ab35c39101c8001209ed19_63ab35c39101c8001209ed1e"}, {"key": "tenantId", "value": "1133"}]}}, "response": []}]}, {"name": "Tenant Allow Validation", "item": [{"name": "Tenant active product", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/allowNewValidation?tenantId=1133&type=PRODUCTS", "host": ["{{base_url}}"], "path": ["productService", "product", "allowNewValidation"], "query": [{"key": "tenantId", "value": "1133"}, {"key": "type", "value": "PRODUCTS"}]}}, "response": []}]}, {"name": "Favorite Product", "item": [{"name": "Favorite product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"type\": \"FAVORITE\",\n    \"ids\": [\"6392f6f8104ae9c7efb22baf\", \"63908b960270e1227dd5a82f\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product/favoriteProduct", "host": ["{{base_url}}"], "path": ["productService", "product", "favoriteProduct"]}}, "response": []}, {"name": "Favorite product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/favoriteProduct?tenantId=1131&perPage=5", "host": ["{{base_url}}"], "path": ["productService", "product", "favoriteProduct"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "perPage", "value": "5"}, {"key": "favoriteProductId", "value": "63b542568901ecaabc69fb02", "description": "last product id", "disabled": true}]}}, "response": []}]}, {"name": "Add product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1004,\n    \"product\": {\n        \"isActive\": true,\n        \"attributes\": [],\n        \"title\": \"MacTest !@!@sdfsdfsda\",\n        \"secondaryLanguageTitle\": \"MacTest !@!@sdfsdfsda\",\n        \"itemNumber\": \"3-1011212-96-85121dds11212ggvo23232323o\",\n        \"barcodes\": [],\n        \"type\": \"VARIANT\",\n        \"brand\": \"637ca66d84b9540012d6ac30\",\n        \"family\": \"6375cc24421cf4001212f4b2\",\n        \"tags\": [],\n        \"variants\": {\n            \"type\": \"Size\",\n            \"values\": [\n                \"1mm\",\n                \"2mm\"\n            ]\n        }\n    },\n    \"variantProducts\": [\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"213424egdsasasaasasfsg2341212d26589kx32fddzds\",\n            \"uomMapping\": {\n                \"uom\": \"637c995784b9540012d6aa2a\",\n                \"minQty\": \"1\"\n            },\n            \"priceMapping\": [],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"quantity\": \"10\"\n                },\n                {\n                    \"warehouse_id\": \"63b80985b1d223001101b718\",\n                    \"branch_id\": \"63b80985b1d223001101b716\",\n                    \"quantity\": \"5\"\n                }\n            ],\n            \"barcodes\": []\n        },\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"rjforefj1cds-csdsasax2ssds12dfsxfd\",\n            \"uomMapping\": {\n                \"uom\": \"637c995784b9540012d6aa2a\",\n                \"minQty\": \"1\"\n            },\n            \"priceMapping\": [],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"quantity\": \"20\"\n                },\n                {\n                    \"warehouse_id\": \"63b80985b1d223001101b718\",\n                    \"branch_id\": \"63b80985b1d223001101b716\",\n                    \"quantity\": \"5\"\n                }\n            ],\n            \"barcodes\": [],\n            \"variantName\": \"2mm\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product", "host": ["{{base_url}}"], "path": ["productService", "product"]}}, "response": []}, {"name": "product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/products?page=1&perPage=50&tenantId=1119&withVariants=true", "host": ["{{base_url}}"], "path": ["productService", "product", "products"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "50"}, {"key": "search<PERSON>ey", "value": "i", "disabled": true}, {"key": "tenantId", "value": "1119"}, {"key": "withV<PERSON>ts", "value": "true"}, {"key": "type", "value": "ALL", "disabled": true}, {"key": "sortBy", "value": "item_number", "description": "item_number, title", "disabled": true}, {"key": "sortType", "value": "ASC", "description": "ASC/DESC", "disabled": true}]}}, "response": []}, {"name": "Tag List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/tags?perPage=10&tenantId=1131", "host": ["{{base_url}}"], "path": ["productService", "product", "tags"], "query": [{"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1131"}]}}, "response": []}, {"name": "product details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product?tenantId=1114&productId=63aebf861ba6a0e7ecc8d029", "host": ["{{base_url}}"], "path": ["productService", "product"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "productId", "value": "63aebf861ba6a0e7ecc8d029"}]}}, "response": []}, {"name": "edit product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"product\": {\n        \"productId\": \"63cf6c07e9129965e51f3e0a\",\n        \"barcodes\": [],\n        \"isActive\": true,\n        \"title\": \"MacTest !@!@sdfsdfsda\",\n        \"secondaryLanguageTitle\": \"MacTest !@!@sdfsdfsda\",\n        \"itemNumber\": \"3-1011212-96-85121dds11212ggvo23232323o\",\n        \"type\": \"VARIANT\",\n        \"attributes\": [],\n        \"brand\": \"637ca66d84b9540012d6ac30\",\n        \"family\": \"6375cc24421cf4001212f4b2\",\n        \"tags\": [],\n        \"taxId\": \"63bd35c8f0b237aec41b0873\"\n    },\n    \"variantProducts\": [\n        {\n            \"variantId\": \"63cf6c07e9129965e51f3e0a_63cf6c35e9129965e51f3e0f\",\n            \"variantName\": \"1mm\",\n            \"isActive\": true,\n            \"itemNumber\": \"213424egdsasasaasasfsg2341212d26589kx32fddzds\",\n            \"uomMapping\": {\n                \"uom\": \"637c995784b9540012d6aa2a\",\n                \"minQty\": 1\n            },\n            \"priceMapping\": [],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"63b80985b1d223001101b716\",\n                    \"warehouse_id\": \"63b80985b1d223001101b718\",\n                    \"quantity\": 5\n                }\n            ],\n            \"barcodes\": []\n        },\n        {\n            \"variantId\": \"63cf6c07e9129965e51f3e0a_63cf6c35e9129965e51f3e10\",\n            \"variantName\": \"2mm\",\n            \"isActive\": true,\n            \"itemNumber\": \"rjforefj1cds-csdsasax2ssds12dfsxfd\",\n            \"uomMapping\": {\n                \"uom\": \"637c995784b9540012d6aa2a\",\n                \"minQty\": 1\n            },\n            \"priceMapping\": [],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"quantity\": 20\n                },\n                {\n                    \"branch_id\": \"63b80985b1d223001101b716\",\n                    \"warehouse_id\": \"63b80985b1d223001101b718\",\n                    \"quantity\": 5\n                }\n            ],\n            \"barcodes\": []\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product", "host": ["{{base_url}}"], "path": ["productService", "product"]}}, "response": []}, {"name": "Search Product", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product/search?tenantId=1131&searchKey=test&page=1&perPage=200&searchType=FILTER&priceListId=63770ee9afe939001295296a&filters={ \"brands\": [\"63732ed3585729637044fb45\"] }&familyId=6388485d7ef5eb001232304f", "host": ["{{base_url}}"], "path": ["productService", "product", "search"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "search<PERSON>ey", "value": "test"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "200"}, {"key": "searchType", "value": "FILTER", "description": "AUTO_COMPLETE / SEARCH / FILTER"}, {"key": "priceListId", "value": "63770ee9afe939001295296a"}, {"key": "filters", "value": "{ \"brands\": [\"63732ed3585729637044fb45\"] }"}, {"key": "familyId", "value": "6388485d7ef5eb001232304f"}]}}, "response": []}, {"name": "check existing item number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/existItemNumber?tenantId=1073&itemNumber=K700103", "host": ["{{base_url}}"], "path": ["productService", "product", "existItemNumber"], "query": [{"key": "tenantId", "value": "1073"}, {"key": "itemNumber", "value": "K700103"}]}}, "response": []}, {"name": "update multiple product/variant-product status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\" : 1131,\n    \"productIds\": [\"637df7223c114064c1a6f743\", \"637df76a3c114064c1a6f752\"],\n    \"variantProductIds\" :  [\"637df7223c114064c1a6f743_637df7243c114064c1a6f748_637df7243c114064c1a6f74a\"],\n    \"type\": \"INACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product/products", "host": ["{{base_url}}"], "path": ["productService", "product", "products"]}}, "response": []}, {"name": "update variant and group list", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"addedVariants\": [\n        \"blue\"\n    ],\n    \"deletedIds\": [\n        \"63b56b19fa4d93d83a7d9381\"\n    ],\n    \"productId\": \"63b56b19fa4d93d83a7d937a\",\n    \"addedGroups\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/product/update-variant-group", "host": ["{{base_url}}"], "path": ["productService", "product", "update-variant-group"]}}, "response": []}, {"name": "Swap images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"imageIds\": [\"63ad7afc251da20a3613554d\", \"63ad7afc251da20a36135550\"],\n    \"tenantId\": 1114\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/swapProductImages", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "swapProductImages"]}}, "response": []}, {"name": "Delete product API", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product?tenantId=1114&productId=63aea24bf499200012c5435d", "host": ["{{base_url}}"], "path": ["productService", "product"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "productId", "value": "63aea24bf499200012c5435d"}]}}, "response": []}, {"name": "Inventory product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/inventoryProductList?tenantId=1114&status=ALL&page=1&perPage=50", "host": ["{{base_url}}"], "path": ["productService", "product", "inventoryProductList"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "status", "value": "ALL"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "50"}, {"key": "priceId", "value": "636bae633faa4a5020118c0f", "disabled": true}, {"key": "branchId", "value": "636bae633faa4a5020118c0f", "disabled": true}, {"key": "search<PERSON>ey", "value": "K7000013", "disabled": true}]}}, "response": []}, {"name": "Get product by barcode", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/productFromBarcode?tenantId=1080&barcode=chifg&priceId=6389c00f05432f00129329c7", "host": ["{{base_url}}"], "path": ["productService", "product", "productFromBarcode"], "query": [{"key": "tenantId", "value": "1080"}, {"key": "barcode", "value": "chifg"}, {"key": "priceId", "value": "6389c00f05432f00129329c7"}]}}, "response": []}, {"name": "check exist barcode", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/product/existTenantBarcode?tenantId=1131&barcode=barcode_3", "host": ["{{base_url}}"], "path": ["productService", "product", "existTenantBarcode"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "barcode", "value": "barcode_3"}]}}, "response": []}]}, {"name": "Configuration", "item": [{"name": "Add and Edit Tax", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1035,\r\n    \"type\": \"GROUP\",\r\n    \"groupName\": \"AAGST + XXGST\",\r\n    \"taxName\": [\"aaGST\", \"xxGST\"],\r\n    \"taxCalculation\": [\"PERCENTAGE\", \"FLAT_VALUE\"],\r\n    \"taxRate\": [\"15\", \"25\"],\r\n    \"status\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/configuration/tax", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"]}}, "response": []}, {"name": "Get Tax and Taxlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?tenantId=1119&taxId=63ca73364132dfa0be311fe6&status=ALL", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "taxId", "value": "63ca73364132dfa0be311fe6"}, {"key": "groupId", "value": "637e20c1cb50a66e083241ef", "disabled": true}, {"key": "type", "value": "GROUP", "disabled": true}, {"key": "status", "value": "ALL", "description": "ALL/ACTIVE/INACTIVE "}]}}, "response": []}, {"name": "Detele Tax", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?taxId=637e20c1cb50a66e083241ef", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "taxId", "value": "637e20c1cb50a66e083241ef"}]}}, "response": []}, {"name": "Change Tax Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?taxId=637df93b47430c10c44469a0&status=INACTIVE", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "taxId", "value": "637df93b47430c10c44469a0"}, {"key": "status", "value": "INACTIVE", "description": "ACTIVE / INACTIVE"}]}}, "response": []}, {"name": "Update Master Tax Setting", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1035,\r\n    \"enableTax\": true,\r\n    \"price\": \"INCLUDE\",\r\n    \"defaultTax\": \"637df93b47430c10c44469a0\",\r\n    \"universalTax\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/configuration/master-tax", "host": ["{{base_url}}"], "path": ["productService", "configuration", "master-tax"]}}, "response": []}, {"name": "Get Master Tax Setting", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/master-tax?tenantId=1035", "host": ["{{base_url}}"], "path": ["productService", "configuration", "master-tax"], "query": [{"key": "tenantId", "value": "1035"}]}}, "response": []}]}, {"name": "Orders", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "(Add/Update/delete) item to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"actionType\": \"ADD_ITEM_TO_CART\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"itemComment\": \"Test1234121212121ffffffff1sadasasd212vvvvvvvvvv\",\n    \"productId\": \"63a53dd02c3ede00122d4bb1\",\n    \n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"tenantId\": 1119,\n    \"uomId\": \"63a3f1da34a14d00127f1d3a\",\n    \"uomName\": \"Inch\",\n    \"basePrice\": 45,\n    \"tax\": 10,\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\",\n    \"minQty\": 1,\n    \"quantity\": 5\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/cart", "host": ["{{base_url}}"], "path": ["productService", "order", "cart"]}}, "response": []}, {"name": "get cart details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/cart?tenantId=1119&customerUserRoleId=63b25f79989ff60011a1ff50", "host": ["{{base_url}}"], "path": ["productService", "order", "cart"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50"}]}}, "response": []}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/cart?tenantId=1114&customerUserRoleId=63bf92d8159d4f0012be3d06&cartItemIds=teast&cartItemIds=test", "host": ["{{base_url}}"], "path": ["productService", "order", "cart"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "customerUserRoleId", "value": "63bf92d8159d4f0012be3d06"}, {"key": "cartItemIds", "value": "teast"}, {"key": "cartItemIds", "value": "test"}]}}, "response": []}, {"name": "get cart item count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/itemCount?tenantId=1114&customerUserRoleId=639c2d6d5359ee0012333664", "host": ["{{base_url}}"], "path": ["productService", "order", "itemCount"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "customerUserRoleId", "value": "639c2d6d5359ee0012333664"}]}}, "response": []}]}, {"name": "Drafts", "item": [{"name": "Add to Draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"tenantId\": 1119,\n    \"customerName\": \"customer_name\",\n    \"customerPrimaryContactName\": \"Customer first name & last name\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/draft", "host": ["{{base_url}}"], "path": ["productService", "order", "draft"]}}, "response": []}, {"name": "draft listing api", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/drafts?page=1&perPage=100&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "order", "drafts"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "100"}, {"key": "tenantId", "value": "1119"}, {"key": "salesPersonRoleId", "value": "6363b6d59a0d2f00129cb973", "disabled": true}, {"key": "search<PERSON>ey", "value": "custome", "disabled": true}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50", "disabled": true}, {"key": "duration", "value": "1", "disabled": true}]}}, "response": []}, {"name": "draft detials", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/draft?tenantId=1119&draftId=63ef03ce72f07c8ae714e414&salesPersonRoleId=6363b6d59a0d2f00129cb973", "host": ["{{base_url}}"], "path": ["productService", "order", "draft"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "draftId", "value": "63ef03ce72f07c8ae714e414"}, {"key": "salesPersonRoleId", "value": "6363b6d59a0d2f00129cb973"}]}}, "response": []}, {"name": "Move draft to cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/draft?tenantId=1119&draftId=63cbd731f1c937d618db8196&salesPersonRoleId=6363b6d59a0d2f00129cb973&customerUserRoleId=63b25f79989ff60011a1ff50", "host": ["{{base_url}}"], "path": ["productService", "order", "draft"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "draftId", "value": "63cbd731f1c937d618db8196"}, {"key": "salesPersonRoleId", "value": "6363b6d59a0d2f00129cb973"}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50"}]}}, "response": []}, {"name": "place order from draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"draftId\": \"641c7de22c9a0c46d6d7e329\",\n    \"tenantId\": 1119,\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"customerName\": \"Ketan POSTMAN\",\n    \"customerLegalName\": \"POSTMAN LEGAL NAME\",\n    \"salePersonName\": \"Ketan Sales Person New POSTMAN\",\n    \"branchId\": \"633aaf5585b9b200129a4503\",\n    \"externalId\": \"ewqeqdsa\",\n    \"orderAppType\": \"SALES_APP\",\n    \"orderPunchDeviceType\": \"MOBILE\",\n    \"orderPunchDeviceOs\": \"IOS\",\n    \"orderRemark\": \"Testing remarks\",\n    \"ShippingAddress\": \"Testing Address of the customer or selected address\",\n    \"cityId\": \"631ffc5c89881e0012660b3b\",\n    \"regionId\": \"630c58a6bfb2982b1cbcd4cf\",\n    \"shippingMobileNumber\": 9685635896,\n    \"shippingCountryCode\": \"+91\",\n    \"regionName\": \"Gujarat\",\n    \"cityName\": \"Surat\",\n    \"shippingCoordinates\": {\n        \"lat\": 20.121,\n        \"lng\": 72.00\n    },\n    \"customerPrimaryContactName\": \"Customer firstname and last name POSTMAN\",\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/placeOrderFromDraft", "host": ["{{base_url}}"], "path": ["productService", "order", "placeOrderFromDraft"]}}, "response": []}, {"name": "Delete drafts", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/drafts?salesPersonRoleId=6371b561f6eba60012a75bc0&tenantId=1114", "host": ["{{base_url}}"], "path": ["productService", "order", "drafts"], "query": [{"key": "draftIds", "value": "63ce455a400ad0ebd0d133ef", "disabled": true}, {"key": "salesPersonRoleId", "value": "6371b561f6eba60012a75bc0"}, {"key": "tenantId", "value": "1114"}]}}, "response": []}]}, {"name": "Stats", "item": [{"name": "user role stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/orderStats?userRoles=6371b561f6eba60012a75bc0&tenantId=1114", "host": ["{{base_url}}"], "path": ["productService", "order", "orderStats"], "query": [{"key": "userRoles", "value": "6371b561f6eba60012a75bc0"}, {"key": "tenantId", "value": "1114"}]}}, "response": []}]}, {"name": "Place order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"apiVersion\": 2,\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"salesPersonRoleId\": \"63ff196cb66cd300208eaeae\",\n    \"customerName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (POSTMAN)\",\n    \"customer_legal_name\": \"(POSTMAN) Pvt Ltd\",\n    \"salePersonName\": \"Brijesh Sales Person\",\n    \"branchId\": \"633aaf5585b9b200129a4503\",\n    \"orderPunchDeviceType\": \"MOBILE\",\n    \"orderPunchDeviceOs\": \"IOS\",\n    \"orderAppType\": \"CUSTOMER_APP\",\n    \"orderRemark\": \"166666 Testing remarks\",\n    \"ShippingAddress\": \"Mayur flats, 2, Motinagar Society, Paldi, Ahmedabad, Gujarat 380007, India\",\n    \"cityId\": \"63244af322e3eb003821e8ba\",\n    \"regionId\": \"631ae5355eece300129f92da\",\n    \"shippingMobileNumber\": 6010203041,\n    \"shippingCountryCode\": \"+91\",\n    \"regionName\": \"Gujarat\",\n    \"cityName\": \"Surat\",\n    \"shippingCoordinates\": {\n        \"lat\": 23.0106567315333,\n        \"lng\": 72.************\n    },\n    \"customerPrimaryContactName\": \"Brijesh Darji\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order", "host": ["{{base_url}}"], "path": ["productService", "order"]}}, "response": []}, {"name": "Order listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/orders?page=1&perPage=20&tenantId=1131&apiVersion=2&salesPersonRoleId=6369eafc87526f00125f5f04&orderAppType=SALES_APP", "host": ["{{base_url}}"], "path": ["productService", "order", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "tenantId", "value": "1131"}, {"key": "apiVersion", "value": "2"}, {"key": "branchId", "value": "6333f1fb67e853cb742142a2", "disabled": true}, {"key": "orderStatus", "value": "SHIPPED", "disabled": true}, {"key": "salesPersonRoleId", "value": "6369eafc87526f00125f5f04"}, {"key": "customerUserRoleId", "value": "6333f1fb67e853cb7421429d", "disabled": true}, {"key": "orderAppType", "value": "SALES_APP"}, {"key": "duration", "value": "1", "disabled": true}, {"key": "search<PERSON>ey", "value": "test", "disabled": true}, {"key": "orderCountStatusType", "value": "PENDING", "disabled": true}, {"key": "onlyCount", "value": "true", "disabled": true}, {"key": "portalAppTypeFilter", "value": "ADMIN", "disabled": true}]}}, "response": [{"name": "Order listing", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/orders?page=1&perPage=1&tenantId=1119&orderStatus=PENDING&salesPersonRoleId=6363b6d59a0d2f00129cb973&orderAppType=SALES_APP", "host": ["{{base_url}}"], "path": ["productService", "order", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "1"}, {"key": "tenantId", "value": "1119"}, {"key": "branchId", "value": "6333f1fb67e853cb742142a2", "disabled": true}, {"key": "orderStatus", "value": "PENDING"}, {"key": "salesPersonRoleId", "value": "6363b6d59a0d2f00129cb973"}, {"key": "orderAppType", "value": "SALES_APP"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1971"}, {"key": "ETag", "value": "W/\"7b3-w+T704h5renhzQ+fdIT6rYDbOiI\""}, {"key": "Date", "value": "Wed, 18 Jan 2023 10:35:53 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"order_list\",\n    \"data\": {\n        \"list\": [\n            {\n                \"_id\": \"63c541b1d8e8bbf0a3546f28\",\n                \"reduce_inventory\": false,\n                \"tenant_id\": 1119,\n                \"customer_user_role_id\": \"6399c6cac871790012407f5c\",\n                \"sales_user_role_id\": \"6363b6d59a0d2f00129cb973\",\n                \"sales_person_name\": \"Sales person name\",\n                \"customer_name\": \"Customer name\",\n                \"branch_id\": \"634015e7a2a6e700126c563d\",\n                \"order_number\": \"1119100003\",\n                \"order_punching_device_type\": \"MOBILE\",\n                \"order_punching_device_os\": \"IOS\",\n                \"order_app_type\": \"SALES_APP\",\n                \"order_status\": \"PENDING\",\n                \"customer_primary_contact_name\": \"Customer name\",\n                \"total_amount\": 326.97999999999996,\n                \"total_tax\": 82.02000000000001,\n                \"created_at\": \"2023-01-16T12:23:13.878Z\",\n                \"order_items\": [\n                    {\n                        \"_id\": \"63c541b1d8e8bbf0a3546f2c\",\n                        \"product_id\": \"63a3f23434a14d00127f1d67\",\n                        \"product_name\": \"16 Inch  Tool Bag with Hard Plastic Bottol\",\n                        \"product_cover_image\": {\n                            \"_id\": \"63aec933251da20a36d9b33b\",\n                            \"image_name\": \"63a3f23434a14d00127f1d67_P1.jpeg\",\n                            \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/63a3f23434a14d00127f1d67_P1.jpeg\",\n                            \"image_number\": 1\n                        }\n                    },\n                    {\n                        \"_id\": \"63c541b1d8e8bbf0a3546f2b\",\n                        \"product_id\": \"63b02100ecf078002c555491\",\n                        \"variant_id\": \"63b02100ecf078002c555491_63b02100ecf078002c555496_63b02100ecf078002c55549c\",\n                        \"product_name\": \"Paint Shack\",\n                        \"variant_name\": \"6mm\",\n                        \"group_name\": \"Plastic\",\n                        \"product_cover_image\": {\n                            \"_id\": \"63b02106251da20a367d68ea\",\n                            \"image_name\": \"63b02100ecf078002c555491_P1.jpeg\",\n                            \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/63b02100ecf078002c555491_P1.jpeg\",\n                            \"image_number\": 1\n                        },\n                        \"variant_cover_image\": {\n                            \"_id\": \"63b28575696929c9599fea1f\",\n                            \"image_name\": \"63b02100ecf078002c555491_63b02100ecf078002c555496_63b02100ecf078002c55549c_P1.jpeg\",\n                            \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/63b02100ecf078002c555491_63b02100ecf078002c555496_63b02100ecf078002c55549c_P1.jpeg\",\n                            \"image_number\": 1\n                        }\n                    },\n                    {\n                        \"_id\": \"63c541b1d8e8bbf0a3546f2d\",\n                        \"product_id\": \"63a536ef2c3ede00122d382c\",\n                        \"product_name\": \"Cookies\"\n                    }\n                ]\n            }\n        ],\n        \"count\": 2\n    }\n}"}]}, {"name": "Get order detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order?tenantId=1119&orderId=648705c928669bc0541c31f6&page=1&perPage=10&apiVersion=2", "host": ["{{base_url}}"], "path": ["productService", "order"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "orderId", "value": "648705c928669bc0541c31f6"}, {"key": "search<PERSON>ey", "value": "Red", "disabled": true}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "orderDetails", "value": "true", "disabled": true}, {"key": "orderItemListingType", "value": "PAGINATION", "description": "PAGINATION, ALL", "disabled": true}, {"key": "apiVersion", "value": "2"}]}}, "response": [{"name": "fetch all order items", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order?tenantId=1114&orderId=63ca3540dd63d068172a4a7e&page=1&perPage=10&orderItemListingType=ALL", "host": ["{{base_url}}"], "path": ["productService", "order"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "orderId", "value": "63ca3540dd63d068172a4a7e"}, {"key": "search<PERSON>ey", "value": "Red", "disabled": true}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "orderDetails", "value": "true", "disabled": true}, {"key": "orderItemListingType", "value": "ALL"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "refreshed-access-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "2071"}, {"key": "ETag", "value": "W/\"817-mAvBtQBsvHXx5GO9/LFB5ND163M\""}, {"key": "Date", "value": "Mon, 23 Jan 2023 10:55:11 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"STATUS.SUCCESS\",\n    \"data\": [\n        {\n            \"_id\": \"63ca3540dd63d068172a4a81\",\n            \"reduce_inventory\": false,\n            \"tenant_id\": 1114,\n            \"branch_id\": \"6333f1fb67e853cb742142a2\",\n            \"order_id\": \"63ca3540dd63d068172a4a7e\",\n            \"customer_user_role_id\": \"63b7d755b1d2230011012bad\",\n            \"sales_user_role_id\": \"6371b561f6eba60012a75bc0\",\n            \"product_id\": \"63c11b0a0a0c563931cc83ba\",\n            \"variant_id\": \"63c11b0a0a0c563931cc83ba_63c11c030a0c563931cc873b\",\n            \"product_item_number\": \"TV101\",\n            \"variant_item_number\": \"T2-11-11\",\n            \"product_name\": \"Type Variant\",\n            \"variant_name\": \"type 2\",\n            \"product_secondary_name\": \"Type\",\n            \"quantity\": 15,\n            \"base_price\": 38,\n            \"tax\": 2,\n            \"master_price_id\": \"637c687c4f63130012f43d15\",\n            \"uom_id\": \"637c9d7884b9540012d6aadc\",\n            \"uom_name\": \"A\",\n            \"item_comment\": \"This is the item level comment\"\n        },\n        {\n            \"_id\": \"63ca3540dd63d068172a4a80\",\n            \"reduce_inventory\": false,\n            \"tenant_id\": 1114,\n            \"branch_id\": \"6333f1fb67e853cb742142a2\",\n            \"order_id\": \"63ca3540dd63d068172a4a7e\",\n            \"customer_user_role_id\": \"63b7d755b1d2230011012bad\",\n            \"sales_user_role_id\": \"6371b561f6eba60012a75bc0\",\n            \"product_id\": \"63b3e96cd1190f8481821c78\",\n            \"variant_id\": \"63b3e96cd1190f8481821c78_63b3e96dd1190f8481821c7d\",\n            \"product_item_number\": \"BM2020\",\n            \"variant_item_number\": \"BM20202\",\n            \"product_name\": \"Bajaj MD 2020 54L Window Air Cooler\",\n            \"variant_name\": \"Standing\",\n            \"product_secondary_name\": \"Bajaj MD 2020 54L Window Air Cooler\",\n            \"quantity\": 1,\n            \"base_price\": 0.95,\n            \"tax\": 0.05,\n            \"master_price_id\": \"637ca6cdfdd8d54d78b89ff1\",\n            \"uom_id\": \"637c995784b9540012d6aa2a\",\n            \"uom_name\": \"UOM\"\n        },\n        {\n            \"_id\": \"63ca3540dd63d068172a4a7f\",\n            \"reduce_inventory\": false,\n            \"tenant_id\": 1114,\n            \"branch_id\": \"6333f1fb67e853cb742142a2\",\n            \"order_id\": \"63ca3540dd63d068172a4a7e\",\n            \"customer_user_role_id\": \"63b7d755b1d2230011012bad\",\n            \"sales_user_role_id\": \"6371b561f6eba60012a75bc0\",\n            \"product_id\": \"63c11ce50a0c563931cc89ca\",\n            \"variant_id\": \"63c11ce50a0c563931cc89ca_63c11ce50a0c563931cc89d0\",\n            \"product_item_number\": \"LN101\",\n            \"variant_item_number\": \"LL101\",\n            \"product_name\": \"Size Variant\",\n            \"variant_name\": \"Long\",\n            \"product_secondary_name\": \"Size\",\n            \"quantity\": 20,\n            \"base_price\": 47.5,\n            \"tax\": 2.5,\n            \"master_price_id\": \"637c68644f63130012f43d11\",\n            \"uom_id\": \"637c995784b9540012d6aa2a\",\n            \"uom_name\": \"UOM\"\n        }\n    ]\n}"}]}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"orderIds\": [ \"6464b8ea75cc67790bdac50c\" ],\n    \"orderStatus\": \"PREPARING\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/orders", "host": ["{{base_url}}"], "path": ["productService", "order", "orders"]}}, "response": []}, {"name": "Update Customer With Orders", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customerUserRoleId\": \"641b093ef4fac2a95c0e77d3\",\n    \"updateInformation\": {\n        \"shippingMobileNumber\": 9685635896\n    },\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order", "host": ["{{base_url}}"], "path": ["productService", "order"]}}, "response": []}, {"name": "checkOrderUsers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/checkOrderUsers?orderId=6463a636343d1e70bdf48de9&tenantId=1131&userRoleId=6369eafc87526f00125f5f04&apiVersion=2&portalType=SALES_APP", "host": ["{{base_url}}"], "path": ["productService", "order", "checkOrderUsers"], "query": [{"key": "orderId", "value": "6463a636343d1e70bdf48de9"}, {"key": "tenantId", "value": "1131"}, {"key": "userRoleId", "value": "6369eafc87526f00125f5f04"}, {"key": "branchId", "value": "631864f5ccdd0600120ec856", "disabled": true}, {"key": "apiVersion", "value": "2"}, {"key": "portalType", "value": "SALES_APP"}]}}, "response": []}, {"name": "Dashboard summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/dashboardSummary?tenantId=1131&branchId=633aaf5585b9b200129a4503&apiVersion=2", "host": ["{{base_url}}"], "path": ["productService", "order", "dashboardSummary"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "branchId", "value": "633aaf5585b9b200129a4503"}, {"key": "apiVersion", "value": "2"}]}}, "response": []}]}, {"name": "Category", "item": [{"name": "Sort product list", "item": [{"name": "All Category Product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/allProductList?tenantId=1114&familyId=6375c5be421cf4001212f453&perPage=10&page=1&status=ALL&incrementLimit=true", "host": ["{{base_url}}"], "path": ["productService", "category", "allProductList"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "familyId", "value": "6375c5be421cf4001212f453", "description": "family category id"}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}, {"key": "status", "value": "ALL"}, {"key": "incrementLimit", "value": "true"}]}}, "response": []}, {"name": "Category Product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/categoryProductList?page=1&status=ACTIVE&appType=NATIVE&productSchemaType=products_2.0&perPage=100&priceListId=64156686ea18a9f9411d6664&filters={\"brands\":[],\"priceRange\": { \"from\":1,\"to\":6},\"tags\":[]}&categoryId=641a2feb5cfb741a6f70de6d&familyId=641a2fcc5cfb741a6f70de4d&tenantId=1001", "host": ["{{base_url}}"], "path": ["productService", "category", "categoryProductList"], "query": [{"key": "page", "value": "1"}, {"key": "status", "value": "ACTIVE"}, {"key": "incrementLimit", "value": "true", "disabled": true}, {"key": "appType", "value": "NATIVE"}, {"key": "hideOutOfStock", "value": "true", "disabled": true}, {"key": "branchId", "value": "6333f1fb67e853cb742142a2", "disabled": true}, {"key": "productSchemaType", "value": "products_2.0"}, {"key": "perPage", "value": "100"}, {"key": "priceListId", "value": "64156686ea18a9f9411d6664"}, {"key": "filters", "value": "{\"brands\":[],\"priceRange\": { \"from\":1,\"to\":6},\"tags\":[]}"}, {"key": "subCategoryId", "value": "63a53d442c3ede00122d4a5d", "disabled": true}, {"key": "categoryId", "value": "641a2feb5cfb741a6f70de6d"}, {"key": "familyId", "value": "641a2fcc5cfb741a6f70de4d"}, {"key": "tenantId", "value": "1001"}]}}, "response": []}, {"name": "Product sequnce", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"productId\": \"63848d4d69a85508d065d179\",\n    \"productSequence\": 1001\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category/categoryProductList", "host": ["{{base_url}}"], "path": ["productService", "category", "categoryProductList"]}}, "response": []}]}, {"name": "Dashboard top category", "item": [{"name": "Top category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/dashboard/topCategory?tenantId=1131", "host": ["{{base_url}}"], "path": ["productService", "category", "dashboard", "topCategory"], "query": [{"key": "tenantId", "value": "1131"}]}}, "response": []}]}, {"name": "Add category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryId\" : \"636cc2aaace2943700645846\",\r\n    \"categoryName\" : \"Test sub category\",\r\n    \"secondaryCategoryName\" : \"Test Secondary lang name\",\r\n    \"tenantId\" : 1035,\r\n    \"type\" : \"FAMILY\",\r\n    \"isActive\" : true,\r\n    \"parentFamilyId\": \"636cc1f1ace2943700645840\",\r\n    \"parentCategoryId\": \"636cc289ace2943700645843\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Add and Edit category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryId\" : \"636c8b35e4f5e5558c72f670\",\r\n    \"categoryName\" : \"NEW family\",\r\n    \"secondaryCategoryName\" : \"NEW Secondary lang name\",\r\n    \"tenantId\" : 1035,\r\n    \"type\" : \"FAMILY\",\r\n    \"isActive\" : true,\r\n    \"parentFamilyId\": \"636bae633faa4a5020118c0f\",\r\n    \"parentCategoryId\": \"636cc289ace2943700645843\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Change category sequnce", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryId\": [\r\n        \"636bae633faa4a5020118c0f\",\r\n        \"637484cb8dd3c800120bd3cf\",\r\n        \"6373668ad35c5b0012db9c19\",\r\n        \"637484be8dd3c800120bd3cc\",\r\n        \"637366d6d35c5b0012db9c1f\",\r\n        \"637484768dd3c800120bd3b9\",\r\n        \"6373674dd35c5b0012db9c23\",\r\n        \"6374852b8dd3c800120bd3e7\",\r\n        \"637484ab8dd3c800120bd3c5\",\r\n        \"637484858dd3c800120bd3bd\",\r\n        \"637485018dd3c800120bd3d9\",\r\n        \"6373676cd35c5b0012db9c27\",\r\n        \"637485118dd3c800120bd3e0\",\r\n        \"63771137afe939001295298a\",\r\n        \"6377676951ecdd00121aa744\",\r\n        \"6377681751ecdd00121aa773\"\r\n    ],\r\n    \"type\": \"FAMILY\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Get  category list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category?tenantId=1001&type=CATEGORY&categoryId=639968068081d30012135137&status=ALL&productSchemaType=products_2.0", "host": ["{{base_url}}"], "path": ["productService", "category"], "query": [{"key": "tenantId", "value": "1001"}, {"key": "type", "value": "CATEGORY", "description": "FAMILY/CATEGORY/SUBCATEGORY"}, {"key": "categoryId", "value": "639968068081d30012135137", "description": "if type = CATEGORY and SUBCATEGORY"}, {"key": "status", "value": "ALL", "description": "ALL/ACTIVE/INACTIVE"}, {"key": "productSchemaType", "value": "products_2.0"}]}}, "response": []}, {"name": "Get category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/get-category?categoryType=FAMILY", "host": ["{{base_url}}"], "path": ["productService", "category", "get-category"], "query": [{"key": "categoryId", "value": "636cc2aaace2943700645846", "disabled": true}, {"key": "categoryType", "value": "FAMILY"}]}}, "response": []}, {"name": "Get category List (With Product Count)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1001,\n    \"priceListId\": \"64156686ea18a9f9411d6664\",\n    \"productSchemaType\": \"products_2.0\",\n    // \"hideOutOfStock\": true,\n    \"filters\": {\n        \"brands\": [],\n        \"priceRange\": {\n            \"from\": 0,\n            \"to\": 6\n        },\n        \"tags\": [],\n        \"productType\": []\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category/get-category", "host": ["{{base_url}}"], "path": ["productService", "category", "get-category"]}}, "response": []}, {"name": "Delete category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1114,\r\n    \"type\": \"CATEGORY\",\r\n    \"familyId\": \"6375c5be421cf4001212f453\",\r\n    \"categoryId\": \"638455101d368924e8d67400\",\r\n    \"subCategoryId\": \"638984bb05432f0012932756\",\r\n    \"newFamilyId\": \"6375c5be421cf4001212f453\",\r\n    \"newCategoryId\": \"637743070bbd13001285669a\",\r\n    \"newSubCategoryId\": \"638456061d368924e8d67430\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}]}, {"name": "Datasheet", "item": [{"name": "export price sheet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dataType\": \"PRICE\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/dataSheet/priceExport", "host": ["{{base_url}}"], "path": ["productService", "dataSheet", "priceExport"]}}, "response": []}, {"name": "export inventory sheet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dataType\": \"INVENTORY\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/dataSheet/inventoryExport", "host": ["{{base_url}}"], "path": ["productService", "dataSheet", "inventoryExport"]}}, "response": []}]}, {"name": "Product 2.0", "item": [{"name": "Add Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"product\": {\n        \"isActive\": true,\n        \"attributes\": [],\n        \"title\": \"Variant product error\",\n        \"secondaryLanguageTitle\": \"Variant product error\",\n        \"itemNumber\": \"3-101-96-85-01\",\n        \"barcodes\": [],\n        \"type\": \"PARENT\",\n        \"brand\": \"63997966281ecb0012eb212f\",\n        \"family\": \"6374d36b421cf4001212f040\",\n        \"tags\": [],\n        \"variants\": {\n            \"type\": \"Color\",\n            \"values\": [\n                \"1/22\"\n            ]\n        },\n        \"taxId\": \"63bd35c8f0b237aec41b0873\"\n    },\n    \"variantProducts\": [\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"78-098-098\",\n            \"uomMapping\": {\n                \"uom\": \"63c9047e6659a0c718f06e31\",\n                \"minQty\": \"1\"\n            },\n            \"priceMapping\": [],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"quantity\": 0\n                },\n                {\n                    \"warehouse_id\": \"63d8f7cf8277c200121be132\",\n                    \"branch_id\": \"63d8f7cf8277c200121be130\",\n                    \"quantity\": 0\n                },\n                {\n                    \"warehouse_id\": \"63d8f7c58277c200121be0fe\",\n                    \"branch_id\": \"63d8f7c58277c200121be0fc\",\n                    \"quantity\": 0\n                }\n            ],\n            \"barcodes\": [],\n            \"groupName\": \"1\",\n            \"variantName\": \"22\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2", "host": ["{{base_url}}"], "path": ["productService", "productV2"]}}, "response": []}, {"name": "Product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/products?page=1&tenantId=1131&withVariants=true&perPage=20", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"], "query": [{"key": "page", "value": "1"}, {"key": "search<PERSON>ey", "value": "Y01", "disabled": true}, {"key": "tenantId", "value": "1131"}, {"key": "withV<PERSON>ts", "value": "true"}, {"key": "type", "value": "ALL", "disabled": true}, {"key": "sortBy", "value": "item_number", "description": "item_number, title", "disabled": true}, {"key": "sortType", "value": "ASC", "description": "ASC/DESC", "disabled": true}, {"key": "perPage", "value": "20"}, {"key": "sortBy", "value": "title", "disabled": true}, {"key": "sortType", "value": "DESC", "disabled": true}]}}, "response": []}, {"name": "product details api", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2?tenantId=1119&productId=645b22e0eaa2d77e9058e783&customerUserRoleId=63b25f79989ff60011a1ff50", "host": ["{{base_url}}"], "path": ["productService", "productV2"], "query": [{"key": "activeVariants", "value": "true", "disabled": true}, {"key": "tenantId", "value": "1119"}, {"key": "productId", "value": "645b22e0eaa2d77e9058e783"}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50"}]}}, "response": [{"name": "product details api", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2?tenantId=1114&productId=64394235bdb2793f615d83c2&activeVariants=true", "host": ["{{base_url}}"], "path": ["productService", "productV2"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "productId", "value": "64394235bdb2793f615d83c2"}, {"key": "activeVariants", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "refreshed-access-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "5273"}, {"key": "ETag", "value": "W/\"1499-YBifNDnDuGUhcaZYe9JXXcQRhlc\""}, {"key": "Date", "value": "Fri, 14 Apr 2023 12:38:35 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"status_success\",\n    \"data\": {\n        \"_id\": \"64394235bdb2793f615d83c2\",\n        \"tags\": [],\n        \"variant_item_numbers\": [\n            \"04-14-2023-13\",\n            \"04-14-2023-14\"\n        ],\n        \"price_mappings\": [\n            {\n                \"master_price_id\": \"637c687c4f63130012f43d15\",\n                \"price\": 2\n            },\n            {\n                \"master_price_id\": \"639035510eb78c00125dd91c\",\n                \"price\": 1\n            },\n            {\n                \"master_price_id\": \"63abfc9c4fac7e00122119fc\",\n                \"price\": 32\n            },\n            {\n                \"master_price_id\": \"63abfca44fac7e0012211a06\",\n                \"price\": 22\n            },\n            {\n                \"master_price_id\": \"63abfcac4fac7e0012211a0c\",\n                \"price\": 11\n            },\n            {\n                \"master_price_id\": \"63c903e36659a0c718f06ca2\",\n                \"price\": 22\n            },\n            {\n                \"master_price_id\": \"63c904016659a0c718f06cab\",\n                \"price\": 14\n            },\n            {\n                \"master_price_id\": \"63c904126659a0c718f06cb1\",\n                \"price\": 43\n            },\n            {\n                \"master_price_id\": \"63cf74ccce91dc4b3504eb97\",\n                \"price\": 23\n            },\n            {\n                \"master_price_id\": \"640b15770d73f2831601afd3\",\n                \"price\": 12\n            },\n            {\n                \"master_price_id\": \"64242f0edfcbe65012804bb7\",\n                \"price\": 12\n            }\n        ],\n        \"inventory_mappings\": [\n            {\n                \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                \"quantity\": 30\n            },\n            {\n                \"branch_id\": \"63d8f7c58277c200121be0fc\",\n                \"warehouse_id\": \"63d8f7c58277c200121be0fe\",\n                \"quantity\": 23\n            },\n            {\n                \"branch_id\": \"63d8f7cf8277c200121be130\",\n                \"warehouse_id\": \"63d8f7cf8277c200121be132\",\n                \"quantity\": 9\n            }\n        ],\n        \"attributes\": [],\n        \"tenant_id\": 1114,\n        \"item_number\": \"04-14-2023-12\",\n        \"unique_item_number\": \"1114_04-14-2023-12\",\n        \"title\": \"NEW PRODUCT WITH GROUP\",\n        \"type\": \"PARENT\",\n        \"brand_id\": \"637ca62e84b9540012d6ac15\",\n        \"family_id\": \"6375c5be421cf4001212f453\",\n        \"is_active\": true,\n        \"is_deleted\": false,\n        \"attribute_set\": null,\n        \"product_order\": 12800,\n        \"variants\": {\n            \"type\": \"Color\",\n            \"values\": [\n                {\n                    \"_id\": \"64394236bdb2793f615d83c5\",\n                    \"name\": \"red\"\n                },\n                {\n                    \"_id\": \"64394236bdb2793f615d83c6\",\n                    \"name\": \"green\"\n                }\n            ]\n        },\n        \"groups\": {\n            \"type\": \"size\",\n            \"values\": [\n                {\n                    \"_id\": \"64394236bdb2793f615d83c7\",\n                    \"name\": \"1mm\"\n                }\n            ]\n        },\n        \"variant_count\": 2,\n        \"created_at\": \"2023-04-14T12:08:58.505Z\",\n        \"updated_at\": \"2023-04-14T12:08:58.505Z\",\n        \"variantProducts\": [\n            {\n                \"packaging_map\": {\n                    \"uom_id\": \"637c995784b9540012d6aa2a\",\n                    \"min_qty\": 1\n                },\n                \"_id\": \"64394258bdb2793f615d83cc\",\n                \"tenant_id\": 1114,\n                \"item_number\": \"04-14-2023-14\",\n                \"type\": \"VARIANT\",\n                \"unique_item_number\": \"1114_04-14-2023-14\",\n                \"price_mappings\": [\n                    {\n                        \"master_price_id\": \"639035510eb78c00125dd91c\",\n                        \"price\": 22\n                    },\n                    {\n                        \"master_price_id\": \"637c687c4f63130012f43d15\",\n                        \"price\": 2\n                    },\n                    {\n                        \"master_price_id\": \"63abfc9c4fac7e00122119fc\",\n                        \"price\": 32\n                    },\n                    {\n                        \"master_price_id\": \"63abfca44fac7e0012211a06\",\n                        \"price\": 23\n                    },\n                    {\n                        \"master_price_id\": \"63abfcac4fac7e0012211a0c\",\n                        \"price\": 11\n                    },\n                    {\n                        \"master_price_id\": \"63c903e36659a0c718f06ca2\",\n                        \"price\": 22\n                    },\n                    {\n                        \"master_price_id\": \"63c904016659a0c718f06cab\",\n                        \"price\": 21\n                    },\n                    {\n                        \"master_price_id\": \"63c904126659a0c718f06cb1\",\n                        \"price\": 43\n                    },\n                    {\n                        \"master_price_id\": \"63cf74ccce91dc4b3504eb97\",\n                        \"price\": 43\n                    },\n                    {\n                        \"master_price_id\": \"640b15770d73f2831601afd3\",\n                        \"price\": 32\n                    },\n                    {\n                        \"master_price_id\": \"64242f0edfcbe65012804bb7\",\n                        \"price\": 12\n                    }\n                ],\n                \"inventory_mappings\": [\n                    {\n                        \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                        \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                        \"quantity\": 20\n                    },\n                    {\n                        \"branch_id\": \"63d8f7c58277c200121be0fc\",\n                        \"warehouse_id\": \"63d8f7c58277c200121be0fe\",\n                        \"quantity\": 3\n                    },\n                    {\n                        \"branch_id\": \"63d8f7cf8277c200121be130\",\n                        \"warehouse_id\": \"63d8f7cf8277c200121be132\",\n                        \"quantity\": 5\n                    }\n                ],\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"tags\": [],\n                \"variant_item_numbers\": [],\n                \"variant_id\": \"64394235bdb2793f615d83c2_64394236bdb2793f615d83c6_64394236bdb2793f615d83c7\",\n                \"parent_id\": \"64394235bdb2793f615d83c2\",\n                \"variant_value_id\": \"64394236bdb2793f615d83c6\",\n                \"group_value_id\": \"64394236bdb2793f615d83c7\",\n                \"variant_order\": 2,\n                \"attributes\": [],\n                \"created_at\": \"2023-04-14T12:08:58.412Z\",\n                \"updated_at\": \"2023-04-14T12:08:58.412Z\",\n                \"barcodes\": [],\n                \"id\": \"64394258bdb2793f615d83cc\"\n            },\n            {\n                \"packaging_map\": {\n                    \"uom_id\": \"637c995784b9540012d6aa2a\",\n                    \"min_qty\": 1\n                },\n                \"_id\": \"64394244bdb2793f615d83cb\",\n                \"tenant_id\": 1114,\n                \"item_number\": \"04-14-2023-13\",\n                \"type\": \"VARIANT\",\n                \"unique_item_number\": \"1114_04-14-2023-13\",\n                \"price_mappings\": [\n                    {\n                        \"master_price_id\": \"637c687c4f63130012f43d15\",\n                        \"price\": 11\n                    },\n                    {\n                        \"master_price_id\": \"639035510eb78c00125dd91c\",\n                        \"price\": 1\n                    },\n                    {\n                        \"master_price_id\": \"63abfc9c4fac7e00122119fc\",\n                        \"price\": 33\n                    },\n                    {\n                        \"master_price_id\": \"63abfca44fac7e0012211a06\",\n                        \"price\": 22\n                    },\n                    {\n                        \"master_price_id\": \"63abfcac4fac7e0012211a0c\",\n                        \"price\": 11\n                    },\n                    {\n                        \"master_price_id\": \"63c903e36659a0c718f06ca2\",\n                        \"price\": 22\n                    },\n                    {\n                        \"master_price_id\": \"63c904016659a0c718f06cab\",\n                        \"price\": 14\n                    },\n                    {\n                        \"master_price_id\": \"63c904126659a0c718f06cb1\",\n                        \"price\": 54\n                    },\n                    {\n                        \"master_price_id\": \"63cf74ccce91dc4b3504eb97\",\n                        \"price\": 23\n                    },\n                    {\n                        \"master_price_id\": \"640b15770d73f2831601afd3\",\n                        \"price\": 12\n                    },\n                    {\n                        \"master_price_id\": \"64242f0edfcbe65012804bb7\",\n                        \"price\": 23\n                    }\n                ],\n                \"inventory_mappings\": [\n                    {\n                        \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                        \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                        \"quantity\": 10\n                    },\n                    {\n                        \"branch_id\": \"63d8f7c58277c200121be0fc\",\n                        \"warehouse_id\": \"63d8f7c58277c200121be0fe\",\n                        \"quantity\": 20\n                    },\n                    {\n                        \"branch_id\": \"63d8f7cf8277c200121be130\",\n                        \"warehouse_id\": \"63d8f7cf8277c200121be132\",\n                        \"quantity\": 4\n                    }\n                ],\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"tags\": [],\n                \"variant_item_numbers\": [],\n                \"variant_id\": \"64394235bdb2793f615d83c2_64394236bdb2793f615d83c5_64394236bdb2793f615d83c7\",\n                \"parent_id\": \"64394235bdb2793f615d83c2\",\n                \"variant_value_id\": \"64394236bdb2793f615d83c5\",\n                \"group_value_id\": \"64394236bdb2793f615d83c7\",\n                \"variant_order\": 1,\n                \"attributes\": [],\n                \"created_at\": \"2023-04-14T12:08:58.412Z\",\n                \"updated_at\": \"2023-04-14T12:08:58.412Z\",\n                \"barcodes\": [\n                    {\n                        \"_id\": \"1114_Testing_barcode\",\n                        \"barcode\": \"Testing_barcode\",\n                        \"created_at\": \"2023-04-14T12:08:58.369Z\",\n                        \"is_active\": true,\n                        \"product_variant_id\": \"64394244bdb2793f615d83cb\",\n                        \"tenant_id\": 1114,\n                        \"updated_at\": \"2023-04-14T12:08:58.369Z\"\n                    }\n                ],\n                \"id\": \"64394244bdb2793f615d83cb\"\n            }\n        ],\n        \"barcodes\": [],\n        \"id\": \"64394235bdb2793f615d83c2\"\n    }\n}"}]}, {"name": "Product search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/search?tenantId=1001&searchKey=3-Piece Colored&page=1&perPage=600&searchType=FILTER&priceListId=64156686ea18a9f9411d6664&filters={\"priceRange\": { \"from\": 0, \"to\":6 }}", "host": ["{{base_url}}"], "path": ["productService", "productV2", "search"], "query": [{"key": "tenantId", "value": "1001"}, {"key": "search<PERSON>ey", "value": "3-Piece Colored"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "600"}, {"key": "searchType", "value": "FILTER"}, {"key": "priceListId", "value": "64156686ea18a9f9411d6664"}, {"key": "isPrimaryLanguage", "value": "true", "disabled": true}, {"key": "filters", "value": "{\"priceRange\": { \"from\": 0, \"to\":6 }}"}, {"key": "hideOutOfStock", "value": "true", "disabled": true}, {"key": "branchId", "value": "63c124e6efa1790013071f93", "disabled": true}]}}, "response": []}, {"name": "Edit Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"product\": {\n        \"productId\": \"6448126a043a6b9a64eeaa37\",\n        \"barcodes\": [],\n        \"isActive\": true,\n        \"title\": \"new testing product 121\",\n        \"secondaryLanguageTitle\": \"new testing product\",\n        \"itemNumber\": \"04-25-069\",\n        \"type\": \"PARENT\",\n        \"attributes\": [],\n        \"brand\": \"637d9c62e1bcfc001292df0d\",\n        \"family\": \"6377044aafe9390012952876\",\n        \"tags\": []\n    },\n    \"variantProducts\": [\n        \n        {\n            \"variantId\": \"6448126a043a6b9a64eeaa37_6453ce4d7f1ced8fbf969f13_6453ce4d7f1ced8fbf969f13\",\n            \"variantName\": \"Orange\",\n            \"groupName\": \"2mm\",\n            \"isActive\": true,\n            \"itemNumber\": \"04-25-069-02\",\n            \"uomMapping\": {\n                \"uom\": \"637c995784b9540012d6aa2a\",\n                \"minQty\": 1\n            },\n            \"priceMapping\": [\n                {\n                    \"master_price_id\": \"637c687c4f63130012f43d15\",\n                    \"price\": 5,\n                    \"product_variant_id\": \"6448126a043a6b9a64eeaa3f\"\n                },\n                {\n                    \"master_price_id\": \"639035510eb78c00125dd91c\",\n                    \"price\": 1,\n                    \"product_variant_id\": \"6448126a043a6b9a64eeaa3f\"\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6333f1fb67e853cb742142a2\",\n                    \"warehouse_id\": \"636ccd34fc3d2d01fc93c89f\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"63d8f7c58277c200121be0fc\",\n                    \"warehouse_id\": \"63d8f7c58277c200121be0fe\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"63d8f7cf8277c200121be130\",\n                    \"warehouse_id\": \"63d8f7cf8277c200121be132\",\n                    \"quantity\": 10\n                }\n            ],\n            \"barcodes\": []\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2", "host": ["{{base_url}}"], "path": ["productService", "productV2"]}}, "response": []}, {"name": "delete prdoucts", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/products?tenantId=1114&productVariantIds=6448bd8d4a2ee8b84ac37871&productVariantIds=6435027387d2d4694ff89dcb", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "productVariantIds", "value": "6448bd8d4a2ee8b84ac37871"}, {"key": "productVariantIds", "value": "6435027387d2d4694ff89dcb"}]}}, "response": []}, {"name": "update product status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"productVariantIds\": [\"64394197653d74030f13b4ec\", \"64353fe1e224158f194d42a7\"],\n    \"tenantId\": 1114,\n    \"type\": \"INACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/products?tenantId=1114", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"], "query": [{"key": "tenantId", "value": "1114"}]}}, "response": []}, {"name": "Check existing item number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/existItemNumber?itemNumber=Y01&tenantId=1113", "host": ["{{base_url}}"], "path": ["productService", "productV2", "existItemNumber"], "query": [{"key": "itemNumber", "value": "Y01"}, {"key": "tenantId", "value": "1113"}]}}, "response": []}, {"name": "Inventory listing API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/inventoryProductList?tenantId=1119&page=1&perPage=10&branchId=634015e7a2a6e700126c563d&sortType=INVENTORY&sortBy=-1", "host": ["{{base_url}}"], "path": ["productService", "productV2", "inventoryProductList"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "priceId", "value": "63906a580eb78c00125ddba8", "disabled": true}, {"key": "branchId", "value": "634015e7a2a6e700126c563d"}, {"key": "search<PERSON>ey", "value": "ITM-1", "disabled": true}, {"key": "sortType", "value": "INVENTORY"}, {"key": "sortBy", "value": "-1"}]}}, "response": []}, {"name": "Update variant group of Parent", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"productId\": \"6448126a043a6b9a64eeaa37\",\n    \"variants\": [\"blue\", \"Orange\"],\n    \"groups\": [\"2mm\"],\n    \"groupType\": \"dimension\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/updateVariantGroupInfo", "host": ["{{base_url}}"], "path": ["productService", "productV2", "updateVariantGroupInfo"]}}, "response": []}, {"name": "changeVariantOrder", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"productVariantIds\": [\"643f8ab895804bc337558bfd\", \"643f8ab495804bc337558bfc\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/changeVariantOrder", "host": ["{{base_url}}"], "path": ["productService", "productV2", "changeVariantOrder"]}}, "response": []}, {"name": "productCount", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/productCount?tenantId=1114&statusType=ACTIVE", "host": ["{{base_url}}"], "path": ["productService", "productV2", "productCount"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "statusType", "value": "ACTIVE"}]}}, "response": []}, {"name": "barcodeDetials", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/barcodeDetails?tenantId=1114&barcode=89897654", "host": ["{{base_url}}"], "path": ["productService", "productV2", "barcodeDetails"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "barcode", "value": "89897654"}]}}, "response": []}, {"name": "Favorite/Unfavorite products", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"ids\": [\n        \"6448b3d84a2ee8b84ac36d35\"\n    ],\n    \"type\": \"UNFAVORITE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/favoriteProduct", "host": ["{{base_url}}"], "path": ["productService", "productV2", "favoriteProduct"]}}, "response": []}, {"name": "Favorite product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/favoriteProduct?tenantId=1119&perPage=3&favoriteProductId=645a1e9c18b3f8dbd3315d72", "host": ["{{base_url}}"], "path": ["productService", "productV2", "favoriteProduct"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "perPage", "value": "3"}, {"key": "favoriteProductId", "value": "645a1e9c18b3f8dbd3315d72"}]}}, "response": []}, {"name": "Tag list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/tags?tenantId=1119&perPage=10", "host": ["{{base_url}}"], "path": ["productService", "productV2", "tags"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "perPage", "value": "10"}]}}, "response": []}]}, {"name": "Images 2.0", "item": [{"name": "Get signature for image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"645b22e0eaa2d77e9058e783_645b22e0eaa2d77e9058e788_P1.jpeg\",\n    \"type\": \"GROUP_IMAGE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/getUploadSignature", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "getUploadSignature"]}}, "response": [{"name": "Get signature for image", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"04-14-2023-12_P1.JPEG\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/getUploadSignature", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "getUploadSignature"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "655"}, {"key": "ETag", "value": "W/\"28f-VnJwWRwd4gGh8zh957PNqyNfbQE\""}, {"key": "Date", "value": "Mon, 17 Apr 2023 13:39:07 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"upload_url_get_success\",\n    \"data\": {\n        \"signedUrl\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIATGBACB6QFTN5S5EP%2F20230417%2Fme-south-1%2Fs3%2Faws4_request&X-Amz-Date=20230417T133905Z&X-Amz-Expires=300&X-Amz-Signature=f6cee5d21e91e73641429b7f5882b6269dc4692960b384b0bb229fe04570f2d9&X-Amz-SignedHeaders=host\",\n        \"s3Url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG\",\n        \"imageName\": \"64394235bdb2793f615d83c2_P1.JPEG\",\n        \"productVariantId\": \"64394235bdb2793f615d83c2\"\n    }\n}"}]}, {"name": "Add Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"s3Url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/643577975989e41e5eeb060e_P1.jpeg\",\n    \"imageSize\": 11140,\n    \"imageName\": \"645b22e0eaa2d77e9058e783_645b22e0eaa2d77e9058e788_P1.jpeg\",\n    \"groupId\": \"645b22e0eaa2d77e9058e788\",\n    \"productVariantId\": \"645b22e0eaa2d77e9058e783\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2", "host": ["{{base_url}}"], "path": ["productService", "imageV2"]}}, "response": []}, {"name": "delete images", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2?tenantId=1119&imageIds=6440cf4ff15122d79fb3a074&imageIds=6440cf4ff15122d79fb3a075", "host": ["{{base_url}}"], "path": ["productService", "imageV2"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "imageIds", "value": "6440cf4ff15122d79fb3a074"}, {"key": "imageIds", "value": "6440cf4ff15122d79fb3a075"}]}}, "response": []}, {"name": "Swap Images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"imageIds\": [\"63ad7afc251da20a3613554d\", \"63ad7afc251da20a36135550\"],\n    \"tenantId\": 1114\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/swapProductImages", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "swapProductImages"]}}, "response": []}, {"name": "Image listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2?tenantId=1118&imageType=NEED_IMAGES&page=1&perPage=10&searchKey=855R", "host": ["{{base_url}}"], "path": ["productService", "imageV2"], "query": [{"key": "tenantId", "value": "1118"}, {"key": "imageType", "value": "NEED_IMAGES"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "855R"}]}}, "response": [{"name": "Image listing", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2?tenantId=1119&imageType=ALL&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["productService", "imageV2"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "imageType", "value": "ALL"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "alle", "disabled": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "refreshed-access-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "6399"}, {"key": "ETag", "value": "W/\"18ff-+ZOb1rkByZIkElAsf48065hfed4\""}, {"key": "Date", "value": "Thu, 20 Apr 2023 09:11:30 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"status_success\",\n    \"data\": {\n        \"list\": [\n            {\n                \"_id\": \"643ff93df15122d79f6a92db\",\n                \"image_name\": \"643f8aaf95804bc337558bf3_P1.jpeg\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T14:22:52.983Z\",\n                \"created_by\": \"63329a849716fe0012d0ee4d\",\n                \"image_number\": 1,\n                \"image_size\": 13919,\n                \"product_variant_id\": \"643f8aaf95804bc337558bf3\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/643f8aaf95804bc337558bf3_P1.jpeg\",\n                \"updated_at\": \"2023-04-19T14:22:52.983Z\",\n                \"updated_by\": \"63329a849716fe0012d0ee4d\",\n                \"productDetails\": {\n                    \"_id\": \"643f8aaf95804bc337558bf3\",\n                    \"item_number\": \"2023-19-04-01\",\n                    \"title\": \"<PERSON>ch\",\n                    \"secondary_language_title\": \"<PERSON> <PERSON>ch\",\n                    \"type\": \"PARENT\"\n                }\n            },\n            {\n                \"_id\": \"643ff93cf15122d79f6a92b2\",\n                \"image_name\": \"643f8aaf95804bc337558bf3_P2.jpeg\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T14:22:52.918Z\",\n                \"created_by\": \"63329a849716fe0012d0ee4d\",\n                \"image_number\": 2,\n                \"image_size\": 2499,\n                \"product_variant_id\": \"643f8aaf95804bc337558bf3\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/643f8aaf95804bc337558bf3_P2.jpeg\",\n                \"updated_at\": \"2023-04-19T14:22:52.918Z\",\n                \"updated_by\": \"63329a849716fe0012d0ee4d\",\n                \"productDetails\": {\n                    \"_id\": \"643f8aaf95804bc337558bf3\",\n                    \"item_number\": \"2023-19-04-01\",\n                    \"title\": \"Allen Wrench\",\n                    \"secondary_language_title\": \"Allen Wrench\",\n                    \"type\": \"PARENT\"\n                }\n            },\n            {\n                \"_id\": \"643f9008f15122d79fad7450\",\n                \"image_name\": \"643f8aaf95804bc337558bf3_643f8aaf95804bc337558bf8_P1.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:54:00.561Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"group_id\": \"643f8aaf95804bc337558bf8\",\n                \"image_number\": 1,\n                \"image_size\": 11140,\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/643f8aaf95804bc337558bf3_643f8aaf95804bc337558bf8_P1.JPEG\",\n                \"updated_at\": \"2023-04-19T06:54:00.561Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"product_variant_id\": \"643f8aaf95804bc337558bf3\",\n                \"productDetails\": {\n                    \"_id\": \"643f8aaf95804bc337558bf3\",\n                    \"item_number\": \"2023-19-04-01\",\n                    \"title\": \"Allen Wrench\",\n                    \"secondary_language_title\": \"Allen Wrench\",\n                    \"type\": \"PARENT\"\n                }\n            },\n            {\n                \"_id\": \"643f8ea0f15122d79faab6a8\",\n                \"image_name\": \"643f8ab495804bc337558bfc_P1.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:48:00.342Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 1,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"643f8ab495804bc337558bfc\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/643f8ab495804bc337558bfc_P1.JPEG\",\n                \"updated_at\": \"2023-04-19T06:48:00.342Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"643f8ab495804bc337558bfc\",\n                    \"item_number\": \"2023-19-04-02\",\n                    \"type\": \"VARIANT\",\n                    \"variant_value_id\": {\n                        \"_id\": \"643f8aaf95804bc337558bf6\",\n                        \"name\": \"red\"\n                    },\n                    \"group_value_id\": {\n                        \"_id\": \"643f8aaf95804bc337558bf8\",\n                        \"name\": \"10mm\"\n                    },\n                    \"parentDetails\": {\n                        \"_id\": \"643f8aaf95804bc337558bf3\",\n                        \"item_number\": \"2023-19-04-01\",\n                        \"title\": \"Allen Wrench\",\n                        \"secondary_language_title\": \"Allen Wrench\",\n                        \"type\": \"PARENT\"\n                    }\n                }\n            },\n            {\n                \"_id\": \"643f8940f15122d79fa0a432\",\n                \"image_name\": \"6437ac34e48504689be2a087_P1.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:25:04.639Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 1,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"6437ac34e48504689be2a087\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/6437ac34e48504689be2a087_P1.JPEG\",\n                \"updated_at\": \"2023-04-19T06:25:04.639Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"6437ac34e48504689be2a087\",\n                    \"item_number\": \"ITM-VAR-10202\",\n                    \"type\": \"VARIANT\",\n                    \"variant_value_id\": {\n                        \"_id\": \"6437ac33e48504689be2a081\",\n                        \"name\": \"blue\"\n                    },\n                    \"parentDetails\": {\n                        \"_id\": \"6437ac33e48504689be2a07d\",\n                        \"item_number\": \"ITM-1020\",\n                        \"title\": \"9 Compartment Storage Organizer\",\n                        \"type\": \"PARENT\"\n                    }\n                }\n            },\n            {\n                \"_id\": \"643f88edf15122d79fa011e7\",\n                \"image_name\": \"6437ac34e48504689be2a087_P2.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:23:40.937Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 2,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"6437ac34e48504689be2a087\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/6437ac34e48504689be2a087_P2.JPEG\",\n                \"updated_at\": \"2023-04-19T06:23:40.937Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"6437ac34e48504689be2a087\",\n                    \"item_number\": \"ITM-VAR-10202\",\n                    \"type\": \"VARIANT\",\n                    \"variant_value_id\": {\n                        \"_id\": \"6437ac33e48504689be2a081\",\n                        \"name\": \"blue\"\n                    },\n                    \"parentDetails\": {\n                        \"_id\": \"6437ac33e48504689be2a07d\",\n                        \"item_number\": \"ITM-1020\",\n                        \"title\": \"9 Compartment Storage Organizer\",\n                        \"type\": \"PARENT\"\n                    }\n                }\n            },\n            {\n                \"_id\": \"643f883ff15122d79f9edf1d\",\n                \"image_name\": \"6437ac33e48504689be2a07d_P2.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:20:47.323Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 2,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"6437ac33e48504689be2a07d\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/6437ac33e48504689be2a07d_P2.JPEG\",\n                \"updated_at\": \"2023-04-19T06:20:47.323Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"6437ac33e48504689be2a07d\",\n                    \"item_number\": \"ITM-1020\",\n                    \"title\": \"9 Compartment Storage Organizer\",\n                    \"type\": \"PARENT\"\n                }\n            },\n            {\n                \"_id\": \"643f8736f15122d79f9d1834\",\n                \"image_name\": \"6436491ce9fce6aa030b620c_P2.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:16:22.898Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 2,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"6436491ce9fce6aa030b620c\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/6436491ce9fce6aa030b620c_P2.JPEG\",\n                \"updated_at\": \"2023-04-19T06:16:22.898Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"6436491ce9fce6aa030b620c\",\n                    \"item_number\": \"ITM-1119-1009\",\n                    \"title\": \"Digital Multimeter Auto-Ranging Tester Black/Orange 28.1cm-Original\",\n                    \"type\": \"SINGLE\"\n                }\n            },\n            {\n                \"_id\": \"643f864ff15122d79f9b6a5a\",\n                \"image_name\": \"6436491ce9fce6aa030b620c_P1.JPEG\",\n                \"tenant_id\": 1119,\n                \"__v\": 0,\n                \"created_at\": \"2023-04-19T06:12:31.391Z\",\n                \"created_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"image_number\": 1,\n                \"image_size\": 11140,\n                \"product_variant_id\": \"6436491ce9fce6aa030b620c\",\n                \"s3_url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1119/6436491ce9fce6aa030b620c_P1.JPEG\",\n                \"updated_at\": \"2023-04-19T06:12:31.391Z\",\n                \"updated_by\": \"62e275ac0b77a6bbdf16fac8\",\n                \"productDetails\": {\n                    \"_id\": \"6436491ce9fce6aa030b620c\",\n                    \"item_number\": \"ITM-1119-1009\",\n                    \"title\": \"Digital Multimeter Auto-Ranging Tester Black/Orange 28.1cm-Original\",\n                    \"type\": \"SINGLE\"\n                }\n            }\n        ],\n        \"count\": 9\n    }\n}"}]}, {"name": "Get image match product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2/imagesMatch?page=1&perPage=10&tenantId=1114&linkedTenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "imagesMatch"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1114"}, {"key": "linkedTenantId", "value": "1119"}, {"key": "search<PERSON>ey", "value": "testing", "disabled": true}]}}, "response": []}, {"name": "Image match action", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"linkedTenantId\": 1119,\n    \"type\": \"SELECTED\",  // ALL or SELECTED\n    \"product\": [\n        {\n            \"productId\": \"63f5b1be478135a96edd68dd\",\n            \"linkedTenantProductId\": \"63ad1a8d43ff4500122d20c6\"\n        },\n        {\n            \"variantId\": \"63f34b2e53ff820a78b4d090\",\n            \"parentId\": \"63f34b2e53ff820a78b4d098\",\n            \"linkedTenantVariantId\": \"63b02100ecf078002c555491\",\n            \"linkedParentId\": \"642bb8f8a5ac388d302e9e1c\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/imagesMatch", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "imagesMatch"]}}, "response": []}]}, {"name": "Deals", "item": [{"name": "Create Deal", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"priceId\": \"637c687c4f63130012f43d15\",\n    \"dealType\": \"DISCOUNT\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Add product in deal", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dealId\": \"64638c1bbce4f83c05c81276\",\n    \"products\": [\n        {\n            \"productId\": \"64661c6861324c754acfe9dc\"\n        }\n    ],\n    \"dealFromDate\": \"2023-06-06T00:00:00.000Z\",\n    \"dealToDate\": \"2023-06-10T00:00:00.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealProduct", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealProduct"]}}, "response": []}, {"name": "Update Product Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealId\": \"64564a0f02f55944c1fa4742\",\n    \"dealProductId\": [\n        \"645a0ccff0f31b9ff6f45c10\",\n        \"645a0ccff0f31b9ff6f45c13\",\n        \"645a0ccff0f31b9ff6f45c16\"\n    ],\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}, {"name": "Get Product List by Item Numbers", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"priceId\": \"637ca9c784b9540012d6aca3\",\n    \"page\": 1,\n    \"perPage\": 20,\n    \"itemNumbers\": [\n        \"VarItem1595\",\n        \"Item211241\",\n        \"Item211386\",\n        \"Item211132\",\n        \"Item211322\",\n        \"Item211074\",\n        \"Item211370\",\n        \"56056\",\n        \"6606\",\n        \"132\",\n        \"125\",\n        \"0251-16515-sdsd\",\n        \"5626\",\n        \"5\",\n        \"0251-16515-sdsd11\",\n        \"021-16515-sdsd\",\n        \"789WEqe\",\n        \"789WE\",\n        \"789WEq\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealsProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealsProductList"]}}, "response": []}, {"name": "Check Valid Items", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1118,\n    \"dealId\": \"6490098d422008c3c00c13f6\",\n    \"priceId\": \"63983f77aa1d4e0011dfa103\",\n    \"dealType\": \"DISCOUNT\",\n    \"itemNumbers\": [\n        \"efawed8\",\n        \"SR\"\n    ],\n    \"dealFromDate\": \"2023-06-19T07:53:48.484Z\",\n    \"dealToDate\": \"2023-06-27T18:29:59.595Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/checkValidItems", "host": ["{{base_url}}"], "path": ["productService", "deal", "checkValidItems"]}}, "response": []}, {"name": "Update deal details", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dealId\": \"64467e1e618c098e14191f81\",\n    \"tenantId\": 1114,\n    \"dealName\": \"first deal\",\n    \"secondaryDealName\": \"first deal\",\n    \"dealFromDate\": \"24-04-2023\",\n    \"dealToDate\": \"29-04-2023\",\n    \"salesPersonId\": [ \"639abd59c87179001240c2df\" ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Update deal product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealProductId\": \"6450e1585d04e0f27b84c1de\",\n    \"dealType\": \"DISCOUNT\",\n    \"discountType\": \"PERCENT\",  // dealType: \"DISCOUNT\" ( PERCENT, AMOUNT )\n    \"percent\": 10,              // dealType: \"DISCOUNT\"\n    \"amount\": 250,              // dealType: \"DISCOUNT\"\n    \"firstTier\": {              // dealType: \"BULK_PRICING\"\n        \"product_qty\": 5,\n        \"price\": 20\n    },\n    \"secondTier\": {              // dealType: \"BULK_PRICING\"\n        \"product_qty\": 10,\n        \"price\": 15\n    },\n    \"thirdTier\": {               // dealType: \"BULK_PRICING\"\n        \"product_qty\": 15,\n        \"price\": 10\n    },\n    \"buyProduct\": 5,              // dealType: \"BUY_X_AND_GET_Y\"\n    \"freeProduct\": 1              // dealType: \"BUY_X_AND_GET_Y\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealProduct", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealProduct"]}}, "response": []}, {"name": "Update Product Sequence", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealProductId\": \"645a0ccff0f31b9ff6f45c16\",\n    \"dealId\": \"6459d47d6b3d9da748e65b9e\",\n    \"dealProductSequence\": 350\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}, {"name": "Get Deal list", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/productService/deal?page=1&perPage=20&tenantId=1114&searchKey=", "host": ["{{base_url}}"], "path": ["productService", "deal"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "tenantId", "value": "1114"}, {"key": "search<PERSON>ey", "value": ""}]}}, "response": []}, {"name": "Get Deal Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/detail?dealId=64564a0f02f55944c1fa4742", "host": ["{{base_url}}"], "path": ["productService", "deal", "detail"], "query": [{"key": "dealId", "value": "64564a0f02f55944c1fa4742"}]}}, "response": []}, {"name": "Product list for deal", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/productList?page=1&perPage=50&searchKey=&tenantId=1131&dealFromDate=2023-06-10T00:00:00.000Z&dealToDate=2023-06-11T00:00:00.000Z&priceId=6374bd481f6d6bdec112ee96&dealId=64847c5450d5eca0ff518aa6", "host": ["{{base_url}}"], "path": ["productService", "deal", "productList"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "50"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "tenantId", "value": "1131"}, {"key": "dealFromDate", "value": "2023-06-10T00:00:00.000Z"}, {"key": "dealToDate", "value": "2023-06-11T00:00:00.000Z"}, {"key": "priceId", "value": "6374bd481f6d6bdec112ee96"}, {"key": "dealId", "value": "64847c5450d5eca0ff518aa6"}]}}, "response": []}, {"name": "Get Current Deal Product List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/dealProduct?page=1&perPage=25&tenantId=1114&dealId=64638b64bce4f83c05c811b8&priceId=63abfc9c4fac7e00122119fc", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealProduct"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "25"}, {"key": "tenantId", "value": "1114"}, {"key": "dealId", "value": "64638b64bce4f83c05c811b8"}, {"key": "priceId", "value": "63abfc9c4fac7e00122119fc"}, {"key": "search<PERSON>ey", "value": "New Single Product", "disabled": true}, {"key": "sortBy", "value": "PRICE", "description": "PRICE / INVENTORY", "disabled": true}, {"key": "sortType", "value": "ASC", "description": "ASC / DESC", "disabled": true}]}}, "response": []}, {"name": "Get Running deals list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/dealsProductList?tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealsProductList"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "salesPersonUserId", "value": "63bd0fa3282c53001268f434", "disabled": true}, {"key": "productLimit", "value": "2", "disabled": true}, {"key": "dealType", "value": "BULK_PRICING", "description": "DISCOUNT, BULK_PRICING, BUY_X_AND_GET_Y", "disabled": true}]}}, "response": []}, {"name": "Get Next Products List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/updateProductList?dealId=64638c1bbce4f83c05c81276&dealProductSequence=300&nextDealProductCount=3", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"], "query": [{"key": "dealId", "value": "64638c1bbce4f83c05c81276"}, {"key": "dealProductSequence", "value": "300"}, {"key": "nextDealProductCount", "value": "3"}]}}, "response": []}, {"name": "Delete Deal Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealId\": \"64564a0f02f55944c1fa4742\",\n    \"dealProductId\": [\n        \"645a0ccff0f31b9ff6f45c16\",\n        \"645a0ccff0f31b9ff6f45c13\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}, {"name": "Delete Deals", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealIds\": [\n        \"6459d47d6b3d9da748e65b9e\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Update Deal status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"status\": \"PAUSED\",  // PAUSED and CANCELLED\n    \"dealIds\": [\n        \"64564a0f02f55944c1fa4742\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}]}, {"name": "Order 2.0", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "cartActions", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\",\n    \"uomId\": \"63a3f1da34a14d00127f1d3a\",\n    \"uomName\": \"Inch\",\n    \"quantity\": 10,\n    \"minQty\": 20,\n    \"productVariantId\": \"6436483be9fce6aa030b61f0\",\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"tenantId\": 1119,\n    \"actionType\": \"ADD_ITEM_TO_CART\",\n    \"basePrice\": 10,\n    \"tax\": 1.5,\n    \"variantId\": \"645b22e0eaa2d77e9058e783_646700b58d1b30bd5a0328ab_645b22e0eaa2d77e9058e788\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/cart", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"]}}, "response": []}, {"name": "cartDetails", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/cart?tenantId=1119&customerUserRoleId=63fc90d06085cd0012c03617", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63fc90d06085cd0012c03617"}]}}, "response": []}, {"name": "delet cart items", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/cart?tenantId=1119&customerUserRoleId=63fc90d06085cd0012c03617&cartItemIds=64882324d32a5bf87f82fe98&cartItemIds=64882324d32a5bf87f82fe98", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63fc90d06085cd0012c03617"}, {"key": "cartItemIds", "value": "64882324d32a5bf87f82fe98"}, {"key": "cartItemIds", "value": "64882324d32a5bf87f82fe98"}]}}, "response": []}, {"name": "cartItemCount", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/itemCount?tenantId=1119&customerUserRoleId=63b25f79989ff60011a1ff50", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "itemCount"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50"}]}}, "response": []}]}, {"name": "Drafts", "item": [{"name": "create draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"customerUserRoleId\": \"63fc90d06085cd0012c03617\",\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerName\": \"MS Dhoni POSTMAN\",\n    \"customerPrimaryContactName\": \"Dhoni POSTMAN\",\n    \"customerId\": \"111910026\",\n    \"externalId\": \"23\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/draft", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draft"]}}, "response": []}, {"name": "draft listing API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/drafts?page=1&perPage=10&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "drafts"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1119"}, {"key": "search<PERSON>ey", "value": null, "disabled": true}]}}, "response": []}, {"name": "draft details api", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/draft?draftId=64882f09a3400ecc1274f33f&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draft"], "query": [{"key": "draftId", "value": "64882f09a3400ecc1274f33f"}, {"key": "tenantId", "value": "1119"}]}}, "response": []}, {"name": "delete drafts api", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/drafts?draftIds=646333a8b1802ecac1d522f0&draftIds=648821f68a35d0a692ce321c&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "drafts"], "query": [{"key": "draftIds", "value": "646333a8b1802ecac1d522f0"}, {"key": "draftIds", "value": "648821f68a35d0a692ce321c"}, {"key": "tenantId", "value": "1119"}]}}, "response": []}, {"name": "Order from Draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"draftId\": \"646252305cd66039ff24e7fd\",\n    \"tenantId\": 1119,\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"customerName\": \"Ketan POSTMAN\",\n    \"customerLegalName\": \"POSTMAN LEGAL NAME\",\n    \"salePersonName\": \"Ketan Sales Person New POSTMAN\",\n    \"branchId\": \"633aaf5585b9b200129a4503\",\n    \"externalId\": \"ewqeqdsa\",\n    \"orderAppType\": \"SALES_APP\",\n    \"orderPunchDeviceType\": \"MOBILE\",\n    \"orderPunchDeviceOs\": \"IOS\",\n    \"orderRemark\": \"Testing remarks\",\n    \"ShippingAddress\": \"Testing Address of the customer or selected address\",\n    \"cityId\": \"631ffc5c89881e0012660b3b\",\n    \"regionId\": \"630c58a6bfb2982b1cbcd4cf\",\n    \"shippingMobileNumber\": 9685635896,\n    \"shippingCountryCode\": \"+91\",\n    \"regionName\": \"Gujarat\",\n    \"cityName\": \"Surat\",\n    \"shippingCoordinates\": {\n        \"lat\": 20.121,\n        \"lng\": 72.00\n    },\n    \"customerPrimaryContactName\": \"Customer firstname and last name POSTMAN\",\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/placeOrderFromDraft", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "placeOrderFromDraft"]}}, "response": []}, {"name": "Draft to cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"draftId\": \"648828a11a76cdf00283115f\",\n    \"tenantId\": 1119,\n    \"customerUserRoleId\": \"63fc90d06085cd0012c03617\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/draftToCart", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draftToCart"]}}, "response": []}]}, {"name": "check order email", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/checkOrderUsers?token=63c160e4da461e0012432ee3&orderId=6486e94a5fc430e9cf1e109f", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "checkOrderUsers"], "query": [{"key": "token", "value": "63c160e4da461e0012432ee3"}, {"key": "orderId", "value": "6486e94a5fc430e9cf1e109f"}]}}, "response": []}]}, {"name": "Reports", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reportType\": \"ORDER_DETAIL\",\n    \"reportData\": {\n        \"tenantId\": 1119,\n        \"orderId\": \"648705c928669bc0541c31f6\",\n        \"timezone\": \"Asia/Riyadh\",\n        \"currency\": \"SAR\",\n        \"apiVersion\": 2\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/report/", "host": ["{{base_url}}"], "path": ["productService", "report", ""]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://rukx2brvl9.execute-api.me-south-1.amazonaws.com/development"}, {"key": "Authorization", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"key": "devicetoken", "value": "cccc066b-c593-47d5-bfb2-795175b9a76d"}, {"key": "refreshToken", "value": "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.ljlY5VaXgq-EAV2LWZ2tDUXH4TPV5mHNCWK95IXI43FJHGjuSlGaZjQ6d12d5PTETxNi-biCl-U2yOMF8BTM1qHH9fwDGCi-YqtMhPJ3VVEcgdLsYZzzAqd8C87Oi_FXhwyINzdIZe772P0QtbmKk1QbMPNjXNYv2GjSlo5-CUqV_sWi0voI9FgYA0LiqRD0vhhWlFrJKvEUqJTgUxz-1U-YfTk1u0NI2Y0esxZXIlLhQiepEGGiMemKG4SPA7zlIzj62t_m_ATAwIIceVKqAN53Q_hm5kN38uzmxjblh2GfYJu8Lww3Npz-6b1BMHKgO3bbDRkpdfGDOBc-kjuaHA.smnhLOiRa4T3QBi1.eXrLLkaxS0D9NJ8jL2butq5OCjHN19N3q1Zk3G1KMI9w83UNTVwlDJH58ik_rM_tjqV-BrWCns_HinbCq1l68d5Es_Ooc95K_1Fso6RruCiHH-YvdMNHBvA-EP9VtnO-3rVQMv6POQv7Q_1iXooOa7iZ7SW6gbjPg5WRHTJgb2kRsPazXpwyZQw0crLiuqFlkfXOFoboVtyixbKZ1UyZsGOgxCVhmI1xJl9PDRz_hbqFO-hclrNupVmLRg1JwOS0pwTGXd26p19IoLwgYmKQe6MavrsmZG1y18KBvUlSXv9jHoMpn63rq3w9S4Mt_G4pmYzc_Kbdr_-cLb0lGQQO2ej85An3fQnhgM0BD-c660QOzmGdSMVASTKWQOz7IL38dlUQ8Iw_WbNCwwSLi_5eXoVFQeB0ckeqprbpw1JT0-QC8y96TEKhg1Xw8zeXThdFeZvKkmMnFgC_EZfndnOtkNQkgnqHnDBb4WsABdce9Zppb0_xvkB6hnPzz01sy4ebKN9wTWIiFhKexoQ0FCRsUZIGrGDrfZ00_-bguT-iHOxSIrLlze0evjJ4lW64lmEEDwa0OlQrj2lJ9u2CEI9Sv229eT5uRT8G27FNuWvwE9goA_RfE9kCuAe6hYmSw-8dT41CtWho-ngmaKEOGFmUGAebSogekULzx0_6c7cGuDLselYznyMZrCbPyoEEj5rXut_0rTEiu1ctCxOfm3YMFhW4xVWxNhSYdgnPwPYZt06LSQF-O9i3j_oSo_9OaA9-gS4NvahhWnEB_o8Ysu-0TDgc2W1i1xRod90vapkQj_fxIH7lrIF_dhf9pfb5WzRA-CHGVksZ8WihbkajKSGtpU1N4mkPpVZsj9ebdOxNqlaKLpCNjsO6LTWVbjaoqUq5Iqc1D0Z-IarGKdDGokXwL-1vmKNJIvXgDAP5gGJRwh20iMXrD3mBKnfb4NdK9Id6RvwSOCchDbsgz_XfE_ljRh26dvMiioZ0tvMKLsynCv13cz-wUFW_hhrNKhfWxeqfBmg3F6Gh0a5iGT_F0SlVFQOxJkeSvFPWun3a2l-0JTzJfaLeUhJaUgvdlw7kyv8Et3rDseXaZIGzKqJSdgfWOaKnGyjQIsybj26DSm2bHHefU1jj4ujTvfqYA4PQs3fKWwsdr1p9dSYycOMdKf-1jxh0dygxaP6Lr_kunRn4fPu6Dmj7gSN9I4u21QuV5AB14hoMbaN3cjYnBj0gCdB-bpyAPFqj0bSQHmMbRTfaIA9elGTWmgXFpWvBhoCadMD42hhLTaddqlR6QCrsb4fvZZ5uzZtJn2MhRZPMecQYdIrK6z-xwfk68zULGV7SSg.lnEb4PTrsDCNrVPbLPObTg"}, {"key": "userRoleId", "value": "636906513b25300011587a90"}, {"key": "devicetype", "value": "IOS"}]}
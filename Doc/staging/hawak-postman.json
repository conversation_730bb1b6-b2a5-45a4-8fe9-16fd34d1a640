{"info": {"_postman_id": "e80de330-841e-4bb5-bd92-739caf98b8f9", "name": "Hawak-Product-Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24480219", "_collection_link": "https://lunar-flare-246963.postman.co/workspace/Hawak-Backend-Devs~838d78ef-c166-4c7b-9ee7-d04bc82e657e/collection/23115891-e80de330-841e-4bb5-bd92-739caf98b8f9?action=share&source=collection_link&creator=24480219"}, "item": [{"name": "Category", "item": [{"name": "Sort product list", "item": [{"name": "All Category Product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/allProductList?tenantId=1114&familyId=6375c5be421cf4001212f453&perPage=10&page=1&status=ALL&incrementLimit=true", "host": ["{{base_url}}"], "path": ["productService", "category", "allProductList"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "familyId", "value": "6375c5be421cf4001212f453", "description": "family category id"}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}, {"key": "status", "value": "ALL"}, {"key": "incrementLimit", "value": "true"}]}}, "response": []}, {"name": "Category Product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/categoryProductList?tenantId=1001&status=ACTIVE&page=1&perPage=20&appType=NATIVE&priceListId=64156686ea18a9f9411d6664&familyId=64bbd91fd28ff53223dd0b16&categoryId=64bbd940d28ff53223dd0b8a&branchId=63c124e6efa1790013071f93&isProductSplitting=true&productSchemaType=products_2.0&salesPersonUserRoleId=648d99c0cd63200012aab771", "host": ["{{base_url}}"], "path": ["productService", "category", "categoryProductList"], "query": [{"key": "tenantId", "value": "1001"}, {"key": "status", "value": "ACTIVE"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "incrementLimit", "value": "true", "disabled": true}, {"key": "appType", "value": "NATIVE"}, {"key": "priceListId", "value": "64156686ea18a9f9411d6664"}, {"key": "familyId", "value": "64bbd91fd28ff53223dd0b16"}, {"key": "subCategoryId", "value": "638848957ef5eb001232305f", "disabled": true}, {"key": "categoryId", "value": "64bbd940d28ff53223dd0b8a"}, {"key": "hideOutOfStock", "value": "true", "disabled": true}, {"key": "branchId", "value": "63c124e6efa1790013071f93"}, {"key": "filters", "value": "{\"productType\": [\"NEW_PRODUCTS\"]}", "disabled": true}, {"key": "isProductSplitting", "value": "true", "description": "Is temporary flag, will be removed soon"}, {"key": "productSchemaType", "value": "products_2.0"}, {"key": "salesPersonUserRoleId", "value": "648d99c0cd63200012aab771"}]}}, "response": []}, {"name": "Product sequnce", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1001,\n    \"status\": \"ACTIVE\",\n    \"appType\": \"NATIVE\",\n    \"priceListId\": \"64156686ea18a9f9411d6664\",\n    \"categoryId\": \"64bbd940d28ff53223dd0b8a\",\n    \"familyId\": \"64bbd91fd28ff53223dd0b16\",\n    \"perPage\": 20,\n    \"page\": 1,\n    \"salesPersonUserRoleId\": \"648d99c0cd63200012aab771\",\n    \"hideOutOfStock\": true,\n    \"branchId\": \"63c124e6efa1790013071f93\",\n    \"productSchemaType\": \"products_2.0\",\n    \"isProductSplitting\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category/categoryProductList", "host": ["{{base_url}}"], "path": ["productService", "category", "categoryProductList"]}}, "response": []}]}, {"name": "Dashboard top category", "item": [{"name": "Top category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/dashboard/topCategory?tenantId=1131", "host": ["{{base_url}}"], "path": ["productService", "category", "dashboard", "topCategory"], "query": [{"key": "tenantId", "value": "1131"}]}}, "response": []}]}, {"name": "Add category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryName\": \"March\",\r\n    \"secondaryCategoryName\": \"March\",\r\n    \"isActive\": true,\r\n    \"type\": \"FAMILY\",\r\n    \"tenantId\": 1119,\r\n    \"imageName\": \"Temp.jpeg\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Add and Edit category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryId\" : \"636c8b35e4f5e5558c72f670\",\r\n    \"categoryName\" : \"NEW family\",\r\n    \"secondaryCategoryName\" : \"NEW Secondary lang name\",\r\n    \"tenantId\" : 1035,\r\n    \"type\" : \"FAMILY\",\r\n    \"isActive\" : true,\r\n    \"parentFamilyId\": \"636bae633faa4a5020118c0f\",\r\n    \"parentCategoryId\": \"636cc289ace2943700645843\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Change category sequnce", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"categoryId\": [\r\n        \"636bae633faa4a5020118c0f\",\r\n        \"637484cb8dd3c800120bd3cf\",\r\n        \"6373668ad35c5b0012db9c19\",\r\n        \"637484be8dd3c800120bd3cc\",\r\n        \"637366d6d35c5b0012db9c1f\",\r\n        \"637484768dd3c800120bd3b9\",\r\n        \"6373674dd35c5b0012db9c23\",\r\n        \"6374852b8dd3c800120bd3e7\",\r\n        \"637484ab8dd3c800120bd3c5\",\r\n        \"637484858dd3c800120bd3bd\",\r\n        \"637485018dd3c800120bd3d9\",\r\n        \"6373676cd35c5b0012db9c27\",\r\n        \"637485118dd3c800120bd3e0\",\r\n        \"63771137afe939001295298a\",\r\n        \"6377676951ecdd00121aa744\",\r\n        \"6377681751ecdd00121aa773\"\r\n    ],\r\n    \"type\": \"FAMILY\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}, {"name": "Get  category list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category?tenantId=1001&status=ACTIVE&appType=NATIVE&priceListId=64156686ea18a9f9411d6664&categoryId=64bbd940d28ff53223dd0b8a&familyId=64bbd91fd28ff53223dd0b16&perPage=20&page=1&salesPersonUserRoleId=648d99c0cd63200012aab771&hideOutOfStock=true&branchId=63c124e6efa1790013071f93,&productSchemaType=products_2.0&isProductSplitting=true", "host": ["{{base_url}}"], "path": ["productService", "category"], "query": [{"key": "tenantId", "value": "1001"}, {"key": "status", "value": "ACTIVE"}, {"key": "appType", "value": "NATIVE"}, {"key": "priceListId", "value": "64156686ea18a9f9411d6664"}, {"key": "categoryId", "value": "64bbd940d28ff53223dd0b8a"}, {"key": "familyId", "value": "64bbd91fd28ff53223dd0b16"}, {"key": "perPage", "value": "20"}, {"key": "page", "value": "1"}, {"key": "salesPersonUserRoleId", "value": "648d99c0cd63200012aab771"}, {"key": "hideOutOfStock", "value": "true"}, {"key": "branchId", "value": "63c124e6efa1790013071f93,"}, {"key": "productSchemaType", "value": "products_2.0"}, {"key": "isProductSplitting", "value": "true"}]}}, "response": []}, {"name": "Get category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/category/get-category?categoryId=660a840da82261fe2fb610c9", "host": ["{{base_url}}"], "path": ["productService", "category", "get-category"], "query": [{"key": "categoryId", "value": "660a840da82261fe2fb610c9"}, {"key": "categoryType", "value": "FAMILY", "disabled": true}]}}, "response": []}, {"name": "Get category List (With Product Count)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1001,\n    \"priceListId\": \"64156686ea18a9f9411d6664\",\n    \"hideOutOfStock\": true,\n    \"branchId\": \"63c124e6efa1790013071f93\"\n    /* \"familyIds\": [\n        // \"6519fb6cf2c7a7d10eb59ce6\",\n        // \"64bbd91fd28ff53223dd0b16\"\n        // \"64287f6f4fbb196d00b5b9da\"\n    ] */\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category/get-category", "host": ["{{base_url}}"], "path": ["productService", "category", "get-category"]}}, "response": []}, {"name": "Delete category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1114,\r\n    \"type\": \"CATEGORY\",\r\n    \"familyId\": \"6375c5be421cf4001212f453\",\r\n    \"categoryId\": \"638455101d368924e8d67400\",\r\n    \"subCategoryId\": \"638984bb05432f0012932756\",\r\n    \"newFamilyId\": \"6375c5be421cf4001212f453\",\r\n    \"newCategoryId\": \"637743070bbd13001285669a\",\r\n    \"newSubCategoryId\": \"638456061d368924e8d67430\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/category", "host": ["{{base_url}}"], "path": ["productService", "category"]}}, "response": []}]}, {"name": "Configuration", "item": [{"name": "Tax", "item": [{"name": "Add and Edit Tax", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1035,\r\n    \"type\": \"GROUP\",\r\n    \"groupName\": \"AAGST + XXGST\",\r\n    \"taxName\": [\"aaGST\", \"xxGST\"],\r\n    \"taxCalculation\": [\"PERCENTAGE\", \"FLAT_VALUE\"],\r\n    \"taxRate\": [\"15\", \"25\"],\r\n    \"status\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/configuration/tax", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"]}}, "response": []}, {"name": "Get Tax and Taxlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?tenantId=1119&taxId=63ca73364132dfa0be311fe6&status=ALL", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "taxId", "value": "63ca73364132dfa0be311fe6"}, {"key": "groupId", "value": "637e20c1cb50a66e083241ef", "disabled": true}, {"key": "type", "value": "GROUP", "disabled": true}, {"key": "status", "value": "ALL", "description": "ALL/ACTIVE/INACTIVE "}]}}, "response": []}, {"name": "Detele Tax", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?taxId=637e20c1cb50a66e083241ef", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "taxId", "value": "637e20c1cb50a66e083241ef"}]}}, "response": []}, {"name": "Change Tax Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/tax?taxId=637df93b47430c10c44469a0&status=INACTIVE", "host": ["{{base_url}}"], "path": ["productService", "configuration", "tax"], "query": [{"key": "taxId", "value": "637df93b47430c10c44469a0"}, {"key": "status", "value": "INACTIVE", "description": "ACTIVE / INACTIVE"}]}}, "response": []}]}, {"name": "Master tax", "item": [{"name": "Update Master Tax Setting", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1035,\r\n    \"enableTax\": true,\r\n    \"price\": \"INCLUDE\",\r\n    \"defaultTax\": \"637df93b47430c10c44469a0\",\r\n    \"universalTax\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/configuration/master-tax", "host": ["{{base_url}}"], "path": ["productService", "configuration", "master-tax"]}}, "response": []}, {"name": "Get Master Tax Setting", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/configuration/master-tax?tenantId=1035", "host": ["{{base_url}}"], "path": ["productService", "configuration", "master-tax"], "query": [{"key": "tenantId", "value": "1035"}]}}, "response": []}]}]}, {"name": "Datasheet", "item": [{"name": "Export price sheet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dataType\": \"PRICE\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/dataSheet/priceExport", "host": ["{{base_url}}"], "path": ["productService", "dataSheet", "priceExport"]}}, "response": []}, {"name": "Export inventory sheet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dataType\": \"INVENTORY\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/dataSheet/inventoryExport", "host": ["{{base_url}}"], "path": ["productService", "dataSheet", "inventoryExport"]}}, "response": []}]}, {"name": "Deals", "item": [{"name": "Deal", "item": [{"name": "Create Deal", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"priceId\": \"637c687c4f63130012f43d15\",\n    \"dealType\": \"DISCOUNT\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Get Deal list", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/productService/deal?page=1&perPage=20&tenantId=1118", "host": ["{{base_url}}"], "path": ["productService", "deal"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "tenantId", "value": "1118"}, {"key": "search<PERSON>ey", "value": "", "disabled": true}, {"key": "priceId", "value": "63906a750eb78c00125ddbb4", "disabled": true}, {"key": "dealType", "value": "DISCOUNT", "disabled": true}, {"key": "dealStatus", "value": "CANCELLED", "disabled": true}]}}, "response": []}, {"name": "Update Deal status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"status\": \"ARCHIVED\",\n    \"dealIds\": [\n        \"64942faa6e995d0aedebce6a\",\n        \"65d464315a8c82581bf059d6\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Update deal details", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dealId\": \"666c2e449df92ca88716a90d\",\n    \"tenantId\": 1118,\n    \"dealName\": \"Test Final Price\",\n    \"secondaryDealName\": \"Test Final Price Arabic\",\n    \"dealFromDate\": \"2024-06-14T11:49:24.242Z\",\n    \"dealToDate\": \"2024-06-15T18:29:59.595Z\",\n    \"salesPersonId\": [\n        \"63d7cce3099872001245d906\",\n        \"6344059ea2a6e700126d2a54\",\n        \"6555ced41eaff80012da551b\",\n        \"6621394d50e6570012327547\",\n        \"665d65d1ed2eba00127505cf\"\n    ],\n    \"dealSortType\": \"MANUAL\",\n    \"products\": [\n        {\n            \"dealProductId\": \"666c2e679df92ca88716a922\",\n            \"dealType\": \"DISCOUNT\",\n            \"discountType\": \"PERCENT\",\n            \"percent\": 10.2,\n            \"amount\": 1.02,\n            \"discountedPrice\": 560\n        },\n        {\n            \"dealProductId\": \"666c2e679df92ca88716a927\",\n            \"dealType\": \"DISCOUNT\",\n            \"discountType\": \"PERCENT\",\n            \"percent\": 23.12,\n            \"amount\": 4.62,\n            \"discountedPrice\": 50\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}, {"name": "Delete Deals", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealIds\": [\n        \"6459d47d6b3d9da748e65b9e\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal", "host": ["{{base_url}}"], "path": ["productService", "deal"]}}, "response": []}]}, {"name": "Update Product List", "item": [{"name": "Update Product Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealId\": \"64564a0f02f55944c1fa4742\",\n    \"dealProductId\": [\n        \"645a0ccff0f31b9ff6f45c10\",\n        \"645a0ccff0f31b9ff6f45c13\",\n        \"645a0ccff0f31b9ff6f45c16\"\n    ],\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}, {"name": "Update Product Sequence", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealProductId\": \"645a0ccff0f31b9ff6f45c16\",\n    \"dealId\": \"6459d47d6b3d9da748e65b9e\",\n    \"dealProductSequence\": 350\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}, {"name": "Get Next Products List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/updateProductList?dealId=64638c1bbce4f83c05c81276&dealProductSequence=300&nextDealProductCount=3", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"], "query": [{"key": "dealId", "value": "64638c1bbce4f83c05c81276"}, {"key": "dealProductSequence", "value": "300"}, {"key": "nextDealProductCount", "value": "3"}]}}, "response": []}, {"name": "Delete Deal Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"dealId\": \"64564a0f02f55944c1fa4742\",\n    \"dealProductId\": [\n        \"645a0ccff0f31b9ff6f45c16\",\n        \"645a0ccff0f31b9ff6f45c13\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/updateProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "updateProductList"]}}, "response": []}]}, {"name": "Deals Product List", "item": [{"name": "Get Product List by Item Numbers", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"priceId\": \"6374bd481f6d6bdec112ee96\",\n    \"page\": 1,\n    \"perPage\": 20,\n    \"itemNumbers\": [\n        \"6606\",\n        \"132\",\n        \"dfd\",\n        \"Ryan1012\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealsProductList", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealsProductList"]}}, "response": []}, {"name": "Get Running Deals List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/dealsProductList?tenantId=1131&priceId=6374bd481f6d6bdec112ee96", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealsProductList"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "priceId", "value": "6374bd481f6d6bdec112ee96"}, {"key": "salesPersonUserId", "value": "6369eafc87526f00125f5f04", "description": "Optional", "disabled": true}, {"key": "productLimit", "value": "10", "description": "Optional", "disabled": true}, {"key": "dealType", "value": "BULK_PRICING", "description": "Optional. DISCOUNT, BULK_PRICING, BUY_X_AND_GET_Y", "disabled": true}, {"key": "hideOutOfStock", "value": "true", "description": "Optional. Defaults to false", "disabled": true}, {"key": "branchId", "value": "6343f50ca2a6e700126d1808", "description": "Mandatory only if hideOutOfStock is true", "disabled": true}, {"key": "", "value": "", "description": "636a0e0587526f00125f886f, 636906513b25300011587a95", "disabled": true}]}}, "response": []}]}, {"name": "Deal Product", "item": [{"name": "Add product in deal", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"dealId\": \"64638c1bbce4f83c05c81276\",\n    \"products\": [\n        {\n            \"productId\": \"64661c6861324c754acfe9dc\"\n        }\n    ],\n    \"dealFromDate\": \"2023-06-06T00:00:00.000Z\",\n    \"dealToDate\": \"2023-06-10T00:00:00.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealProduct", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealProduct"]}}, "response": []}, {"name": "Get Current Deal Product List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/dealProduct?tenantId=1131&page=1&perPage=100&priceId=6374bd481f6d6bdec112ee96&dealId=66b481ac8f26bf821e6860d1", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealProduct"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "100"}, {"key": "priceId", "value": "6374bd481f6d6bdec112ee96"}, {"key": "dealId", "value": "66b481ac8f26bf821e6860d1"}, {"key": "search<PERSON>ey", "value": "New Single Product", "disabled": true}, {"key": "sortBy", "value": "PRICE", "description": "PRICE / INVENTORY", "disabled": true}, {"key": "sortType", "value": "ASC", "description": "ASC / DESC", "disabled": true}]}}, "response": []}]}, {"name": "Check Valid Items", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1118,\n    \"dealId\": \"6490098d422008c3c00c13f6\",\n    \"priceId\": \"63983f77aa1d4e0011dfa103\",\n    \"dealType\": \"DISCOUNT\",\n    \"itemNumbers\": [\n        \"efawed8\",\n        \"SR\"\n    ],\n    \"dealFromDate\": \"2023-06-19T07:53:48.484Z\",\n    \"dealToDate\": \"2023-06-27T18:29:59.595Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/checkValidItems", "host": ["{{base_url}}"], "path": ["productService", "deal", "checkValidItems"]}}, "response": []}, {"name": "Get Deal Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/detail?dealId=64564a0f02f55944c1fa4742", "host": ["{{base_url}}"], "path": ["productService", "deal", "detail"], "query": [{"key": "dealId", "value": "64564a0f02f55944c1fa4742"}]}}, "response": []}, {"name": "Product list for deal", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/deal/productList?tenantId=1131&page=1&perPage=100&dealFromDate=2024-09-12T08:28:00.000Z&dealToDate=2024-10-18T18:29:59.595Z&priceId=6374bd481f6d6bdec112ee96&dealId=66b481ac8f26bf821e6860d1", "host": ["{{base_url}}"], "path": ["productService", "deal", "productList"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "100"}, {"key": "dealFromDate", "value": "2024-09-12T08:28:00.000Z", "description": "Prod: 2024-09-11T05:36:24.251Z"}, {"key": "dealToDate", "value": "2024-10-18T18:29:59.595Z", "description": "Prod: 2024-09-12T20:59:59.595Z"}, {"key": "priceId", "value": "6374bd481f6d6bdec112ee96", "description": "Prod: 647e49113c92ecdd432390b7"}, {"key": "dealId", "value": "66b481ac8f26bf821e6860d1", "description": "Prod: 66b0c8e40f2ce7ed9e5ff705"}, {"key": "search<PERSON>ey", "value": "Air", "disabled": true}]}}, "response": []}, {"name": "Update Deal Statistics (click/view)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dealId\": \"647f0f4d9a1bed63a46da77f\",\n    \"dealProductId\": \"64807a99b0fa86f117a3664d\",\n    // \"customerUserRoleId\" : \"641b093ef4fac2a95c0e77d3\",\n    \"tenantId\": 1131\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/deal/dealStatistics", "host": ["{{base_url}}"], "path": ["productService", "deal", "dealStatistics"]}}, "response": []}]}, {"name": "Images", "item": [{"name": "Get signature for image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"imageName\": \"dfd_P1.png\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/getUploadSignature", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "getUploadSignature"]}}, "response": [{"name": "Get signature for image", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"imageName\": \"04-14-2023-12_P1.JPEG\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/getUploadSignature", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "getUploadSignature"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Expose-Headers", "value": "refreshed-access-token"}, {"key": "Cache-Control", "value": "no-cache"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "655"}, {"key": "ETag", "value": "W/\"28f-VnJwWRwd4gGh8zh957PNqyNfbQE\""}, {"key": "Date", "value": "Mon, 17 Apr 2023 13:39:07 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"message\": \"upload_url_get_success\",\n    \"data\": {\n        \"signedUrl\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIATGBACB6QFTN5S5EP%2F20230417%2Fme-south-1%2Fs3%2Faws4_request&X-Amz-Date=20230417T133905Z&X-Amz-Expires=300&X-Amz-Signature=f6cee5d21e91e73641429b7f5882b6269dc4692960b384b0bb229fe04570f2d9&X-Amz-SignedHeaders=host\",\n        \"s3Url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG\",\n        \"imageName\": \"64394235bdb2793f615d83c2_P1.JPEG\",\n        \"productVariantId\": \"64394235bdb2793f615d83c2\"\n    }\n}"}]}, {"name": "Add Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"s3Url\": \"https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/643577975989e41e5eeb060e_P1.jpeg\",\n    \"imageSize\": 11140,\n    \"imageName\": \"645b22e0eaa2d77e9058e783_645b22e0eaa2d77e9058e788_P1.jpeg\",\n    \"groupId\": \"645b22e0eaa2d77e9058e788\",\n    \"productVariantId\": \"645b22e0eaa2d77e9058e783\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2", "host": ["{{base_url}}"], "path": ["productService", "imageV2"]}}, "response": []}, {"name": "delete images", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2?tenantId=1004", "host": ["{{base_url}}"], "path": ["productService", "imageV2"], "query": [{"key": "tenantId", "value": "1004"}, {"key": "imageIds[]", "value": "66c6f55393f37cfe4d3f3769", "disabled": true}, {"key": "imageIds[]", "value": "66793e5f2c7c677356eac05a", "disabled": true}, {"key": "imageIds[]", "value": "6679401e2c7c677356f8dcca", "disabled": true}, {"key": "imageIds[]", "value": "6679401e2c7c677356f8dc91", "disabled": true}, {"key": "imageIds[]", "value": "667940352c7c677356fa0823", "disabled": true}, {"key": "imageIds[]", "value": "667940352c7c677356fa081b", "disabled": true}, {"key": "imageIds[]", "value": "6679413d2c7c6773560263de", "disabled": true}, {"key": "imageIds[]", "value": "6679413d2c7c67735602668c", "disabled": true}, {"key": "imageIds[]", "value": "6679414d2c7c67735602daa2", "disabled": true}, {"key": "imageIds[]", "value": "6679414d2c7c67735602da5d", "disabled": true}]}}, "response": []}, {"name": "Swap images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"imageIds\": [\"63ad7afc251da20a3613554d\", \"63ad7afc251da20a36135550\"],\n    \"tenantId\": 1114\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/swapProductImages", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "swapProductImages"]}}, "response": []}, {"name": "Image listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2?tenantId=1118&imageType=NEED_IMAGES&page=1&perPage=10&searchKey=7kg", "host": ["{{base_url}}"], "path": ["productService", "imageV2"], "query": [{"key": "tenantId", "value": "1118"}, {"key": "imageType", "value": "NEED_IMAGES", "description": "ALL / NEW / NEED_IMAGES"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "7kg"}]}}, "response": []}, {"name": "Get image match product list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/imageV2/imagesMatch?linkedTenantId=1119&tenantId=1114&searchKey=testing&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "imagesMatch"], "query": [{"key": "linkedTenantId", "value": "1119"}, {"key": "tenantId", "value": "1114"}, {"key": "search<PERSON>ey", "value": "testing"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "Image match action", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"linkedTenantId\": 1119,\n    \"type\": \"SELECTED\",  // ALL or SELECTED\n    \"product\": [\n        {\n            \"productId\": \"63f5b1be478135a96edd68dd\",\n            \"linkedTenantProductId\": \"63ad1a8d43ff4500122d20c6\"\n        },\n        {\n            \"variantId\": \"63f34b2e53ff820a78b4d090\",\n            \"parentId\": \"63f34b2e53ff820a78b4d098\",\n            \"linkedTenantVariantId\": \"63b02100ecf078002c555491\",\n            \"linkedParentId\": \"642bb8f8a5ac388d302e9e1c\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/imagesMatch", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "imagesMatch"]}}, "response": []}, {"name": "Get Images By Item Numbers", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"itemNumbers\": [\n        \"TRT71662\",\n        \"ABC-abc-5678\",\n        \"78CUFI92\",\n        \"Cr1002\",\n        \"dfd\",\n        \"78CUFI91\",\n        \"TRT71663\",\n        \"ABC-abc-1234\",\n        \"78CUFI93\",\n        // Production\n        \"W14-0683\",\n        \"W14-0956\",\n        \"W14-0957\",\n        \"W14-0958\",\n        \"K11639\",\n        \"K11640\",\n        \"K11641\",\n        \"K11642\",\n        \"K11643\",\n        \"K11644\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/imageV2/imagesByItemNumbers", "host": ["{{base_url}}"], "path": ["productService", "imageV2", "imagesByItemNumbers"]}}, "response": []}]}, {"name": "Master Data", "item": [{"name": "Attribute", "item": [{"name": "Add Attribute", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeName\": \"Bottghle\",\n    \"secondaryLanguageAttributeName\": \"Boghttle\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}, {"name": "Edit Attribute", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63c8eba84b8de14dd4400595\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeName\": \"Helmegt kings\",\n    \"secondaryLanguageAttributeName\": \"Earbuddds\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}, {"name": "Get Attribute List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute?tenantId=1131&status=ALL&searchKey=d&perPage=20&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": "d"}, {"key": "perPage", "value": "20"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Attribute", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"6371d602f4d7553be184b791\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attribute", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attribute"]}}, "response": []}]}, {"name": "Attribute Set", "item": [{"name": "Add Attribute Set", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeSetName\": \"Office Products\",\n    \"secondaryLanguageAttributeSetName\":\"office chiz\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}, {"name": "Edit Attribute Set", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63724673f46f0cb0dc66fef5\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"attributeSetName\": \"Power Tools\",\n    \"secondaryLanguageAttributeSetName\": \"urja samgri tools\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}, {"name": "Get Attribute Set List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet?tenantId=1131&status=ALL&searchKey=gri&perPage=10&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": "gri"}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Attribute Set", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63724673f46f0cb0dc66fef5\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeSet", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeSet"]}}, "response": []}]}, {"name": "Brand", "item": [{"name": "Add Brand", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"brandName\": \"Dell\",\n    \"secondaryLanguageBrandName\": \"del\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}, {"name": "Edit Brand", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"637331be585729637044fb5c\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"brandName\": \"philips\",\n    \"secondaryLanguageBrandName\": \"philips t\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}, {"name": "Get Brand List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand?tenantId=1131&status=ALL&searchKey=&perPage=2&page=2", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "2"}, {"key": "page", "value": "2"}]}}, "response": []}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63724673f46f0cb0dc66fef5\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/brand", "host": ["{{base_url}}"], "path": ["productService", "masterData", "brand"]}}, "response": []}]}, {"name": "Unit", "item": [{"name": "Add Unit", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"unitName\": \"indiUnit\",\n    \"secondaryLanguageUnitName\": \"indi\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}, {"name": "Edit Unit", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"6373522431cd87bd062f72e2\",\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"unitName\": \"KG\",\n    \"secondaryLanguageUnitName\": \"ke giii\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}, {"name": "Get Unit List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit?tenantId=1131&status=ACTIVE&searchKey=&perPage=10&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ACTIVE"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Unit", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"6373522431cd87bd062f72e2\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/unit", "host": ["{{base_url}}"], "path": ["productService", "masterData", "unit"]}}, "response": []}]}, {"name": "Price", "item": [{"name": "Add Price", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isActive\": false,\n    \"tenantId\": 1131,\n    \"priceName\": \"Battery Capacity\",\n    \"secondaryLanguagePriceName\": \"Second Capacity\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}, {"name": "Edit Price", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"63c7ee8bf692a06e910ef5f8\",\n    \"isActive\": \"true\",\n    \"tenantId\": 1131,\n    \"priceName\": \"capacity\",\n    \"secondaryLanguagePriceName\": \"capacity\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}, {"name": "Get Price List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price?tenantId=1131&status=ALL&searchKey=&perPage=200&page=1", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ALL"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "200"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Delete Price", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"63c7f1cae87e65931cc1f34f\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/price", "host": ["{{base_url}}"], "path": ["productService", "masterData", "price"]}}, "response": []}]}, {"name": "Attribute Association", "item": [{"name": "Update Attribute Association", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"attributeSetId\": \"63724673f46f0cb0dc66fef5\",\n    \"attributeIds\": [\"6374a9ed4dfc96001260858c\", \"6371d602f4d7553be184b791\",\"6371de646c6c8e71cec916b3\",\n    \"sdsd\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeAssociation", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeAssociation"]}}, "response": []}, {"name": "Get Attribute Association List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/attributeAssociation?tenantId=1131&status=ACTIVE&searchKey=&perPage=10&page=1&_id=63731436e604224cd8124845", "host": ["{{base_url}}"], "path": ["productService", "masterData", "attributeAssociation"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "status", "value": "ACTIVE"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "perPage", "value": "10"}, {"key": "page", "value": "1"}, {"key": "_id", "value": "63731436e604224cd8124845"}]}}, "response": []}]}, {"name": "Update Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"entity\": \"PRICE\",\n    \"tenantId\": 1131,\n    \"isActive\": false,\n    \"ids\": [\n        \"6373538e31cd87bd062f72ec\",\n        \"6374bf41cc15a0e922008be2\"\n    ]   \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/masterData/updateStatus", "host": ["{{base_url}}"], "path": ["productService", "masterData", "updateStatus"]}}, "response": []}]}, {"name": "Order", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "cartActions", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1118,\n    \"masterPriceId\": \"642525297d7f06d4f591fd61\",\n    \"uomId\": \"6470be241ae26f9afb04b35d\",\n    \"uomName\": \"Inch\",\n    \"quantity\": 100,\n    \"minQty\": 50,\n    \"itemComment\": \"Item Kelvinator Comments\",\n    \"productVariantId\": \"662f50129958d544b8aafc81\",\n    \"salesPersonRoleId\": \"6344059ea2a6e700126d2a54\",\n    \"customerUserRoleId\": \"662138ec50e657001232747f\",\n    \"actionType\": \"ADD_ITEM_TO_CART\",\n    \"tax\": 84.785\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/cart", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"]}}, "response": []}, {"name": "cartDetails", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/cart?tenantId=1119&customerUserRoleId=6501ad7b40d5660012f06593", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "6501ad7b40d5660012f06593"}]}}, "response": []}, {"name": "delet cart items", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/cart?tenantId=1119&customerUserRoleId=63fc90d06085cd0012c03617&cartItemIds=64882324d32a5bf87f82fe98&cartItemIds=64882324d32a5bf87f82fe98", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "cart"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63fc90d06085cd0012c03617"}, {"key": "cartItemIds", "value": "64882324d32a5bf87f82fe98"}, {"key": "cartItemIds", "value": "64882324d32a5bf87f82fe98"}]}}, "response": []}, {"name": "cartItemCount", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/itemCount?tenantId=1119&customerUserRoleId=63b25f79989ff60011a1ff50", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "itemCount"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50"}]}}, "response": []}]}, {"name": "Stats", "item": [{"name": "user role stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/orderStats?userRoles=6371b561f6eba60012a75bc0&tenantId=1114", "host": ["{{base_url}}"], "path": ["productService", "order", "orderStats"], "query": [{"key": "userRoles", "value": "6371b561f6eba60012a75bc0"}, {"key": "tenantId", "value": "1114"}]}}, "response": []}]}, {"name": "Drafts", "item": [{"name": "create draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"customerUserRoleId\": \"63fc90d06085cd0012c03617\",\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerName\": \"MS Dhoni POSTMAN\",\n    \"customerPrimaryContactName\": \"Dhoni POSTMAN\",\n    \"customerId\": \"111910026\",\n    \"externalId\": \"23\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/draft", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draft"]}}, "response": []}, {"name": "draft listing API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/drafts?page=1&perPage=10&tenantId=1119&searchKey=sd", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "drafts"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1119"}, {"key": "search<PERSON>ey", "value": "sd"}]}}, "response": []}, {"name": "draft details api", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/draft?draftId=64882f09a3400ecc1274f33f&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draft"], "query": [{"key": "draftId", "value": "64882f09a3400ecc1274f33f"}, {"key": "tenantId", "value": "1119"}]}}, "response": []}, {"name": "delete drafts api", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/orderV2/drafts?draftIds=646333a8b1802ecac1d522f0&draftIds=648821f68a35d0a692ce321c&tenantId=1119", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "drafts"], "query": [{"key": "draftIds", "value": "646333a8b1802ecac1d522f0"}, {"key": "draftIds", "value": "648821f68a35d0a692ce321c"}, {"key": "tenantId", "value": "1119"}]}}, "response": []}, {"name": "Order from Draft", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"draftId\": \"646252305cd66039ff24e7fd\",\n    \"tenantId\": 1119,\n    \"salesPersonRoleId\": \"6363b6d59a0d2f00129cb973\",\n    \"customerUserRoleId\": \"63b25f79989ff60011a1ff50\",\n    \"customerName\": \"Ketan POSTMAN\",\n    \"customerLegalName\": \"POSTMAN LEGAL NAME\",\n    \"salePersonName\": \"Ketan Sales Person New POSTMAN\",\n    \"branchId\": \"633aaf5585b9b200129a4503\",\n    \"externalId\": \"ewqeqdsa\",\n    \"orderAppType\": \"SALES_APP\",\n    \"orderPunchDeviceType\": \"MOBILE\",\n    \"orderPunchDeviceOs\": \"IOS\",\n    \"orderRemark\": \"Testing remarks\",\n    \"ShippingAddress\": \"Testing Address of the customer or selected address\",\n    \"cityId\": \"631ffc5c89881e0012660b3b\",\n    \"regionId\": \"630c58a6bfb2982b1cbcd4cf\",\n    \"shippingMobileNumber\": 9685635896,\n    \"shippingCountryCode\": \"+91\",\n    \"regionName\": \"Gujarat\",\n    \"cityName\": \"Surat\",\n    \"shippingCoordinates\": {\n        \"lat\": 20.121,\n        \"lng\": 72.00\n    },\n    \"customerPrimaryContactName\": \"Customer firstname and last name POSTMAN\",\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/placeOrderFromDraft", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "placeOrderFromDraft"]}}, "response": []}, {"name": "Draft to cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"draftId\": \"648828a11a76cdf00283115f\",\n    \"tenantId\": 1119,\n    \"customerUserRoleId\": \"63fc90d06085cd0012c03617\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/draftToCart", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "draftToCart"]}}, "response": []}]}, {"name": "Export", "item": [{"name": "Export Orders", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"startDate\": \"2024-05-01\",\n    \"endDate\": \"2024-07-27\",\n    \"timezone\": \"Asia/Riyadh\",\n    \"orderStatusList\": [\n        \"PENDING\",\"RECEIVED\",\"RELEASED\",\"PREPARING\",\"SHIPPED\",\"DELIVERED\",\"CANCELLED\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/exportTask", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "exportTask"]}}, "response": []}]}, {"name": "Orders", "item": [{"name": "Order listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/orders?page=1&perPage=10&tenantId=1004&apiVersion=2&salesPersonRoleId=6414283cc80b530012f43163&orderAppType=SALES_APP", "host": ["{{base_url}}"], "path": ["productService", "order", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "tenantId", "value": "1004"}, {"key": "apiVersion", "value": "2"}, {"key": "branchId", "value": "6333f1fb67e853cb742142a2", "disabled": true}, {"key": "orderStatus", "value": "PENDING", "disabled": true}, {"key": "salesPersonRoleId", "value": "6414283cc80b530012f43163"}, {"key": "customerUserRoleId", "value": "6333f1fb67e853cb7421429d", "disabled": true}, {"key": "orderAppType", "value": "SALES_APP"}, {"key": "duration", "value": "1", "disabled": true}, {"key": "search<PERSON>ey", "value": "500", "disabled": true}, {"key": "orderCountStatusType", "value": "PENDING", "disabled": true}, {"key": "onlyCount", "value": "true", "disabled": true}, {"key": "portalAppTypeFilter", "value": "ADMIN", "disabled": true}]}}, "response": []}]}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n  \"tenantId\": 1241,\n  \"orderIds\": [\n    \"669a73dfc92b57100e2ca3ee\"\n  ],\n  \"orderStatus\": \"DELIVERED\",\n  \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/orders", "host": ["{{base_url}}"], "path": ["productService", "order", "orders"]}}, "response": []}, {"name": "Get order detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order?tenantId=1131&orderId=6667f848daa19f09e04c6730&page=1&perPage=10&apiVersion=2", "host": ["{{base_url}}"], "path": ["productService", "order"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "orderId", "value": "6667f848daa19f09e04c6730"}, {"key": "search<PERSON>ey", "value": "Samsung", "disabled": true}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "orderDetails", "value": "true", "disabled": true}, {"key": "orderItemListingType", "value": "PAGINATION", "description": "PAGINATION, ALL", "disabled": true}, {"key": "apiVersion", "value": "2"}]}}, "response": []}, {"name": "Send Hold Reason Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"orderId\": \"667c244c6be9309e27742304\",\n    \"holdReasonId\": \"658bf22c096cd031d1124abe\",\n    \"messageDetails\": {\n        \"salesPerson\": {\n            \"templateId\": \"7b417d51-63c5-4077-9ca1-4fff1a52b752\",\n            \"language\": \"en\",\n            \"inputs\": {\n                \"order_value\": \"1007\"\n            }\n        },\n        \"customer\": {\n            \"templateId\": \"94ad1db4-44b0-48cf-a495-22b673233683\",\n            \"language\": \"en\",\n            \"inputs\": {}\n        }\n    },\n    \"isReleaseMessage\": false,\n    \"isSendMessage\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/sendReasonMessage", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "sendReasonMessage"]}}, "response": []}, {"name": "Update Customer With Orders", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customerUserRoleId\": \"641b093ef4fac2a95c0e77d3\",\n    \"updateInformation\": {\n        \"shippingMobileNumber\": 9685635896\n    },\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order", "host": ["{{base_url}}"], "path": ["productService", "order"]}}, "response": []}, {"name": "Dashboard summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/order/dashboardSummary?tenantId=1131&branchId=633aaf5585b9b200129a4503&apiVersion=2", "host": ["{{base_url}}"], "path": ["productService", "order", "dashboardSummary"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "branchId", "value": "633aaf5585b9b200129a4503"}, {"key": "apiVersion", "value": "2"}]}}, "response": []}, {"name": "Order Reports", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order/orderReport?tenantId=1131&page=2&perPage=2&startDate=2023-01-01&endDate=2023-10-10&timezone=Asia/Kolkata", "host": ["{{base_url}}"], "path": ["productService", "order", "orderReport"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "page", "value": "2"}, {"key": "perPage", "value": "2"}, {"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-10-10"}, {"key": "timezone", "value": "Asia/Kolkata"}, {"key": "userRoleIds[]", "value": "641b093ef4fac2a95c0e77d3", "disabled": true}, {"key": "userRoleIds[]", "value": "6369eafc87526f00125f5f04", "disabled": true}, {"key": "userRoleIds[]", "value": "6419cfe6bcb0663d44b1d3fc", "disabled": true}]}}, "response": []}, {"name": "Place order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"apiVersion\": 2,\n    \"customerUserRoleId\": \"6621333850e6570012327222\",\n    \"salesPersonRoleId\": \"6369eafc87526f00125f5f04\",\n    \"customerName\": \"<PERSON><PERSON>l Preet\",\n    \"customer_legal_name\": \"Rakul Preet Pvt Ltd\",\n    \"salePersonName\": \"Real Brijesh saleperson Pvt Ltd\",\n    \"branchId\": \"634015e7a2a6e700126c563d\",\n    \"orderPunchDeviceType\": \"MOBILE\",\n    \"orderPunchDeviceOs\": \"ANDROID\",\n    \"orderAppType\": \"TENANT_OWNER\",\n    \"customerPrimaryContactName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"itemComment\": \"Item Kelvinator Comments\",\n    \"orderRemark\": \"Kelvinator remarks\",\n    //-------------------------------------\n    // Shipping fields\n    //-------------------------------------\n    \"ShippingAddress\": \"32A, Nehru Society, Dahegam, Gujarat 382305, India\",\n    \"cityId\": \"64709595e90a2700125dc2ce\",\n    \"regionId\": \"\",\n    \"shippingMobileNumber\": 0,\n    \"shippingCountryCode\": \"\",\n    \"regionName\": \"\",\n    \"cityName\": \"Dahegam\",\n    \"shippingCoordinates\": {\n        \"lat\": 23.0106567315333,\n        \"lng\": 72.************\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/order", "host": ["{{base_url}}"], "path": ["productService", "order"]}}, "response": []}, {"name": "Check order email", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/productService/orderV2/checkOrderUsers?token=63c160e4da461e0012432ee3&orderId=6486e94a5fc430e9cf1e109f", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "checkOrderUsers"], "query": [{"key": "token", "value": "63c160e4da461e0012432ee3"}, {"key": "orderId", "value": "6486e94a5fc430e9cf1e109f"}]}}, "response": []}, {"name": "Order Status List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/status", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "status"]}}, "response": []}, {"name": "Order pre approved", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\" : 1241,\n    \"externalId\": \"C20311\",\n    \"orderId\": \"6639e7d2430be360c93d665f\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/orderV2/checkPreApproved", "host": ["{{base_url}}"], "path": ["productService", "orderV2", "checkPreApproved"]}}, "response": []}]}, {"name": "Product", "item": [{"name": "Product", "item": [{"name": "Add Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1170,\n    \"product\": {\n        \"isActive\": true,\n        \"attributeSet\": \"6522623ff2c7a7d10ec5376f\",\n        \"attributes\": [\n            {\n                \"value\": \"12inch\",\n                \"type\": \"MANU<PERSON>\",\n                \"attribute_id\": \"6522628ff2c7a7d10ec53e04\"\n            },\n            {\n                \"value\": \"500\",\n                \"type\": \"MANUAL\",\n                \"attribute_id\": \"652262bef2c7a7d10ec53f6a\"\n            },\n            {\n                \"value\": \"6\",\n                \"type\": \"MANUAL\",\n                \"attribute_id\": \"652262adf2c7a7d10ec53eac\"\n            }\n        ],\n        \"title\": \"Water Pump Plier\",\n        \"secondaryLanguageTitle\": \"كماشة مضخة مياه\",\n        \"itemNumber\": \"M0861\",\n        \"barcodes\": [],\n        \"description\": \"Water Pump Pliers are versatile hand tools commonly used in plumbing, automotive, and electrical work. They feature adjustable jaws that can grip a variety of sizes and shapes, making them ideal for gripping and turning pipes, fittings, nuts, bolts, and other irregularly shaped objects.\",\n        \"secondaryLanguageDescription\": \"كماشة مضخة المياه هي أدوات يدوية متعددة الاستخدامات شائعة الاستخدام في أعمال السباكة والسيارات والكهرباء. تتميز بفك قابل للتعديل يمكنه الإمساك بمجموعة متنوعة من الأحجام والأشكال، مما يجعلها مثالية للإمساك بالأنابيب والتجهيزات والصواميل والمسامير وغيرها من الأشياء غير المنتظمة.\",\n        \"type\": \"PARENT\",\n        \"brand\": \"6415665dea18a9f9411d6652\",\n        \"family\": \"6419a2a75cfb741a6f7080da\",\n        \"category\": \"6419d12b5cfb741a6f70a630\",\n        \"subCategory\": \"6419d18a5cfb741a6f70a72b\",\n        \"tags\": [\n            \"Pump\",\n            \"Pliers\"\n        ],\n        \"variants\": {\n            \"type\": \"Size\",\n            \"values\": [\n                \"8inch\",\n                \"12inch\"\n            ]\n        },\n        \"taxId\": \"6415656bea18a9f9411d653d\",\n        \"groups\": {\n            \"type\": \"Color\",\n            \"values\": [\n                \"Red\",\n                \"Black\"\n            ]\n        }\n    },\n    \"variantProducts\": [\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"\\tK12102\",\n            \"uomMapping\": {\n                \"uom\": \"6415659cea18a9f9411d65ee\",\n                \"qtyCtn\": \"12\",\n                \"minQty\": \"12\"\n            },\n            \"priceMapping\": [\n                {\n                    \"price\": 51.15,\n                    \"master_price_id\": \"671b7806f2a0800aeb6418ef\"\n                },\n                {\n                    \"price\": 51.515,\n                    \"master_price_id\": \"64156686ea18a9f9411d6664\"\n                },\n                {\n                    \"price\": 155.515,\n                    \"master_price_id\": \"647e49113c92ecdd432390b7\"\n                },\n                {\n                    \"price\": 79.581,\n                    \"master_price_id\": \"64bbacf7d28ff53223dc6fc3\"\n                },\n                {\n                    \"price\": 2843.181,\n                    \"master_price_id\": \"64bbad34d28ff53223dc6ffd\"\n                },\n                {\n                    \"price\": 283.1581,\n                    \"master_price_id\": \"64bbad57d28ff53223dc7048\"\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"63c124e6efa1790013071f95\",\n                    \"branch_id\": \"671b72dadf056899fc2f479b\",\n                    \"quantity\": 1000\n                }\n            ],\n            \"barcodes\": [],\n            \"groupName\": \"Red\",\n            \"variantName\": \"8inch\"\n        },\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"\\tK12103\",\n            \"uomMapping\": {\n                \"uom\": \"6415659cea18a9f9411d65ee\",\n                \"qtyCtn\": \"6\",\n                \"minQty\": \"6\"\n            },\n            \"priceMapping\": [\n                {\n                    \"price\": 246,\n                    \"master_price_id\": \"671b7806f2a0800aeb6418ef\"\n                },\n                {\n                    \"price\": 51.518,\n                    \"master_price_id\": \"64156686ea18a9f9411d6664\"\n                },\n                {\n                    \"price\": 25.051,\n                    \"master_price_id\": \"647e49113c92ecdd432390b7\"\n                },\n                {\n                    \"price\": 125.51,\n                    \"master_price_id\": \"64bbacf7d28ff53223dc6fc3\"\n                },\n                {\n                    \"price\": 2181.515,\n                    \"master_price_id\": \"64bbad34d28ff53223dc6ffd\"\n                },\n                {\n                    \"price\": 2518.518,\n                    \"master_price_id\": \"64bbad57d28ff53223dc7048\"\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"63c124e6efa1790013071f95\",\n                    \"branch_id\": \"671b72dadf056899fc2f479b\",\n                    \"quantity\": 1800\n                }\n            ],\n            \"barcodes\": [],\n            \"groupName\": \"Red\",\n            \"variantName\": \"12inch\"\n        },\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"\\tK12104\",\n            \"uomMapping\": {\n                \"uom\": \"6415659cea18a9f9411d65ee\",\n                \"qtyCtn\": \"12\",\n                \"minQty\": \"12\"\n            },\n            \"priceMapping\": [\n                {\n                    \"price\": 151,\n                    \"master_price_id\": \"671b7806f2a0800aeb6418ef\"\n                },\n                {\n                    \"price\": 984.1,\n                    \"master_price_id\": \"64156686ea18a9f9411d6664\"\n                },\n                {\n                    \"price\": 1813.051,\n                    \"master_price_id\": \"647e49113c92ecdd432390b7\"\n                },\n                {\n                    \"price\": 125.158,\n                    \"master_price_id\": \"64bbacf7d28ff53223dc6fc3\"\n                },\n                {\n                    \"price\": 789.51,\n                    \"master_price_id\": \"64bbad34d28ff53223dc6ffd\"\n                },\n                {\n                    \"price\": 78,\n                    \"master_price_id\": \"64bbad57d28ff53223dc7048\"\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"63c124e6efa1790013071f95\",\n                    \"branch_id\": \"671b72dadf056899fc2f479b\",\n                    \"quantity\": 500\n                }\n            ],\n            \"barcodes\": [],\n            \"groupName\": \"Black\",\n            \"variantName\": \"8inch\"\n        },\n        {\n            \"isActive\": true,\n            \"itemNumber\": \"\\tK12105\",\n            \"uomMapping\": {\n                \"uom\": \"6415659cea18a9f9411d65ee\",\n                \"qtyCtn\": \"6\",\n                \"minQty\": \"6\"\n            },\n            \"priceMapping\": [\n                {\n                    \"price\": 25.51,\n                    \"master_price_id\": \"671b7806f2a0800aeb6418ef\"\n                },\n                {\n                    \"price\": 81.51,\n                    \"master_price_id\": \"64156686ea18a9f9411d6664\"\n                },\n                {\n                    \"price\": 51351.5,\n                    \"master_price_id\": \"647e49113c92ecdd432390b7\"\n                },\n                {\n                    \"price\": 862.515,\n                    \"master_price_id\": \"64bbacf7d28ff53223dc6fc3\"\n                },\n                {\n                    \"price\": 219.515,\n                    \"master_price_id\": \"64bbad34d28ff53223dc6ffd\"\n                },\n                {\n                    \"price\": 152,\n                    \"master_price_id\": \"64bbad57d28ff53223dc7048\"\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"warehouse_id\": \"63c124e6efa1790013071f95\",\n                    \"branch_id\": \"671b72dadf056899fc2f479b\",\n                    \"quantity\": 784\n                }\n            ],\n            \"barcodes\": [],\n            \"groupName\": \"Black\",\n            \"variantName\": \"12inch\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2", "host": ["{{base_url}}"], "path": ["productService", "productV2"]}}, "response": []}, {"name": "Product details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2?tenantId=1241&productId=6572b154ac7f5859a950e729", "host": ["{{base_url}}"], "path": ["productService", "productV2"], "query": [{"key": "tenantId", "value": "1241"}, {"key": "productId", "value": "6572b154ac7f5859a950e729"}, {"key": "activeVariants", "value": "true", "disabled": true}, {"key": "customerUserRoleId", "value": "63b25f79989ff60011a1ff50", "disabled": true}]}}, "response": []}, {"name": "Edit Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"product\": {\n        \"productId\": \"6572fd64ac7f5859a950f7ed\",\n        \"barcodes\": [],\n        \"isActive\": true,\n        \"title\": \"Tata Nexon\",\n        \"secondaryLanguageTitle\": \"Tata Nexon\",\n        \"itemNumber\": \"TataNexon\",\n        \"description\": \"The Tata Nexon has 1 Diesel Engine and 1 Petrol Engine on offer. The Diesel engine is 1497 cc while the Petrol engine is 1199 cc . It is available with Manual & Automatic transmission.Depending upon the variant and fuel type the Nexon has a mileage of 17.01 to 24.08 kmpl & Ground clearance of Nexon is 208. The Nexon is a 5 seater 4 cylinder car and has length of 3995, width of 1804 and a wheelbase of 2498.\",\n        \"secondaryLanguageDescription\": \"The Tata Nexon has 1 Diesel Engine and 1 Petrol Engine on offer. The Diesel engine is 1497 cc while the Petrol engine is 1199 cc . It is available with Manual & Automatic transmission.Depending upon the variant and fuel type the Nexon has a mileage of 17.01 to 24.08 kmpl & Ground clearance of Nexon is 208. The Nexon is a 5 seater 4 cylinder car and has length of 3995, width of 1804 and a wheelbase of 2498.\",\n        \"type\": \"PARENT\",\n        \"attributes\": [],\n        \"brand\": \"65700bbb5e1daf926bbfe7e6\",\n        \"family\": \"6572fb57ac7f5859a950f6cc\",\n        \"tags\": []\n    },\n    \"variantProducts\": [\n        {\n            \"variantId\": \"6572fd64ac7f5859a950f7ed_6572fd64ac7f5859a950f7f2_6572fd64ac7f5859a950f7f4\",\n            \"variantName\": \"Glossy Redd\",\n            \"isActive\": true,\n            \"itemNumber\": \"Nexon Smart Red\",\n            \"uomMapping\": {\n                \"uom\": \"6572f99fac7f5859a950f511\",\n                \"qtyCtn\": 1,\n                \"minQty\": 1\n            },\n            \"priceMapping\": [\n                {\n                    \"master_price_id\": \"65263d3ed5e2ed3184c7b894\",\n                    \"price\": 0\n                },\n                {\n                    \"master_price_id\": \"65700b7c5e1daf926bbfe797\",\n                    \"price\": 0\n                },\n                {\n                    \"master_price_id\": \"65700b895e1daf926bbfe79e\",\n                    \"price\": 0\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6512bb311b4b3b0012c4e2de\",\n                    \"warehouse_id\": \"6512bb311b4b3b0012c4e2e0\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"65263babe018db0012285bc0\",\n                    \"warehouse_id\": \"65263babe018db0012285bc2\",\n                    \"quantity\": 100\n                },\n                {\n                    \"branch_id\": \"65700af9a7d38b001216368d\",\n                    \"warehouse_id\": \"65700af9a7d38b001216368f\",\n                    \"quantity\": 2\n                },\n                {\n                    \"branch_id\": \"65700b01a7d38b00121636b0\",\n                    \"warehouse_id\": \"65700b01a7d38b00121636b2\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"6662c24971ad7700126c7e92\",\n                    \"warehouse_id\": \"6662c24971ad7700126c7e94\",\n                    \"quantity\": 0\n                }\n            ],\n            \"barcodes\": [\n                \"21212\",\n                \"45678\"\n            ],\n            \"groupName\": \"Smartt work\"\n        },\n        {\n            \"variantId\": \"6572fd64ac7f5859a950f7ed_6572fd64ac7f5859a950f7f3_6572fd64ac7f5859a950f7f4\",\n            \"variantName\": \"Whit\",\n            \"isActive\": true,\n            \"itemNumber\": \"Nexon Smart White\",\n            \"uomMapping\": {\n                \"uom\": \"6572f99fac7f5859a950f511\",\n                \"qtyCtn\": 1,\n                \"minQty\": 1\n            },\n            \"priceMapping\": [\n                {\n                    \"master_price_id\": \"65263d3ed5e2ed3184c7b894\",\n                    \"price\": 800000\n                },\n                {\n                    \"master_price_id\": \"65700b7c5e1daf926bbfe797\",\n                    \"price\": 900000\n                },\n                {\n                    \"master_price_id\": \"65700b895e1daf926bbfe79e\",\n                    \"price\": 750000\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6512bb311b4b3b0012c4e2de\",\n                    \"warehouse_id\": \"6512bb311b4b3b0012c4e2e0\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"65263babe018db0012285bc0\",\n                    \"warehouse_id\": \"65263babe018db0012285bc2\",\n                    \"quantity\": 100\n                },\n                {\n                    \"branch_id\": \"65700af9a7d38b001216368d\",\n                    \"warehouse_id\": \"65700af9a7d38b001216368f\",\n                    \"quantity\": 2\n                },\n                {\n                    \"branch_id\": \"65700b01a7d38b00121636b0\",\n                    \"warehouse_id\": \"65700b01a7d38b00121636b2\",\n                    \"quantity\": 5\n                },\n                {\n                    \"branch_id\": \"6662c24971ad7700126c7e92\",\n                    \"warehouse_id\": \"6662c24971ad7700126c7e94\",\n                    \"quantity\": 0\n                }\n            ],\n            \"barcodes\": [\n                \"45454\"\n            ],\n            \"groupName\": \"Smartt work\"\n        },\n        {\n            \"variantId\": \"6572fd64ac7f5859a950f7ed_6572fd64ac7f5859a950f7f2_6572fd64ac7f5859a950f7f5\",\n            \"variantName\": \"Glossy Redd\",\n            \"isActive\": true,\n            \"itemNumber\": \"Nexon Pure Red\",\n            \"uomMapping\": {\n                \"uom\": \"6572f99fac7f5859a950f511\",\n                \"qtyCtn\": 1,\n                \"minQty\": 1\n            },\n            \"priceMapping\": [\n                {\n                    \"master_price_id\": \"65263d3ed5e2ed3184c7b894\",\n                    \"price\": 800000\n                },\n                {\n                    \"master_price_id\": \"65700b7c5e1daf926bbfe797\",\n                    \"price\": 900000\n                },\n                {\n                    \"master_price_id\": \"65700b895e1daf926bbfe79e\",\n                    \"price\": 750000\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6512bb311b4b3b0012c4e2de\",\n                    \"warehouse_id\": \"6512bb311b4b3b0012c4e2e0\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"65263babe018db0012285bc0\",\n                    \"warehouse_id\": \"65263babe018db0012285bc2\",\n                    \"quantity\": 120\n                },\n                {\n                    \"branch_id\": \"65700af9a7d38b001216368d\",\n                    \"warehouse_id\": \"65700af9a7d38b001216368f\",\n                    \"quantity\": 2\n                },\n                {\n                    \"branch_id\": \"65700b01a7d38b00121636b0\",\n                    \"warehouse_id\": \"65700b01a7d38b00121636b2\",\n                    \"quantity\": 15\n                },\n                {\n                    \"branch_id\": \"6662c24971ad7700126c7e92\",\n                    \"warehouse_id\": \"6662c24971ad7700126c7e94\",\n                    \"quantity\": 0\n                }\n            ],\n            \"barcodes\": [\n                \"21212\"\n            ],\n            \"groupName\": \"Pure\"\n        },\n        {\n            \"variantId\": \"6572fd64ac7f5859a950f7ed_6572fd64ac7f5859a950f7f3_6572fd64ac7f5859a950f7f5\",\n            \"variantName\": \"Whit\",\n            \"isActive\": true,\n            \"itemNumber\": \"Nexon Pure White\",\n            \"uomMapping\": {\n                \"uom\": \"6572f99fac7f5859a950f511\",\n                \"qtyCtn\": 1,\n                \"minQty\": 1\n            },\n            \"priceMapping\": [\n                {\n                    \"master_price_id\": \"65263d3ed5e2ed3184c7b894\",\n                    \"price\": 800000\n                },\n                {\n                    \"master_price_id\": \"65700b7c5e1daf926bbfe797\",\n                    \"price\": 900000\n                },\n                {\n                    \"master_price_id\": \"65700b895e1daf926bbfe79e\",\n                    \"price\": 750000\n                }\n            ],\n            \"inventoryMapping\": [\n                {\n                    \"branch_id\": \"6512bb311b4b3b0012c4e2de\",\n                    \"warehouse_id\": \"6512bb311b4b3b0012c4e2e0\",\n                    \"quantity\": 10\n                },\n                {\n                    \"branch_id\": \"65263babe018db0012285bc0\",\n                    \"warehouse_id\": \"65263babe018db0012285bc2\",\n                    \"quantity\": 150\n                },\n                {\n                    \"branch_id\": \"65700af9a7d38b001216368d\",\n                    \"warehouse_id\": \"65700af9a7d38b001216368f\",\n                    \"quantity\": 4\n                },\n                {\n                    \"branch_id\": \"65700b01a7d38b00121636b0\",\n                    \"warehouse_id\": \"65700b01a7d38b00121636b2\",\n                    \"quantity\": 20\n                },\n                {\n                    \"branch_id\": \"6662c24971ad7700126c7e92\",\n                    \"warehouse_id\": \"6662c24971ad7700126c7e94\",\n                    \"quantity\": 0\n                }\n            ],\n            \"barcodes\": [\n                \"22121\"\n            ],\n            \"groupName\": \"Pure\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2", "host": ["{{base_url}}"], "path": ["productService", "productV2"]}}, "response": []}]}, {"name": "Products", "item": [{"name": "Delete Products", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "url": {"raw": "{{base_url}}/productService/productV2/products?tenantId=1170", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"], "query": [{"key": "tenantId", "value": "1170"}, {"key": "productVariantIds[]", "value": "6781524846a168e98d2d1473", "disabled": true}, {"key": "productVariantIds[]", "value": "6645b3b6abe49ad250e0bcb0", "disabled": true}, {"key": "productVariantIds[]", "value": "6645b3b6abe49ad250e0bcb1", "disabled": true}, {"key": "productVariantIds[]", "value": "66436266589c7f8d6cbc3745", "disabled": true}, {"key": "productVariantIds[]", "value": "66436266589c7f8d6cbc374d", "disabled": true}, {"key": "productVariantIds[]", "value": "66436266589c7f8d6cbc374c", "disabled": true}]}}, "response": []}, {"name": "Update product status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1118,\n    // \"singleProductIds\": [\n    //     \"646339e7b1802ecac1d523d3\"\n    // ],\n    \"variantProductIds\": [\n        {\n            \"parentId\": \"6645b3b6abe49ad250e0bca9\",\n            \"updateIds\": [\n                \"6645b3b6abe49ad250e0bcb0\",\n                \"6645b3b6abe49ad250e0bcb1\"\n            ]\n        },\n        {\n            \"parentId\": \"66436266589c7f8d6cbc3745\",\n            \"updateIds\": [\n                \"66436266589c7f8d6cbc374d\",\n                \"66436266589c7f8d6cbc374c\"\n            ]\n        }\n    ],\n    \"type\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/products", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"]}}, "response": []}, {"name": "Product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/products?tenantId=1118&page=1&perPage=10&searchKey=K10968&withVariants=true&type=INACTIVE", "host": ["{{base_url}}"], "path": ["productService", "productV2", "products"], "query": [{"key": "tenantId", "value": "1118"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "K10968"}, {"key": "withV<PERSON>ts", "value": "true"}, {"key": "type", "value": "INACTIVE", "description": "ACTIVE  /  INACTIVE"}, {"key": "sortBy", "value": "item_number", "description": "item_number, title", "disabled": true}, {"key": "sortType", "value": "ASC", "description": "ASC/DESC", "disabled": true}]}}, "response": []}]}, {"name": "Favorite products", "item": [{"name": "Favorite/Unfavorite products", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1118,\n    \"ids\": [\n        \"6645b3b6abe49ad250e0bca9\",\n        \"66436266589c7f8d6cbc3745\"\n    ],\n    \"type\": \"FAVORITE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/favoriteProduct", "host": ["{{base_url}}"], "path": ["productService", "productV2", "favoriteProduct"]}}, "response": []}, {"name": "Favorite product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/favoriteProduct?tenantId=1119&perPage=100", "host": ["{{base_url}}"], "path": ["productService", "productV2", "favoriteProduct"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "perPage", "value": "100"}, {"key": "favoriteProductId", "value": "645a1e9c18b3f8dbd3315d72", "disabled": true}, {"key": "branchId", "value": "634015e7a2a6e700126c563d", "disabled": true}, {"key": "hideOutOfStock", "value": "true", "disabled": true}]}}, "response": []}]}, {"name": "Products List By ( Auto complete / Search / Filter( New ) )", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/search?tenantId=1241&page=1&perPage=100&searchType=SEARCH&searchKey=451&priceListId=65700b7c5e1daf926bbfe797", "host": ["{{base_url}}"], "path": ["productService", "productV2", "search"], "query": [{"key": "tenantId", "value": "1241"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "100"}, {"key": "searchType", "value": "SEARCH", "description": "AUTO_COMPLETE  SEARCH  FILTER"}, {"key": "search<PERSON>ey", "value": "451"}, {"key": "priceListId", "value": "65700b7c5e1daf926bbfe797"}, {"key": "isPrimaryLanguage", "value": "true", "disabled": true}, {"key": "filters", "value": "%7B%22productType%22%3A%5B%22RESTOCKED_PRODUCTS%22%5D%7D", "disabled": true}, {"key": "hideOutOfStock", "value": "true", "disabled": true}, {"key": "branchId", "value": "63c124e6efa1790013071f93", "disabled": true}, {"key": "salesPersonUserRoleId", "value": "648d99c0cd63200012aab771", "disabled": true}, {"key": "isProductSplitting", "value": "true", "description": "Is temporary falg, will be removed soon", "disabled": true}]}}, "response": []}, {"name": "Check existing item number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/existItemNumber?itemNumber=Y01&tenantId=1113", "host": ["{{base_url}}"], "path": ["productService", "productV2", "existItemNumber"], "query": [{"key": "itemNumber", "value": "Y01"}, {"key": "tenantId", "value": "1113"}]}}, "response": []}, {"name": "Reward product listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "url": {"raw": "{{base_url}}/productService/productV2/rewardProductList?tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["productService", "productV2", "rewardProductList"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "Inventory listing API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/inventoryProductList?tenantId=1118&page=1&perPage=100&searchKey=SRed&branchId=633bfcc0d80b7f00129952ec", "host": ["{{base_url}}"], "path": ["productService", "productV2", "inventoryProductList"], "query": [{"key": "tenantId", "value": "1118"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "100"}, {"key": "search<PERSON>ey", "value": "SRed"}, {"key": "priceId", "value": "63906a650eb78c00125ddbac", "disabled": true}, {"key": "branchId", "value": "633bfcc0d80b7f00129952ec"}, {"key": "sortType", "value": "INVENTORY", "disabled": true}, {"key": "sortBy", "value": "-1", "disabled": true}, {"key": "status", "value": "ACTIVE", "disabled": true}, {"key": "quantityRange", "value": "{\"from\":\"5\",\"to\":\"100\"}", "disabled": true}]}}, "response": []}, {"name": "Update variant group of Parent", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1170,\n    \"productId\": \"677d397fffcaecfd239ecae1\",\n    \"variants\": [\"10inch\",\"4inch\", \"5inch\"]\n    // \"groups\": [\"Chi<PERSON>on\", \" Wool \",\" Polyester \"],\n    // \"groupType\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/updateVariantGroupInfo", "host": ["{{base_url}}"], "path": ["productService", "productV2", "updateVariantGroupInfo"]}}, "response": []}, {"name": "Change Variant Order", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1119,\n    \"productVariantIds\": [\"643f8ab895804bc337558bfd\", \"643f8ab495804bc337558bfc\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/changeVariantOrder", "host": ["{{base_url}}"], "path": ["productService", "productV2", "changeVariantOrder"]}}, "response": []}, {"name": "Update Variant Type", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"productId\": \"6572b154ac7f5859a950e729\",\n    \"variantTypeId\": \"6572b154ac7f5859a950e72d\",\n    \"name\": \"Dark Black\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/variantType", "host": ["{{base_url}}"], "path": ["productService", "productV2", "variantType"]}}, "response": []}, {"name": "Product Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/productCount?tenantId=1114&statusType=ACTIVE", "host": ["{{base_url}}"], "path": ["productService", "productV2", "productCount"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "statusType", "value": "ACTIVE"}]}}, "response": []}, {"name": "Barcode Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/barcodeDetails?tenantId=1114&barcode=89897654", "host": ["{{base_url}}"], "path": ["productService", "productV2", "barcodeDetails"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "barcode", "value": "89897654"}]}}, "response": []}, {"name": "Tag list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/productService/productV2/tags?tenantId=1004&perPage=10", "host": ["{{base_url}}"], "path": ["productService", "productV2", "tags"], "query": [{"key": "tenantId", "value": "1004"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "Change Product Type", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"typeChangeTo\": \"SINGLE\",\n    \"productVariantId\": \"65f99e6a5a8c82581bf15369\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/productV2/changeProductType", "host": ["{{base_url}}"], "path": ["productService", "productV2", "changeProductType"]}}, "response": []}]}, {"name": "Reports", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reportType\": \"ORDER_DETAIL\",\n    \"reportData\": {\n        \"tenantId\": 1131,\n        \"orderId\": \"6667f848daa19f09e04c6730\",\n        \"timezone\": \"Asia/Kolkata\",\n        \"currency\": \"INR\",\n        \"apiVersion\": 2\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/productService/report/", "host": ["{{base_url}}"], "path": ["productService", "report", ""]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://rukx2brvl9.execute-api.me-south-1.amazonaws.com/development"}, {"key": "local_base_url", "value": "localhost:3510", "type": "string", "disabled": true}, {"key": "production_base_url", "value": "https://hxfvnwimb6.execute-api.me-south-1.amazonaws.com/production", "type": "string", "disabled": true}, {"key": "staging_base_url", "value": "https://jxdyvef8c6.execute-api.me-south-1.amazonaws.com/staging", "type": "string", "disabled": true}]}
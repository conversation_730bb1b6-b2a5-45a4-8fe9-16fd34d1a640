const sgMail = require("@sendgrid/mail")

sgMail.setApiKey(process.env.SEND_GRID_API_KEY)

sendEmail = async (payload) => {
    const {
        from = "<EMAIL>",
        to,
        cc = [],
        subject,
        data,
    } = payload

    try {
        await sgMail.send({
            from, // for otp verification and <NAME_EMAIL> & for order notification, <NAME_EMAIL>
            // from: "<EMAIL>",
            to,
            cc,
            subject,
            html: data["html"]
        })
    }
    catch (error) {
        logger.error(error, {
            errorMessage: "sendEmail ~ error",
            errors: error.response?.body?.errors
        })
    }
}

module.exports = {
    sendEmail,
}

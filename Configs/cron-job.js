const CronJob = require('cron').CronJob;

const OrderReportController = new (require('../Controllers/OrderReportController'))()
const OrderController = new (require('../Controllers/OrderController'))()
const OrderExportController = new (require('../Controllers/OrderExportController'))()
const NotificationController = new (require('../Controllers/NotificationController'))()
const ProductCronJobs = new (require('../Controllers/CronJobs/ProductCronJobs'))()
const DeletedImageService = require('../Services/images/DeletedImageService')
const DeletedMaterializedProductService = require('../Services/materialized_product/DeletedMaterializedProductService')

const {
    VALUES
} = require('./constants');

/*
CronJob star explanation:
    * * * * * *
    | | | | | |
    | | | | | day of week
    | | | | month
    | | | day of month
    | | hour
    | minute
    second ( optional )
*/

/**
 * @description CRON job to generate sales person's report.
 */

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in server only.
    new CronJob({
        cronTime: "0 3 * * *", // Run cron job everyday at 03:00:00
        onTick: OrderReportController.orderStatistics,
        onComplete: function () {
            logger.info("COMPLETED: sales person's order job \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in server only.
    new CronJob({
        cronTime: "0 0 * * *", // Run cron job everyday at 00:00:00
        onTick: OrderController.setAutoEnableCatalogModeForCustomer,
        onComplete: function () {
            logger.info("COMPLETED: Auto enable catalog mode for customer \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 * * * *", // Run cron job after every hours at 00:00
        onTick: NotificationController.sentNotificationsForNewProducts,
        onComplete: function () {
            console.log("\n COMPLETED: Sent notifications for New Product\n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 3 * * *", // Run cron job everyday at 03:00:00
        onTick: OrderExportController.deleteOldOrderExportTasks,
        onComplete: function () {
            logger.info("COMPLETED: Delete old order exports entries\n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only. 
    new CronJob({
        cronTime: "0 3 * * *", // Run cron job everyday at 03:00:00
        onTick: ProductCronJobs.syncRestockedItems,
        onComplete: function () {
            logger.info("COMPLETED: Sync restocked items\n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only. 
    new CronJob({
        cronTime: "0 4 * * *", // Run cron job everyday at 04:00:00
        onTick: DeletedImageService.removeDeletedImages,
        onComplete: function () {
            logger.info("COMPLETED: Remove deleted images\n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only. 
    new CronJob({
        cronTime: "15 4 * * *", // Run cron job everyday at 04:15:00
        onTick: DeletedMaterializedProductService.removeDeletedMaterializedProducts,
        onComplete: function () {
            logger.info("COMPLETED: Remove deleted materialized products\n")
        },
        start: true,
        timeZone: "UTC",
    })
}

const fs = require('fs');
const path = require('path');
const stream = require("stream");

const AWS = require('aws-sdk');

const {
    MEDIA_TYPE,
    BUCKET_TYPE,
    VALUES,
    STATUS_CODES,
} = require('./../Configs/constants');

//FILE FILTER
const imagesExt = ["jpg", "jpeg", "png"]
const videosExt = ["mp4", "avi"]

var self
//GET AWS CONFIG
//Note: First of all, you have to create a bucket on AWS with proper permissions.
const credentials = {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
    Bucket: process.env.AWS_BUCKET_NAME,
    region: process.env.AWS_REGION,
    signatureVersion: 'v4'
}

//S3 CONFIG
let s3Config;
class S3Upload {

    constructor(bucketType = BUCKET_TYPE.PRIVATE) {
        self = this;
        if (bucketType === BUCKET_TYPE.PUBLIC) {
            credentials["Bucket"] = process.env.AWS_PUBLIC_BUCKET_NAME;
        }
        s3Config = new AWS.S3(credentials)
    }

    //UPLOAD FILES
    uploadFiles(pathName, fileNames, localFilePath, metadata) {
        try {
            //Create params and promise Array
            let promiseArray = [];

            if (Array.isArray(fileNames)) {
                fileNames.forEach(async element => {

                    promiseArray.push(self.promisifyFile(element, pathName, localFilePath, metadata))
                });
            }
            else {
                promiseArray.push(self.promisifyFile(fileNames, pathName, localFilePath, metadata))
            }

            //Upload to S3
            return Promise.all(promiseArray).then(result => {
                return {
                    status: STATUS_CODES.SUCCESS,
                    data: result
                }
            }).catch(err => {
                return {
                    status: STATUS_CODES.SERVER_ERROR,
                    error: err
                }
            })

        } catch (error) {
            logger.error(error)
        }
    }

    promisifyFile(fileName, pathName, localFilePath, metadata = {}) {
        return new Promise((resolve, reject) => {
            let directoryPath = './../Assets/Images/Temp/Original/'
            const getFileFromLocal = localFilePath || path.join(__dirname, directoryPath + fileName)
            const filePathName = pathName
            const params = {
                Bucket: s3Config.config.Bucket,
                Body: fs.createReadStream(getFileFromLocal),
                Key: (filePathName === '/' || filePathName === '') ? fileName : filePathName + '/' + fileName,
                ContentType: path.extname(getFileFromLocal),
            };

            if (metadata["CacheControl"] !== undefined) {
                params["CacheControl"] = metadata["CacheControl"]
            }
            s3Config.upload(params, async function (err, data) {
                if (err) {
                    reject(err)
                }

                //success
                if (data) {
                    //Remove file from local
                    let ratio = 0;
                    let ext = fileName.split("."), mediaType = '';
                    if (imagesExt.indexOf(ext[ext.length - 1]) >= 0)
                        mediaType = MEDIA_TYPE.IMAGE;
                    else if (videosExt.indexOf(ext[ext.length - 1]) >= 0) {
                        mediaType = MEDIA_TYPE.VIDEO;
                    }
                    fs.unlinkSync(getFileFromLocal)

                    resolve({
                        fileName: fileName,
                        location: data.Location,
                        mediaType, ratio
                    })
                }
            })
        })
    }

    //GET SIGNED URL
    async getSignedUrl(pathName, fileName) {
        const Key =
            ["", "/"].includes(pathName)
                ? fileName
                : pathName + '/' + fileName
        const Expires = process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 || 60

        const s3Params = {
            Bucket: s3Config.config.Bucket,
            Key,
            Expires,  // time in seconds: e.g. 5 * 60 = 5 mins
        }
        const signedUrl = await s3Config.getSignedUrl('putObject', s3Params)
        return signedUrl
    }

    //GET MULTIPLE SIGNED URLS
    getMultipleSignedUrls(pathName, fileNames) {
        let promiseArray = []
        fileNames.forEach(element => {
            let createPromise = new Promise((resolve, reject) => {
                let url = s3Config.getSignedUrl('getObject', {
                    Bucket: process.env.AWS_BUCKET_NAME,
                    Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + element,
                    Expires: process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 || 60 // time in seconds: e.g. 60 * 5 = 5 mins
                    // AWS_SIGNED_URL_EXPIRE_TIME = 60, Expiry time will be 1 hour
                })
                if (url)
                    resolve(url)
                else
                    reject()
            })
            promiseArray.push(createPromise)
        })

        return Promise.all(promiseArray).then(result => {
            return {
                status: STATUS_CODES.SUCCESS,
                data: result
            }
        }).catch(err => {
            return {
                status: STATUS_CODES.SERVER_ERROR,
                error: err
            }
        })

    }

    //DELETE SINGLE FILE
    deleteFile(pathName, fileName) {

        let params = {
            Bucket: s3Config.config.Bucket,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
        }

        return new Promise((resolve, reject) => {

            s3Config.deleteObject(params, function (err, data) {
                if (err) {
                    reject({
                        status: STATUS_CODES.SERVER_ERROR,
                        error: err
                    })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })


    }

    //DELETE MULTIPLE FILES
    deleteFiles(deleteObjects) {
        const params = {
            Bucket: s3Config.config.Bucket,
            Delete: { // required
                Objects: deleteObjects
            }
        }

        return new Promise((resolve, reject) => {

            s3Config.deleteObjects(params, function (err, data) {
                logger.info("S3Upload ~ deleteFiles ~ err, data:", {
                    err,
                    data
                })

                if (err) {
                    reject({
                        status: STATUS_CODES.SERVER_ERROR,
                        error: err
                    })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })
    }

    //GET SIGNED URL WITH DEFINED TIME
    getSignedUrlWithExpiryTime(pathName, fileName, expiryTime) {
        const url = s3Config.getSignedUrl('getObject', {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + element,
            Expires: expiryTime * 60 || 60 // time in seconds: e.g. 60 * 5 = 5 mins
        })
        return url
    }

    //GET PUBLIC URL
    getPublicUrl(pathName, fileName) {
        let params = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
        }

        return new Promise((resolve, reject) => {
            s3Config.getObject(params, function (err, data) {
                if (err) {
                    if (err.code === 'NoSuchKey')
                        resolve({
                            status: STATUS_CODES.NOT_FOUND,
                            data: err
                        })
                    else
                        reject({
                            status: STATUS_CODES.SERVER_ERROR,
                            error: err
                        })
                }

                //Success
                if (data) {
                    let location = `https://${process.env.AWS_BUCKET_NAME}.s3.amazonaws.com/${params.Key}`
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: {
                            ...data,
                            location: location
                        }
                    })
                }
            });
        })
    }

    getReadStreamOfObject(Key) {
        return s3Config.getObject({
            Bucket: s3Config.config.Bucket,
            Key,
        }).createReadStream();
    }

    writeStreamToS3(Key) {
        const pass = new stream.PassThrough();

        return {
            writeStream: pass,
            uploadFinished: s3Config.upload({ Key, Body: pass, Bucket: s3Config.config.Bucket }).promise()
        };
    }

    copyImage(type, tenantId, linkedTenantId, imageName, newImageName) {
        let params = {
            CopySource: VALUES.awsPublicBucketBaseURL + VALUES.awsBucketName + 'product/' + type + linkedTenantId + '/' + imageName,
            Bucket: VALUES.awsPublicBucketName + '/' + VALUES.awsBucketName + 'product/' + type + tenantId,
            Key: newImageName
        }

        return new Promise((resolve, reject) => {
            s3Config.copyObject(params, function (err, data) {
                if (err) {
                    if (err.code === 'NoSuchKey')
                        resolve({
                            status: STATUS_CODES.NOT_FOUND,
                            data: err.message
                        })
                    else
                        reject({
                            status: STATUS_CODES.SERVER_ERROR,
                            error: err.message
                        })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })
    }

    MigrationCopyImage(copySource, copyTo) {
        let params = {
            CopySource: encodeURIComponent(s3Config.config.Bucket + '/' + copySource),
            Bucket: s3Config.config.Bucket,
            Key: copyTo
        }

        return new Promise((resolve, reject) => {
            s3Config.copyObject(params, function (err, data) {
                if (err) {
                    if (err.code === 'NoSuchKey')
                        reject({
                            status: STATUS_CODES.NOT_FOUND,
                            data: err.message
                        })
                    else
                        reject({
                            status: STATUS_CODES.SERVER_ERROR,
                            error: err.message
                        })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })
    }

}

module.exports = { S3Upload }

const { EMAIL_ID, VALUES } = require("../Configs/constants")
const { sendEmail } = require("../Configs/Mailer")

exports.sendCatalogModeEnabledEmail = (
    customerIds,
    tenantId
) => {
    if (VALUES.IS_DEV_ENV) {
        return
    }

    const payload = {
        from: EMAIL_ID.NOTIFICATION,
        to: EMAIL_ID.FARIS,
        cc: [
            EMAIL_ID.BRIJESH,
            EMAIL_ID.DEEP
        ],
        subject: "Catalog Mode Enabled",
        data: {
            html: `
                The customers' catalog mode has been enabled through a cron job.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Customer Ids: </b> ${customerIds.join(', ')}
                <br/><br/>
            `
        },
    }
    
    return sendEmail(payload)
}

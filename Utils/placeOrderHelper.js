const InternalServiceModel = new (require("../Models/InternalServiceModel"))

const { stringifyObjectId } = require("./helpers")

class placeOrderHelper {

    handleMissingShipData = async (req) => {

        const missingShippingData =
            !req.body.ShippingAddress ||
            !req.body.regionId ||
            !req.body.regionName ||
            !req.body.cityId ||
            !req.body.cityName ||
            !req.body.shippingMobileNumber ||
            !req.body.shippingCountryCode ||
            !req.body.shippingCoordinates?.lat ||
            !req.body.shippingCoordinates?.lng

        if (missingShippingData) {
            const getMissingFields = () => {
                const fields = []

                if (!req.body.ShippingAddress) {
                    fields.push("'ShippingAddress'")
                }

                if (!req.body.regionId) {
                    fields.push("'regionId'")
                }

                if (!req.body.regionName) {
                    fields.push("'regionName'")
                }

                if (!req.body.cityId) {
                    fields.push("'cityId'")
                }

                if (!req.body.cityName) {
                    fields.push("'cityName'")
                }

                if (!+req.body.shippingMobileNumber) {
                    fields.push("'shippingMobileNumber'")
                }

                if (!req.body.shippingCountryCode) {
                    fields.push("'shippingCountryCode'")
                }

                if (!req.body.shippingCoordinates?.lat) {
                    fields.push("'shippingCoordinates.lat'")
                }

                if (!req.body.shippingCoordinates?.lng) {
                    fields.push("'shippingCoordinates.lng'")
                }
                return fields
            }

            const getErrMsg = (fields) => {
                return `Please provide valid ${fields.join(", ")}`
            }

            const customerInfo = await InternalServiceModel.getCustomer({
                "_id": req.body.customerUserRoleId,
                "projection": "_id shipping_address shipping_city_id shipping_region_id shipping_country_code shipping_mobile_number gps_coordinates",
                "options": { lean: true },
                "populate": [
                    {
                        path: "shipping_region_id",
                        select: "name country_id"
                    },
                    {
                        path: "shipping_city_id",
                        select: "name region_id country_id"
                    },
                ]
            })

            if (!customerInfo) {
                return getErrMsg(getMissingFields())
            }

            const {
                _id: customerUserRoleId,
                shipping_address: customerShipAddress,
                shipping_region_id: {
                    _id: customerRegionId,
                    name: customerRegionName,
                } = {},
                shipping_city_id: {
                    _id: customerCityId,
                    name: customerCityName,
                    region_id: customerCityRegionId,
                } = {},
                shipping_country_code: customerShipCountryCode,
                shipping_mobile_number: customerShipMobileNo,
                gps_coordinates: {
                    latitude: customerLat,
                    longitude: customerLng,
                } = {}
            } = customerInfo

            if (
                !req.body.ShippingAddress ||
                !req.body.shippingCoordinates?.lat ||
                !req.body.shippingCoordinates?.lng
            ) {
                if (
                    customerShipAddress &&
                    customerRegionId &&
                    customerRegionName &&
                    customerCityId &&
                    customerCityName &&
                    customerLat &&
                    customerLng
                ) {
                    req.body.ShippingAddress = customerShipAddress
                    req.body.regionId = customerRegionId
                    req.body.regionName = customerRegionName
                    req.body.cityId = customerCityId
                    req.body.cityName = customerCityName
                    req.body.shippingCoordinates = {
                        lat: customerLat,
                        lng: customerLng
                    }

                    logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - All Except MobNo`)
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }

            if (!req.body.regionId) {
                if (customerRegionId) {
                    req.body.regionId = customerRegionId
                    logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - customerRegionId: ${customerRegionId}`)
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }

            if (!req.body.regionName) {
                if (customerRegionName) {
                    if (stringifyObjectId(req.body.regionId) === stringifyObjectId(customerRegionId)) {
                        req.body.regionName = customerRegionName
                        logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - customerRegionName: ${customerRegionName}`)
                    }
                    else {
                        return `Can't assign 'customerRegionName' to order, as provided 'regionId' is mismatched with 'customerRegionId'. ${getErrMsg(getMissingFields())}`
                    }
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }

            if (!req.body.cityId) {
                if (customerCityId) {
                    if (stringifyObjectId(req.body.regionId) === stringifyObjectId(customerRegionId)) {
                        req.body.cityId = customerCityId
                        logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - customerCityId: ${customerCityId}`)
                    }
                    else {
                        return `Can't assign 'customerCityId' to order, as provided 'regionId' is mismatched with 'customerRegionId'. ${getErrMsg(getMissingFields())}`
                    }
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }

            if (!req.body.cityName) {
                if (customerCityName) {
                    if (stringifyObjectId(req.body.cityId) === stringifyObjectId(customerCityId)) {
                        req.body.cityName = customerCityName
                        logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - customerCityName: ${customerCityName}`)
                    }
                    else {
                        return `Provided 'cityId' is mismatched with 'customerCityId'. ${getErrMsg(getMissingFields())}`
                    }
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }

            if (
                !req.body.shippingCountryCode ||
                !req.body.shippingMobileNumber
            ) {
                if (customerShipCountryCode && customerShipMobileNo) {
                    req.body.shippingCountryCode = customerShipCountryCode
                    req.body.shippingMobileNumber = customerShipMobileNo

                    logger.info(`Place_Order_Field_Set_From_Customer_Info(${customerUserRoleId}) - customerShipCountryCode: ${customerShipCountryCode}, customerShipMobileNo: ${customerShipMobileNo}`)
                }
                else {
                    return getErrMsg(getMissingFields())
                }
            }
        }
    }
}

module.exports = placeOrderHelper

const path = require('path')
const https = require('https')
const fs = require('fs')
const Stream = require('stream').Transform

const snakeCase = require('lodash.snakecase');
const mapKeys = require('lodash.mapkeys');
const axios = require("axios")
const isEmpty = require("lodash.isempty")

const { VALUES } = require("../Configs/constants");

/*
GENERATE RANDOM NUMBER FROM SPECIFIED RANGE
*/
function generateRandom(min, max) {
    return Math.floor(Math.random() * (max - min)) + min
}

/*
CHECK IF TWO ARRAYS ARE EQUAL
*/
function arraysEqual(a, b) {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;

    for (var i = 0; i < a.length; ++i)  if (a[i] !== b[i]) return false;

    return true;
}

const httpService = (request) => axios.create({
    baseURL: VALUES.internalServiceBaseURL,
    headers: {
        userDetails: request?.headers?.userDetails?._id ?
            JSON.stringify({
                _id: request?.headers?.userDetails?._id,
                mobile_number: request?.headers?.userDetails?.mobile_number,
                country_code: request?.headers?.userDetails?.country_code,
                email: request?.headers?.userDetails?.email,
                first_name: request?.headers?.userDetails?.first_name,
                last_name: request?.headers?.userDetails?.last_name,
                cognito_username: request?.headers?.userDetails?.cognito_username,
            }) : ''
    }
});

const roundOf = (number = 0, toFix = 3, type = "number") => (
    type === "number"
        ? Number(number.toFixed(toFix))
        : number.toFixed(toFix)
)

const formatAmount = (amount = 0, decimal = 2, currency) => {
    return amount.toFixed(decimal) + (currency ? ` ${currency}` : "")
}

const arrayBufferToBase64 = (arrayBuffer) => {
    const bytes = new Uint8Array(arrayBuffer)
    const len = bytes.byteLength
    let binary = ''

    for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
    }
    return Buffer.from(binary, "binary").toString("base64")
}

const generateFileFromS3Object = async (fileS3Url, folderName, newFileName) => {
    return new Promise((resolve, reject) => {
        const filePath = path.join(__dirname, `../Assets/${folderName}/` + newFileName)

        https.get(fileS3Url, response => {
            const data = new Stream()

            response.on('data', (chunk) => {
                data.push(chunk)
            })

            response.on('end', () => {
                fs.writeFile(
                    filePath,
                    data.read(),
                    function (err, result) {
                        if (err) {
                            reject(err)
                            return
                        }
                        resolve(result)
                    })
            })
        }).end()
    })
}

const generateExcelColumnCellName = () => {
    let i = 65
    const firstArr = [""]
    const secondArr = []
    const cellNames = []

    while (i <= 90) {
        firstArr.push(String.fromCharCode(i))
        secondArr.push(String.fromCharCode(i))
        i++
    }

    for (let index = 0; index < firstArr.length; index++) {
        const element = firstArr[index]

        for (let index = 0; index < secondArr.length; index++) {
            const secondElement = secondArr[index]
            const key = `${element}` + `${secondElement}`
            cellNames.push(key)
        }
    }
    return cellNames
}

const includeProjections = {
    tenant_id: 1,
}

const excludeProjections = {
    __v: 0,
    created_at: 0,
    updated_at: 0,
    created_by: 0,
    updated_by: 0
};

const toLeanOption = {
    lean: true
}

const stringifyObjectId = (objectId) => {
    if (objectId) {
        return objectId.toString()
    }
    return ""
}

/**
 * Converts a value to a Mongoose ObjectId.
 * @param {string} id - The string to be converted to ObjectId.
 * @returns {mongoose.Types.ObjectId|null} The ObjectId if valid, otherwise null.
 */
const toObjectId = (id) => {
    if (mongoose.Types.ObjectId.isValid(id)) {
        return new mongoose.Types.ObjectId(id)
    }
    return null
}

const convertListOfKeysToSnakeCase = (list = []) => {
    return list
        .map((element) =>
            mapKeys(
                element,
                (_, key) => snakeCase(key)
            )
        );
}

const convertToSnakeCase = (data) => {
    if (Array.isArray(data)) {
        return convertListOfKeysToSnakeCase(data);
    }
    else {
        if (!Object.keys(data).length) return {};

        const update = {};

        Object.keys((data)).forEach((key) => {
            if (key) {
                if (Array.isArray(data[key])) {
                    update[snakeCase(key)] = convertListOfKeysToSnakeCase(data[key]);
                }
                else if (typeof data[key] === 'object') {
                    update[snakeCase(key)] = convertToSnakeCase(data[key]);
                }
                else {
                    update[snakeCase(key)] = data[key];
                }
            }
        })
        return update;
    }
}

const getKeyByValue = (object, value) => {
    return Object.keys(object).find(key => object[key] === value);
}

const titleCase = (text) => {
    const result = text.replace(/([A-Z])/g, " $1");
    return result.charAt(0).toUpperCase() + result.slice(1);
}

const prepareApiLog = (req, data, error) => {
    const errorMessage = error?.response?.data?.message || error?.message

    const {
        method,
        originalUrl,
        query,
        body,
        headers: {
            authorization,
            refreshtoken,
            userroleid,
            userDetails,
            devicetoken,
            devicetype,
            portalType,
            deviceTypeDetected,
            deviceaccesstype,
            version,
            build,
        } = {},
    } = req

    const apiInfo = {
        method,
        url: originalUrl,
        headers: {
            authorization,
            refreshtoken,
            userDetails,
            userroleid,
            portalType,
            devicetoken,
            devicetype,
            deviceTypeDetected,
            deviceaccesstype,
            version,
            build,
        },
        errorMessage,
        stack: error?.stack
    }

    if (!isEmpty(data)) {
        apiInfo.data = data
    }

    if (!isEmpty(query)) {
        apiInfo.query = query
    }

    if (!isEmpty(body)) {
        apiInfo.body = body
    }
    return apiInfo
}

const addToSet = (set, setValue) => {
    if (Array.isArray(setValue)) {
        if (setValue.length) {
            setValue.forEach(set.add, set)
        }
    }
    else if (setValue) {
        set.add(setValue)
    }
}

const addToSetIfExists = (set, value, list) => {
    if (Array.isArray(value)) {
        if (value.length) {
            value.forEach(id => {
                const newId = stringifyObjectId(id)
                const hasValue = list[newId]

                if (!hasValue) {
                    set.add(newId)
                }
            })
        }
    }
    else if (value) {
        const newId = stringifyObjectId(value)
        const hasValue = list[newId]

        if (!hasValue) {
            set.add(newId)
        }
    }
}

/**
 * 
 * @param {*} object 
 * @param {*} key 
 * @param {*} value 
 * @description Strictly use only if `value` is not falsy. 
 */
const addToObject = (object, key, value) => {
    if (!value) {
        return
    }
    object[key] = value
}

const keyBy = (array, key) => {
    return array.reduce((accumulator, currentValue) => {
        accumulator[currentValue[key]] = currentValue
        return accumulator
    }, {})
}

const isNullish = (value) => [null, undefined].includes(value)

const delay = async (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
}

const writeObjectToFile = (filePath, input) => {
    try {
        const encoding = 'utf-8'

        // Normalize input to array
        const objects = Array.isArray(input) ? input : [input]

        // Check if the file already exists
        let existingData = []

        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, encoding)
            existingData = JSON.parse(data)
        }

        // Merge and write
        const mergedData = [...existingData, ...objects]
        fs.writeFileSync(filePath, JSON.stringify(mergedData, null, 4), encoding)

        console.log('Object(s) written successfully!')
    }
    catch (err) {
        console.error('Error writing to file:', err)
    }
}


module.exports = {
    generateRandom,
    arraysEqual,
    httpService,
    roundOf,
    formatAmount,
    arrayBufferToBase64,
    generateFileFromS3Object,
    generateExcelColumnCellName,
    includeProjections,
    excludeProjections,
    toLeanOption,
    toObjectId,
    stringifyObjectId,
    titleCase,
    getKeyByValue,
    prepareApiLog,
    addToSet,
    addToSetIfExists,
    addToObject,
    keyBy,
    isNullish,
    delay,
    writeObjectToFile,
}

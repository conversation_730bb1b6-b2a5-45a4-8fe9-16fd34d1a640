const InternalServiceModel = new (require("../Models/InternalServiceModel"))();
const TemplateService = new (require("../Services/TemplateService"))();

const {
    NOTIFICATION,
} = require("../Configs/constants")

class NotificationManger {

    sentNewProductNotification = async (
        tenantId,
        userRoleName,
        userRoleIds,
        priceListIds,
        priceName,
        productImages,
    ) => {
        const notificationData = {
            ...TemplateService.getNewProductTemplates({ userRoleName, priceName }),
            userRoleIds,
            tenantId: tenantId,
            type: NOTIFICATION.TYPE.NEW_PRODUCTS,
            threadId: NOTIFICATION.TYPE.NEW_PRODUCTS,
            payloadData: {
                priceListIds,
                priceName,
                productImages,
            },
        }
        await InternalServiceModel.sentNotification(notificationData)
    }
}

module.exports = NotificationManger

const { VALUES } = require('../../Configs/constants');

const CategorySchema = new mongoose.Schema({
    category_name: {
        type: String,
        required: true,
    },
    secondary_language_category_name: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        ref: 'tenants'
    },
    type: {
        type: String,
        required: true,
        enum: Object.values(VALUES.category)
    },
    family_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'category'
    },
    parent_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'category',
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    sequence: {
        type: Number,
    },
    image_name: {
        type: String,
        trim: true,
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
    },
    product_counter: {
        type: Number,
        default: 0
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

CategorySchema.index({
    tenant_id: 1,
    is_active: 1,
    is_deleted: 1,
    type: 1,
    parent_id: 1,
})

CategorySchema.index(
    {
        // Equality fields
        tenant_id: 1,
        is_deleted: 1,
        is_active: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "categories_updated_init_sync_idx" }
);

CategorySchema.index(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "categories_updated_sync_idx" }
);

module.exports = mongoose.model('categories', CategorySchema);

/** listen for updates to documents in this collection  */
require("./change_streams/CategoryChangeStream")

const Schema = mongoose.Schema;

const { VALUES } = require('../../../Configs/constants');

const MasterTaxSchema = new Schema({
    tenant_id: {
        type: Number,
        ref: 'tenants'
    },
    enable_tax: {
        type: Boolean,
        required: true
    },
    price: {
        type: String,
        required: true,
        enum: Object.values(VALUES.MASTER_PRICE)
    },
    default_tax: {
        type: Schema.Types.ObjectId,
        ref: 'tax_configurations',
        default: null
    },
    universal_tax: {
        type: Boolean,
        required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: 'users',
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

MasterTaxSchema.index({
    tenant_id: 1
})

module.exports = mongoose.model('master_tax', MasterTaxSchema);
const Schema = mongoose.Schema;

const { VALUES } = require('../../../Configs/constants');

const TaxConfigurationSchema = new Schema({
    tenant_id: {
        type: Number,
    },
    type: {
        type: String,
        required: true,
        enum: Object.values(VALUES.TAX_TYPE)
    },
    group_id: {
        type: Schema.Types.ObjectId,
        ref: 'tax_configurations'
    },
    tax_name: {
        type: String,
        required: true,
    },
    tax_calculation: {
        type: String,
        enum: Object.values(VALUES.TAX_CALCULATION)
    },
    tax_rate: {
        type: String,
    },
    status: {
        type: <PERSON><PERSON><PERSON>,
    },
    order: {
        type: Number,
    },
    created_by: {
        type: Schema.Types.ObjectId,
    },
    updated_by: {
        type: Schema.Types.ObjectId,
    },
}, {
    timestamps: {
    createdAt: "created_at",
    updatedAt: "updated_at"
    }
});

TaxConfigurationSchema.index({
    tenant_id: 1
})

module.exports = mongoose.model('tax_configurations', TaxConfigurationSchema);
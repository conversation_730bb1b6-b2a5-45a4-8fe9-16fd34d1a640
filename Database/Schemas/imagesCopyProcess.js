const Schema = mongoose.Schema;

const ImageCopyProcessSchema = new Schema({
    tenant_id: {
        type: Number,
        required: true
    },
    linked_tenant_id: {
        type: Number,
        required: true
    },
    created_at: {
        type: Date,
        default: moment().utc(),
        required: true
    }
});

module.exports = mongoose.model('images_copy_process', ImageCopyProcessSchema);
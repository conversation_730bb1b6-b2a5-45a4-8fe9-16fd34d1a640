const schema = new mongoose.Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        attribute_name: {
            type: String,
            trim: true,
            required: true,
        },
        secondary_language_attribute_name: {
            type: String,
            trim: true,
            required: true,
        },
        is_deleted: {
            type: Boolean,
            required: true,
            default: false
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        },
        strict: true,
    },
)

schema.index(
    {
        attribute_name: 1
    },
    {
        collation: {
            locale: "en",
            strength: 2
        }
    }
)

schema.index({
    tenant_id: 1
})

module.exports = mongoose.model('master_attributes', schema)

/** listen for updates to documents in this collection  */
require("../change_streams/AttributeChangeStream")

const schema = new mongoose.Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        price_id: {
            type: String,
            trim: true,
            required: true,
        },
        price_name: {
            type: String,
            trim: true,
            required: true,
        },
        secondary_language_price_name: {
            type: String,
            trim: true,
            required: true,
        },
        is_deleted: {
            type: Boolean,
            required: true,
            default: false
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true
        },
        is_default: {
            type: Boolean,
            required: true,
            default: false
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        },
        strict: true,
    }
)

schema.index(
    {
        price_name: 1
    },
    {
        collation: {
            locale: "en",
            strength: 2
        }
    }
)

schema.index({
    tenant_id: 1
})

module.exports = mongoose.model('master_prices', schema)

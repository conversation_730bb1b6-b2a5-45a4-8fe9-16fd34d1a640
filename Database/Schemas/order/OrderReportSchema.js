const OrderReportSchema = new mongoose.Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
            index: true,
        },
        user_role_id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            index: true,
        },
        date: {
            type: Date,
            required: true,
        },
        timezone: {
            type: String,
            required: true,
        },
        total_orders: {
            type: Number,
            default: 0,
            required: true,
        },
        total_sales_amount: {
            type: Number,
            default: 0,
            required: true,
        },
        total_tax: {
            type: Number,
            default: 0
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        },
    },
)

module.exports = mongoose.model('order_reports', OrderReportSchema)

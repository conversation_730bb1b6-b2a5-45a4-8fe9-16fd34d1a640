const Schema = mongoose.Schema;

const DraftCounterSchema = new Schema({
    _id: {
        type: String, // `${tenant_id}_${salespersonUserRoleId}`
        required: true
    },
    counter: {
        type: Number,
        required: true,
        default: 100000
    },
    created_by: {
        type: Schema.Types.ObjectId,
    },
    update_by: {
        type: Schema.Types.ObjectId,
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("draft_counter", DraftCounterSchema)
const Schema = mongoose.Schema;

const OrderStatSchema = new Schema({
    _id: { // _id of user roles schema ( sales person & customer schema )
        // TODO: LINK_TENANT_CHANGE ( if the link tenant changes are added then need to add tenant_id in this field and convert this field in to string type `${tenant_id}_{user_role_id}`)
        type: Schema.Types.ObjectId,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    accepted_order_count: {
        type: Number,
        default: 0
    },
    total_sales: {
        type: Number,
        default: 0
    },
    total_tax: {
        type: Number,
        default: 0
    }

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})




module.exports = mongoose.model("order_sales_statistics", OrderStatSchema);
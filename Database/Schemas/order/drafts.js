const mongoose = require("mongoose");

const { TaxCalculationSchema } = require("./common");

const Schema = mongoose.Schema;

const DraftSchema = new Schema({
    draft_number: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        required: true
    },
    customer_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    total_amount: {
        type: Number,
        required: true,
    },
    total_tax: {
        type: Number,
        required: true,
    },
    customer_id: {
        type: String,
        // required: true
    },
    external_id: {
        type: String,
        // required: true
    },
    customer_name: {
        type: String,
        required: true,
    },
    customer_primary_contact_name: {
        type: String,
        required: true
    },
    sales_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    draft_owner_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
        index: true
    },
    tax_calculation_info: {
        type: Map,
        of: TaxCalculationSchema
    },
    created_by: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    update_by: {
        type: Schema.Types.ObjectId,
        required: true,
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

DraftSchema.virtual('draftItems', {
    ref: 'draft_items_2.0',
    localField: '_id',
    foreignField: 'draft_id',
})


DraftSchema.set('toJSON', { virtuals: true })
DraftSchema.set('toObject', { virtuals: true })

module.exports = mongoose.model('drafts_2.0', DraftSchema);
const mongoose = require("mongoose");

const Schema = mongoose.Schema;

const {
    ORDER_EXPORT
} = require("../../../Configs/constants")

const OrderExportTaskSchema = new Schema({
    tenant_id: {
        type: Number,
        required: true,
        index: true,
    },
    user_role_name: {
        type: String,
        required: true,
        trim: true,
    },
    user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    start_date: {
        type: Date,
        required: true,
    },
    end_date: {
        type: Date,
        required: true,
    },
    timezone: {
        type: String,
        required: true,
        trim: true,
    },
    order_status_list: {
        type: [String],
        required: true,
        trim: true,
        enum: Object.keys(ORDER_EXPORT.ORDER_STATUS_LIST)
    },
    filename: {
        type: String,
        required: true,
        trim: true,
    },
    to_user_mail_id: {
        //To sent exported order to their email id
        type: String,
        required: true,
        trim: true,
    },
    status: {
        type: String,
        required: true,
        trim: true,
        enum: Object.values(ORDER_EXPORT.STATUS)
    },
    failure_message: {
        type: String,
        trim: true,
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('order_export_tasks', OrderExportTaskSchema);

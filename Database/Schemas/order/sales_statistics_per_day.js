const Schema = mongoose.Schema;

const SaleStatisticSchema = new Schema({
    _id: { 
        type: Schema.Types.ObjectId,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    branch_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    sales_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    day_sales_amount: {
        type: Number,
        default: 0
    },
    day_sales_tax: {
        type: Number,
        default: 0
    },
    date: {
        type: String
    }

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

SaleStatisticSchema.index({
    tenant_id: 1
})

SaleStatisticSchema.index({
    branch_id: 1
})

module.exports = mongoose.model("sales_statistics_per_day", SaleStatisticSchema);
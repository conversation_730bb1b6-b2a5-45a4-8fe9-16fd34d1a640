const Schema = mongoose.Schema;

const CategoryStatisticSchema = new Schema({
    _id: { 
        type: Schema.Types.ObjectId,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    category_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    sales_amount: {
        type: Number
    },
    tax_amount: {
        type: Number
    },
    quantity: {
        type: Number
    },
    date: {
        type: String    // "YYYY-MM-DD" 
    }

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

CategoryStatisticSchema.index({
    tenant_id: 1,
    category_id: 1,
    date: 1,
})

module.exports = mongoose.model("category_statistics", CategoryStatisticSchema);
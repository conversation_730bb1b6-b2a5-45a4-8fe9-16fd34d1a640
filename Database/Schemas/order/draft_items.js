const mongoose = require("mongoose");

const { TaxCalculationSchema, ProductDealInfoSchema } = require("./common");

const Schema = mongoose.Schema;

const DraftItemSchema = new Schema({
    draft_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "drafts_2.0",
        index: true,
    },
    tenant_id: {
        type: Number,
        required: true,
        index: true,
    },
    product_variant_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "products_2.0",
        index: true,
    },
    variant_id: {
        type: String,
    },
    item_number: {
        type: String,
        required: true
    },
    parent_item_number: {
        type: String,
    },
    product_name: {
        type: String,
        required: true
    },
    product_secondary_name: {
        type: String
    },
    variant_name: {
        type: String,
    },
    variant_secondary_name: {
        type: String,
    },
    group_name: {
        type: String,
    },
    group_secondary_name: {
        type: String,
    },
    master_price_id: {
        type: Schema.Types.ObjectId,
        ref: "master_prices",
        required: true
    },
    original_price: { // for storing display price
        type: Number
    },
    base_price: {
        type: Number,
        required: true
    },
    tax: {
        type: Number,
        required: true,
        default: 0,
    },
    quantity: {
        type: Number,
        required: true,
    },
    uom_id: {
        type: Schema.Types.ObjectId,
        ref: "master_units",
        required: true,
    },
    uom_name: {
        type: String,
        required: true
    },
    customer_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    sales_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    created_by: {
        type: Schema.Types.ObjectId,
    },
    updated_by: {
        type: Schema.Types.ObjectId,
    },
    tax_calculation_info: {
        type: TaxCalculationSchema
    },
    deal_info: {
        type: ProductDealInfoSchema
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('draft_items_2.0', DraftItemSchema);
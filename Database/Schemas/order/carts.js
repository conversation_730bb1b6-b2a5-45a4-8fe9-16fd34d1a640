// const mongoose = require("mongoose");
// const Schema = mongoose.Schema;

// const CartSchema = new Schema({
//     _id: {
//         type: String,
//         required: true
//         // Customer cart id: ${tenant_id}_${customer_cart_id}
//         // Owner id: ${tenant_id}_${customer_cart_id}_{owner_id}
//         // admin id: ${tenant_id}_${customer_cart_id}_{admin_id}
//         // salespersonId id: ${tenant_id}_${customer_cart_id}_{salesperson_id}
//     },
//     tenant_id: {
//         type: Number,
//         required: true
//     },
//     customer_user_role_id: {
//         type: Schema.Types.ObjectId,
//         required: true
//     },
//     sales_user_role_id: {
//         type: Schema.Types.ObjectId,
//         required: true
//     },
//     created_by: {
//         type: Schema.Types.ObjectId,
//     },
//     update_by: {
//         type: Schema.Types.ObjectId,
//     }
// }, {
//     timestamps: {
//         createdAt: "created_at",
//         updatedAt: "updated_at"
//     }
// });

// CartSchema.virtual('cartItems', {
//     localField: "_id",
//     foreignField: "cart_id",
//     ref: "cart_items_2.0",
// });


// CartSchema.set('toJSON', { virtuals: true })
// CartSchema.set('toObject', { virtuals: true })
// module.exports = mongoose.model("carts_2.0", CartSchema);
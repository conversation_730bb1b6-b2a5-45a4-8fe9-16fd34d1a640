const Schema = mongoose.Schema;

const {
    VALUES,
    DEAL_TYPE, DISCOUNT_TYPE
} = require("../../../Configs/constants");

const GroupTaxInfoSchema = new Schema({
    tax_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "tax_configurations",
    },
    tax_name: {
        type: String,
        required: true
    },
    tax_rate: {
        type: Number,
        required: true
    },
    calculated_tax: {
        type: Number,
        required: true
    },
    tax_calculation: {
        type: String,
        enum: Object.values(VALUES.TAX_CALCULATION)
    },
}, { _id: false })

const TaxCalculationSchema = new Schema({
    tax_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "tax_configurations",
    },
    type: {
        type: String,
        required: true,
        enm: [VALUES.TAX_TYPE.SINGLE, VALUES.TAX_TYPE.GROUP],
    },
    tax_name: {
        type: String,
        required: true
    },
    tax_rate: {
        type: Number
    },
    tax_calculation: {
        type: String,
        enum: Object.values(VALUES.TAX_CALCULATION)
    },
    calculated_tax: {
        type: Number,
    },
    group_taxes: {
        type: Map,
        of: GroupTaxInfoSchema
    },
    price: {
        type: String,
        enum: Object.values(VALUES.MASTER_PRICE),
    }

}, { _id: false });


// deals schema for cart, order and draft items

const TierSchema = new Schema({
    product_qty: {
        type: Number
    },
    price: {
        type: Number,
    }
}, { _id: false });


const ProductDealInfoSchema = new Schema({
    // DEAL INFO STARTS //
    deal_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'deals'
    },
    deal_name: {
        type: String,
        required: true
    },
    deal_number: {
        type: String,
        required: true
    },
    deal_type: {
        type: String,
        required: true,
        enum: Object.values(DEAL_TYPE)
    },
    deal_from_date: {
        type: Date,
        required: true
    },
    deal_to_date: {
        type: Date,
        required: true
    },
    secondary_deal_name: {
        type: String,
    },

    // DEAL INFO ENDS //

    // deal item settings starts //
    deal_product_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'deal_products'
    },

    discount_type: { // ( for DISCOUNT  deal_type )
        type: String,
        enum: Object.values(DISCOUNT_TYPE),
    },
    percent: { // ( for DISCOUNT deal_type )
        type: Number,
    },
    amount: { // ( for DISCOUNT deal_type ) ( all time we store the discount amount on individual item )
        type: Number,
    },
    discounted_price: { // ( for DISCOUNT deal_type ) (calculated price after discount applied)
        type: Number
    },

    first_tier: { // ( for BULK_PRICING  deal_type )
        type: TierSchema
    },
    second_tier: { // ( for BULK_PRICING  deal_type )
        type: TierSchema
    },
    third_tier: { // ( for BULK_PRICING  deal_type )
        type: TierSchema
    },
    bulk_price: {
        type: Number
    },

    buy_product: { // ( for BUY_X_AND_GET_Y )
        type: Number,
    },
    free_product: { // ( for BUY_X_AND_GET_Y )
        type: Number,
    },
    free_item_count: { // ( for BUY_X_AND_GET_Y ) ( will use for storing )
        type: Number,
    }
    // deal item settings ends //

}, { _id: false })

module.exports = { TaxCalculationSchema, ProductDealInfoSchema }
const mongoose = require('mongoose');
const {
    ORDER_PUNCH_DEVICE_TYPES,
    ORDER_PUNCH_DEVICE_OS,
    ORDER_APP_TYPE,
    ORDER_STATUS_TYPES,
    FALSELY_VALUES,
    DEAL_STATISTICS_EVENT_TYPE,
    VALUES,
} = require("../../../Configs/constants")

const { TaxCalculationSchema } = require("./common")
const OrderSalesStatSchema = require("./order_sales_statistics")
const SaleStatisticSchema = require("./sales_statistics_per_day")
const CategoryStatSchema = require("./category_statistics")
const OrderItemSchema = require("./OrderItemSchema");
const { sendDealStatMessageToSQS } = require('../../../SQS/DealStatisticsQueue');

const Schema = mongoose.Schema

const GPSCoordinatesSchema = new Schema(
    {
        lat: {
            type: Number,
            required: true
        },
        lng: {
            type: Number,
            required: true
        }
    },
    {
        _id: false
    }
)

const OrderStatusTrackSchema = new Schema(
    {
        order_status: {
            type: String,
            required: true
        },
        time: {
            type: Date,
            required: true
        },
        user_info: {
            user_role_id: {
                type: Schema.Types.ObjectId,
            },
            name: {
                type: String,
            },
        },
        reason_id: {
            type: Schema.Types.ObjectId,
        }
    },
    {
        _id: false
    }
)

const OrdersSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true
        },
        customer_user_role_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        external_id: {
            type: String
        },
        sales_user_role_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        sales_person_name: {
            type: String,
            required: true
        },
        customer_name: {
            type: String,
            required: true
        },
        customer_legal_name: {
            type: String,
            required: true,
        },
        customer_primary_contact_name: {
            type: String,
            // required: true
        },
        branch_id: {
            type: Schema.Types.ObjectId,
            required: true
        },

        //================ ORDER INFO ================//
        // TODO: ORDER LISTING CHANGES: need add array of master_price_id
        master_price_ids: [Schema.Types.ObjectId],
        order_number: {
            type: String,
            required: true,
        },
        unique_order_number: {
            type: String,
            required: true,
            unique: true
        },
        order_remark: {
            type: String,
        },
        order_punching_device_type: {
            type: String,
            enum: Object.values(ORDER_PUNCH_DEVICE_TYPES),
            required: true
        },
        order_punching_device_os: {
            type: String,
            enum: Object.values(ORDER_PUNCH_DEVICE_OS),
            required: true
        },
        order_app_type: {
            type: String,
            enum: Object.values(ORDER_APP_TYPE),
            required: true
        },
        order_creator_user_role_id: {
            type: Schema.Types.ObjectId,
            // required: true // TODO: LINK_TENANT_CHANGE need to make it required after updating existing order
        },
        order_creator_name: {
            type: String,
            // required: true // TODO: LINK_TENANT_CHANGE need to make it required after updating existing order
        },
        order_status: {
            type: String,
            enum: Object.values(ORDER_STATUS_TYPES),
            required: true
        },
        order_hold_reason_id: {
            type: Schema.Types.ObjectId,
        },
        whatsapp_message_sent: {
            type: {
                sales_person: {
                    type: Boolean,
                    required: true
                },
                customer: {
                    type: Boolean,
                    required: true
                },
            },
        },
        order_status_track: {
            type: [OrderStatusTrackSchema],
            required: true
        },
        pre_approved: {
            type: Boolean,
            default: false
        },

        //================ PRICE CALCULATIONS ================//
        total_amount: {
            type: Number,
            required: true,
            default: 0,
        },
        total_tax: {
            type: Number,
            required: true,
            default: 0,
        },
        reduce_inventory: {
            type: Boolean,
            default: false
        },
        tax_calculation_info: {
            type: Map,
            of: TaxCalculationSchema
        },

        //================ ADDRESS ================//
        shipping_address: {
            type: String,
            required: true,
        },
        city_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        region_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        shipping_mobile_number: {
            type: Number,
            required: true
        },
        shipping_country_code: {
            type: String,
            required: true
        },
        city_name: {
            type: String,
            required: true,
        },
        region_name: {
            type: String,
            required: true,
        },
        gps_coordinate: {
            type: GPSCoordinatesSchema,
            required: true,
        },
        created_by: {
            type: Schema.Types.ObjectId,
        },
        update_by: {
            type: Schema.Types.ObjectId,
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

OrdersSchema.index({ customer_user_role_id: 1 })

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
OrdersSchema.index({
    // Equality fields
    "tenant_id": 1,
    "branch_id": 1,
    "order_status": 1,
    "sales_user_role_id": 1,
    "customer_user_role_id": 1,
    "order_creator_user_role_id": 1,
    "order_app_type": 1,
    "master_price_ids": 1,

    // Sort fields
    "created_at": 1,
})

OrdersSchema.index(
    {
        // Equality fields
        "tenant_id": 1,
        "order_creator_user_role_id": 1,

        // Sort fields
        "updated_at": 1,
        "_id": 1,

        // Range fields
        "created_at": 1,
    },
    { name: "orders_updated_sync_idx" }
)


const OrderModel = mongoose.model("orders", OrdersSchema)

module.exports = OrderModel

const topCatArr = async (orderId) => {
    const updateArr = []

    const orderItemWithProduct =
        await OrderItemSchema
            .find(
                {
                    "order_id": orderId
                },
                {
                    "product_variant_id": 1,
                    "quantity": 1,
                    "base_price": 1,
                    "tax": 1
                }
            )
            .populate({
                "path": 'product_variant_id',
                "select": 'family_id category_id subcategory_id'
            })

    const groupedData = orderItemWithProduct.reduce((acc, curr, index) => {
        const key =
            curr.product_variant_id.subcategory_id ||
            curr.product_variant_id.category_id ||
            curr.product_variant_id.family_id

        if (!acc[key]) {
            acc[key] = { "quantity": 0 }
        }

        acc[key].tenant_id = curr.tenant_id
        acc[key].quantity += curr.quantity
        acc[key].category_id = key
        acc[key].base_price = curr.base_price
        acc[key].tax = curr.tax
        return acc
    }, {})

    const dataValue = Object.values(groupedData)
    dataValue.map((item, index) => {

        updateArr[index] = {}
        updateArr[index].tenant_id = item.tenant_id
        updateArr[index].quantity = item.quantity
        updateArr[index].category_id = item.category_id
        updateArr[index].base_price = item.base_price
        updateArr[index].tax = item.tax
    })
    return updateArr
}

const checkAppliedDealInOrderItems = function (orderId, orderStatus) {
    OrderItemSchema.find({ order_id: orderId }, { deal_info: 1, tenant_id: 1, quantity: 1, base_price: 1, tax: 1 })
        .then(ordeRItems => {
            ordeRItems.forEach(oItem => {
                if (oItem.deal_info?.deal_product_id) {
                    const messageBody = {
                        dealId: oItem.deal_info.deal_id.toString(),
                        dealProductId: oItem.deal_info.deal_product_id.toString(),
                        freeItemCount: oItem.deal_info.free_item_count,
                        orderStatus,
                        dealType: oItem.deal_info.deal_type,
                        quantity: oItem.quantity,
                        basPrice: oItem.base_price,
                        tax: oItem.tax,
                        dealStatisticsEventType: DEAL_STATISTICS_EVENT_TYPE.SALES_COUNT_UPDATE,
                        ts: new Date().toISOString(),
                    }
                    return sendDealStatMessageToSQS(messageBody, oItem.tenant_id)
                }
            })
        })
        .then(sqsResponse => {
            if (sqsResponse) {
                logger.info("OrderSchema.js ~ checkAppliedDealInOrderItems ~ sqsResponse", { sqsResponse })
            }
        })
        .catch(error => {
            logger.error(error)
        })
}

// blow check is to attach change stream only on server
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    OrderModel
        .watch(
            [],
            {
                "fullDocument": "updateLookup"
            }
        )
        .on("change", async (data) => {
            try {
                // fullDocument will provide updated document
                const updateOrderCheck = !(FALSELY_VALUES.includes(data.updateDescription?.updatedFields?.order_status))
                const order_app_type = data.fullDocument?.order_app_type
                const order_status = data.fullDocument?.order_status
                const promises = []
                const dateFormat = moment(data.fullDocument?.created_at).format("YYYY-MM-DD")

                if (
                    data.operationType === "update"
                    && order_status === ORDER_STATUS_TYPES.RECEIVED
                    && data.fullDocument?.order_status_track[0]?.order_status === ORDER_STATUS_TYPES.RECEIVED
                    && data.fullDocument?.order_status_track?.length === 1
                ) { // this will check, whether the order was placed & updated after assigning order_number with RECEIVED status or not

                    //TO CHECK FOR TENANT OWNER AND TENANT ADMIN ROLES
                    if (
                        [
                            ORDER_APP_TYPE.SALES_APP,
                            ORDER_APP_TYPE.ADMIN,
                            ORDER_APP_TYPE.SUPERVISOR_APP,
                            ORDER_APP_TYPE.TENANT_OWNER
                        ].includes(order_app_type)
                    ) {
                        // logger.info("In case 1", { fullDocument: data.fullDocument, })

                        const salesPersonStats = {
                            "_id": data.fullDocument.sales_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": data.fullDocument.total_amount,
                                "total_tax": data.fullDocument.total_tax,
                                "accepted_order_count": 1
                            },
                        }

                        const customerStats = {
                            "_id": data.fullDocument.customer_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": data.fullDocument.total_amount,
                                "total_tax": data.fullDocument.total_tax,
                                "accepted_order_count": 1
                            },
                        }

                        const dailyUserStats = {
                            "tenant_id": data.fullDocument.tenant_id,
                            "branch_id": data.fullDocument.branch_id,
                            "sales_user_role_id": data.fullDocument.sales_user_role_id,
                            "$inc": {
                                "day_sales_amount": data.fullDocument.total_amount,
                                "day_sales_tax": data.fullDocument.total_tax
                            },
                            "date": dateFormat
                        }
                        const updateTopCatArr = await topCatArr(data.fullDocument._id)
                        const catPromises = []

                        updateTopCatArr.forEach((catStats => {
                            const categoryStats = {
                                "tenant_id": data.fullDocument.tenant_id,
                                "category_id": catStats.category_id,
                                "$inc": {
                                    "sales_amount": catStats.base_price,
                                    "tax_amount": catStats.tax,
                                    "quantity": catStats.quantity
                                },
                                "date": dateFormat
                            }

                            catPromises.push(
                                CategoryStatSchema.updateOne(
                                    {
                                        "category_id": catStats.category_id,
                                        "tenant_id": data.fullDocument.tenant_id,
                                        "date": dateFormat
                                    },
                                    categoryStats,
                                    {
                                        "upsert": true
                                    }
                                )
                            )
                        }))

                        promises.push(
                            OrderSalesStatSchema.updateOne(
                                {
                                    "_id": data.fullDocument.sales_user_role_id
                                },
                                salesPersonStats,
                                {
                                    "upsert": true
                                }
                            )
                        )

                        promises.push(
                            OrderSalesStatSchema.updateOne(
                                {
                                    "_id": data.fullDocument.customer_user_role_id
                                },
                                customerStats,
                                {
                                    "upsert": true
                                }
                            )
                        )

                        promises.push(
                            SaleStatisticSchema.updateOne(
                                {
                                    "sales_user_role_id": data.fullDocument.sales_user_role_id,
                                    "date": dateFormat,
                                    "branch_id": data.fullDocument.branch_id
                                },
                                dailyUserStats,
                                {
                                    "upsert": true
                                }
                            )
                        )
                        promises.push(Promise.all(catPromises))
                        checkAppliedDealInOrderItems(data.fullDocument._id, order_status)
                    }
                }
                else if (data.operationType === "update" && updateOrderCheck) {

                    if (
                        [
                            ORDER_STATUS_TYPES.CANCELLED,
                            ORDER_STATUS_TYPES.ON_HOLD
                        ].includes(order_status)
                    ) {
                        const orderStatusTrack = data.fullDocument?.order_status_track || []
                        const orderStatusTrackLength = orderStatusTrack.length

                        // for skipping rejected orders
                        const rejectedCustomerOrder =
                            order_app_type === ORDER_APP_TYPE.CUSTOMER_APP &&
                            orderStatusTrackLength === 2 &&
                            orderStatusTrack[0].order_status === ORDER_STATUS_TYPES.PENDING

                        /* logger.info("In case 2", {
                            fullDocument: data.fullDocument,
                            rejectedCustomerOrder,
                        }) */

                        const salesPersonStats = {
                            "_id": data.fullDocument.sales_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": (-1) * data.fullDocument.total_amount,
                                "total_tax": (-1) * data.fullDocument.total_tax,
                                "accepted_order_count": -1
                            },
                        }

                        const customerStats = {
                            "_id": data.fullDocument.customer_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": (-1) * (data.fullDocument.total_amount),
                                "total_tax": (-1) * (data.fullDocument.total_tax),
                                "accepted_order_count": -1
                            },
                        }

                        const dailyUserStats = {
                            "tenant_id": data.fullDocument.tenant_id,
                            "branch_id": data.fullDocument.branch_id,
                            "sales_user_role_id": data.fullDocument.sales_user_role_id,
                            "$inc": {
                                "day_sales_amount": (-1) * data.fullDocument.total_amount,
                                "day_sales_tax": (-1) * data.fullDocument.total_tax
                            },
                            "date": dateFormat
                        }

                        const updateTopCatArr = await topCatArr(data.fullDocument._id)
                        const catPromises = []

                        updateTopCatArr.forEach((catStats => {
                            const categoryStats = {
                                "tenant_id": data.fullDocument.tenant_id,
                                "category_id": catStats.category_id,
                                "$inc": {
                                    "sales_amount": catStats.base_price * (-1),
                                    "tax_amount": catStats.tax * (-1),
                                    "quantity": catStats.quantity * (-1)
                                },
                                "date": dateFormat
                            }

                            catPromises.push(
                                CategoryStatSchema.updateOne(
                                    {
                                        "category_id": catStats.category_id,
                                        "tenant_id": data.fullDocument.tenant_id,
                                        "date": dateFormat
                                    },
                                    categoryStats,
                                    {
                                        "upsert": false
                                    }
                                )
                            )
                        }))

                        if (
                            !rejectedCustomerOrder &&
                            orderStatusTrackLength >= 2 &&
                            ![
                                ORDER_STATUS_TYPES.CANCELLED,
                                ORDER_STATUS_TYPES.ON_HOLD,
                            ].includes(orderStatusTrack[orderStatusTrackLength - 2].order_status)
                        ) {
                            promises.push(
                                OrderSalesStatSchema.updateOne(
                                    {
                                        "_id": data.fullDocument.sales_user_role_id,
                                        "accepted_order_count": { "$gt": 0 }
                                    },
                                    salesPersonStats,
                                    {
                                        "upsert": false
                                    }
                                )
                            )

                            promises.push(
                                OrderSalesStatSchema.updateOne(
                                    {
                                        "_id": data.fullDocument.customer_user_role_id,
                                        "accepted_order_count": { "$gt": 0 }
                                    },
                                    customerStats,
                                    {
                                        "upsert": false
                                    }
                                )
                            )

                            promises.push(
                                SaleStatisticSchema.updateOne(
                                    {
                                        "sales_user_role_id": data.fullDocument.sales_user_role_id,
                                        "date": dateFormat,
                                        "branch_id": data.fullDocument.branch_id
                                    },
                                    dailyUserStats,
                                    {
                                        "upsert": false
                                    }
                                )
                            )
                            promises.push(Promise.all(catPromises))
                            checkAppliedDealInOrderItems(data.fullDocument._id, order_status)
                        }
                    }
                    else if (
                        [
                            ORDER_STATUS_TYPES.RECEIVED,
                            ORDER_STATUS_TYPES.RELEASED
                        ].includes(order_status)
                    ) {
                        /**
                         *  Increment statistics if we get order in RECEIVED or RELEASED status,
                         *  either from PENDING or ON_HOLD status.
                         */
                        let incrementStatistics = false

                        const orderStatusTrack = data.fullDocument?.order_status_track || []
                        const orderStatusTrackLength = orderStatusTrack.length

                        if (orderStatusTrackLength) {
                            if (
                                (
                                    orderStatusTrackLength === 1 &&
                                    orderStatusTrack[0].order_status === ORDER_STATUS_TYPES.RECEIVED
                                ) ||
                                (
                                    orderStatusTrackLength > 1 && (
                                        [
                                            ORDER_STATUS_TYPES.PENDING,
                                            ORDER_STATUS_TYPES.ON_HOLD
                                        ].includes(orderStatusTrack[orderStatusTrackLength - 2].order_status)
                                    )
                                )
                            ) {
                                incrementStatistics = true
                            }
                        }

                        /* logger.info("In case 3", {
                            fullDocument: data.fullDocument,
                            incrementStatistics,
                        }) */

                        const salesPersonStats = {
                            "_id": data.fullDocument.sales_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": data.fullDocument.total_amount,
                                "total_tax": data.fullDocument.total_tax,
                                "accepted_order_count": 1
                            },
                        }

                        const customerStats = {
                            "_id": data.fullDocument.customer_user_role_id,
                            "tenant_id": data.fullDocument.tenant_id,
                            "$inc": {
                                "total_sales": data.fullDocument.total_amount,
                                "total_tax": data.fullDocument.total_tax,
                                "accepted_order_count": 1
                            },
                        }

                        const dailyUserStats = {
                            "tenant_id": data.fullDocument.tenant_id,
                            "branch_id": data.fullDocument.branch_id,
                            "sales_user_role_id": data.fullDocument.sales_user_role_id,
                            "$inc": {
                                "day_sales_amount": data.fullDocument.total_amount,
                                "day_sales_tax": data.fullDocument.total_tax
                            },
                            "date": dateFormat
                        }
                        const updateTopCatArr = await topCatArr(data.fullDocument._id)
                        const catPromises = []

                        updateTopCatArr.forEach((catStats => {
                            const categoryStats = {
                                "tenant_id": data.fullDocument.tenant_id,
                                "category_id": catStats.category_id,
                                "$inc": {
                                    "sales_amount": catStats.base_price,
                                    "tax_amount": catStats.tax,
                                    "quantity": catStats.quantity
                                },
                                "date": dateFormat
                            }

                            catPromises.push(
                                CategoryStatSchema.updateOne(
                                    {
                                        "category_id": catStats.category_id,
                                        "tenant_id": data.fullDocument.tenant_id,
                                        "date": dateFormat
                                    },
                                    categoryStats,
                                    {
                                        "upsert": true
                                    }
                                )
                            )
                        }))

                        if (incrementStatistics) {
                            promises.push(
                                OrderSalesStatSchema.updateOne(
                                    {
                                        "_id": data.fullDocument.sales_user_role_id
                                    },
                                    salesPersonStats,
                                    {
                                        "upsert": true
                                    }
                                )
                            )

                            promises.push(
                                OrderSalesStatSchema.updateOne(
                                    {
                                        "_id": data.fullDocument.customer_user_role_id
                                    },
                                    customerStats,
                                    {
                                        "upsert": true
                                    }
                                )
                            )

                            promises.push(
                                SaleStatisticSchema.updateOne(
                                    {
                                        "sales_user_role_id": data.fullDocument.sales_user_role_id,
                                        "date": dateFormat,
                                        "branch_id": data.fullDocument.branch_id
                                    },
                                    dailyUserStats,
                                    {
                                        "upsert": true
                                    }
                                )
                            )
                            promises.push(Promise.all(catPromises));
                            checkAppliedDealInOrderItems(data.fullDocument._id, order_status);
                        }
                    }
                }
                await Promise.all(promises)
            }
            catch (error) {
                logger.error(error)
            }
        })
}

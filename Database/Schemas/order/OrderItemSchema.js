const mongoose = require("mongoose");
const { TaxCalculationSchema } = require("./common");
const { ProductDealInfoSchema } = require("./common");

const Schema = mongoose.Schema

const OrderItemsSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true
        },
        branch_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        customer_user_role_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        sales_user_role_id: {
            type: Schema.Types.ObjectId,
            required: true
        },
        order_id: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "orders",
            index: true
        },
        sort_order: {
            type: Number,
            required: true,
        },
        product_variant_id: {
            type: Schema.Types.ObjectId,
            ref: "products_2.0",
            required: true
        },
        variant_id: {
            type: String,
            trim: true,
        },
        parent_id: {
            type: Schema.Types.ObjectId,
            ref: "products_2.0",
            trim: true,
        },
        variant_value_id: {
            type: String,
            ref: "variant_types_2.0",
            trim: true,
        },
        group_value_id: {
            type: String,
            ref: "variant_types_2.0",
            trim: true,
        },
        product_name: {
            type: String,
            required: true
        },
        product_secondary_name: {
            type: String
        },
        variant_name: {
            type: String
        },
        variant_secondary_name: {
            type: String
        },
        group_name: {
            type: String
        },
        group_secondary_name: {
            type: String
        },
        item_number: {
            type: String,
            required: true
        },
        parent_item_number: {
            type: String,
        },
        quantity: {
            type: Number,
            required: true
        },
        base_price: {
            type: Number,
            required: true
        },
        tax: {
            type: Number,
            required: true
        },
        original_price: { // for storing display price
            type: Number
        },
        reduce_inventory: {
            type: Boolean,
            default: false
        },
        master_price_id: {
            type: Schema.Types.ObjectId,
            ref: "master_prices",
            required: true
        },
        item_comment: {
            type: [String],
        },
        uom_id: {
            type: Schema.Types.ObjectId,
            ref: "master_units",
            required: true,
        },
        uom_name: {
            type: String,
            required: true
        },
        tax_calculation_info: {
            type: TaxCalculationSchema
        },
        deal_info: {
            type: ProductDealInfoSchema
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

module.exports = mongoose.model("order_items", OrderItemsSchema)

const { default: mongoose } = require("mongoose");
const Schema = mongoose.Schema;

const ProductTagSchema = new Schema({
    _id: {
        type: String,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    barcode: {
        type: String,
        required: true,
        trim: true
    },
    product_variant_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "products_2.0",
        index: true
    },
    is_active: {
        type: Boolean,
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

ProductTagSchema.virtual("productDetails", {
    localField: "product_variant_id",
    foreignField: "_id",
    ref: "products_2.0",
    justOne: true
});


ProductTagSchema.set('toJSON', { virtuals: true });
ProductTagSchema.set('toObject', { virtuals: true });
module.exports = mongoose.model("product_2.0_barcodes", ProductTagSchema);
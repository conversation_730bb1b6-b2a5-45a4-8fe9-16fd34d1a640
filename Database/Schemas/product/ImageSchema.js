const ImageSchema = new mongoose.Schema({
    product_variant_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "products_2.0",
        required: true,
    },
    group_id: {
        type: String,
        ref: "variant_types_2.0",
    },
    image_name: {
        type: String,
        required: true,
        trim: true,
    },
    image_size: {
        type: Number,
        required: true,
    },
    image_number: {
        type: Number,
        required: true
    },
    s3_url: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        required: true,
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    updated_by: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */

/* Useful in aggregations */
ImageSchema.index({
    // Equality fields
    product_variant_id: 1,
    group_id: 1,

    // Sort fields
    image_number: 1,
})

/* Useful in aggregations */
ImageSchema.index({
    // Equality fields
    group_id: 1,

    // Sort fields
    image_number: 1,
})

/* Useful in add, update and list images */
ImageSchema.index({
    // Equality fields
    tenant_id: 1,
    image_name: 1,

    // Range fields
    created_at: 1,
})

ImageSchema.index(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "images_updated_sync_idx" }
);

module.exports = mongoose.model('images_2.0', ImageSchema);

/** listen for updates to documents in this collection  */
require("../change_streams/ImageChangeStream")

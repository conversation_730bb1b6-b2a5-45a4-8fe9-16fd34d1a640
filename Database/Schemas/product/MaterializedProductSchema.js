const {
    SELECTED_ATTRIBUTES_SET_TYPE,
    LISTING_PRODUCT_TYPE,
    VARIANT_TYPE_ENUM,
} = require("../../../Configs/constants")

const {
    InventorySchema,
    PriceMapSchema,
    PackagingMapSchema,
    VariantTypeSchema,
    VariantTypeValueSchema,
} = require("./common")

const AttributeChildSchema = new mongoose.Schema(
    {
        attribute_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "master_attributes",
            required: true
        },
        type: {
            type: String,
            default: SELECTED_ATTRIBUTES_SET_TYPE.MANUAL,
            required: true,
            trim: true,
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
        value: {
            type: String,
            trim: true,
        }
    },
    {
        _id: false
    }
)

const VariantsChildSchema = new mongoose.Schema(
    {
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(VARIANT_TYPE_ENUM)
        },
        values: {
            type: [VariantTypeValueSchema]
        }
    },
    {
        _id: false
    }
)

const GroupsChildSchema = new mongoose.Schema(
    {
        type: {
            type: String,
            required: true,
            trim: true
        },
        values: {
            type: [VariantTypeValueSchema]
        }
    },
    {
        _id: false
    }
)

const ProductVariantSchema = new mongoose.Schema({
    /** Identifiers */
    _id: { // Specified here, so that `_id` will be stored in DB as first prop. in object
        type: mongoose.Schema.Types.ObjectId,
    },
    item_number: {
        type: String,
        required: true,
        trim: true,
    },
    barcodes: {
        type: [String],
    },


    /** Booleans */
    is_active: {
        type: Boolean,
        required: true
    },
    is_restocked: {
        type: Boolean,
        required: true,
        default: false
    },
    restocked_at: {
        type: Date,
        required: function () {
            return this.is_restocked
        },
    },


    /** Variant fields */
    variant_order: {
        type: Number,
        required: true,
    },
    variant: {
        type: VariantTypeSchema,
        required: true,
    },
    group: {
        type: VariantTypeSchema,
        default: null,
    },


    /** Stock information */
    inventory_mappings: [{
        type: InventorySchema,
        required: true,
    }],
    packaging_map: {
        type: PackagingMapSchema,
        required: true,
    },
    price_mappings: [{
        type: PriceMapSchema,
        required: true,
    }],
})

//NOTE: validate.validator or validate function check won't run if the property is undefined
const MaterializedProductSchema = new mongoose.Schema(
    {
        /** Identifiers */
        tenant_id: {
            type: Number,
            required: true,
        },
        item_number: {
            type: String,
            required: true,
            trim: true,
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(LISTING_PRODUCT_TYPE)
        },
        barcodes: {
            type: [String],
        },


        /** Basic Information */
        title: {
            type: String,
            required: true,
            trim: true,
        },
        secondary_language_title: {
            type: String,
            required: true,
            trim: true,
        },
        description: {
            type: String,
            trim: true,
            default: "",
        },
        secondary_language_description: {
            type: String,
            trim: true,
            default: "",
        },


        search_identifiers: { // For type PARENT
            type: {
                active_variant_item_numbers: {
                    type: [String]
                },
                inactive_variant_item_numbers: {
                    type: [String]
                },
                active_variant_barcodes: {
                    type: [String]
                },
                inactive_variant_barcodes: {
                    type: [String]
                },
            },
            _id: false,
            required: function () {
                return this.type === LISTING_PRODUCT_TYPE.PARENT
            },
        },


        /** Classification and Hierarchy */
        product_order: {
            type: Number,
            required: true
        },
        family: {
            _id: {
                type: mongoose.Schema.Types.ObjectId,
                ref: "category",
                required: true,
            },
            name: {
                type: String,
                trim: true,
                required: true,
            }
        },
        category: {
            type: {
                _id: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: "category",
                    required: true,
                },
                name: {
                    type: String,
                    trim: true,
                    required: true,
                }
            },
            default: null
        },
        subcategory: {
            type: {
                _id: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: "category",
                    required: true,
                },
                name: {
                    type: String,
                    trim: true,
                    required: true,
                }
            },
            default: null
        },
        brand: {
            type: {
                _id: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: "master_brands",
                    required: true,
                },
                name: {
                    type: String,
                    trim: true,
                    required: true,
                }
            },
            required: true,
        },


        /** Booleans */
        is_active: {
            type: Boolean,
            required: true
        },
        is_restocked: {
            type: Boolean,
            required: true,
            default: false
        },
        restocked_at: {
            type: Date,
            required: function () {
                return this.is_restocked
            },
        },

        /** Variant fields */
        variants: {
            type: VariantsChildSchema
        },
        groups: {
            type: GroupsChildSchema,
            default: function () {
                return this.type === LISTING_PRODUCT_TYPE.PARENT ? null : undefined
            },
        },
        variant_count: {
            type: Number,
            required: function () {
                return this.type === LISTING_PRODUCT_TYPE.PARENT
            },
        },
        product_variants: {
            type: [ProductVariantSchema],
            default: function () {
                return this.type === LISTING_PRODUCT_TYPE.PARENT ? [] : undefined
            },
        },


        /** Stock information */
        inventory_mappings: [{
            type: InventorySchema,
            required: true,
        }],
        packaging_map: { // For type SINGLE
            type: PackagingMapSchema,
            required: function () {
                return this.type === LISTING_PRODUCT_TYPE.SINGLE
            },
        },
        price_mappings: [{
            type: PriceMapSchema,
            required: true,
        }],
        tags: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: "product_2.0_tags"
        }],
        attributes: [{
            type: AttributeChildSchema,
        }],


        /** Record owner data */
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
MaterializedProductSchema.index(
    {
        // Equality fields
        tenant_id: 1,
        is_active: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "mv_products_updated_sync_idx" }
)

module.exports = mongoose.model("mv_products", MaterializedProductSchema)

/** listen for updates to documents in this collection  */
require("../change_streams/MaterializedProductChangeStream")

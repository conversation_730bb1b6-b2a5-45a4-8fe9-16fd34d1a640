const DeletedMaterializedProductSchema = new mongoose.Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        deleted_at: {
            type: Date,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

DeletedMaterializedProductSchema.index(
    { created_at: 1 },
    { name: "deleted_mv_products_cleanup_idx" }
)

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
DeletedMaterializedProductSchema.index(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,
    },
    { name: "deleted_mv_products_updated_sync_idx" }
);

module.exports = mongoose.model("deleted_mv_products", DeletedMaterializedProductSchema)

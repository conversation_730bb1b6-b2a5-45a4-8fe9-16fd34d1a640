const PriceMapSchema = new mongoose.Schema(
    {
        master_price_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "master_prices",
            required: true,
        },
        price: {
            type: Number,
            default: 0,
        },
        product_variant_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "products_2.0"
        }
    },
    {
        _id: false
    }
)

const InventorySchema = new mongoose.Schema(
    {
        branch_id: {
            type: String,
            required: true
        },
        warehouse_id: {
            type: String,
            required: true
        },
        quantity: {
            type: Number,
            default: 0,
        }
    },
    {
        _id: false
    }
)

const PackagingMapSchema = new mongoose.Schema(
    {
        uom_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "master_units",
            required: true,
        },
        uom_name: {
            type: String,
            trim: true,
            required: true,
        },
        min_qty: {
            type: Number,
            required: true,
        },
        qty_ctn: {
            type: Number,
        },
    },
    {
        _id: false
    }
)

const nameFields = {
    _id: { // Specified here, so that `_id` will be stored in DB as first prop. in object
        type: mongoose.Schema.Types.ObjectId,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        trim: true,
        default: "",
    }
}

const VariantTypeSchema = new mongoose.Schema(nameFields)

const VariantTypeValueSchema = new mongoose.Schema({
    ...nameFields,
    order: {
        type: Number,
        required: true,
    },
})

module.exports = {
    PriceMapSchema,
    InventorySchema,
    PackagingMapSchema,
    VariantTypeSchema,
    VariantTypeValueSchema,
}

const { default: mongoose } = require("mongoose")
const Schema = mongoose.Schema;

const ProductTagSchema = new Schema({
    tenant_id: {
        type: Number,
        required: true
    },
    name: {
        type: String,
        required: true
    },
    attached_count: {
        type: Number,
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("product_2.0_tags", ProductTagSchema);
const Schema = mongoose.Schema;

const {
    NEW_PRODUCT_TYPE,
    SELECTED_ATTRIBUTES_SET_TYPE,
    VARIANT_TYPE_ENUM,
} = require("../../../Configs/constants");

const { InventorySchema } = require("./common");

const NewPriceMapSchema = Schema({
    master_price_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "master_prices"
    },
    price: {
        type: Number,
        required: true
    },
    product_variant_id: {
        type: Schema.Types.ObjectId,
        ref: "products_2.0"
    }
}, { _id: false });

const ProductAttributeSchema = new Schema({
    attribute_id: {
        type: Schema.Types.ObjectId,
        ref: "master_attributes",
        required: true
    },
    type: {
        type: String,
        default: SELECTED_ATTRIBUTES_SET_TYPE.MANUAL,
        required: true
    },
    value: {
        type: String,
        // required: true
    }
}, { _id: false });

const VariantSubDocSchema = new Schema({
    type: {
        type: String,
        required: true,
        trim: true,
        enum: Object.values(VARIANT_TYPE_ENUM)
    },
    values: [{
        type: String,
        ref: "variant_types_2.0",
        trim: true
    }]
}, { _id: false });

const GroupSubDocSchema = new Schema({
    type: {
        type: String,
        required: true,
        trim: true
    },
    values: [{
        type: String,
        ref: "variant_types_2.0",
        trim: true
    }]
}, { _id: false });


//NOTE: validate.validator or validate function check won't run if the property is undefined
const ProductSchema = new Schema({
    // _id: {
    //     type: String,
    //     required: true,
    //     // FOR TYPE SINGLE & PARENT it will be normal mongoId
    //     // FOR TYPE VARIANT it will be `${parent_id}_${variant_id}_${group_id}`
    // },
    tenant_id: {
        type: Number,
        required: true,
    },
    title: {
        type: String,
        trim: true,
        validate: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.PARENT || this.get("type") === NEW_PRODUCT_TYPE.SINGLE) ? v : undefined)
        }
    },
    secondary_language_title: {
        type: String,
        trim: true
    },
    item_number: {
        type: String,
        required: true,
        trim: true
    },
    type: {
        type: String,
        required: true,
        enum: Object.values(NEW_PRODUCT_TYPE)
    },
    unique_item_number: {
        type: String,
        required: true,
        trim: true,
        unique: true // https://masteringjs.io/tutorials/mongoose/e11000-duplicate-key
        // it should throw error like => // MongoError: E11000 duplicate key error
    },
    brand_id: {
        type: Schema.Types.ObjectId,
        ref: "master_brands",
        validate: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.PARENT || this.get("type") === NEW_PRODUCT_TYPE.SINGLE) ? Boolean(v) : undefined)
        }
    },
    family_id: {
        type: Schema.Types.ObjectId,
        ref: "category",
        // required: true,
        validate: {
            validator: function (v) {
                return ((this.get("type") === NEW_PRODUCT_TYPE.PARENT || this.get("type") === NEW_PRODUCT_TYPE.SINGLE) ? v : undefined)
            }
        }
    },
    tax_id: {
        type: Schema.Types.ObjectId,
        ref: "tax_configurations"
    },
    product_order: {
        type: Number,
        // required: true
        validate: {
            validator: function (v) {
                return ((this.get("type") === NEW_PRODUCT_TYPE.PARENT || this.get("type") === NEW_PRODUCT_TYPE.SINGLE) ? (v > -1) : undefined)
            }
        }
    },
    category_id: {
        type: Schema.Types.ObjectId,
        ref: "category",
    },
    subcategory_id: {
        type: Schema.Types.ObjectId,
        ref: "category",
    },
    variants: {
        type: VariantSubDocSchema
    },
    groups: {
        type: GroupSubDocSchema
    },
    variant_count: { // only for ordering the variants
        type: Number,
        // default: 0
        validate: {
            validator: function (v) {
                return ((this.get("type") === NEW_PRODUCT_TYPE.PARENT) ? (v > -1) : undefined)
            }
        }
    },
    packaging_map: {
        uom_id: {
            type: Schema.Types.ObjectId,
            ref: "master_units"
        },
        min_qty: {
            type: Number,
        },
        qty_ctn: {
            type: Number,
        },
    },
    price_mappings: [{
        type: NewPriceMapSchema,
    }],
    inventory_mappings: [
        { type: InventorySchema, }
    ],
    description: {
        type: String,
    },
    secondary_language_description: {
        type: String,
    },
    attribute_set: {
        type: Schema.Types.ObjectId,
        ref: "master_attributes_set"
    },
    attributes: [{
        type: ProductAttributeSchema,
    }],
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    tags: [{
        type: Schema.Types.ObjectId,
        ref: "product_2.0_tags"
    }],
    created_by: {
        type: Schema.Types.ObjectId,
        required: false
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        required: false
    },

    variant_item_numbers: { // for saving variant's items number in parent so searching can be done without joining child variants of the parent
        type: [String]
    },

    active_variant_item_numbers: { // for saving active variant's items number in parent so searching can be done without joining child variants of the parent
        type: [String]
    },

    inactive_variant_item_numbers: { // for saving inactive variant's items number in parent so searching can be done without joining child variants of the parent
        type: [String]
    },

    barcodes: {
        type: [String],
        default: []
    },

    active_variant_barcodes: { // for saving active variant's barcodes in parent so searching can be done without joining child variants of the parent
        type: [String],
        default: []
    },

    inactive_variant_barcodes: { // for saving inactive variant's barcodes in parent so searching can be done without joining child variants of the parent
        type: [String],
        default: []
    },

    // properties which will contain only for VARIANT type documents (products)
    variant_id: {
        type: String,
        validate: {
            validator: function (v) {
                return (
                    (this.get("type") === NEW_PRODUCT_TYPE.VARIANT)
                        ? v === `${this.get("parent_id")}_${this.get("variant_value_id")}${this.get("group_value_id") ? `_${this.get("group_value_id")}` : ""}` : undefined
                )
            }
        },
        unique: true,
        sparse: true,
    },
    parent_id: {
        type: Schema.Types.ObjectId,
        ref: "products_2.0",
        validator: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.VARIANT) ? v : undefined)
        },
        index: true
    },
    variant_value_id: {
        type: String,
        ref: "variant_types_2.0",
        validator: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.VARIANT) ? v : undefined)
        }
    },
    group_value_id: {
        type: String,
        ref: "variant_types_2.0",
        validator: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.VARIANT) ? v : undefined)
        }
    },
    variant_order: {
        type: Number,
        validator: function (v) {
            return ((this.get("type") === NEW_PRODUCT_TYPE.VARIANT) ? (v > -1) : undefined)
        }
    },
    is_restocked: {
        type: Boolean,
        required: true,
        default: false
    },
    restocked_at: {
        type: Date,
        required: function (v) {
            return this.get("is_restocked")
        },
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});


ProductSchema.virtual('variantProducts', {
    localField: "_id",
    foreignField: "parent_id",
    ref: "products_2.0",
    match: { is_deleted: false },
    options: { sort: { variant_order: 1 } },
});

ProductSchema.virtual("images", {
    localField: "_id",
    foreignField: "product_variant_id",
    ref: "images_2.0",
});

ProductSchema.virtual('cartItem', {
    localField: "_id",
    foreignField: "product_variant_id",
    ref: "cart_items_2.0",
    justOne: true,
    limit: 1
});

ProductSchema.virtual('product_deal_info', {
    localField: "_id",
    foreignField: "product_id",
    ref: "deal_products",
    justOne: true,
    limit: 1
});

ProductSchema.set('toJSON', { virtuals: true })
ProductSchema.set('toObject', { virtuals: true })

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
ProductSchema.index({
    // Equality fields
    "tenant_id": 1,
    "is_active": 1,
    "is_deleted": 1,
    "type": 1,
    "price_mappings.master_price_id": 1,
    "brand_id": 1,
    "family_id": 1,
    "category_id": 1,
    "subcategory_id": 1,

    // Sort fields
    "product_order": 1,

    // Range fields
    "created_at": 1,
    "price_mappings.price": 1,
})

ProductSchema.index({
    // Equality fields
    "tenant_id": 1,
    "is_active": 1,
    "is_deleted": 1,
    "type": 1,
    "price_mappings.master_price_id": 1,

    // Sort fields
    "created_at": -1,

    // Range fields
    "price_mappings.price": 1,
    "packaging_map.qty_ctn": 1,
})

ProductSchema.index({
    // Equality fields
    "tenant_id": 1,
    "is_deleted": 1,
    "type": 1,
})

module.exports = mongoose.model("products_2.0", ProductSchema);

/** listen for updates to documents in this collection  */
require("../change_streams/ProductChangeStream")

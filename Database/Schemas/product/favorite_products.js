const mongoose = require("mongoose");

const Schema = mongoose.Schema;

const FavoriteProductSchema = new Schema({
    user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    tenant_id: {
        type: Number,
        required: true
    },
    product_variant_id: {
        type: Schema.Types.ObjectId,
        ref: "products_2.0",
        required: true,
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
FavoriteProductSchema.index({
    product_variant_id: 1,
    user_role_id: 1,
    tenant_id: 1,
})

FavoriteProductSchema.index(
    {
        tenant_id: 1,
        user_role_id: 1,
        updated_at: 1,
        _id: 1,
    },
    { name: "favorite_products_updated_sync_idx" }
);

module.exports = mongoose.model("favorite_products_2.0", FavoriteProductSchema);

const { PRODUCT_VARIANT_TYPES } = require("../../../Configs/constants");

const VariantTypesSchema = new mongoose.Schema({
    _id: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        required: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        trim: true,
    },
    product_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "products_2.0"
    },
    type: {
        type: String,
        required: true,
        enum: Object.values(PRODUCT_VARIANT_TYPES)
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    order: {
        type: Number,
        default: 0
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

VariantTypesSchema.virtual("groupImages", {
    localField: "_id",
    foreignField: "group_id",
    ref: "images_2.0"
});

VariantTypesSchema.set('toJSON', { virtuals: true })
VariantTypesSchema.set('toObject', { virtuals: true })

module.exports = mongoose.model("variant_types_2.0", VariantTypesSchema);

/** listen for updates to documents in this collection  */
require("../change_streams/VariantTypeChangeStream")

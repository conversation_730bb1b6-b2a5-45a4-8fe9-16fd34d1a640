const mongoose = require('mongoose');
const { DEAL_TYPE, DEAL_STATUS, DEAL_SORT_TYPE } = require("../../../Configs/constants");

const Schema = mongoose.Schema;

const DealSchema = new Schema({
    deal_id: {
        type: String
    },
    tenant_id: {
        type: Number,
        required: true,
    },
    price_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "master_prices"
    },
    deal_type: {
        type: String,
        required: true,
        enum: Object.values(DEAL_TYPE)
    },
    deal_name: {
        type: String,
    },
    secondary_deal_name: {
        type: String,
    },
    deal_from_date: {
        type: Date,
    },
    deal_to_date: {
        type: Date,
    },
    sales_persons: {
        type: [Schema.Types.ObjectId],
    },
    deal_status: {
        type: String,
        enum: Object.values(DEAL_STATUS),
        default: DEAL_STATUS.PROGRESS
    },
    sort_type: {
        type: String,
        enum: Object.values(DEAL_SORT_TYPE),
        default: DEAL_SORT_TYPE.MANUAL 
    },
    deal_product_counter: {
        type: Number,
        default: 0
    },
    created_by: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        required: true,
    },

    // ------ DEAL STATISTICS PROPERTIES START ------ //
    click_count: { // it will be unique clicks based on user
        type: Number,
        default: 0
    },
    view_count: {
        type: Number,
        default: 0
    },
    add_to_cart_count: {
        type: Number,
        default: 0
    },
    unit_sold_count: {
        type: Number,
        default: 0
    },
    sales_base_price: {
        type: Number,
        default: 0
    },
    sales_tax: {
        type: Number,
        default: 0
    },
    // ------ DEAL STATISTICS PROPERTIES END ------ //
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

/**
 * @description Followed ESR rule for below compound index
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
DealSchema.index({
    // Equality fields
    tenant_id: 1,
    price_id: 1,
    sales_persons: 1,

    // Range fields
    deal_status: 1,
});

DealSchema.index(
    {
        // Equality fields
        tenant_id: 1,

        // Sort fields
        updated_at: 1,
        _id: 1,

        // Range fields
        deal_status: 1,
    },
    { name: "deals_updated_sync_idx" }
)

module.exports = mongoose.model('deals', DealSchema);

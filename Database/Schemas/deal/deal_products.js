const mongoose = require("mongoose");
const { DISCOUNT_TYPE, DEAL_TYPE } = require("../../../Configs/constants");

const Schema = mongoose.Schema;

const TierSchema = new Schema({
	product_qty: {
		type: Number
	},
	price: {
		type: Number,
	}
}, { _id: false });

const DealProductSchema = new Schema(
	{
		deal_id: {
			type: Schema.Types.ObjectId,
			required: true,
			ref: 'deals',
		},
		price_id: {
			type: Schema.Types.ObjectId,
			required: true,
			ref: "master_prices"
		},
		tenant_id: {
			type: Number,
			required: true,
		},
		// deal_type: {
		//     type: String,
		//     enum: Object.values(DEAL_TYPE)
		// },
		product_id: {
			type: Schema.Types.ObjectId,
			required: true,
			ref: 'products_2.0',
		},
		parent_id: {
			type: Schema.Types.ObjectId,
			ref: 'products_2.0',
		},
		is_active: {
			type: <PERSON><PERSON>an,
			default: true,
		},

		// ------ Discount related fields ------
		discount_type: {
			type: String,
			enum: Object.values(DISCOUNT_TYPE),
		},
		percent: {
			type: Number,
		},
		amount: {
			type: Number,
		},
		discounted_price: {
			type: Number,
		},

		// ------ Bulk price related fields ------
		first_tier: {
			type: TierSchema,
		},
		second_tier: {
			type: TierSchema,
		},
		third_tier: {
			type: TierSchema,
		},

		// ------ Buy x Get Y related fields ------
		buy_product: {
			type: Number,
		},
		free_product: {
			type: Number,
		},

		deal_product_sequence: {
			type: Number,
		},
		deal_from_date: {
			type: Date,
		},
		deal_to_date: {
			type: Date,
		},
		created_by: {
			type: Schema.Types.ObjectId,
			required: true,
		},
		updated_by: {
			type: Schema.Types.ObjectId,
			required: true,
		},

		// ------ DEAL STATISTICS PROPERTIES START ------ //
		click_count: { // it will be unique clicks based on user
			type: Number,
			default: 0
		},
		view_count: {
			type: Number,
			default: 0
		},
		add_to_cart_count: {
			type: Number,
			default: 0
		},
		unit_sold_count: {
			type: Number,
			default: 0
		},
		sales_base_price: {
			type: Number,
			default: 0
		},
		sales_tax: {
			type: Number,
			default: 0
		},
		// ------ DEAL STATISTICS PROPERTIES END ------ //
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
	},
);

DealProductSchema.index({
	deal_id: 1,
	deal_product_sequence: 1
});

/**
 * @description Followed ESR rule for below compound indexes
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
DealProductSchema.index({
	// Equality fields
	product_id: 1,
	price_id: 1,

	// Range fields
	deal_from_date: 1,
	deal_to_date: 1,
})

DealProductSchema.index({
	// Equality fields
	parent_id: 1,
	price_id: 1,

	// Range fields
	deal_from_date: 1,
	deal_to_date: 1,
})

DealProductSchema.index(
	{
		// Equality fields
		tenant_id: 1,
		is_active: 1,

		// Sort fields
		updated_at: 1,
		_id: 1,

		// Range fields
		deal_id: 1,
	},
	{ name: "deal_products_updated_sync_idx" }
);

module.exports = mongoose.model('deal_products', DealProductSchema);

const mongoose = require('mongoose');

const Schema = mongoose.Schema;
const DealClickStatistics = new Schema({
    /**
     *  _id logic:
     * 
     *  If you have _id with 2 mongo ids (i.e. "65040702e592af6b110b9ae7_6504070e77db53d191df6005") then, 
     *  - first id represents deal_product_id and, 
     *  - second id can be any userRoleId (Owner, Admin, Supervisor, Salesperson, Customer)
     * 
     *  If you have _id with 3 mongo ids (i.e. "65040702e592af6b110b9ae7_6504070e77db53d191df6005_650407e846e106c264cd96d2") then, 
     *  - first id represents deal_product_id and, 
     *  - second id can be any userRoleId (Owner, Admin, Supervisor, Salesperson) and,
     *  - third id represents customer's userRoleId (Customer)
     */
    _id: {
        type: String,
        required: true
    },
    deal_product_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    deal_id: {
        type: Schema.Types.ObjectId,
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    },
});
module.exports = mongoose.model('deal_click_statistics', DealClickStatistics);
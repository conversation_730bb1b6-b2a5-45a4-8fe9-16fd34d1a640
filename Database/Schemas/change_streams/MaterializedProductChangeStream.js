const MaterializedProductSchema = require("../product/MaterializedProductSchema")
const DeletedMaterializedProductQueue = require("../../../SQS/DeletedMaterializedProductQueue")

const {
    MONGODB,
    VALUES,
} = require("../../../Configs/constants")

// On server, listen for updates to documents in a "mv_products" collection
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    MaterializedProductSchema
        .watch(
            [ /** pipeline */
                {
                    "$match": {
                        "operationType": {
                            "$in": [
                                MONGODB.OPERATION_TYPE.DELETE,
                            ]
                        },
                    },
                }
            ],
            { /** options */
                "fullDocumentBeforeChange": "required", // Get the document before it was deleted
            }
        )
        .on("change", async (event) => {
            try {
                const {
                    operationType,
                    documentKey: {
                        _id: documentId,
                    }, // Document that contains the `_id` value of the document created or modified by the CRUD operation.
                    fullDocumentBeforeChange: {
                        tenant_id,
                    }, // This contains the document before deletion
                } = event || {}

                const sendMessageToSQS = async () => {
                    await DeletedMaterializedProductQueue.sendMessageToSQS({
                        MessageGroupId: String(tenant_id),
                        MessageBody: {
                            // Fields to make content unique
                            documentId,

                            // Fields to update
                            tenantId: tenant_id,
                            deletedAt: new Date().toISOString(),
                        },
                    })
                }

                switch (operationType) {
                    case MONGODB.OPERATION_TYPE.DELETE: {
                        sendMessageToSQS()
                        break
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error: (MaterializedProductChangeStream) Event listen and `sendMessageToSQS` failed",
                    event,
                })
            }
        })
}

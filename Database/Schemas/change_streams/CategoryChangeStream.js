const CategorySchema = require("../CategorySchema")

const ProductMetadataUpdateQueue = new (require("../../../SQS/ProductMetadataUpdateQueue"))

const {
    MONGODB,
    VALUES,
} = require("../../../Configs/constants")

// On server, listen for updates to documents in a "categories" collection
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    CategorySchema
        .watch(
            [ /** pipeline */
                {
                    "$match": {
                        "operationType": {
                            "$in": [
                                MONGODB.OPERATION_TYPE.UPDATE,
                            ]
                        },
                        "updateDescription.updatedFields.category_name": {
                            "$exists": true
                        }
                    },
                }
            ],
            { /** options */
                "fullDocument": "updateLookup", // By default, change stream returns the delta of those fields modified by an update operation, instead of the entire updated document. So this option returns the most current majority-committed version of the updated document.
            }
        )
        .on("change", async (event) => {
            try {
                const {
                    operationType,
                    documentKey: {
                        _id: documentId,
                    }, // Document that contains the `_id` value of the document created or modified by the CRUD operation.

                    updateDescription: { // A document describing the fields that were updated or removed by the update operation.
                        updatedFields: {
                            category_name: categoryName,
                        } = {},
                    } = {},

                    fullDocument: { // The document inserted & updated by the operation.
                        tenant_id,
                        type,
                        updated_at: updatedAt,
                    } = {}
                } = event || {}

                const MessageGroupId = `${tenant_id}_${VALUES.category.CATEGORY}_${documentId}`

                const sendMessageToSQS = async () => {
                    await ProductMetadataUpdateQueue.sendMessageToSQS({
                        MessageGroupId,
                        MessageBody: {
                            // Fields to make content unique
                            documentId,
                            updatedAt,

                            // Miscellaneous
                            type,

                            // Fields to update
                            categoryName,
                        },
                    })
                }

                switch (operationType) {
                    case MONGODB.OPERATION_TYPE.UPDATE: {
                        const hasCatName = Object.hasOwn(event.updateDescription.updatedFields, "category_name")

                        if (!hasCatName) {
                            // If `category_name` is not changed in `update` operationType then skip it
                            return
                        }
                        sendMessageToSQS()
                        break
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error: (CategoryChangeStream) Event listen and `sendMessageToSQS` failed",
                    event,
                })
            }
        })
}

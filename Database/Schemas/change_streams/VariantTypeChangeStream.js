const VariantTypeSchema = require("../product/variant_types")

const ProductMetadataUpdateQueue = new (require("../../../SQS/ProductMetadataUpdateQueue"))

const {
    MONGODB,
    PRODUCT_VARIANT_TYPES,
    VARIANT_TYPES,
    VALUES,
} = require("../../../Configs/constants")

// On server, listen for updates to documents in a "variant_types_2.0" collection
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    VariantTypeSchema
        .watch(
            [ /** pipeline */
                {
                    "$match": {
                        "operationType": {
                            "$in": [
                                MONGODB.OPERATION_TYPE.UPDATE,
                            ]
                        },
                        "$or": [
                            {
                                "updateDescription.updatedFields.name": {
                                    "$exists": true
                                }
                            },
                            {
                                "updateDescription.updatedFields.secondary_language_name": {
                                    "$exists": true
                                }
                            },
                            {
                                "updateDescription.updatedFields.order": {
                                    "$exists": true
                                }
                            },
                        ],
                    },
                }
            ],
            { /** options */
                "fullDocument": "updateLookup", // By default, change stream returns the delta of those fields modified by an update operation, instead of the entire updated document. So this option returns the most current majority-committed version of the updated document.
            }
        )
        .on("change", async (event) => {
            try {
                const {
                    operationType,
                    documentKey: {
                        _id: documentId,
                    }, // Document that contains the `_id` value of the document created or modified by the CRUD operation.

                    updateDescription: { // A document describing the fields that were updated or removed by the update operation.
                        updatedFields: {
                            name,
                            secondary_language_name,
                            order,
                        } = {},
                    } = {},

                    fullDocument: { // The document inserted & updated by the operation.
                        tenant_id,
                        type,
                        updated_at: updatedAt,
                    } = {}
                } = event || {}

                const metadataType = {
                    [PRODUCT_VARIANT_TYPES.VARIANT_VALUE]: VARIANT_TYPES.VARIANT,
                    [PRODUCT_VARIANT_TYPES.GROUP_VALUE]: VARIANT_TYPES.GROUP,
                }

                const MessageGroupId = `${tenant_id}_${metadataType[type]}_${documentId}`

                const sendMessageToSQS = async () => {
                    await ProductMetadataUpdateQueue.sendMessageToSQS({
                        MessageGroupId,
                        MessageBody: {
                            // Fields to make content unique
                            documentId,
                            updatedAt,

                            // Fields to update
                            name,
                            secondary_language_name,
                            order,
                        },
                    })
                }

                switch (operationType) {
                    case MONGODB.OPERATION_TYPE.UPDATE: {
                        const hasName = Object.hasOwn(event.updateDescription.updatedFields, "name")
                        const hasSlName = Object.hasOwn(event.updateDescription.updatedFields, "secondary_language_name")
                        const hasOrder = Object.hasOwn(event.updateDescription.updatedFields, "order")

                        if (!hasName && !hasSlName && !hasOrder) {
                            // If `name`, `secondary_language_name` and `order` are not changed in `update` operationType then skip it
                            return
                        }
                        sendMessageToSQS()
                        break
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error: (BrandChangeStream) Event listen and `sendMessageToSQS` failed",
                    event,
                })
            }
        })
}

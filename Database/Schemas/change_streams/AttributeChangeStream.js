const MasterAttributeSchema = require("../master_data/MasterAttributeSchema")

const ProductMetadataUpdateQueue = new (require("../../../SQS/ProductMetadataUpdateQueue"))

const {
    MONGODB,
    VALUES,
    MASTER_DATA_ENTITIES,
} = require("../../../Configs/constants")

// On server, listen for updates to documents in a "master_attributes" collection
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    MasterAttributeSchema
        .watch(
            [ /** pipeline */
                {
                    "$match": {
                        "operationType": {
                            "$in": [
                                MONGODB.OPERATION_TYPE.UPDATE,
                            ]
                        },
                        "updateDescription.updatedFields.attribute_name": {
                            "$exists": true
                        }
                    },
                }
            ],
            { /** options */
                "fullDocument": "updateLookup", // By default, change stream returns the delta of those fields modified by an update operation, instead of the entire updated document. So this option returns the most current majority-committed version of the updated document.
            }
        )
        .on("change", async (event) => {
            try {
                const {
                    operationType,
                    documentKey: {
                        _id: documentId,
                    }, // Document that contains the `_id` value of the document created or modified by the CRUD operation.

                    updateDescription: { // A document describing the fields that were updated or removed by the update operation.
                        updatedFields: {
                            attribute_name: attributeName,
                        } = {},
                    } = {},

                    fullDocument: { // The document inserted & updated by the operation.
                        tenant_id,
                        updated_at: updatedAt,
                    } = {}
                } = event || {}

                const MessageGroupId = `${tenant_id}_${MASTER_DATA_ENTITIES.ATTRIBUTE}_${documentId}`

                const sendMessageToSQS = async () => {
                    await ProductMetadataUpdateQueue.sendMessageToSQS({
                        MessageGroupId,
                        MessageBody: {
                            // Fields to make content unique
                            documentId,
                            updatedAt,

                            // Fields to update
                            attributeName,
                        },
                    })
                }

                switch (operationType) {
                    case MONGODB.OPERATION_TYPE.UPDATE: {
                        const hasName = Object.hasOwn(event.updateDescription.updatedFields, "attribute_name")

                        if (!hasName) {
                            // If `attribute_name` is not changed in `update` operationType then skip it
                            return
                        }
                        sendMessageToSQS()
                        break
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error: (BrandChangeStream) Event listen and `sendMessageToSQS` failed",
                    event,
                })
            }
        })
}

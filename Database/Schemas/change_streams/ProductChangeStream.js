const ProductSchema = require("../product/ProductSchema")

const MaterializedProductQueue = new (require("../../../SQS/MaterializedProductQueue"))

const {
    MONGODB,
    NEW_PRODUCT_TYPE,
    VALUES,
} = require("../../../Configs/constants")

// On server, listen for updates to documents in a "products" collection
if (VALUES.IS_APP_RUNNING_ON_SERVER) {
    ProductSchema
        .watch(
            [ /** pipeline */
                {
                    "$match": {
                        "operationType": {
                            "$in": [
                                MONGODB.OPERATION_TYPE.INSERT,
                                MONGODB.OPERATION_TYPE.UPDATE,
                            ]
                        },
                    }
                }
            ],
            { /** options */

                // By default, change stream returns the delta of those fields modified by an update operation, instead of the entire updated document.
                // So below option returns the most current majority-committed version of the updated document
                "fullDocument": "updateLookup"
            },
        )
        .on("change", async (event) => {
            try {
                const {
                    operationType,
                    documentKey: {
                        _id: documentId
                    }, // Document that contains the `_id` value of the document created or modified by the CRUD operation.

                    updateDescription: { // A document describing the fields that were updated or removed by the update operation.
                        updatedFields = {},
                        removedFields = [],
                    } = {},

                    fullDocument: { // The document inserted & updated by the operation.
                        _id,
                        tenant_id,
                        parent_id,
                        type,
                        is_deleted,
                    } = {}
                } = event || {}

                let productId = _id
                let messageProductType

                if (type === NEW_PRODUCT_TYPE.SINGLE) {
                    messageProductType = NEW_PRODUCT_TYPE.SINGLE
                }
                else {
                    if (type === NEW_PRODUCT_TYPE.VARIANT) {
                        productId = parent_id
                    }
                    messageProductType = NEW_PRODUCT_TYPE.PARENT
                }
                const MessageGroupId = `${tenant_id}_${productId}_${messageProductType}`

                switch (operationType) {
                    case MONGODB.OPERATION_TYPE.INSERT: {
                        // Pick `fullDocument` and add doc into `products_with_variants` collections

                        await MaterializedProductQueue.sendMessageToSQS({
                            MessageGroupId,
                            MessageBody: {
                                operationType,
                                documentId,
                                fullDocument: event.fullDocument,
                            },
                        })
                        break
                    }

                    case MONGODB.OPERATION_TYPE.UPDATE: {
                        // Pick `documentKey` and `updateDescription` then update doc into `products_with_variants` collections

                        const updatedFieldKeys = Object.keys(updatedFields)
                        const updatedFieldCount = updatedFieldKeys.length
                        const removedFieldCount = removedFields.length

                        const isSkippedUpdate =
                            (
                                is_deleted &&
                                !Object.hasOwn(updatedFields, "is_deleted")
                            ) ||
                            (
                                is_deleted &&
                                Object.hasOwn(updatedFields, "is_deleted") &&
                                !updatedFields.is_deleted
                            ) ||
                            (
                                updatedFieldCount === 1 &&
                                Object.hasOwn(updatedFields, "updated_at") && (
                                    !removedFieldCount || (
                                        removedFieldCount === 1 &&
                                        removedFields.includes("updated_by")
                                    )
                                )
                            )

                        if (isSkippedUpdate) {
                            logger.info("ProductChangeStream: SKIPPED UPDATE", { updatedFields, removedFields })
                            return // Skip updates in `MaterializedProduct`
                        }

                        await MaterializedProductQueue.sendMessageToSQS({
                            MessageGroupId,
                            MessageBody: {
                                operationType,
                                documentId,
                                fullDocument: {
                                    parent_id,
                                    type,
                                },
                                updatedDocument: {
                                    updatedFields,
                                    removedFields,
                                }
                            },
                        })
                        break
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "Error: (ProductChangeStream) Event listen and `sendMessageToSQS` failed",
                    event,
                })
            }
        })
}

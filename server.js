// PARSE .ENV
require('dotenv').config();

// FOR SERVER
// CHECK WITH PROTOCOL TO USE
const http = require('http')

const express = require('express') // NODE FRAMEWORK
const bodyParser = require('body-parser') // TO PARSE POST REQUEST
const cors = require('cors') // ALLOW CROSS ORIGIN REQUESTS

const swaggerUI = require('swagger-ui-express')

const { VALUES } = require('./Configs/constants');

const swaggerDocument = require(`./Doc/${VALUES.ENVIRONMENT}/openapi.json`)

// ---------------------------    SERVER CONFIGS ----------------------------------
const port = process.env.PORT || 8000
const app = express();

require('./Configs/globals'); // GLOBAL SETTINGS FILES
require("./Configs/Logger")
require('./Configs/cron-job'); // CRONJOB
require('./SQS/InitializeAllReceivers') //AWS-SQS

const server = http.createServer(app)

// --------------------------  SWAGGER    -----------------------------------------
app.use("/api-docs", swaggerUI.serve, swaggerUI.setup(swaggerDocument))


// ------------------------      GLOBAL MIDDLEWARES -------------------------
app.use(bodyParser.json()) // ALLOW APPLICATION JSON
app.use(bodyParser.urlencoded({ extended: false })) // ALLOW URL ENCODED PARSER
app.use(cors({
    exposedHeaders: "refreshed-access-token"
})) // ALLOWED ALL CROSS ORIGIN REQUESTS

app.use(express.static(__dirname + '/Assets')); // SERVE STATIC IMAGES FROM ASSETS FOLDER
app.use(express.static(__dirname + '/Logs')); // LOGGER LOG FOLDER


// --------------------------  MORGAN LOGGER MIDDLEWARE  -----------------------------------------
try {
    const morganMiddleware = require("./Middleware/MorganMiddleware")
    // Apply the Morgan middleware
    morganMiddleware(app)
}
catch (err) {
    // Handle the error here.
    logger.error(err)
}


// ------------------------    RESPONSE HANDLER    -------------------
app.use((req, res, next) => {
    const ResponseHandler = require('./Configs/responseHandler')
    res.handler = new ResponseHandler(req, res);
    next()
})

// --------------------------    ROUTES    ------------------
const appRoutes = require("./Routes/Router")
app.use("/productService", appRoutes);


// --------------------------    GLOBAL ERROR HANDLER    ------------------
app.use((err, req, res, next) => {
    if (res.headersSent) {
        return next(err)
    }
    res.handler.serverError(err)
})

// --------------------------   EJS TEMPLATE   ------------------
app.use("/Assets/", express.static(__dirname + '/Assets/'));


// --------------------------    START SERVER    ---------------------
server.listen(port, () => {
    const message = `Server started on port ${port} :)`

    logger.info(message)
})

const isEmpty = require("lodash.isempty")

const ProductModel = new (require("../../Models/ProductModel"))()

const {
    VALUES,
    NEW_PRODUCT_TYPE,
    PRODUCT_FILTER_TYPES,
} = require("../../Configs/constants")

const { toLeanOption } = require("../../Utils/helpers")

class CategoryService {

    /** 
     * @function getCategoriesProductCount
     * @returns Array []
     * @description Generates final category list with total & own product's count. 
     */
    getCategoriesProductCount = async (params) => {
        const {
            tenantId,
            priceListId,
            categoryList,
            hideOutOfStock,
            branchId,
            filters,
        } = params

        /** Create all the necessary category's mapping */
        const queryFamilyIds = [],
            queryCategoryIds = [],
            querySubcategoryIds = [],

            familyData = {},
            categoryData = {},
            subcategoryData = {}

        for (let index = 0, len = categoryList.length; index < len; index++) {
            const {
                _id,
                parent_id,
                type,
            } = categoryList[index]

            if (type === VALUES.category.FAMILY) {
                familyData[_id] = categoryList[index]
                queryFamilyIds.push(_id)
            }
            else if (type === VALUES.category.CATEGORY) {
                categoryData[parent_id] = (categoryData[parent_id] || []).concat(categoryList[index])
                queryCategoryIds.push(_id)
            }
            else if (type === VALUES.category.SUBCATEGORY) {
                subcategoryData[parent_id] = (subcategoryData[parent_id] || []).concat(categoryList[index])
                querySubcategoryIds.push(_id)
            }
        }

        /** Fetch products based on filters */
        const matchFields = {
            "tenant_id": tenantId,
            "is_active": true,
            "is_deleted": false,
            "type": {
                "$in": [
                    NEW_PRODUCT_TYPE.SINGLE,
                    NEW_PRODUCT_TYPE.PARENT,
                ]
            },
            "price_mappings": {
                "$elemMatch": {
                    "master_price_id": new mongoose.Types.ObjectId(priceListId),
                    "price": { "$gt": 0 }
                }
            },
            "$and": [
                {
                    "$or": [
                        { "family_id": { "$in": queryFamilyIds } },
                        { "family_id": null },
                    ],
                },
                {
                    "$or": [
                        { "category_id": { "$in": queryCategoryIds } },
                        { "category_id": null },
                    ],
                },
                {
                    "$or": [
                        { "subcategory_id": { "$in": querySubcategoryIds } },
                        { "subcategory_id": null }
                    ],
                },
            ]
        };

        if (hideOutOfStock && branchId) {
            matchFields["inventory_mappings"] = {
                "$elemMatch": {
                    "branch_id": branchId,
                    "quantity": { "$gt": 0 }
                }
            }
        }

        if (!isEmpty(filters)) {
            const {
                productType = [],
                brands = [],
                priceRange = {},
                tags = [],
                newItemDays,
            } = filters

            if (productType.length) {
                for (let type of productType) {

                    if (type === PRODUCT_FILTER_TYPES.NEW_PRODUCTS) {
                        matchFields["created_at"] = {
                            "$gte": moment().subtract(newItemDays, "days").toDate()
                        }
                    }
                }
            }

            if (brands.length) {
                const brandIds = brands.map(id => new mongoose.Types.ObjectId(id))

                matchFields["brand_id"] = { "$in": brandIds }
            }

            if ("from" in priceRange && "to" in priceRange) {
                matchFields["price_mappings"] = {
                    "$elemMatch": {
                        "master_price_id": new mongoose.Types.ObjectId(priceListId),
                        "price": {
                            "$gte": +(priceRange.from || 0),
                            "$lte": +(priceRange.to || 0),
                        }
                    }
                }
            }

            if (tags.length) {
                const tagsQuery = []

                tags.forEach(tag => {
                    tagsQuery.push({
                        tags: new mongoose.Types.ObjectId(tag)
                    })
                })
                matchFields["$or"] = tagsQuery
            }
        }

        // Using cursor-based pagination to handle large products data
        const pageSize = 2000
        let allProducts = []
        let lastProductId
        let products

        do {
            if (lastProductId) {
                matchFields["_id"] = { "$gt": new mongoose.Types.ObjectId(lastProductId) }
            }

            products = await ProductModel.findProducts(
                matchFields,
                {
                    "subcategory_id": 1,
                    "category_id": 1,
                    "family_id": 1
                },
                {
                    ...toLeanOption,
                    "sort": { _id: 1 },
                    "limit": pageSize
                },
                /**
                 * @description Query is not using index due to `null` value check so provided `hint` here
                 * @reference https://www.mongodb.com/docs/v5.0/reference/operator/meta/hint/
                 */
                "tenant_id_1_is_active_1_is_deleted_1_type_1_price_mappings.master_price_id_1_brand_id_1_family_id_1_category_id_1_subcategory_id_1_product_order_1_created_at_1_price_mappings.price_1"
            )

            const totalProductCount = products.length
            if (!totalProductCount) {
                break
            }
            allProducts = allProducts.concat(products)

            if (totalProductCount < pageSize) {
                break // exit loop if fewer than pageSize results are returned
            }
            lastProductId = products[totalProductCount - 1]._id
        } while (products.length < pageSize) // stop when fewer than pageSize results are returned

        /** Generate category wise products count */
        const totalProducts = {}
        const ownProducts = {}
        let totalProductCount = 0

        const incrementCount = (productType, categoryId) => {
            productType[categoryId] = (productType[categoryId] || 0) + 1
        }

        for (let index = 0, len = allProducts.length; index < len; index++) {
            const {
                family_id,
                category_id,
                subcategory_id,
            } = allProducts[index];

            if (family_id) {
                incrementCount(totalProducts, family_id)
                totalProductCount++

                if (family_id && category_id && subcategory_id) {
                    incrementCount(totalProducts, category_id)
                    incrementCount(totalProducts, subcategory_id)
                }
                else if (family_id && category_id) {
                    incrementCount(totalProducts, category_id)
                    incrementCount(ownProducts, category_id)
                }
                else {
                    incrementCount(ownProducts, family_id)
                }
            }
        }

        /** Generates final category list for client */
        const processedSubcategories = {}
        const processedCategories = {}
        const finalList = []

        // 1. Process subcategories
        for (const catId in subcategoryData) {
            const list = subcategoryData[catId]
            const listLen = list.length
            const validSubCats = []

            for (let i = 0; i < listLen; i++) {
                const subCat = list[i]
                const subcategoryId = String(subCat._id)
                delete subCat.type

                subCat.totalProducts = totalProducts[subcategoryId]
                subCat.ownProducts = ownProducts[subcategoryId]

                if (subCat.totalProducts > 0) {
                    validSubCats.push(subCat)
                }
            }

            if (validSubCats.length > 0) {
                processedSubcategories[catId] = validSubCats
            }
        }

        // 2. Process categories
        for (const familyId in categoryData) {
            const list = categoryData[familyId]
            const listLen = list.length
            const validCats = []

            for (let i = 0; i < listLen; i++) {
                const cat = list[i]
                const catId = String(cat._id)
                delete cat.type

                cat.totalProducts = totalProducts[catId]
                cat.ownProducts = ownProducts[catId]

                const subCategoryList = processedSubcategories[catId] || []
                if (subCategoryList.length > 0) {
                    cat.subCategoryList = subCategoryList
                }

                if (cat.totalProducts > 0) {
                    validCats.push(cat)
                }
            }

            if (validCats.length > 0) {
                processedCategories[familyId] = validCats
            }
        }

        // 3. Process families
        for (const familyId in familyData) {
            const family = familyData[familyId]
            delete family.type

            family.totalProducts = totalProducts[familyId]
            family.ownProducts = ownProducts[familyId]

            const categoryList = processedCategories[familyId] || []
            if (categoryList.length > 0) {
                family.categoryList = categoryList
            }
            finalList.push(family)
        }

        return { finalList, totalProductCount }
    }
}

module.exports = CategoryService

const ProductModel = new (require("../../Models/ProductModel"))
const ImageModel = new (require("../../Models/ImageModel"))
const MasterDataModel = new (require("../../Models/MasterDataModel"))
const InternalServiceModel = new (require("../../Models/InternalServiceModel"))

const NotificationManager = new (require("../../Managers/NotificationManger"))()
const ImageGetService = new (require("../images/ImageGetService"))()

const {
    delay,
    toLeanOption,
    stringifyObjectId,
} = require("../../Utils/helpers")

const {
    NEW_PRODUCT_TYPE,
    FILE_PATH,
    PRIMITIVE_ROLES,
    PROMISE_STATES
} = require("../../Configs/constants")

class ProductNotificationService {

    constructor() {
        this.notificationHour = "10" // 10 AM
        this.limit = 50
        this.maxAttempt = 5
        this.retryDelay = 2000 // In ms
        this.imageLimit = 10
        this.batchSize = 200
    }

    async getEligibleTenants() {
        const countries = await InternalServiceModel.getCountries({
            "filter": {
                "is_active": true,
                "is_deleted": false,
            },
            "projection": "_id timezone",
            "options": toLeanOption
        })

        const eligibleCountryIds = countries
            .filter(country =>
                // Only need to send notification for tenants have time between 10-11 AM
                moment.tz(country.timezone).format("HH") === this.notificationHour
            )
            .map(country => country._id)

        if (!eligibleCountryIds.length) {
            return []
        }

        return InternalServiceModel.getTenants({
            "filter": {
                _id: 1131,

                "is_active": true,
                "is_deleted": false,
                "country": {
                    "$in": eligibleCountryIds
                },
            },
            "projection": "_id",
            "options": toLeanOption
        })
    }

    getNewProducts(tenantId) {
        return ProductModel.findProducts(
            {
                "tenant_id": tenantId,
                "is_active": true,
                "is_deleted": false,
                "type": {
                    "$in": [NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.VARIANT]
                },
                "created_at": {
                    "$gt": moment().subtract(1, "day")
                }
            },
            "title type price_mappings group_value_id parent_id",
            toLeanOption
        )
    }

    fetchAllPriceList(products) {
        const recentProductPriceListIdSet = new Set()

        products.forEach(({ price_mappings }) => {
            price_mappings.forEach(({ price, master_price_id }) => {
                if (price <= 0 || !master_price_id) {
                    return
                }

                const priceId = stringifyObjectId(master_price_id)
                recentProductPriceListIdSet.add(priceId)
            })
        })

        return recentProductPriceListIdSet
    }

    async fetchRecipientsData(tenantId, priceListIds) {
        const response = await Promise.allSettled([
            this.getCustomers(tenantId, priceListIds),
            this.getSalespersonSettings(tenantId, priceListIds)
        ])

        const result = { customers: [], salespersonSettings: [] }
        const keys = ['customers', 'salespersonSettings']

        response.forEach((res, index) => {
            if (res.status === PROMISE_STATES.FULFILLED) {
                result[keys[index]] = res.value
            } else {
                logger.error(`Error fetching ${keys[index]}:`, res.reason)
            }
        })

        return result
    }

    async getCustomers(tenantId, priceListIds) {
        let lastCustomerId = null
        let attempt = 0
        let customerList = []

        do {
            try {
                const customers = await InternalServiceModel.getCustomers({
                    "filter": {
                        "_id": lastCustomerId ? { "$gt": lastCustomerId } : undefined,
                        "tenant_id": tenantId,
                        "is_active": true,
                        "is_deleted": false,
                        "price_list_id": { "$in": priceListIds }
                    },
                    "projection": "_id price_list_id",
                    "options": {
                        "lean": true,
                        "sort": { "_id": 1 },
                        "limit": this.limit
                    },
                })

                if (!customers.length) {
                    logger.info(`No customers found for tenant: ${tenantId}`)
                    break
                }
                customerList = customerList.concat(customers)

                if (customers.length < this.limit) {
                    break
                }

                lastCustomerId = customers[customers.length - 1]._id
                attempt = 0
            }
            catch (error) {
                attempt++
                logger.error(error)
                await delay(this.retryDelay)
            }
        } while (attempt <= this.maxAttempt)

        return customerList
    }

    async getSalespersonSettings(tenantId, priceListIds) {
        let lastSalespersonId = null
        let attempt = 0
        let salespersonSettingList = []

        do {
            try {
                const salespersons = await InternalServiceModel.getSalesPersons({
                    "filter": {
                        "_id": lastSalespersonId ? { "$gt": lastSalespersonId } : undefined,
                        "tenant_id": tenantId,
                        "is_active": true,
                        "is_deleted": false,
                    },
                    "projection": { "_id": 1 },
                    "options": {
                        "lean": true,
                        "sort": { "_id": 1 },
                        "limit": this.limit
                    }
                })

                if (!salespersons.length) {
                    logger.info(`sentNotificationsForNewProducts: No salesperson found for tenant: ${tenantId}`)
                    break
                }

                lastSalespersonId = salespersons[salespersons.length - 1]._id
                const spSettingIds = salespersons.map(({ _id }) => `${tenantId}_${stringifyObjectId(_id)}`)

                const salespersonSettings = await InternalServiceModel.getUserRoleSettings({
                    "filter": {
                        "_id": { "$in": spSettingIds },
                        "default_master_price_id": { "$in": priceListIds },
                    },
                    "projection": "_id default_master_price_id",
                    "options": toLeanOption
                })

                if (!salespersonSettings.length) {
                    logger.info(`sentNotificationsForNewProducts: No salesperson settings found for tenant: ${tenantId}`)

                    if (salespersons.length < this.limit) {
                        break
                    }
                    continue
                }
                salespersonSettingList = salespersonSettingList.concat(salespersonSettings)

                if (salespersons.length < this.limit) {
                    break
                }

                attempt = 0
            }
            catch (error) {
                attempt++
                logger.error(error)
                await delay(this.retryDelay)
            }
        } while (attempt <= this.maxAttempt)

        return salespersonSettingList
    }

    async prepareNotificationData(products, customers, salespersonSettings, tenantId) {
        const {
            priceIdSet,
            customerIdsByPriceId,
            salespersonIdsByPriceId
        } = this.groupRecipientsByPriceList(customers, salespersonSettings)

        const priceListInfoMap = await this.getPriceListInfo(priceIdSet)
        const productData = this.processProductData(products, priceIdSet)
        const imageData = await this.getProductImages(tenantId, productData)

        return {
            priceIdSet,
            customerIdsByPriceId,
            salespersonIdsByPriceId,
            priceListInfoMap,
            productData,
            imageData
        }
    }

    groupRecipientsByPriceList(customers, salespersonSettings) {
        const priceIdSet = new Set()
        const customerIdsByPriceId = {}
        const salespersonIdsByPriceId = {}

        // Process customers
        customers.forEach(({ price_list_id, _id }) => {
            const customerId = stringifyObjectId(_id)
            const priceListId = stringifyObjectId(price_list_id)

            priceIdSet.add(priceListId)
            this.addToGroup(customerIdsByPriceId, priceListId, customerId)
        })

        // Process salespersons
        salespersonSettings.forEach(({ default_master_price_id, _id }) => {
            const salespersonId = _id.split("_")[1]
            const priceListId = stringifyObjectId(default_master_price_id)

            priceIdSet.add(priceListId)
            this.addToGroup(salespersonIdsByPriceId, priceListId, salespersonId)
        })

        return { priceIdSet, customerIdsByPriceId, salespersonIdsByPriceId }
    }

    addToGroup(groupObject, key, value) {
        if (!groupObject[key]) {
            groupObject[key] = []
        }
        groupObject[key].push(value)
    }

    async getPriceListInfo(priceIdSet) {
        const priceListInfo = await MasterDataModel.findMasterPriceList(
            { _id: { $in: Array.from(priceIdSet) } },
            "_id price_name",
            toLeanOption
        )

        const priceListInfoMap = {}
        priceListInfo.forEach(({ _id, price_name }) => {
            priceListInfoMap[stringifyObjectId(_id)] = price_name
        })

        return priceListInfoMap
    }

    processProductData(products, priceIdSet) {
        const productIdSet = new Set()
        const groupIdSet = new Set()
        const parentIdSet = new Set()

        const productIdsByPriceId = {}
        const groupIdByProductId = {}
        const parentIdByGroupId = {}

        products.forEach(({ price_mappings, _id, parent_id, type, group_value_id }) => {
            price_mappings.forEach(({ price, master_price_id }) => {
                const priceId = stringifyObjectId(master_price_id)

                if (price <= 0 || !priceId || !priceIdSet.has(priceId)) {
                    return
                }

                const productId = stringifyObjectId(_id)
                productIdSet.add(productId)
                this.addToGroup(productIdsByPriceId, priceId, productId)

                if (type === NEW_PRODUCT_TYPE.VARIANT) {
                    const parentId = stringifyObjectId(parent_id)
                    const groupValueId = stringifyObjectId(group_value_id)

                    parentIdSet.add(parentId)

                    if (groupValueId) {
                        groupIdSet.add(groupValueId)
                        groupIdByProductId[productId] = groupValueId
                        parentIdByGroupId[groupValueId] = parentId
                    }
                }
            })
        })

        const groupCountPerProduct = this.countOccurrences(Object.values(groupIdByProductId))
        const parentCountPerGroup = this.countOccurrences(Object.values(parentIdByGroupId))

        return {
            productIdSet,
            groupIdSet,
            parentIdSet,
            productIdsByPriceId,
            groupIdByProductId,
            parentIdByGroupId,
            groupCountPerProduct,
            parentCountPerGroup
        }
    }

    countOccurrences(array) {
        return array.reduce((acc, item) => {
            acc[item] = (acc[item] || 0) + 1
            return acc
        }, {})
    }

    async getProductImages(tenantId, productData) {
        const {
            productIdSet,
            groupIdSet,
            parentIdSet,
            groupIdByProductId,
            groupCountPerProduct,
            parentCountPerGroup
        } = productData

        const imageProjection = "image_name image_number product_variant_id group_id updated_at"
        const imageOptions = {
            "lean": true,
            "sort": {
                "image_number": 1,
            }
        }

        const productImageUrl = ImageGetService.getImageURL(
            tenantId,
            FILE_PATH.PRODUCT_IMAGE.LOGO
        )

        let remainingLimit = this.imageLimit

        const singleVariantImages = await ImageModel.getImages(
            {
                "product_variant_id": { "$in": Array.from(productIdSet) },
                "group_id": null
            },
            imageProjection,
            imageOptions
        )

        const mainSingleVariantImage = {}
        const mainGroupImage = {}
        const mainParentImage = {}

        let totalMainSingleVariantImages = 0
        let totalMainGroupImages = 0
        let totalMainParentImages = 0

        singleVariantImages.forEach(({ image_name, image_number, product_variant_id, updated_at }) => {
            const productVariantId = stringifyObjectId(product_variant_id)

            if (
                !mainSingleVariantImage[productVariantId] ||
                image_number < mainSingleVariantImage[productVariantId].image_number
            ) {
                mainSingleVariantImage[productVariantId] = {
                    image_name: this.getImageURL(productImageUrl, image_name, updated_at),
                    image_number,
                }
            }

            const groupId = groupIdByProductId[productVariantId]
            if (groupId) {
                if (groupCountPerProduct[groupId]) {
                    groupCountPerProduct[groupId]--
                }

                if (
                    !groupCountPerProduct[groupId] ||
                    groupCountPerProduct[groupId] <= 0
                ) {
                    groupIdSet.delete(groupId)
                }
            }
        })

        totalMainSingleVariantImages = Object.keys(mainSingleVariantImage).length
        remainingLimit = this.imageLimit - totalMainSingleVariantImages

        if (remainingLimit > 0) {
            imageOptions.limit = remainingLimit

            if (groupIdSet.size) {
                const groupImages = await ImageModel.getImages(
                    {
                        "group_id": { "$in": Array.from(groupIdSet) },
                    },
                    imageProjection,
                    imageOptions
                )

                groupImages.forEach(({ image_name, image_number, product_variant_id, group_id, updated_at }) => {
                    const parentId = stringifyObjectId(product_variant_id)
                    const groupId = stringifyObjectId(group_id)

                    if (
                        !mainGroupImage[groupId] ||
                        image_number < mainGroupImage[groupId].image_number
                    ) {
                        mainGroupImage[groupId] = {
                            image_name: this.getImageURL(productImageUrl, image_name, updated_at),
                            image_number,
                        }
                    }

                    if (parentCountPerGroup[parentId]) {
                        parentCountPerGroup[parentId]--
                    }

                    if (
                        !parentCountPerGroup[parentId] ||
                        parentCountPerGroup[parentId] <= 0
                    ) {
                        parentIdSet.delete(parentId)
                    }
                })

                totalMainGroupImages = Object.keys(mainGroupImage).length
                remainingLimit -= totalMainGroupImages
            }

            if (remainingLimit > 0) {
                imageOptions.limit = remainingLimit

                if (parentIdSet.size) {
                    const parentImages = await ImageModel.getImages(
                        {
                            "product_variant_id": { "$in": Array.from(parentIdSet) },
                            "group_id": null
                        },
                        imageProjection,
                        imageOptions
                    )

                    parentImages.forEach(({ image_name, image_number, product_variant_id, updated_at }) => {
                        const parentId = stringifyObjectId(product_variant_id)

                        if (
                            !mainParentImage[parentId] ||
                            image_number < mainParentImage[parentId].image_number
                        ) {
                            mainParentImage[parentId] = {
                                image_name: this.getImageURL(productImageUrl, image_name, updated_at),
                                image_number,
                            }
                        }
                    })
                    totalMainParentImages = Object.keys(mainParentImage).length
                }
            }
        }

        return {
            mainSingleVariantImage,
            mainGroupImage,
            mainParentImage,
        }
    }

    getImageURL(imageUrl, imageName, updatedAt) {
        return imageUrl + imageName + "?timestamp=" + new Date(updatedAt).getTime()
    }

    async sendNotifications(tenantId, notificationData) {
        const {
            customerIdsByPriceId,
            salespersonIdsByPriceId,
            priceListInfoMap,
            productData,
            imageData
        } = notificationData

        // Send customer notifications
        await this.sendNotificationsByRole(
            tenantId,
            customerIdsByPriceId,
            priceListInfoMap,
            productData,
            imageData,
            PRIMITIVE_ROLES.CUSTOMER
        )

        // Send salesperson notifications
        await this.sendNotificationsByRole(
            tenantId,
            salespersonIdsByPriceId,
            priceListInfoMap,
            productData,
            imageData,
            PRIMITIVE_ROLES.SALES_PERSON
        )
    }

    async sendNotificationsByRole(tenantId, recipientsByPriceId, priceListInfoMap, productData, imageData, userRoleName) {
        for (const [priceId, recipientIds] of Object.entries(recipientsByPriceId)) {
            const priceName = priceListInfoMap[priceId]
            const images = this.getImagesForPriceList(priceId, productData, imageData)

            await this.sendBatchedNotifications(
                tenantId,
                userRoleName,
                recipientIds,
                priceId,
                priceName,
                images
            )
        }
    }

    getImagesForPriceList(priceId, productData, imageData) {
        const { productIdsByPriceId, groupIdByProductId, parentIdByGroupId } = productData
        const { mainSingleVariantImage, mainGroupImage, mainParentImage } = imageData

        const imageSet = new Set()
        const productIds = productIdsByPriceId[priceId] || []

        productIds.forEach(productId => {
            const groupId = groupIdByProductId[productId]
            const parentId = parentIdByGroupId[groupId]

            let imageName

            if (mainSingleVariantImage[productId]) {
                imageName = mainSingleVariantImage[productId].image_name
            }
            else if (mainGroupImage[groupId]) {
                imageName = mainGroupImage[groupId].image_name
            }
            else if (mainParentImage[parentId]) {
                imageName = mainParentImage[parentId].image_name
            }

            if (imageName) {
                imageSet.add(imageName)
            }
        })
        return Array.from(imageSet).slice(0, this.imageLimit)
    }

    async sendBatchedNotifications(tenantId, userRoleName, recipientIds, priceId, priceName, images) {
        const totalRecipients = recipientIds.length
        const totalBatches = Math.ceil(totalRecipients / this.batchSize)

        for (let i = 0; i < totalBatches; i++) {
            const startIndex = i * this.batchSize
            const endIndex = Math.min(startIndex + this.batchSize, totalRecipients)
            const userRoleIds = recipientIds.slice(startIndex, endIndex)

            let attempt = 0
            do {
                try {
                    await NotificationManager.sentNewProductNotification(
                        tenantId,
                        userRoleName,
                        userRoleIds,
                        priceId,
                        priceName,
                        images,
                    )
                    break // Exit loop if successful
                }
                catch (error) {
                    attempt++
                    logger.error(error)
                    await delay(this.retryDelay)
                }
            } while (attempt <= this.maxAttempt)

            // Add delay between batches except for the last one
            if (i < totalBatches - 1) {
                await delay(this.batchDelay)
            }
        }
    }
}

module.exports = ProductNotificationService

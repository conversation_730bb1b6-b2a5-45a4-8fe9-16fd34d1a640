const { PRIMITIVE_ROLES } = require("../Configs/constants")

class TemplateService {

    getNewProductTemplates = ({ userRoleName, priceName }) => {
        const template = {
            [PRIMITIVE_ROLES.CUSTOMER]: {
                title: "🚀 New Arrivals 🚀",
                message: "🆕 Check out the latest products we’ve just added!",
                secondaryLanguageTitle: "🚀 وصلنا الجديد 🚀",
                secondaryLanguageMessage: "🆕 اطلع على أحدث المنتجات التي تمت إضافتها الآن!",
                centerMessage: "New products have been added. Take a look!",
                centerSecondaryLanguageMessage: "تمت إضافة منتجات جديدة. اطلع عليها الآن!",
            },

            [PRIMITIVE_ROLES.SALES_PERSON]: {
                title: "🚀 New Arrivals 🚀",
                message: `🆕 Check out the latest ${priceName} products we’ve just added!`,
                secondaryLanguageTitle: "🚀 وصلنا الجديد 🚀",
                secondaryLanguageMessage: `🆕 اطّلع على أحدث منتجات ${priceName} التي أضفناها للتو!`,
                centerMessage: `New ${priceName} products have been added. Take a look!`,
                centerSecondaryLanguageMessage: `تمت إضافة منتجات جديدة في ${priceName} ألقِ نظرة!`
            },
        }
        return template[userRoleName]
    }
}

module.exports = TemplateService

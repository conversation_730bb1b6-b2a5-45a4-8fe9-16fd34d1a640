const ConfigurationModel = new (require("../../Models/ConfigurationModel"))()
const MasterDataModel = new (require("../../Models/MasterDataModel"))();
const CategoryModel = new (require("../../Models/CategoryModel"))();
const ImageModel = new (require("../../Models/ImageModel"))();
const DeletedImageModel = require("../../Models/DeletedImageModel");
const ProductModel = new (require("../../Models/ProductModel"))();
const DealModel = new (require("../../Models/DealModel"))();
const DealProductModel = new (require("../../Models/DealProductModel"))();
const OrderModel = new (require("../../Models/OrderModel"))();
const MaterializedProductModelMethods = new (require("../../Models/materialized_product/MaterializedProductModelMethods"))();
const DeletedMaterializedProductModel = require("../../Models/materialized_product/DeletedMaterializedProductModel");

const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../../Utils/helpers');

const { VALUES, DEAL_STATUS } = require('../../Configs/constants');

class DataSyncService extends BaseDataSyncService {

    async getMasterData(query) {
        const {
            tenantId,
        } = query;

        const filter = { tenant_id: tenantId };

        const [
            taxSetting,
            taxTypes,
            masterPrices,
            masterUnits,
        ] = await Promise.all([
            ConfigurationModel.findMasterTaxInfo(filter, "-__v -created_by -updated_by", toLeanOption),
            ConfigurationModel.findTaxes(
                { tenant_id: tenantId },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
            MasterDataModel.findMasterPriceList(
                {
                    tenant_id: tenantId,
                    is_deleted: false,
                    is_active: true,
                },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
            MasterDataModel.findUnitList(
                {
                    tenant_id: tenantId,
                    is_deleted: false,
                    is_active: true,
                },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
        ]);

        const activeTaxes = []
        const taxGroupIds = []
        let taxGroups = []

        taxTypes.forEach(tax => {
            if (tax.status) {
                activeTaxes.push(tax)

                if (tax.type === VALUES.TAX_TYPE.GROUP) {
                    taxGroupIds.push(tax._id)
                }
            }
        });

        if (taxGroupIds.length > 0) {
            taxGroups = await ConfigurationModel.findTaxes(
                {
                    tenant_id: tenantId,
                    group_id: { $in: taxGroupIds },
                },
                "-__v -created_by -updated_by",
                toLeanOption
            );
        }

        return {
            taxSetting,
            taxTypes: activeTaxes,
            taxGroups,
            masterPrices,
            masterUnits,
        };
    }

    async getCategoryList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            isInitialSync,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        // Only apply is_deleted and is_active filters for initial sync
        if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => CategoryModel.getCategoryCount(filter),
            findFunction: (filter, selectFields, options) => CategoryModel.allCategory(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getMvProductList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
            isInitialSync,
        } = query;

        const filter = { tenant_id: tenantId };

        // Only apply is_deleted and is_active filters for initial sync
        if (isInitialSync) {
            filter.is_active = true;
        }

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => MaterializedProductModelMethods.countProducts(filter),
            findFunction: (filter, selectFields, options) => MaterializedProductModelMethods.findProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getProductList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ProductModel.countProducts(filter),
            findFunction: (filter, selectFields, options) => ProductModel.findProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by -inactive_variant_item_numbers",
            leanOptions: {
                ...toLeanOption,
                populate: [
                    { path: "variants.values", select: { name: 1, order: 1 , tenant_id: 1 ,product_id: 1 ,type: 1   } },
                    { path: "groups.values", select: { name: 1, order: 1  ,  tenant_id: 1 ,product_id: 1 ,type: 1 } },
                    { path: "attributes.attribute_id", select: { attribute_name: 1, secondary_language_attribute_name: 1, is_active: 1  ,tenant_id: 1 ,is_deleted: 1 ,created_at: 1 ,updated_at: 1   } },
                ]
            }
        });
    }

    async getDeletedProductList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = body;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DeletedMaterializedProductModel.getDeletedMaterializedProductCount(filter),
            findFunction: (filter, selectFields, options) => DeletedMaterializedProductModel.findDeletedMaterializedProducts(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption,
        });
    }


    async getImageList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ImageModel.getImageCount(filter),
            findFunction: (filter, selectFields, options) => ImageModel.getImages(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by -image_size",
            leanOptions: toLeanOption
        });
    }

    async getDeletedImageList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = body;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DeletedImageModel.getDeletedImageCount(filter),
            findFunction: (filter, selectFields, options) => DeletedImageModel.findDeletedImages(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption,
        });
    }

    async getFavoriteProductList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            user_role_id: req.headers.userroleid
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ProductModel.getFavoriteProductCount(filter),
            findFunction: (filter, selectFields, options) => ProductModel.findFavoriteProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getDealList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = {
            tenant_id: tenantId,
            deal_status: { $in: [DEAL_STATUS.RUNNING, DEAL_STATUS.SCHEDULED] }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DealModel.getDealCount(filter),
            findFunction: (filter, selectFields, options) => DealModel.getDeals(filter, selectFields, options),
            selectFields: `
                deal_id
                tenant_id
                price_id
                deal_type
                deal_name
                secondary_deal_name
                deal_from_date
                deal_to_date
                sales_persons
                deal_status
                deal_product_counter
                created_at
                updated_at
            `,
            leanOptions: toLeanOption
        });
    }

    async getDealProductList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
            dealIds,
        } = body;

        const filter = {
            tenant_id: tenantId,
            is_active: true,
            deal_id: { $in: dealIds }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DealProductModel.getDealProductCount(filter),
            findFunction: (filter, selectFields, options) => DealProductModel.getDealProducts(filter, selectFields, options),
            selectFields: `
                deal_id
                price_id
                tenant_id
                product_id
                parent_id
                is_active
                discount_type
                percent
                amount
                discounted_price
                first_tier
                second_tier
                third_tier
                buy_product
                free_product
                deal_product_sequence
                deal_from_date
                deal_to_date
                created_at
                updated_at
            `,
            leanOptions: toLeanOption
        });
    }

    async getCartItemList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            cart_id: new RegExp(`^${tenantId}_[^_]+_${req.headers.userroleid}$`),
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => OrderModel.cartItemCount(filter),
            findFunction: (filter, selectFields, options) => OrderModel.findCartItems(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getOrderList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            order_creator_user_role_id: req.headers.userroleid,
            created_at: { $gte: moment().subtract(3, "months") }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => OrderModel.countOrders(filter),
            findFunction: (filter, selectFields, options) => OrderModel.findOrders(filter, selectFields, options),
            selectFields: `
                -__v
                -created_by
                -updated_by
                -master_price_ids
                -unique_order_number
                -order_hold_reason_id
                -whatsapp_message_sent
                -pre_approved
                -gps_coordinate
            `,
            leanOptions: toLeanOption
        });
    }
}

module.exports = new DataSyncService(); 

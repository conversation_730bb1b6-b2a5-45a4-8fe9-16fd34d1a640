const DeletedImageModel = require("../../Models/DeletedImageModel")
const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))

const {
    stringifyObjectId,
    toObjectId,
} = require("../../Utils/helpers")

class DeleteImageSqsService {

    handleMsgByGroup = (messageGroupMap, updateArray) => {
        messageGroupMap.forEach((bulkMessage, messageGroupId, map) => {
            bulkMessage.forEach(msg => {
                const { documentId, tenantId, deletedAt } = msg.messageBody

                updateArray.push({
                    filter: {
                        _id: toObjectId(documentId),
                        tenant_id: +tenantId,
                    },
                    update: {
                        deleted_at: deletedAt,
                    }
                })
            })
        })
    }

    upsertDeletedImages = async (documentIdMap, deleteMsgEntriesMap, updateArray) => {
        const filePath = "DeleteImageSQS -> Services/sqs/DeleteImageSqsService/upsertDeletedImages"

        try {
            const bulkOps = updateArray.map((doc, index) => ({
                updateOne: {
                    filter: doc.filter,
                    update: { $set: doc.update },
                    upsert: true
                }
            }))

            const bulkWriteResult = await DeletedImageModel.bulkWrite(
                bulkOps,
                { ordered: false }
            )

            const successfulIds = []
            const failedIds = []

            // Handle successful upserts (newly created documents)
            if (bulkWriteResult.upsertedIds && Object.keys(bulkWriteResult.upsertedIds).length > 0) {
                for (const index in bulkWriteResult.upsertedIds) {
                    if (bulkWriteResult.upsertedIds.hasOwnProperty(index)) {
                        const upsertedId = bulkWriteResult.upsertedIds[index]
                        const stringId = stringifyObjectId(upsertedId)

                        successfulIds.push(stringId)
                        MaterializedProductHelper.handleDeleteMsgEntriesMap(stringId, documentIdMap, deleteMsgEntriesMap)
                    }
                }
            }

            // Handle matched documents (existing documents that were updated)
            if (bulkWriteResult.matchedCount > 0) {
                updateArray.forEach((doc, index) => {
                    const stringId = stringifyObjectId(doc.filter._id)
                    if (!successfulIds.includes(stringId)) {
                        successfulIds.push(stringId)
                        MaterializedProductHelper.handleDeleteMsgEntriesMap(stringId, documentIdMap, deleteMsgEntriesMap)
                    }
                })
            }

            // Handle write errors if any occurred
            if (bulkWriteResult.writeErrors?.length > 0) {
                bulkWriteResult.writeErrors.forEach(error => {
                    const {
                        index,
                        code,
                        errmsg,
                        op
                    } = error

                    // Get the document ID that failed
                    const failedDoc = updateArray[index]
                    if (failedDoc) {
                        const stringId = stringifyObjectId(failedDoc.filter._id)
                        failedIds.push(stringId)

                        // Remove from successfulIds if it was added there
                        const successfulIndex = successfulIds.indexOf(stringId)
                        if (successfulIndex > -1) {
                            successfulIds.splice(successfulIndex, 1)
                        }

                        // Log specific error details
                        logger.error(`Bulk write error for document ${stringId}`, {
                            errorMessage: `Error: (BULK_WRITE_ERROR) ${filePath}. ${errmsg}`,
                            documentId: stringId,
                            code,
                            operation: op,
                            index,
                        })
                    }
                })
            }

            // Log bulk write results for monitoring
            logger.info(`${filePath} - Bulk write completed`, {
                successfulIds,
                failedIds,
                totalOperations: updateArray.length,
                bulkWriteResult,
            })
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ${filePath}`,
                updateArray,
            })
        }
    }
}

module.exports = new DeleteImageSqsService()

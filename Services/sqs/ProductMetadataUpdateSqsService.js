
const isEmpty = require("lodash.isempty")

const MaterializedProductModelMethods = new (require("../../Models/materialized_product/MaterializedProductModelMethods"))
const MaterializedProductHelper = new (require("../../Models/materialized_product/MaterializedProductHelper"))

const {
    VALUES,
    MASTER_DATA_ENTITIES,
    VARIANT_TYPES,
    PROMISE_STATES,
} = require("../../Configs/constants")

const {
    stringifyObjectId,
    toObjectId,
} = require("../../Utils/helpers")

class ProductMetadataUpdateSqsService {

    handleMsgByGroup = (messageGroupMap, messages) => {
        messageGroupMap.forEach((bulkMessage, messageGroupId, map) => {
            const [tenantId, metadataType, _id] = messageGroupId.split("_")

            const arrayMap = {
                [VALUES.category.CATEGORY]: messages.categoryMsgs,
                [MASTER_DATA_ENTITIES.BRAND]: messages.brandMsgs,
                [MASTER_DATA_ENTITIES.ATTRIBUTE]: messages.attributeMsgs,
                [VARIANT_TYPES.VARIANT]: messages.variantMsgs,
                [VARIANT_TYPES.GROUP]: messages.groupMsgs,
            }

            if (Object.hasOwn(arrayMap, metadataType)) {
                const latestMsg = bulkMessage.reduce((max, obj) =>
                    new Date(obj.messageBody.updatedAt) >
                        new Date(max.messageBody.updatedAt)
                        ? obj
                        : max
                )

                arrayMap[metadataType].push(latestMsg)
            }
        })
    }

    updateMetadata = async (documentIdMap, deleteMsgEntriesMap, messages) => {
        const filePath = "ProductMetadataUpdateSQS -> Services/sqs/ProductMetadataUpdateSqsService/updateMetadata"

        try {
            const { filter, updateObj } = this.getProductFindFilter(messages)

            const materializedProduct = await this.findAndUpdateProducts(filter, updateObj)
            if (!materializedProduct.length) {
                logger.info("ProductMetadataUpdateSqsService/updateMetadata: materializedProduct not found!", { filter, updateObj })
                return
            }

            const {
                categoryUpdate,
                brandUpdate,
                attributeUpdate,
                variantUpdate,
                groupUpdate,
            } = updateObj

            const resultList = await Promise.allSettled(
                materializedProduct.map(doc => {
                    return doc.save()
                })
            )

            const handleDeleteMsgEntries = (id, updateObj) => {
                const stringId = stringifyObjectId(id)

                if (Object.hasOwn(updateObj, stringId)) {
                    MaterializedProductHelper.handleDeleteMsgEntriesMap(stringId, documentIdMap, deleteMsgEntriesMap)
                }
            }

            resultList.forEach(result => {
                const {
                    status,
                    value: resultValue,
                    reason,
                } = result

                if (status === PROMISE_STATES.FULFILLED && resultValue._id) {
                    const mappings = [
                        {
                            "key": "family",
                            "updateObj": categoryUpdate
                        },
                        {
                            "key": "category",
                            "updateObj": categoryUpdate
                        },
                        {
                            "key": "subcategory",
                            "updateObj": categoryUpdate
                        },
                        {
                            "key": "brand",
                            "updateObj": brandUpdate
                        },
                    ]

                    // Handle single object fields
                    mappings.forEach(({ key, updateObj }) => {
                        if (resultValue[key]) {
                            handleDeleteMsgEntries(resultValue[key]._id, updateObj)
                        }
                    })

                    // Handle array fields
                    const arrayMappings = [
                        {
                            "key": "variants",
                            "valuesKey": "values",
                            "updateObj": variantUpdate
                        },
                        {
                            "key": "groups",
                            "valuesKey": "values",
                            "updateObj": groupUpdate
                        },
                        {
                            "key": "attributes",
                            "valuesKey": null,
                            "updateObj": attributeUpdate,
                            "idKey": "attribute_id"
                        },
                    ]

                    arrayMappings.forEach(({ key, valuesKey, updateObj, idKey = "_id" }) => {
                        const values = valuesKey
                            ? resultValue[key]?.[valuesKey]
                            : resultValue[key]

                        if (Array.isArray(values)) {
                            values.forEach(item => handleDeleteMsgEntries(item[idKey], updateObj))
                        }
                    })
                }
                else if (status === PROMISE_STATES.REJECTED) {
                    logger.error(reason, {
                        errorMessage: `Error: (PROMISE_REJECTED) ${filePath}`,
                    })
                }
            })
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ${filePath}`,
            })
        }
    }

    getProductFindFilter = (messages) => {
        const filter = { $or: [] }

        const {
            categoryMsgs,
            brandMsgs,
            attributeMsgs,
            variantMsgs,
            groupMsgs,
        } = messages

        const updateObj = {
            categoryUpdate: {},
            brandUpdate: {},
            attributeUpdate: {},
            variantUpdate: {},
            groupUpdate: {},
        }

        const idList = {
            family: [],
            category: [],
            subcategory: [],
            brand: [],
            attribute: [],
            variant: [],
            group: [],
        }

        const idKeys = {
            familyIdKey: "family._id",
            categoryIdKey: "category._id",
            subcategoryIdKey: "subcategory._id",
            brandIdKey: "brand._id",
            attributeIdKey: "attributes.attribute_id",
            variantIdKey: "variants.values._id",
            groupIdKey: "groups.values._id",
        }

        const keyMap = {
            FAMILY: idKeys.familyIdKey,
            CATEGORY: idKeys.categoryIdKey,
            SUBCATEGORY: idKeys.subcategoryIdKey,
        }

        categoryMsgs.forEach(({ messageBody }) => {
            const { documentId, type, categoryName } = messageBody

            if (keyMap[type]) {
                updateObj.categoryUpdate[documentId] = categoryName
                idList[type.toLowerCase()].push(toObjectId(documentId))
            }
        })

        brandMsgs.forEach(({ messageBody }) => {
            const { documentId, brandName } = messageBody

            updateObj.brandUpdate[documentId] = brandName
            idList.brand.push(toObjectId(documentId))
        })

        attributeMsgs.forEach(({ messageBody }) => {
            const { documentId, attributeName } = messageBody

            updateObj.attributeUpdate[documentId] = attributeName
            idList.attribute.push(toObjectId(documentId))
        })

        const processVariantOrGroup = (msgs, updateObj, idKey) => {
            msgs.forEach(({ messageBody }) => {
                const { documentId, name, secondary_language_name, order } = messageBody
                const updates = {}

                if (name) {
                    updates.name = name
                }

                if (secondary_language_name) {
                    updates.secondary_language_name = secondary_language_name
                }

                if (Object.hasOwn(messageBody, "order")) {
                    updates.order = order
                }

                updateObj[documentId] = updates
                idList[idKey].push(toObjectId(documentId))
            })
        }

        processVariantOrGroup(variantMsgs, updateObj.variantUpdate, "variant")
        processVariantOrGroup(groupMsgs, updateObj.groupUpdate, "group")

        const keyIDMap = [
            { key: idKeys.familyIdKey, ids: idList.family },
            { key: idKeys.categoryIdKey, ids: idList.category },
            { key: idKeys.subcategoryIdKey, ids: idList.subcategory },
            { key: idKeys.brandIdKey, ids: idList.brand },
            { key: idKeys.attributeIdKey, ids: idList.attribute },
            { key: idKeys.variantIdKey, ids: idList.variant },
            { key: idKeys.groupIdKey, ids: idList.group },
        ]

        keyIDMap.forEach(({ key, ids }) => {
            if (ids.length) {
                filter.$or.push({
                    [key]: {
                        $in: ids
                    }
                })
            }
        })

        return { filter, updateObj }
    }

    findAndUpdateProducts = async (filter, updateObj) => {
        const {
            categoryUpdate,
            brandUpdate,
            attributeUpdate,
            variantUpdate,
            groupUpdate,
        } = updateObj

        const materializedProduct = await MaterializedProductModelMethods.findProducts(
            filter,
            {
                "family": 1,
                "category": 1,
                "subcategory": 1,
                "brand": 1,
                "attributes": 1,
                "variants": 1,
                "groups": 1,
                "product_variants.variant": 1,
                "product_variants.group": 1,
            },
        )

        materializedProduct.forEach(product => {
            if (!isEmpty(categoryUpdate)) {
                ["family", "category", "subcategory"].forEach(key =>
                    this.updateProperty(product[key], categoryUpdate, "name")
                )
            }

            this.updateProperty(product.brand, brandUpdate, "name")
            this.updateVariants(product, variantUpdate, "variants", "variant")
            this.updateVariants(product, groupUpdate, "groups", "group")

            // Update attributes
            if (!isEmpty(attributeUpdate)) {
                product.attributes?.forEach(att => {
                    this.updateProperty(att, attributeUpdate, "name", "attribute_id")
                })
            }
        })
        return materializedProduct
    }

    updateProperty = (lookupObj, updates, updateProp, idKey = "_id") => {
        const stringId = stringifyObjectId(lookupObj?.[idKey])
        if (!stringId || !Object.hasOwn(updates, stringId)) { return }

        const updateValue = updates[stringId]

        if (typeof updateValue === "string") {
            if (updateProp) {
                lookupObj[updateProp] = updateValue
            }
        }
        else if (typeof updateValue === "object") {
            for (const [key, value] of Object.entries(updateValue)) {
                lookupObj[key] = value
            }
        }
    }

    updateVariants = (product, updates, parentKey, variantKey) => {
        if (isEmpty(updates)) { return }

        product[parentKey]?.values?.forEach(variant => {
            this.updateProperty(variant, updates)
        })

        product.product_variants?.forEach(pv => {
            this.updateProperty(pv[variantKey], updates)
        })
    }
}

module.exports = ProductMetadataUpdateSqsService

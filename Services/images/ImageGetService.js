const isEmpty = require("lodash.isempty")
const difference = require("lodash.difference");

const ProductModel = new (require("../../Models/ProductModel"))()
const ImageModel = new (require("../../Models/ImageModel"))()

const {
    NEW_PRODUCT_TYPE,
    FILE_PATH,
    VALUES,
} = require("../../Configs/constants");

const {
    toLeanOption,
    stringifyObjectId,
} = require("../../Utils/helpers");

class ImageService {

    fetchProductsByItmNum = (tenantId, itemNumbers) => {
        const uniqueItemNumbers = [...new Set(itemNumbers)].map(
            (itemNum) => `${tenantId}_${itemNum}`
        )

        return ProductModel.findProducts(
            {
                "unique_item_number": { "$in": uniqueItemNumbers },
                "type": {
                    "$in": [
                        NEW_PRODUCT_TYPE.SINGLE,
                        NEW_PRODUCT_TYPE.VARIANT,
                    ]
                }
            },
            {
                "_id": 1,
                "item_number": 1,
                "parent_id": 1,
                "group_value_id": 1,
            },
            toLeanOption,
        )
    }

    fetchImageMapping = (productIds, groupId = null) => {
        return ImageModel.aggregateImages([
            {
                "$match": {
                    "product_variant_id": { "$in": productIds },
                    "group_id": groupId,
                }
            },
            {
                "$project": {
                    "product_variant_id": 1,
                    "group_id": 1,
                    "image_name": 1,
                    "image_number": 1,
                }
            },
            {
                "$sort": {
                    "image_number": 1
                }
            },
            {
                "$group": {
                    "_id": "$product_variant_id",
                    "record": { "$first": "$$ROOT" } // Take the first (smallest sequenceNumber) record per id
                }
            },
            {
                "$replaceRoot": { "newRoot": "$record" } // Flatten the output to remove nesting
            }
        ])
    }

    fetchImagesByProductIds = (productIds) => {
        return ImageModel.getImages(
            {
                "product_variant_id": {
                    "$in": productIds
                },
            },
            {
                "product_variant_id": 1,
                "group_id": 1,
                "image_name": 1,
                "image_number": 1,
            },
            toLeanOption,
        )
    }

    prepareProductMapping = (
        products,
        productIds,
        productObjMap,
        groupIdByProductId,
        itmNumByParentAndGroupId,
        itmNumByParentAndProductId,
    ) => {
        products.forEach(product => {
            const {
                _id,
                parent_id,
                item_number,
                group_value_id,
            } = product

            const productId = stringifyObjectId(_id)

            productIds.push(productId)
            productObjMap[productId] = product

            if (parent_id) {
                const parentId = stringifyObjectId(parent_id)

                // For VARIANT type products (WITH GROUP) 
                if (group_value_id) {
                    const mapping = itmNumByParentAndGroupId[parentId] || {}

                    if (mapping[group_value_id]?.length) {
                        mapping[group_value_id].push(item_number)
                    }
                    else {
                        mapping[group_value_id] = [item_number]
                    }

                    groupIdByProductId[productId] = group_value_id
                    itmNumByParentAndGroupId[parentId] = mapping
                }

                // For VARIANT type products (WITH or WITHOUT GROUP)
                const mapping = itmNumByParentAndProductId[parentId] || {}
                mapping[productId] = item_number
                itmNumByParentAndProductId[parentId] = mapping
            }
        })
    }

    getImageURL = (
        tenantId,
        path = FILE_PATH.PRODUCT_IMAGE.PHONE
    ) => {
        return VALUES.awsPublicBucketBaseURL +
            `${path}/${tenantId}` +
            "/"
    }

    prepareImageMapping = (
        imageURL,
        images,
        imageProductIds,
        imageList,
        productObjMap,
        groupIdByProductId,
        itmNumByParentAndProductId,
        itmNumByParentAndGroupId,
    ) => {
        images.forEach(image => {
            const {
                product_variant_id,
                group_id,
                image_name,
                image_number,
            } = image

            const productId = stringifyObjectId(product_variant_id)
            const itemNumber = productObjMap[productId].item_number

            imageProductIds.push(productId)

            imageList.push({
                itemNumber,
                imageUrl: imageURL + image_name
            })

            // For VARIANT type products (WITH or WITHOUT GROUP)
            const parentId = stringifyObjectId(productObjMap[productId].parent_id)

            if (parentId) {
                // Delete mapping of `itmNumByParentAndProductId` which are pushed into `imageList`
                const productIdMapping = itmNumByParentAndProductId[parentId]
                delete productIdMapping?.[productId]

                if (isEmpty(productIdMapping)) {
                    delete itmNumByParentAndProductId[parentId]
                }

                // Delete mapping of `itmNumByParentAndGroupId` which are pushed into `imageList`
                const groupId = groupIdByProductId[productId]
                const itemNumbers = itmNumByParentAndGroupId[parentId]?.[groupId] || []

                if (itemNumbers.length) {
                    const index = itemNumbers.findIndex(num => num === itemNumber)

                    if (index > -1) {
                        itemNumbers.splice(index, 1)

                        if (itemNumbers.length) {
                            itmNumByParentAndGroupId[parentId][groupId] = itemNumbers
                        }
                        else {
                            delete itmNumByParentAndGroupId[parentId][groupId]
                        }
                    }
                }
            }
        })
    }

    prepareParentImageMap = (
        parentImages,
        parentImageMap,
        itmNumByParentAndGroupId,
    ) => {
        parentImages.forEach(image => {
            const {
                product_variant_id,
                group_id,
                image_name,
                image_number,
            } = image

            const parentId = stringifyObjectId(product_variant_id)
            const mapping = parentImageMap.get(parentId) || {}

            if (group_id) {
                const validItemNumbers = itmNumByParentAndGroupId[parentId]?.[group_id]

                if (!validItemNumbers?.length) {
                    return
                }

                if (!mapping.group_image) {
                    mapping.group_image = {}
                }

                // Parent type product's group image
                if (
                    !mapping.group_image[group_id] ||
                    image_number < mapping.group_image[group_id].image_number
                ) {
                    mapping.group_image[group_id] = image
                }
            }
            else {
                // Parent type product's image
                if (
                    !mapping.cover_image ||
                    image_number < mapping.cover_image.image_number
                ) {
                    mapping.cover_image = image
                }
            }
            parentImageMap.set(parentId, mapping)
        })
    }

    prepareParentImageMapping = (
        imageURL,
        itmNumByParentAndProductId,
        parentImageMap,
        groupIdByProductId,
        imageList,
    ) => {
        for (const [parentId, productObj] of Object.entries(itmNumByParentAndProductId)) {
            const parentImageObj = parentImageMap.get(parentId)

            if (isEmpty(parentImageObj)) {
                return
            }

            for (const [productId, itemNumber] of Object.entries(productObj)) {
                const groupId = groupIdByProductId[productId]
                const groupImage = parentImageObj.group_image?.[groupId]
                const coverImage = parentImageObj.cover_image

                const setImageList = (imageName) => {
                    imageList.push({
                        itemNumber,
                        imageUrl: imageURL + imageName
                    })
                }

                if (groupImage) {
                    setImageList(groupImage.image_name)
                }
                else if (coverImage) {
                    setImageList(coverImage.image_name)
                }
            }
        }
    }

    getImagesByItemNumbers = async (products, tenantId) => {
        const productIds = []
        const imageList = []
        const imageProductIds = []

        const productObjMap = {}
        const groupIdByProductId = {}
        const itmNumByParentAndGroupId = {}
        const itmNumByParentAndProductId = {}

        const imageURL = this.getImageURL(tenantId)

        this.prepareProductMapping(
            products,
            productIds,
            productObjMap,
            groupIdByProductId,
            itmNumByParentAndGroupId,
            itmNumByParentAndProductId,
        )

        const images = await this.fetchImageMapping(
            productIds.map(id => new mongoose.Types.ObjectId(id))
        )

        this.prepareImageMapping(
            imageURL,
            images,
            imageProductIds,
            imageList,
            productObjMap,
            groupIdByProductId,
            itmNumByParentAndProductId,
            itmNumByParentAndGroupId,
        )

        const remainingProductIds = difference(productIds, imageProductIds)
        const remainingParentIdSet = new Set()

        remainingProductIds.forEach(remainingProductId => {
            const {
                _id,
                item_number,
                parent_id,
                group_value_id,
            } = productObjMap[remainingProductId]

            if (parent_id) {
                remainingParentIdSet.add(stringifyObjectId(parent_id))
            }
        })

        if (remainingParentIdSet.size) {
            const parentImageMap = new Map()

            const parentImages = await this.fetchImagesByProductIds(
                Array.from(remainingParentIdSet)
            )

            this.prepareParentImageMap(
                parentImages,
                parentImageMap,
                itmNumByParentAndGroupId,
            )

            this.prepareParentImageMapping(
                imageURL,
                itmNumByParentAndProductId,
                parentImageMap,
                groupIdByProductId,
                imageList,
            )
        }
        return imageList
    }
}

module.exports = ImageService


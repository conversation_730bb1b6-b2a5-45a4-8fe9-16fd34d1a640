const DeletedImageModel = require("../../Models/DeletedImageModel")

const { delay } = require("../../Utils/helpers")

class DeletedImageService {

    constructor() {
        this.limit = 1000
        this.maxAttempt = 5
        this.retryDelay = 2000 // In ms
    }

    removeDeletedImages = async () => {
        let lastImageId = null
        let attempt = 0

        do {
            try {
                const filter = {
                    "created_at": { $lt: moment().subtract(40, "days").toDate() }
                }

                if (lastImageId) {
                    filter._id = { "$gt": lastImageId }
                }

                const deletedImages = await DeletedImageModel.findDeletedImages(
                    filter,
                    "_id",
                    {
                        "lean": true,
                        "sort": { "_id": 1 },
                        "limit": this.limit
                    },
                )

                if (!deletedImages.length) {
                    break
                }

                const result = await DeletedImageModel.deleteMany({
                    _id: { $in: deletedImages.map(image => image._id) }
                })

                if (result.deletedCount) {
                    logger.info(`DeletedImageService: Removed ${result.deletedCount} deleted images`)
                }

                if (deletedImages.length < this.limit) {
                    break
                }

                lastImageId = deletedImages[deletedImages.length - 1]._id
                attempt = 0
            }
            catch (error) {
                attempt++
                logger.error(error)
                await delay(this.retryDelay)
            }
        } while (attempt <= this.maxAttempt)
    }
}

module.exports = new DeletedImageService()

const DeletedMaterializedProductModel = require("../../Models/materialized_product/DeletedMaterializedProductModel")

const { delay } = require("../../Utils/helpers")

class DeletedMaterializedProductService {

    constructor() {
        this.limit = 1000
        this.maxAttempt = 5
        this.retryDelay = 2000 // In ms
    }

    removeDeletedMaterializedProducts = async () => {
        let lastMaterializedProductId = null
        let attempt = 0

        do {
            try {
                const filter = {
                    "created_at": { $lt: moment().subtract(40, "days").toDate() }
                }

                if (lastMaterializedProductId) {
                    filter._id = { "$gt": lastMaterializedProductId }
                }

                const deletedMaterializedProducts = await DeletedMaterializedProductModel.findDeletedMaterializedProducts(
                    filter,
                    "_id",
                    {
                        "lean": true,
                        "sort": { "_id": 1 },
                        "limit": this.limit
                    },
                )

                if (!deletedMaterializedProducts.length) {
                    break
                }

                const result = await DeletedMaterializedProductModel.deleteMany({
                    _id: { $in: deletedMaterializedProducts.map(product => product._id) }
                })

                if (result.deletedCount) {
                    logger.info(`DeletedMaterializedProductService: Removed ${result.deletedCount} deleted materialized products`)
                }

                if (deletedMaterializedProducts.length < this.limit) {
                    break
                }

                lastMaterializedProductId = deletedMaterializedProducts[deletedMaterializedProducts.length - 1]._id
                attempt = 0
            }
            catch (error) {
                attempt++
                logger.error(error)
                await delay(this.retryDelay)
            }
        } while (attempt <= this.maxAttempt)
    }
}

module.exports = new DeletedMaterializedProductService()

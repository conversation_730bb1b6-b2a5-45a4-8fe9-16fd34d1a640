const InternalServiceModel = new (require("../../Models/InternalServiceModel"))()
const OrderModel = new (require("../../Models/OrderModel"))()

const { STATUS_CODES } = require("../../Configs/constants")

class OrderService {

    async updatePreApprovedStatus(customerUserRoleId, tenantId, orderId) {
        try {
            const customerApiParams = {
                "_id": customerUserRoleId,
                "projection": "-_id external_id",
                "options": { lean: true },
            }

            const customerInfo = await InternalServiceModel.getCustomer(customerApiParams)

            if (customerInfo?.external_id) {
                const {
                    statuscode,
                    data,
                    error
                } = await InternalServiceModel.checkCustomersPreApprovedStatus(
                    tenantId,
                    [customerInfo?.external_id]
                )

                if (statuscode === STATUS_CODES.SUCCESS) {
                    const preApproved = data?.[customerInfo?.external_id] ?? false

                    // Only update the order if the pre-approved check was successful
                    if (preApproved) {
                        await OrderModel.updateOrder(
                            {
                                _id: orderId,
                                tenant_id: tenantId
                            },
                            {
                                pre_approved: preApproved
                            }
                        )
                    }
                }
                else {
                    logger.error(error, {
                        errorMessage: "placeOrder: Failed to check customer pre-approved status"
                    })
                }
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: "placeOrder: Error in pre-approved status check"
            })
        }
    }
}

module.exports = new OrderService();

const isEmpty = require("lodash.isempty");

const CategorySchema = require("../Database/Schemas/CategorySchema");
const ProductSchema = require("../Database/Schemas/product/ProductSchema");
const CategoryStatisticSchema = require("../Database/Schemas/order/category_statistics");

const InternalServiceModel = new (require("../Models/InternalServiceModel"));

const {
  VALUES,
  ENTITY_STATUS,
  FALSELY_VALUES,
  APP_TYPE,
  NEW_PRODUCT_TYPE,
  PRODUCT_FILTER_TYPES,
  DEAL_STATUS,
} = require('../Configs/constants');

class CategoryModel {

  async categoryById(categoryId) {
    return CategorySchema.findById(categoryId);
  }

  async addFamilyCategory(body, header) {
    return CategorySchema({
      category_name: body.category_name,
      secondary_language_category_name: body.secondaryCategoryName,
      tenant_id: body.tenant_id,
      type: body.type,
      is_active: body.is_active,
      sequence: body.sequence,
      image_name: body.image_name,
      created_by: header.userDetails._id,
      updated_by: header.userDetails._id
    })
  }

  async addCategory(body, header) {
    return CategorySchema({
      category_name: body.category_name,
      secondary_language_category_name: body.secondaryCategoryName,
      tenant_id: body.tenant_id,
      type: body.type,
      family_id: body.family_id,
      parent_id: body.parent_id,
      is_active: body.is_active,
      sequence: body.sequence,
      created_by: header.userDetails._id,
      updated_by: header.userDetails._id
    })
  }

  async existCategoryName(categoryId, categoryName, tenantId, type, parentFamilyId, parentCategoryId) {

    const match = {
      tenant_id: tenantId,
      is_deleted: false,
      type: type,
    }

    if (type !== VALUES.category.FAMILY) {
      match["parent_id"] = type === VALUES.category.CATEGORY ? new mongoose.Types.ObjectId(parentFamilyId) : new mongoose.Types.ObjectId(parentCategoryId)
    }

    const pipeline = [
      {
        $match: match
      },
      {
        $addFields: {
          lowerName: {
            $toLower: "$category_name"
          }
        }
      },
      {
        $match: { lowerName: categoryName.toLowerCase() }
      }
    ]

    if (!categoryId) {
      return CategorySchema.aggregate(pipeline);
    } else {
      pipeline.unshift({
        $match: {
          _id: { $ne: new mongoose.Types.ObjectId(categoryId) }
        }
      });
      return CategorySchema.aggregate(pipeline);
    }
  }

  async countCategory(type, tenantId, parentId) {
    if (!parentId) {
      return CategorySchema.countDocuments({ type: type, tenant_id: tenantId, is_deleted: false })
    }
    return CategorySchema.countDocuments({ type: type, tenant_id: tenantId, parent_id: new mongoose.Types.ObjectId(parentId), is_deleted: false })
  }

  getCategoryCount(filter) {
    return CategorySchema.countDocuments(filter);
  }

  async categoryList(tenantId, type, status, categoryId, searchKey, sort = true, projection) {

    const match = {
      'tenant_id': tenantId,
      'type': type,
      'is_deleted': false
    }

    if (categoryId && type !== VALUES.category.FAMILY) {
      match["parent_id"] = new mongoose.Types.ObjectId(categoryId)
    }

    if (status !== ENTITY_STATUS.ALL) {
      match["is_active"] = status === ENTITY_STATUS.ACTIVE ? true : false
    }

    if (searchKey && !categoryId) {
      match["$or"] = [
        { category_name: { $regex: searchKey, $options: "i" } }
      ]
    }

    const pipeline = [
      {
        '$match': match
      },
    ]

    if (sort) {
      pipeline.push({
        "$sort": {
          "sequence": 1
        }
      })
    }

    if (projection) {
      pipeline.push({
        "$project": projection
      })
    }

    return CategorySchema.aggregate(pipeline);
  }

  getProductPipeline = (
    tenantId,
    priceListId,
    categoryType,
    pipelineType,
    familyId,
    hideOutOfStock,
    branchId,
    filters,
  ) => {
    const variables = {}
    const isOwnProductPipeline = pipelineType === "ownProducts"
    const pipelineExpression = []

    const matchFields = {
      "tenant_id": tenantId,
      "is_active": true,
      "is_deleted": false,
      "type": {
        $ne: NEW_PRODUCT_TYPE.VARIANT
      },
      "price_mappings": {
        "$elemMatch": {
          "master_price_id": new mongoose.Types.ObjectId(priceListId),
          "price": { "$gt": 0 }
        }
      }
    };

    if (hideOutOfStock && branchId) {
      matchFields["inventory_mappings"] = {
        "$elemMatch": {
          "branch_id": branchId,
          "quantity": { "$gt": 0 }
        }
      }
    }

    if (!isEmpty(filters)) {
      const {
        productType = [],
        brands = [],
        priceRange = {},
        tags = [],
        newItemDays,
      } = filters

      if (productType.length) {
        for (let type of productType) {

          if (type === PRODUCT_FILTER_TYPES.NEW_PRODUCTS) {
            matchFields["created_at"] = {
              "$gte": moment().subtract(newItemDays, "days").toDate()
            }
          }
        }
      }

      if (brands.length) {
        const brandIds = brands.map(id => new mongoose.Types.ObjectId(id))

        matchFields["brand_id"] = { "$in": brandIds }
      }

      if ("from" in priceRange && "to" in priceRange) {
        matchFields["price_mappings"] = {
          "$elemMatch": {
            "master_price_id": new mongoose.Types.ObjectId(priceListId),
            "price": {
              "$gte": +(priceRange.from || 0),
              "$lte": +(priceRange.to || 0),
            }
          }
        }
      }

      if (tags.length) {
        const tagsQuery = []

        tags.forEach(tag => {
          tagsQuery.push({
            tags: new mongoose.Types.ObjectId(tag)
          })
        })
        matchFields["$or"] = tagsQuery
      }
    }

    if (categoryType === VALUES.category.FAMILY) {
      variables["family_id"] = "$_id"

      pipelineExpression.push({
        "$eq": ["$family_id", "$$family_id"],
      })

      if (isOwnProductPipeline) {
        matchFields["category_id"] = {
          "$in": FALSELY_VALUES
        }
        matchFields["subcategory_id"] = {
          "$in": FALSELY_VALUES
        }
      }
    }
    else if (categoryType === VALUES.category.CATEGORY) {
      variables["family_id"] = "$parent_id"
      variables["category_id"] = "$_id"

      pipelineExpression.push(
        {
          "$eq": ["$family_id", "$$family_id"],
        },
        {
          "$eq": ["$category_id", "$$category_id"],
        },
      )

      if (isOwnProductPipeline) {
        matchFields["subcategory_id"] = {
          "$in": FALSELY_VALUES
        }
      }
    }
    else if (categoryType === VALUES.category.SUBCATEGORY) {
      variables["category_id"] = "$parent_id"
      variables["sub_category_id"] = "$_id"

      pipelineExpression.push(
        {
          "$eq": ["$category_id", "$$category_id"],
        },
        {
          "$eq": ["$subcategory_id", "$$sub_category_id"],
        },
      )

      matchFields["family_id"] = new mongoose.Types.ObjectId(familyId)
    }

    return {
      "$lookup": {
        "from": "products_2.0",
        "let": variables,
        "pipeline": [
          {
            "$match": {
              ...matchFields,
              "$expr": {
                "$and": pipelineExpression
              },
            },
          },
          {
            "$project": {
              "subcategory_id": 1,
              "category_id": 1,
              "family_id": 1
            }
          }
        ],
        "as": pipelineType
      }
    }
  }

  /** Get all category list with total & own product's count. */
  getAllCategoryList = async (
    tenantId,
    priceListId,
    categoryType,
    familyId,
    categoryId,
    hideOutOfStock,
    branchId,
    filters,
    otherMatches,
  ) => {
    const totalProductType = "totalProducts"
    const ownProductType = "ownProducts"
    const projections = {}

    // Category filters.
    const match = {
      "tenant_id": tenantId,
      "is_active": true,
      "is_deleted": false,
      "type": categoryType,
    };

    const getParentId = {
      [VALUES.category.CATEGORY]: familyId,
      [VALUES.category.SUBCATEGORY]: categoryId
    }
    const parentId = getParentId[categoryType]

    if (parentId) {
      match["parent_id"] = new mongoose.Types.ObjectId(parentId)
    }

    if (otherMatches) {
      for (const key in otherMatches) {
        if (Object.hasOwnProperty.call(otherMatches, key)) {
          match[key] = otherMatches[key]
        }
      }
    }

    // Product lookups based on category.
    const lookups = [
      this.getProductPipeline(
        tenantId,
        priceListId,
        categoryType,
        totalProductType,
        familyId,
        hideOutOfStock,
        branchId,
        filters,
      )
    ]

    if (categoryType !== VALUES.category.SUBCATEGORY) {
      lookups.push(
        this.getProductPipeline(
          tenantId,
          priceListId,
          categoryType,
          ownProductType,
          familyId,
          hideOutOfStock,
          branchId,
          filters,
        )
      )
    }

    // Api response projections.
    if (categoryType !== VALUES.category.SUBCATEGORY) {
      projections[ownProductType] = {
        "$size": `$${ownProductType}`,
      }
    }

    // Pipeline to run as an aggregation on category.
    const pipeline = [
      {
        "$match": match
      },
      ...lookups,
      {
        "$project": {
          "parent_id": 1,
          "category_name": 1,
          "secondary_language_category_name": 1,
          "sequence": 1,
          [totalProductType]: {
            "$size": `$${totalProductType}`,
          },
          ...projections,
        }
      },
      {
        "$sort": {
          "sequence": 1
        }
      }
    ]

    return CategorySchema.aggregate(pipeline)
  }

  async sequenceCategory(id, sequence, type, header) {
    return CategorySchema.updateMany({ _id: id, type: type }, { sequence: sequence, updated_by: header.userDetails._id })
  }

  async getProductList(familyId, categoryId, subCategoryId) {
    if (subCategoryId) {
      return ProductSchema.find({ family_id: familyId, category_id: categoryId, subcategory_id: subCategoryId, is_deleted: false }, { _id: 1 })
    } else if (categoryId) {
      return ProductSchema.find({ family_id: familyId, category_id: categoryId, is_deleted: false }, { _id: 1 })
    } else {
      return ProductSchema.find({ family_id: familyId, is_deleted: false }, { _id: 1 })
    }
  }

  // async updateCategory(_id, newFamilyId, newCategoryId, newSubCategoryId) {
  //   if (newSubCategoryId) {
  //     return ProductSchema.updateMany({ _id: {$in: _id} }, 
  //       { 
  //         family_id: newFamilyId , category_id: newCategoryId, subcategory_id: newSubCategoryId 
  //       });
  //   } else if (newCategoryId) {
  //     return ProductSchema.updateMany({ _id: {$in: _id} }, 
  //       { 
  //         family_id: newFamilyId , category_id: newCategoryId, subcategory_id: null 
  //       });
  //   } else {
  //     return ProductSchema.updateMany({ _id: {$in: _id} }, 
  //       { 
  //         family_id: newFamilyId , category_id: null, subcategory_id: null 
  //       });
  //   }
  // }

  async deleteCategory(_id, header) {
    return CategorySchema.updateMany({ _id: { $in: _id } }, { is_deleted: true, updated_by: header.userDetails._id })
  }

  async allCategory(filter = {}, projection = {}, options = {}) {
    return CategorySchema.find(filter, projection, options);
  }

  async topCategoryStatistic(tenantId, date, categoryId) {
    const match = {
      'tenant_id': tenantId,
      'date': date
    }
    if (categoryId) {
      match['category_id'] = { $in: categoryId }
    }
    const pipeline = [
      {
        '$match': match
      }, {
        '$group': {
          '_id': '$category_id',
          'sum': {
            '$sum': {
              '$add': [
                '$sales_amount', '$tax_amount'
              ]
            }
          },
          'quantity': {
            '$sum': {
              '$add': [
                '$quantity'
              ]
            }
          }
        }
      }, {
        '$project': {
          'total_amount': {
            '$multiply': [
              '$sum', '$quantity'
            ]
          }
        }
      }
    ]

    return await CategoryStatisticSchema.aggregate(pipeline)
  }

  async getCategory(categoryId) {
    return await CategorySchema.findOne({ _id: new mongoose.Types.ObjectId(categoryId) }, { category_name: 1, type: 1, parent_id: 1, family_id: 1, secondary_language_category_name: 1, sequence: 1, is_active: 1, product_counter: 1, is_deleted: 1, tenant_id: 1, image_name: 1 })
      .populate({ path: 'family_id', select: '_id category_name type parent_id family_id secondary_language_category_name sequence is_active product_counter' })
      .populate({ path: 'parent_id', select: '_id category_name type parent_id family_id secondary_language_category_name sequence is_active product_counter' })
    // return await CategorySchema.aggregate([
    //     {
    //       '$match': {
    //         '_id': new mongoose.Types.ObjectId(categoryId),
    //       }
    //     }, {
    //       '$lookup': {
    //         'from': 'categories', 
    //         'localField': 'parent_id', 
    //         'foreignField': '_id', 
    //         'as': 'category'
    //       }
    //     }, {
    //       '$unwind': {
    //         'path': '$category', 
    //         'preserveNullAndEmptyArrays': true
    //       }
    //     }, {
    //       '$lookup': {
    //         'from': 'categories', 
    //         'localField': 'category.parent_id', 
    //         'foreignField': '_id', 
    //         'as': 'family'
    //       }
    //     }, {
    //       '$unwind': {
    //         'path': '$family', 
    //         'preserveNullAndEmptyArrays': true
    //       }
    //     }, {
    //       '$project': {
    //         'created_by': 0, 
    //         'updated_by': 0, 
    //         'created_at': 0, 
    //         'updated_at': 0, 
    //         'category.created_by': 0, 
    //         'category.updated_by': 0, 
    //         'category.created_at': 0, 
    //         'category.updated_at': 0, 
    //         'family.created_by': 0, 
    //         'family.updated_by': 0, 
    //         'family.created_at': 0, 
    //         'family.updated_at': 0
    //       }
    //     }
    //   ])

  }

  async categoryAndSubcategoryExist(tenantId, familyId) {
    return ProductSchema.countDocuments({ tenant_id: tenantId, family_id: new mongoose.Types.ObjectId(familyId), is_deleted: false })
  }

  async categoryProductCount(tenantId, familyId, categoryId, subCategoryId) {
    const filter = {
      tenant_id: tenantId,
      family_id: new mongoose.Types.ObjectId(familyId),
      // category_id: null,
      // subcategory_id: null,
      // is_active: true,
      is_deleted: false
    }

    if (categoryId) {
      filter["category_id"] = new mongoose.Types.ObjectId(categoryId)
    }

    if (subCategoryId) {
      filter["subcategory_id"] = new mongoose.Types.ObjectId(subCategoryId)
    }

    return ProductSchema.countDocuments(filter);
  }

  async productCount(familyId, categoryId, subCategoryId) {
    const filter = {
      family_id: new mongoose.Types.ObjectId(familyId),
      is_active: true,
      is_deleted: false
    }

    if (categoryId) {
      filter["category_id"] = new mongoose.Types.ObjectId(categoryId)
    }

    if (subCategoryId) {
      filter["subcategory_id"] = new mongoose.Types.ObjectId(subCategoryId)
    }

    return ProductSchema.countDocuments(filter);
  }

  async categoryExist(tenantId, categoryId) {
    return CategorySchema.findOne({ tenant_id: tenantId, parent_id: new mongoose.Types.ObjectId(categoryId), is_deleted: false })
  }

  async productList(req, categoryId, subCategoryId) {
    let {
      page,
      perPage,
      tenantId,
      familyId,
      status,
      incrementLimit = false,
      appType = APP_TYPE.WEB,
      priceListId,
      hideOutOfStock,
      branchId,
      filters,
      salesPersonUserRoleId,
      isProductSplitting,
    } = req.query

    const userRoleId = req.headers.userroleid;
    const category = req.query.categoryId || categoryId
    const subCategory = req.query.subCategoryId || subCategoryId

    const offset = (page - 1) * perPage;

    const filter = {
      tenant_id: tenantId,
      family_id: new mongoose.Types.ObjectId(familyId),
      category_id: null,
      subcategory_id: null,
      is_deleted: false,
      type: { $ne: NEW_PRODUCT_TYPE.VARIANT },
    }

    const date = moment().utc().toDate();

    const project = {
      'product_order': 1,
      'cover_image': 1,
      'item_number': 1,
    }

    if (appType === APP_TYPE.NATIVE) {
      if (priceListId) {
        filter["price_mappings"] = {
          "$elemMatch": {
            "master_price_id": new mongoose.Types.ObjectId(priceListId),
            "price": { "$gt": 0 }
          }
        }
      }

      if (hideOutOfStock === "true" && branchId) {
        filter["inventory_mappings"] = {
          "$elemMatch": {
            "branch_id": branchId,
            "quantity": { "$gt": 0 }
          }
        }
      }

      if (!isEmpty(filters)) {
        const {
          productType = [],
          brands = [],
          priceRange = {},
          tags = [],
        } = filters

        if (productType.length) {
          for await (let type of productType) {

            if (type === PRODUCT_FILTER_TYPES.NEW_PRODUCTS) {
              const settings = await InternalServiceModel.tenantAppSettings(tenantId, "consider_new_item")

              const days = "consider_new_item" in settings
                ? settings.consider_new_item
                : 30

              filter["created_at"] = {
                "$gte": moment().subtract(days, "days").toDate()
              }
            }
          }
        }

        if (brands.length) {
          const brandIds = brands.map(id => new mongoose.Types.ObjectId(id))

          filter["brand_id"] = { "$in": brandIds }
        }

        if ("from" in priceRange && "to" in priceRange) {
          filter["price_mappings"] = {
            "$elemMatch": {
              "master_price_id": new mongoose.Types.ObjectId(priceListId),
              "price": {
                "$gte": +(priceRange.from || 0),
                "$lte": +(priceRange.to || 0),
              }
            }
          }
        }

        if (tags.length) {
          const tagsQuery = []

          tags.forEach(tag => {
            tagsQuery.push({
              tags: new mongoose.Types.ObjectId(tag)
            })
          })
          filter["$or"] = tagsQuery
        }
      }

      project['variant_count'] = 1
      project['title'] = 1
      project['secondary_language_title'] = 1
      project['price_mappings'] = 1
      project['inventory_mappings'] = 1
      project['type'] = 1;
      project['variants'] = 1;
      project['groups'] = 1;
      project['family_id'] = 1;
      project['category_id'] = 1;
      project['is_favorite'] = { $toBool: "$is_favorite" };
      project['variant_deal_info'] = {
        $cond: [{ $eq: ['$variant_deal_size', 1] }, '$variant_deal', null]
      };
      project['product_deal_info'] = 1;
      project['variant_deal_size'] = 1;
      project['multipleDeal'] = {
        $cond: [{ $gt: ['$variant_deal_size', 1] }, true, false
        ]
      };
    }

    if (incrementLimit) {
      perPage = perPage + 1
    }

    if (status !== ENTITY_STATUS.ALL) {
      filter.is_active = status === ENTITY_STATUS.ACTIVE ? true : false
    }

    if (category) {
      filter["category_id"] = new mongoose.Types.ObjectId(category)
    }

    if (subCategory) {
      filter["subcategory_id"] = new mongoose.Types.ObjectId(subCategory)
    }

    const pipeline = [
      {
        '$match': filter
      },
      {
        '$sort': {
          'product_order': 1,
        }
      },
    ];

    pipeline.push(
      {
        $lookup: {
          from: "images_2.0",
          as: "cover_image",
          foreignField: "product_variant_id",
          localField: "_id",
          pipeline: [
            {
              $match: {
                group_id: null
              }
            },
            {
              $project: {
                _id: 1,
                image_name: 1,
                s3_url: 1,
                image_number: 1,
                updated_at: 1
              }
            },
            {
              $sort: { "image_number": 1 }
            },
            {
              $limit: 1
            }
          ]
        }
      },
    )

    if (priceListId) {

      const dealLookUpMatch = {
        tenant_id: tenantId,
        deal_status: {
          $nin: [DEAL_STATUS.PROGRESS, DEAL_STATUS.PAUSED, DEAL_STATUS.CANCELLED],
        },
        price_id: new mongoose.Types.ObjectId(priceListId)
      }

      if (salesPersonUserRoleId) {
        dealLookUpMatch['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserRoleId);
      }

      pipeline.push(
        {
          $lookup: {
            from: "deal_products",
            localField: "_id",
            foreignField: "parent_id",
            as: "variant_deal",
            pipeline: [
              {
                $match: {
                  deal_from_date: { $lte: date },
                  deal_to_date: { $gte: date },
                  price_id: new mongoose.Types.ObjectId(priceListId)
                }
              },
              {
                $lookup: {
                  from: 'deals',
                  localField: 'deal_id',
                  foreignField: '_id',
                  as: 'deal_id',
                  pipeline: [
                    {
                      $match: dealLookUpMatch
                    },
                    {
                      $project: {
                        deal_type: 1,
                        deal_name: 1,
                        secondary_deal_name: 1,
                        deal_from_date: 1,
                        deal_to_date: 1
                      }
                    }
                  ]
                },
              },
              {
                $addFields: {
                  deal_id: { $first: "$deal_id" }
                }
              },
              {
                $match: { deal_id: { $ne: null } }
              },
              {
                $project: {
                  product_id: 0,
                  is_active: 0,
                  created_by: 0,
                  updated_by: 0,
                  created_at: 0,
                  updated_at: 0,
                  __v: 0,
                  deal_product_sequence: 0,
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: "deal_products",
            localField: "_id",
            foreignField: "product_id",
            as: "product_deal_info",
            pipeline: [
              {
                $match: {
                  deal_from_date: { $lte: date },
                  deal_to_date: { $gte: date },
                  price_id: new mongoose.Types.ObjectId(priceListId)
                }
              },
              {
                $lookup: {
                  from: 'deals',
                  localField: 'deal_id',
                  foreignField: '_id',
                  as: 'deal_id',
                  pipeline: [
                    {
                      $match: dealLookUpMatch
                    },
                    {
                      $project: {
                        deal_type: 1,
                        deal_name: 1,
                        secondary_deal_name: 1,
                        deal_from_date: 1,
                        deal_to_date: 1
                      }
                    }
                  ]
                },
              },
              {
                $addFields: {
                  deal_id: { $first: "$deal_id" }
                }
              },
              {
                $match: { deal_id: { $ne: null } }
              },
              {
                $project: {
                  product_id: 0,
                  is_active: 0,
                  created_by: 0,
                  updated_by: 0,
                  created_at: 0,
                  updated_at: 0,
                  __v: 0,
                  deal_product_sequence: 0,
                }
              }
            ]
          }
        },
        {
          $addFields: {
            product_deal_info: {
              $first: "$product_deal_info"
            },
            variant_deal_size: {
              $size: "$variant_deal"
            },
            variant_deal: {
              $first: "$variant_deal"
            },
          }
        }
      )
    }

    if (appType === APP_TYPE.NATIVE) {
      pipeline.push(
        {
          '$lookup': {
            'from': "favorite_products_2.0",
            'localField': '_id',
            'foreignField': "product_variant_id",
            'as': 'isFavorite',
            'pipeline': [
              {
                '$match': {
                  'user_role_id': new mongoose.Types.ObjectId(userRoleId),
                  'tenant_id': tenantId
                }
              }
            ]
          }
        },
        {
          $addFields: {
            'cover_image': {
              $first: '$cover_image'
            },
            'is_favorite': {
              $size: '$isFavorite'
            }
          }
        }
      )

      if (isProductSplitting) {
        /**
         * Added variant products in the list to show,
         * group products in the listing from mobile side.
         */
        const variantProductFilter = {
          is_deleted: false,
          group_value_id: {
            $ne: null,
          }
        }
        let priceMappingProject = 1

        if ("price_mappings" in filter) {
          variantProductFilter.price_mappings = filter.price_mappings

          priceMappingProject = {
            "$filter": {
              "input": "$price_mappings",
              "as": "item",
              "cond": {
                "$eq": ["$$item.master_price_id", new mongoose.Types.ObjectId(priceListId)]
              }
            }
          }
        }

        if ("inventory_mappings" in filter) {
          variantProductFilter.inventory_mappings = filter.inventory_mappings
        }

        if ("is_active" in filter) {
          variantProductFilter.is_active = filter.is_active
        }

        pipeline.push(
          {
            "$lookup": {
              "from": "products_2.0",
              "localField": "_id",
              "foreignField": "parent_id",
              "as": "variant_products",
              "pipeline": [
                {
                  "$match": variantProductFilter,
                },
                {
                  "$project": {
                    "price_mappings": priceMappingProject,
                    "inventory_mappings": 1,
                    "parent_id": 1,
                    "variant_value_id": 1,
                    "group_value_id": 1,
                  }
                },
                {
                  "$lookup": {
                    "from": "variant_types_2.0",
                    "localField": "group_value_id",
                    "foreignField": "_id",
                    "as": "group_value_id",
                    "pipeline": [
                      {
                        "$project": {
                          "name": 1,
                          "product_id": 1,
                          "order": 1,
                        }
                      },
                      {
                        "$lookup": {
                          "from": "images_2.0",
                          "localField": "product_id",
                          "foreignField": "product_variant_id",
                          "as": "group_cover_image",
                          "let": {
                            "groupId": "$_id"
                          },
                          "pipeline": [
                            {
                              "$match": {
                                "$expr": {
                                  "$eq": ["$group_id", "$$groupId"],
                                },
                                "group_id": { "$ne": null },
                              },
                            },
                            {
                              "$project": {
                                "image_name": 1,
                                "image_number": 1,
                                'product_variant_id': 1,
                                "s3_url": 1,
                                "updated_at": 1,
                              },
                            },
                            {
                              "$sort": { "image_number": 1 },
                            },
                            {
                              "$limit": 1,
                            },
                          ],
                        },
                      },
                      {
                        "$project": {
                          "name": 1,
                          "group_cover_image": {
                            "$first": "$group_cover_image"
                          },
                          "order": 1,
                        }
                      },
                    ]
                  }
                },
                {
                  "$addFields": {
                    "group_value_id": {
                      "$first": "$group_value_id"
                    }
                  }
                },
                {
                  "$lookup": {
                    "from": "images_2.0",
                    "localField": "_id",
                    "foreignField": "product_variant_id",
                    "as": "variant_cover_image",
                    "pipeline": [
                      {
                        "$match": {
                          "group_id": null
                        },
                      },
                      {
                        "$project": {
                          "image_name": 1,
                          "image_number": 1,
                          'product_variant_id': 1,
                          "s3_url": 1,
                          "updated_at": 1,
                        },
                      },
                      {
                        "$sort": { "image_number": 1 },
                      },
                      {
                        "$limit": 1,
                      },
                    ],
                  },
                },
                {
                  "$addFields": {
                    "variant_cover_image": {
                      "$first": "$variant_cover_image"
                    }
                  }
                }
              ]
            },
          },
        )
        project["variant_products"] = 1
      }
    }
    else {
      pipeline.push(
        {
          $addFields: {
            'cover_image': {
              $first: '$cover_image'
            }
          }
        }
      )
    }

    pipeline.push(
      {
        '$project': project
      },
      {
        '$facet': {
          'list':
            page === 0 && perPage === 0
              ? []
              : [{ '$skip': offset }, { '$limit': perPage }],
          'count': [{ '$count': 'count' }]
        }
      },
      {
        '$unwind': '$count'
      }
    )

    const data = await ProductSchema.aggregate(pipeline);
    const list = data.length ? data[0].list : [];
    const count = data.length ? data[0].count.count : 0;
    return { count, list };
  }

  async productSequence(productId, productSequence, header) {
    return ProductSchema.findByIdAndUpdate(
      productId,
      {
        "product_order": productSequence,
        "updated_by": header.userDetails._id
      },
      {
        "lean": true,
        "new": true,
      }
    )
  }

}

module.exports = CategoryModel;

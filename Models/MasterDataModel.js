const axios = require("axios").default

const MasterAttributeSchema = require("../Database/Schemas/master_data/MasterAttributeSchema")
const MasterAttributeSetSchema = require("../Database/Schemas/master_data/MasterAttributeSetSchema")
const MasterBrandSchema = require("../Database/Schemas/master_data/MasterBrandSchema")
const MasterUnitSchema = require("../Database/Schemas/master_data/MasterUnitSchema")
const MasterPriceSchema = require("../Database/Schemas/master_data/MasterPriceSchema")

const { ENTITY_STATUS, MASTER_DATA_ENTITIES, VALUES } = require("../Configs/constants")
const { MASTER_DATA } = require("../Configs/ApiType")

class MasterDataModel {
    async customError(err) {
        let error = err || {}
        const errorCode = error.response?.status || STATUS_CODES.SERVER_ERROR

        return {
            code: errorCode,
            message: error.message,
            data: error.response?.data,
            error,
        }
    }

    async getTenantInfo(_id, projection, options, populate) {
        const params = {
            _id,
            projection,
            options,
            populate
        }
        const url = VALUES.internalServiceBaseURL + "tenant"

        const authResponse = await axios({
            url,
            params,
        })
        const details = authResponse?.["data"]?.["data"]
        return details
    }

    async updateTenantInfo(_id, incObj) {
        const data = {
            _id,
            incObj,
        }
        const url = VALUES.internalServiceBaseURL + "tenant"
        const authResponse = await axios.put(url, data)
        const details = authResponse?.["data"]?.["data"]
        return details
    }

    async getApiParams(apiType, reqBody) {
        const {
            attributeName,
            secondaryLanguageAttributeName,
            attributeSetName,
            secondaryLanguageAttributeSetName,
            brandName,
            secondaryLanguageBrandName,
            unitName,
            secondaryLanguageUnitName,
            priceName,
            secondaryLanguagePriceName,
            tenantId,
            isDefault
        } = reqBody || {}
        let params = {}

        switch (apiType) {
            // Attribute Apis
            case MASTER_DATA.ATTRIBUTE.ADD:
            case MASTER_DATA.ATTRIBUTE.EDIT: {
                params["attribute_name"] = attributeName
                params["secondary_language_attribute_name"] = secondaryLanguageAttributeName
                break
            }

            // Attribute Set Apis
            case MASTER_DATA.ATTRIBUTE_SET.ADD:
            case MASTER_DATA.ATTRIBUTE_SET.EDIT: {
                params["attribute_set_name"] = attributeSetName
                params["secondary_language_attribute_set_name"] = secondaryLanguageAttributeSetName
                break
            }

            // Brand Apis
            case MASTER_DATA.BRAND.ADD:
            case MASTER_DATA.BRAND.EDIT: {
                params["brand_name"] = brandName
                params["secondary_language_brand_name"] = secondaryLanguageBrandName
                break
            }

            // Unit Apis
            case MASTER_DATA.UNIT.ADD:
            case MASTER_DATA.UNIT.EDIT: {
                params["unit_name"] = unitName
                params["secondary_language_unit_name"] = secondaryLanguageUnitName
                break
            }

            // Price Apis
            case MASTER_DATA.PRICE.ADD: {
                const tenantInfo = await this.getTenantInfo(tenantId, "master_price_id")

                if (tenantInfo?.master_price_id) {
                    const { prefix, current_counter } = tenantInfo.master_price_id
                    let counter = current_counter + 1

                    if (counter < 10) {
                        counter = "0" + counter
                    }
                    params["price_id"] = prefix + counter
                }
                params["price_name"] = priceName
                params["secondary_language_price_name"] = secondaryLanguagePriceName
                params["is_default"] = isDefault
                break
            }

            case MASTER_DATA.PRICE.EDIT: {
                params["price_name"] = priceName
                params["secondary_language_price_name"] = secondaryLanguagePriceName
                params["is_default"] = isDefault
                break
            }

            default:
                params = params
        }
        return params
    }

    getSearchParams(apiType, regex) {
        const params = {
            [MASTER_DATA.ATTRIBUTE.LIST]: [
                { "attribute_name": regex },
                { "secondary_language_attribute_name": regex },
            ],
            [MASTER_DATA.ATTRIBUTE_SET.LIST]: [
                { "attribute_set_name": regex },
                { "secondary_language_attribute_set_name": regex },
            ],
            [MASTER_DATA.BRAND.LIST]: [
                { "brand_name": regex },
                { "secondary_language_brand_name": regex },
            ],
            [MASTER_DATA.UNIT.LIST]: [
                { "unit_name": regex },
                { "secondary_language_unit_name": regex },
            ],
            [MASTER_DATA.PRICE.LIST]: [
                { "price_name": regex },
                { "secondary_language_price_name": regex },
            ],
            [MASTER_DATA.ASSOCIATED_ATTRIBUTES.LIST]: [
                { "attribute_name": regex },
            ],
        }
        return params[apiType]
    }

    getApiSchema(apiType) {
        let schema = ""

        switch (apiType) {
            // Attribute Apis
            case MASTER_DATA.ATTRIBUTE.ADD:
            case MASTER_DATA.ATTRIBUTE.EDIT:
            case MASTER_DATA.ATTRIBUTE.LIST:
            case MASTER_DATA.ATTRIBUTE.DELETE:
            case MASTER_DATA.ASSOCIATED_ATTRIBUTES.LIST: {
                schema = MasterAttributeSchema
                break
            }

            // Attribute Set Apis
            case MASTER_DATA.ATTRIBUTE_SET.ADD:
            case MASTER_DATA.ATTRIBUTE_SET.EDIT:
            case MASTER_DATA.ATTRIBUTE_SET.LIST:
            case MASTER_DATA.ATTRIBUTE_SET.DELETE: {
                schema = MasterAttributeSetSchema
                break
            }

            // Brand Apis
            case MASTER_DATA.BRAND.ADD:
            case MASTER_DATA.BRAND.EDIT:
            case MASTER_DATA.BRAND.LIST:
            case MASTER_DATA.BRAND.DELETE: {
                schema = MasterBrandSchema
                break
            }

            // Unit Apis
            case MASTER_DATA.UNIT.ADD:
            case MASTER_DATA.UNIT.EDIT:
            case MASTER_DATA.UNIT.LIST:
            case MASTER_DATA.UNIT.DELETE: {
                schema = MasterUnitSchema
                break
            }

            // Price Apis
            case MASTER_DATA.PRICE.ADD:
            case MASTER_DATA.PRICE.EDIT:
            case MASTER_DATA.PRICE.LIST:
            case MASTER_DATA.PRICE.DELETE: {
                schema = MasterPriceSchema
                break
            }

            default:
                schema = MasterAttributeSchema
        }
        return schema
    }

    getApiFindElement(apiType, reqBody) {
        const {
            attributeName,
            attributeSetName,
            brandName,
            unitName,
            priceName,
        } = reqBody
        let element = {
            key: "",
            value: ""
        }

        switch (apiType) {
            case MASTER_DATA.ATTRIBUTE.ADD:
            case MASTER_DATA.ATTRIBUTE.EDIT: {
                element["key"] = "attribute_name"
                element["value"] = attributeName
                break
            }

            case MASTER_DATA.ATTRIBUTE_SET.ADD:
            case MASTER_DATA.ATTRIBUTE_SET.EDIT: {
                element["key"] = "attribute_set_name"
                element["value"] = attributeSetName
                break
            }

            case MASTER_DATA.BRAND.ADD:
            case MASTER_DATA.BRAND.EDIT: {
                element["key"] = "brand_name"
                element["value"] = brandName
                break
            }

            case MASTER_DATA.UNIT.ADD:
            case MASTER_DATA.UNIT.EDIT: {
                element["key"] = "unit_name"
                element["value"] = unitName
                break
            }

            case MASTER_DATA.PRICE.ADD:
            case MASTER_DATA.PRICE.EDIT: {
                element["key"] = "price_name"
                element["value"] = priceName
                break
            }

            default:
                element = element
        }
        return element
    }

    getEntityName(reqPath) {
        const entityByPath = {
            "/attribute": MASTER_DATA_ENTITIES.ATTRIBUTE,
            "/attributeSet": MASTER_DATA_ENTITIES.ATTRIBUTE_SET,
            "/brand": MASTER_DATA_ENTITIES.BRAND,
            "/unit": MASTER_DATA_ENTITIES.UNIT,
            "/price": MASTER_DATA_ENTITIES.PRICE,
        }
        return entityByPath[reqPath]
    }

    async findAttribute(Schema, key, value, tenantId) {
        const pipeline = [
            {
                "$match": {
                    "tenant_id": tenantId,
                    "is_deleted": false,
                }
            },
            {
                "$addFields": {
                    "lowerName": {
                        "$toLower": `$${key}`
                    }
                }
            },
            {
                "$match": {
                    "lowerName": value.toLowerCase()
                }
            },
            {
                "$limit": 1,
            },
        ]

        const attribute = await Schema.aggregate(pipeline)
        return attribute?.[0] || {}
    }

    async findAttributeById(Schema, _id) {
        const attribute = await Schema.findById(new mongoose.Types.ObjectId(_id))
        return attribute
    }

    async updateBulkDataByIds(Schema, ids, fieldsToUpdate) {
        const response = await Schema.updateMany(
            {
                "_id": { "$in": ids }
            },
            {
                "$set": fieldsToUpdate,
            }
        )
        return response
    }

    async getAttribute(req, apiType) {
        const { tenantId } = req.body
        const Schema = this.getApiSchema(apiType)
        const element = this.getApiFindElement(apiType, req.body)

        const attribute = await this.findAttribute(
            Schema,
            element.key,
            element.value,
            tenantId,
        )
        return attribute
    }

    async getAttributeById(_id, apiType) {
        const Schema = this.getApiSchema(apiType)

        const attribute = await this.findAttributeById(
            Schema,
            _id
        )
        return attribute
    }

    async addAttribute(req, apiType) {
        const { tenantId, isActive } = req.body
        const { userDetails } = req.headers
        const Schema = this.getApiSchema(apiType)

        const attribute = await Schema.create({
            "tenant_id": tenantId,
            "is_active": isActive,
            "created_by": userDetails._id,
            "updated_by": userDetails._id,
            ...await this.getApiParams(apiType, req.body)
        })

        if (apiType === MASTER_DATA.PRICE.ADD) {
            const incObj = {
                "master_price_id.current_counter": 1
            }
            const updateInfo = await this.updateTenantInfo(tenantId, incObj)
        }
        return attribute
    }

    async editAttribute(req, apiType, attribute) {
        const { tenantId, isActive } = req.body
        const { userDetails } = req.headers

        attribute["tenant_id"] = +tenantId
        attribute["is_active"] = isActive
        attribute["updated_by"] = userDetails._id

        const otherParams = await this.getApiParams(apiType, req.body)

        Object
            .entries(otherParams)
            .forEach(([key, value]) => {
                attribute[key] = value
            })
        await attribute.save()

        return attribute
    }

    async getAttributeList(req, apiType, ids) {
        let { tenantId, status, searchKey, perPage = 10, page = 1, projection } = req.query
        tenantId = +tenantId
        perPage = +perPage
        page = +page

        const offset = perPage * (page - 1)

        const $match = {
            "is_deleted": false,
            "tenant_id": tenantId,
        }

        if (ids?.length) {
            $match._id = {
                "$in": ids
            }
        }

        if (status !== ENTITY_STATUS.ALL) {
            $match.is_active = status === ENTITY_STATUS.ACTIVE
                ? true
                : false
        }

        if (searchKey?.trim()) {
            const regex = {
                "$regex": searchKey.trim(),
                "$options": "i"
            }

            $match.$or = [
                ...this.getSearchParams(apiType, regex)
            ]
        }

        const pipeline = [{
            $match
        }]

        if (projection) {
            pipeline.push({
                "$project": projection
            })
        }

        let listOptions = [
            {
                "$skip": offset
            },
            {
                "$limit": perPage
            }
        ]

        if (!perPage && !page) {
            listOptions = []
        }

        pipeline.push(
            {
                "$facet": {
                    list: listOptions,
                    count: [{
                        "$count": "count"
                    }]
                }
            },
            {
                $unwind: "$count"
            }
        )
        const Schema = this.getApiSchema(apiType)
        const data = await Schema.aggregate(pipeline)

        const list = data.length ? data[0].list : []
        const count = data.length ? data[0].count.count : 0

        return {
            list,
            count
        }
    }

    async updateAttributes(req, updateField, apiType) {
        const { ids, tenantId } = req.body
        const { userDetails } = req.headers

        const fieldsToUpdate = {
            ...updateField,
            "tenant_id": +tenantId,
            "updated_by": userDetails._id,
        }
        const Schema = this.getApiSchema(apiType)
        const response = await this.updateBulkDataByIds(Schema, ids, fieldsToUpdate)
        return response
    }

    async updateAttributeAssociation(req) {
        const { attributeSetId, attributeIds } = req.body

        const response = await MasterAttributeSetSchema.updateMany(
            {
                "_id": attributeSetId
            },
            {
                "$set": {
                    "updated_by": req.headers.userDetails._id,
                    "attribute_ids": [...new Set(attributeIds)]
                },
            }
        )
        return response
    }

    async masterPriceList(tenantId, status = ENTITY_STATUS.ACTIVE, projection = "", options = {}) {
        const filter = {
            is_deleted: false,
            tenant_id: tenantId,
        }

        if (status !== ENTITY_STATUS.ALL) {
            filter["is_active"] = status === ENTITY_STATUS.ACTIVE ? true : false
        }

        return MasterPriceSchema.find(filter, projection, options)
    }

    findMasterPriceList = (filter, projection, options) => {
        return MasterPriceSchema.find(filter, projection, options)
    }

    findBrandList = (filter, projection, options) => {
        return MasterBrandSchema.find(filter, projection, options)
    }

    findUomList = (filter, projection, options) => {
        return MasterUnitSchema.find(filter, projection, options)
    }

    findAttributeList = (filter, projection, options) => {
        return MasterAttributeSchema.find(filter, projection, options)
    }

    findUnitList = (filter, projection, options) => {
        return MasterUnitSchema.find(filter, projection, options)
    }
}

module.exports = MasterDataModel

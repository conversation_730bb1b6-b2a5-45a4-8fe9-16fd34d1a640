const ProductSchema = require("../Database/Schemas/product/ProductSchema")
const DealProductSchema = require("../Database/Schemas/deal/deal_products")

class DealProductModel {

    getProductListByItemNumbers = async (req) => {
        const {
            tenantId,
            priceId,
            itemNumbers,
            page = 0,
            perPage = 20,
        } = req.body

        const offset = (page - 1) * perPage
        const uniqueItemNumbers = itemNumbers.map(itemNumber => `${tenantId}_${itemNumber}`)

        const pipeline = [
            {
                "$match": {
                    "unique_item_number": {
                        "$in": uniqueItemNumbers,
                    },
                },
            },
            {
                "$project": {
                    "item_number": 1,
                    "title": 1,
                    "type": 1,
                    "parent_id": 1,
                    "variant_value_id": 1,
                    "group_value_id": 1,
                    "price_mappings": {
                        "$first": {
                            "$filter": {
                                "input": "$price_mappings",
                                "as": "price_mappings",
                                "cond": {
                                    "$eq": [
                                        "$$price_mappings.master_price_id",
                                        new mongoose.Types.ObjectId(priceId),
                                    ],
                                },
                            },
                        },
                    },
                    "inventory": {
                        "$sum": "$inventory_mappings.quantity",
                    },
                },
            },
            {
                "$lookup": {
                    "from": "products_2.0",
                    "localField": "parent_id",
                    "foreignField": "_id",
                    "as": "parent_product",
                    "pipeline": [
                        {
                            "$project": {
                                "item_number": 1,
                                "title": 1,
                            },
                        },
                        {
                            "$lookup": {
                                "from": "images_2.0",
                                "localField": "_id",
                                "foreignField": "product_variant_id",
                                "as": "image",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "group_id": null,
                                        },
                                    },
                                    {
                                        "$project": {
                                            "image_number": 1,
                                            "image_name": 1,
                                            "s3_url": 1,
                                        },
                                    },
                                    {
                                        "$sort": {
                                            "image_number": 1,
                                        },
                                    },
                                    {
                                        "$limit": 1,
                                    },
                                ],
                            },
                        },
                        {
                            "$project": {
                                "item_number": 1,
                                "title": 1,
                                "image": {
                                    "$first": "$image",
                                },
                            },
                        },
                    ],
                },
            },
            {
                "$addFields": {
                    "parent_product": {
                        "$first": "$parent_product",
                    },
                },
            },
            {
                "$lookup": {
                    "from": "images_2.0",
                    "localField": "_id",
                    "foreignField": "product_variant_id",
                    "as": "image",
                    "pipeline": [
                        {
                            "$match": {
                                "group_id": null,
                            },
                        },
                        {
                            "$project": {
                                "image_number": 1,
                                "image_name": 1,
                                "s3_url": 1,
                            },
                        },
                        {
                            "$sort": {
                                "image_number": 1,
                            },
                        },
                        {
                            "$limit": 1,
                        },
                    ],
                },
            },
            {
                "$addFields": {
                    "image": {
                        "$first": "$image",
                    },
                },
            },
            {
                "$lookup": {
                    "from": "images_2.0",
                    "let": {
                        "group_id": "$group_value_id",
                    },
                    "localField": "group_value_id",
                    "foreignField": "group_id",
                    "as": "group_cover_image",
                    "pipeline": [
                        {
                            "$match": {
                                "group_id": {
                                    "$ne": null,
                                },
                                "$expr": {
                                    "$eq": ["$group_id", "$$group_id"],
                                },
                            },
                        },
                        {
                            "$project": {
                                "image_number": 1,
                                "image_name": 1,
                                "s3_url": 1,
                            },
                        },
                        {
                            "$sort": {
                                "image_number": 1,
                            },
                        },
                        {
                            "$limit": 1,
                        },
                    ],
                },
            },
            {
                "$lookup": {
                    "from": 'variant_types_2.0',
                    "localField": 'variant_value_id',
                    "foreignField": '_id',
                    "as": 'variant_value_id',
                    "pipeline": [
                        {
                            "$project": {
                                "name": 1
                            },
                        },
                    ],
                },
            },
            {
                "$lookup": {
                    "from": 'variant_types_2.0',
                    "localField": 'group_value_id',
                    "foreignField": '_id',
                    "as": 'group_value_id',
                    "pipeline": [
                        {
                            "$project": {
                                "name": 1
                            },
                        },
                    ],
                },
            },
            {
                "$addFields": {
                    "group_cover_image": {
                        "$first": "$group_cover_image",
                    },
                    "variant_name": {
                        "$first": '$variant_value_id.name',
                    },
                    "group_name": {
                        "$first": '$group_value_id.name',
                    },
                },
            },
            {
                "$project": {
                    "parent_id": 0,
                    "variant_value_id": 0,
                    "group_value_id": 0,
                },
            },
            {
                "$facet": {
                    "list": [
                        {
                            "$skip": offset,
                        },
                        {
                            "$limit": perPage,
                        },
                    ],
                    "count": [
                        {
                            "$count": "count",
                        },
                    ],
                },
            },
            {
                "$unwind": {
                    "path": "$count",
                },
            },
        ]

        const data = await ProductSchema.aggregate(pipeline)

        return {
            count: data[0]?.count?.count || 0,
            list: data[0]?.list || [],
        }
    }

    getDealProductCount(filter) {
        return DealProductSchema.countDocuments(filter);
    }

    getDealProducts(filter, selectFields, options) {
        return DealProductSchema.find(filter, selectFields, options);
    }
}

module.exports = DealProductModel

const OrderExportTaskSchema = require("../Database/Schemas/order/OrderExportTaskSchema")

module.exports = class {

    createOrderExportTask = (body) => {
        return new OrderExportTaskSchema(body).save()
    }

    updateOrderExportTask = (filter, object, options) => {
        return OrderExportTaskSchema.updateOne(filter, object, options)
    }

    updateOrderExportTaskById = (_id, object, options) => {
        return this.updateOrderExportTask(
            {
                _id
            },
            object,
            options
        )
    }

    deleteOrderExportTasks = async (filter) => {
        return await OrderExportTaskSchema.deleteMany(filter)
    }

    findOrderExportTasks = (filter, projection, option) => {
        return OrderExportTaskSchema.find(filter, projection, option)
    }

    findOrderExportTask = (filter, projection, option) => {
        return OrderExportTaskSchema.findOne(filter, projection, option)
    }

    getLastOrderExportTask = (filter, projection) => {
        return this.findOrderExportTask(
            filter,
            projection,
            {
                sort: {
                    created_at: -1
                },
                lean: true
            }
        )
    }

    countTasks = async (filter) => {
        return OrderExportTaskSchema.countDocuments(filter)
    }

}

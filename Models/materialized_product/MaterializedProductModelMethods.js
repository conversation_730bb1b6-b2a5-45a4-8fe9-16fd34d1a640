const MaterializedProductSchema = require("../../Database/Schemas/product/MaterializedProductSchema")

class MaterializedProductModelMethods {

    createProducts = (docs, options) => {
        return MaterializedProductSchema.create(docs, options)
    }

    findProducts = (filter, projection, options) => {
        return MaterializedProductSchema.find(filter, projection, options)
    }

    createDocument = (object) => {
        return new MaterializedProductSchema(object)
    }

    deleteProducts = (conditions, options) => {
        return MaterializedProductSchema.deleteMany(conditions, options)
    }

    countProducts = (filter) => {
        return MaterializedProductSchema.countDocuments(filter)
    }
}

module.exports = MaterializedProductModelMethods

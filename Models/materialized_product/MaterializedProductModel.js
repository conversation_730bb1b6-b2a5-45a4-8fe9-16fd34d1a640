const isEmpty = require("lodash.isempty")
const difference = require("lodash.difference")

const ProductModel = new (require("../ProductModel"))
const MaterializedProductModelMethods = new (require("./MaterializedProductModelMethods"))
const MaterializedProductHelper = new (require("./MaterializedProductHelper"))
const MaterializedProductUpdateHelper = new (require("./MaterializedProductUpdateHelper"))

const {
    NEW_PRODUCT_TYPE,
    PROMISE_STATES,
    STATUS_CODES,
    ACTIONS,
    MONGODB,
    LISTING_PRODUCT_TYPE,
} = require("../../Configs/constants")

const {
    toLeanOption,
    stringifyObjectId,
} = require("../../Utils/helpers")

const mapKeys = {
    CATEGORY: "categoryList",
    BRAND: "brandList",
    UOM: "uomList",
    VARIANT_TYPE: "variantTypeList",
    ATTRIBUTE: "attributeList",
}

class MaterializedProductModel {

    addBulkSingleProduct = async (
        messages,
        mappingData,
        documentIdMap,
        deleteMsgEntriesMap,
    ) => {
        try {
            const docs = []
            for (let index = 0; index < messages.length; index++) {
                const {
                    messageBody: {
                        fullDocument,
                    },
                } = messages[index]

                const doc = MaterializedProductHelper.generateSingleParentDoc(fullDocument, mappingData)
                docs.push(doc)
            }

            try {
                const result = await MaterializedProductModelMethods.createProducts(
                    docs,
                    {
                        "ordered": false, // will insert all the documents it can and report errors later
                        "aggregateErrors": true, // Aggregate Errors instead of throwing the first one that occurs. Default: false
                    }
                )

                const successfulDocs = []
                const failedDocs = []

                result.forEach(doc => {
                    if (doc instanceof Error) {
                        failedDocs.push(doc)
                    }
                    else {
                        successfulDocs.push(doc)
                    }
                })

                if (successfulDocs.length) {
                    successfulDocs.forEach(doc => {
                        MaterializedProductHelper.handleDeleteMsgEntriesMap(doc._id, documentIdMap, deleteMsgEntriesMap)
                    })
                }

                if (failedDocs.length) {
                    failedDocs.forEach(error => {
                        logger.error(error, {
                            errorMessage: `Error: (FAILED_DOCS) ProductSQS/MaterializedProductModel/addBulkSingleProduct`,
                        })

                        const {
                            code,
                            keyPattern,
                            keyValue,
                        } = error

                        if (
                            code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE &&
                            "_id" in keyPattern
                        ) {
                            MaterializedProductHelper.handleDeleteMsgEntriesMap(keyValue._id, documentIdMap, deleteMsgEntriesMap)
                        }
                    })
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: `Error: (API_ERROR) ProductSQS/MaterializedProductModel/addBulkSingleProduct`,
                    messages
                })
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ProductSQS/MaterializedProductModel/addBulkSingleProduct`,
                messages
            })
        }
    }

    addBulkVariantProduct = async (
        messages,
        mappingData,
        documentIdMap,
        deleteMsgEntriesMap,
    ) => {
        try {
            const docs = []
            const deleteDocIds = []
            const parentProductMap = new Map()
            const parentVariantIdMap = new Map()

            for (let index = 0; index < messages.length; index++) {
                const {
                    messageBody: {
                        operationType,
                        documentId,
                        fullDocument: {
                            parent_id,
                            type,
                        } = {},
                        updatedDocument: {
                            updatedFields = {},
                            removedFields = [],
                        } = {}
                    },
                } = messages[index]

                let mapKey = parent_id

                if (type === NEW_PRODUCT_TYPE.PARENT) {
                    mapKey = documentId
                }
                const mapping = parentProductMap.get(mapKey)

                let parent = mapping?.parent
                let variants = mapping?.variants || []

                if (type === NEW_PRODUCT_TYPE.PARENT) {
                    if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
                        if (!parent) {
                            parent = []
                        }
                        parent.push(messages[index].messageBody)
                    }
                    else {
                        parent = messages[index].messageBody
                    }
                }
                else {
                    variants.push(messages[index].messageBody)

                    const variantIds = parentVariantIdMap.get(parent_id) || []
                    variantIds.push(documentId)
                    parentVariantIdMap.set(parent_id, variantIds)
                }

                parentProductMap.set(mapKey, {
                    operationType,
                    parent,
                    variants,
                })
            }

            const missingParentIds = []
            const materializedParentIds = []

            const mainParentsMap = {}
            const materializedParentsMap = {}

            parentProductMap.forEach((product, parentId, map) => {
                if (!product.parent) {
                    missingParentIds.push(parentId)
                }
            })

            const materializedParents = await MaterializedProductModelMethods.findProducts(
                {
                    "_id": Array.from(parentProductMap.keys())
                },
                undefined,
            )

            materializedParents.forEach(p => {
                materializedParentIds.push(p._id)
                materializedParentsMap[stringifyObjectId(p._id)] = p
            })

            const remainingParentIds = difference(missingParentIds, materializedParentIds)
            const categoryIdSet = new Set()
            const brandIdSet = new Set()
            const uomIdSet = new Set()
            const variantTypesIdSet = new Set()
            const attributeIdSet = new Set()

            if (remainingParentIds.length) {
                const mainParents = await ProductModel.findProducts(
                    {
                        "_id": remainingParentIds
                    },
                    undefined,
                    toLeanOption
                )

                mainParents.forEach(product => {
                    mainParentsMap[stringifyObjectId(product._id)] = product

                    MaterializedProductHelper.prepareNewDataMapping({
                        product,
                        mappingData,
                        categoryIdSet,
                        brandIdSet,
                        uomIdSet,
                        variantTypesIdSet,
                        attributeIdSet,
                    })
                })
            }

            if (
                categoryIdSet.size ||
                brandIdSet.size ||
                uomIdSet.size ||
                variantTypesIdSet.size ||
                attributeIdSet.size
            ) {
                const responseObj = await MaterializedProductHelper.getMappingData({
                    categoryIdSet,
                    brandIdSet,
                    uomIdSet,
                    variantTypesIdSet,
                    attributeIdSet,
                })

                MaterializedProductHelper.mapApiData(responseObj, mappingData, mapKeys.CATEGORY)
                MaterializedProductHelper.mapApiData(responseObj, mappingData, mapKeys.BRAND)
                MaterializedProductHelper.mapApiData(responseObj, mappingData, mapKeys.UOM)
                MaterializedProductHelper.mapApiData(responseObj, mappingData, mapKeys.VARIANT_TYPE)
                MaterializedProductHelper.mapApiData(responseObj, mappingData, mapKeys.ATTRIBUTE)
            }

            parentProductMap.forEach((product, parentId, map) => {
                const materializedParent = materializedParentsMap[parentId]
                const mainParentProduct = mainParentsMap[parentId]

                if (
                    isEmpty(product.parent) &&
                    isEmpty(materializedParent) &&
                    mainParentProduct
                ) {
                    product.parent = {
                        fullDocument: mainParentProduct
                    }
                }

                const doc = MaterializedProductHelper.generateParentVariantDoc(product, mappingData, materializedParent)

                if (
                    typeof doc === "string" &&
                    doc === ACTIONS.DELETE
                ) {
                    deleteDocIds.push(product.parent[0].documentId)
                }
                else {
                    docs.push(doc)
                }
            })

            const handleDeleteMsgEntries = (id) => {
                const docId = stringifyObjectId(id)
                const variantIds = parentVariantIdMap.get(docId) || []

                MaterializedProductHelper.handleDeleteMsgEntriesMap(docId, documentIdMap, deleteMsgEntriesMap)

                variantIds.forEach(vId => {
                    MaterializedProductHelper.handleDeleteMsgEntriesMap(vId, documentIdMap, deleteMsgEntriesMap)
                })
            }

            try {
                const resultList = await Promise.allSettled(
                    docs.map(doc => {
                        return doc.save()
                    })
                )

                resultList.forEach(result => {
                    const {
                        status,
                        value,
                        reason,
                    } = result

                    if (status === PROMISE_STATES.FULFILLED) {
                        if (!value._id) {
                            return
                        }
                        handleDeleteMsgEntries(value._id)
                    }
                    else if (status === PROMISE_STATES.REJECTED) {
                        logger.error(reason, {
                            errorMessage: `Error: (PROMISE_REJECTED) ProductSQS/MaterializedProductModel/addBulkVariantProduct`,
                        })
                    }
                })
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: `Error: (API_ERROR) ProductSQS/MaterializedProductModel/addBulkVariantProduct`,
                    messages
                })
            }

            // Remove soft deleted products from materialize view
            try {
                if (deleteDocIds.length) {
                    const res = await MaterializedProductModelMethods.deleteProducts({
                        _id: { $in: deleteDocIds }
                    })

                    if (res.deletedCount) {
                        deleteDocIds.forEach(id => {
                            handleDeleteMsgEntries(id)
                        })
                    }
                    else {
                        logger.error({
                            errorMessage: `Error: (DELETE_DOCS_API_ERROR ${res.deletedCount}) ProductSQS/MaterializedProductModel/addBulkVariantProduct`,
                            messages
                        })
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: `Error: (DELETE_DOCS_ERROR) ProductSQS/MaterializedProductModel/addBulkVariantProduct`,
                    messages
                })
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ProductSQS/MaterializedProductModel/addBulkVariantProduct`,
                messages
            })
        }
    }

    updateBulkSingleProduct = async (
        messages,
        mappingData,
        documentIdMap,
        deleteMsgEntriesMap,
    ) => {
        try {
            const docs = []
            const deleteDocIds = []
            const productIds = []
            const productMessages = []
            const materializedProductMap = new Map()
            const productType = LISTING_PRODUCT_TYPE.SINGLE

            messages.forEach(msg => {
                const {
                    messageBody: {
                        documentId,
                        updatedDocument: {
                            updatedFields: {
                                is_deleted,
                            } = {},
                        },
                    }
                } = msg

                if (is_deleted) {
                    deleteDocIds.push(documentId)
                }
                else {
                    productIds.push(documentId)
                    productMessages.push(msg)
                }
            })

            if (productIds.length) {
                const materializedProducts = await MaterializedProductModelMethods.findProducts(
                    {
                        "_id": productIds
                    },
                    undefined,
                )

                materializedProducts.forEach(p => {
                    materializedProductMap.set(stringifyObjectId(p._id), p)
                })
            }

            for (let index = 0; index < productMessages.length; index++) {
                const {
                    messageBody: {
                        documentId,
                        updatedDocument,
                    },
                } = productMessages[index]

                const existingProduct = materializedProductMap.get(documentId)

                if (existingProduct) {
                    MaterializedProductUpdateHelper.updateProductDoc(existingProduct, updatedDocument, mappingData, productType)
                    docs.push(existingProduct)
                }
                else {
                    logger.error(`Error: (NOT_FOUND; MaterializedProduct) ProductSQS/MaterializedProductModel/updateBulkSingleProduct`, productMessages[index])
                }
            }

            const handleDeleteMsgEntries = (id) => {
                const docId = stringifyObjectId(id)
                MaterializedProductHelper.handleDeleteMsgEntriesMap(docId, documentIdMap, deleteMsgEntriesMap)
            }

            try {
                if (docs.length) {
                    const resultList = await Promise.allSettled(
                        docs.map(doc => {
                            return doc.save()
                        })
                    )

                    resultList.forEach(result => {
                        const {
                            status,
                            value,
                            reason,
                        } = result

                        if (status === PROMISE_STATES.FULFILLED) {
                            if (!value._id) {
                                return
                            }
                            handleDeleteMsgEntries(value._id)
                        }
                        else if (status === PROMISE_STATES.REJECTED) {
                            logger.error(reason, {
                                errorMessage: `Error: (PROMISE_REJECTED) ProductSQS/MaterializedProductModel/updateBulkSingleProduct`,
                            })
                        }
                    })
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: `Error: (API_ERROR) ProductSQS/MaterializedProductModel/updateBulkSingleProduct`,
                    messages
                })
            }

            // Remove soft deleted products from materialize view
            try {
                if (deleteDocIds.length) {
                    const res = await MaterializedProductModelMethods.deleteProducts({
                        _id: { $in: deleteDocIds }
                    })

                    if (res.deletedCount) {
                        deleteDocIds.forEach(id => {
                            handleDeleteMsgEntries(id)
                        })
                    }
                    else {
                        logger.error({
                            errorMessage: `Error: (DELETE_DOCS_API_ERROR ${res.deletedCount}) ProductSQS/MaterializedProductModel/updateBulkSingleProduct`,
                            messages
                        })
                    }
                }
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: `Error: (DELETE_DOCS_ERROR) ProductSQS/MaterializedProductModel/updateBulkSingleProduct`,
                    messages
                })
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ProductSQS/MaterializedProductModel/updateBulkSingleProduct`,
                messages
            })
        }
    }
}

module.exports = MaterializedProductModel

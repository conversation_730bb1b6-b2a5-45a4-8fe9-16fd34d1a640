const DeletedMaterializedProductSchema = require("../../Database/Schemas/product/DeletedMaterializedProductSchema")

class DeletedMaterializedProductModel {

    bulkWrite(operations, options = {}) {
        return DeletedMaterializedProductSchema.bulkWrite(operations, options)
    }

    getDeletedMaterializedProductCount(filter) {
        return DeletedMaterializedProductSchema.countDocuments(filter)
    }

    findDeletedMaterializedProducts(filter, projection, options) {
        return DeletedMaterializedProductSchema.find(filter, projection, options)
    }

    deleteMany(filter) {
        return DeletedMaterializedProductSchema.deleteMany(filter)
    }
}

module.exports = new DeletedMaterializedProductModel() 

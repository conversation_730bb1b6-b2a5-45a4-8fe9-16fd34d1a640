const {
    NEW_PRODUCT_TYPE,
} = require("../../Configs/constants")

const {
    toObjectId,
    stringifyObjectId,
    isNullish,
} = require("../../Utils/helpers")

const mapKeys = {
    CATEGORY: "categoryList",
    BRAND: "brandList",
    UOM: "uomList",
    VARIANT_TYPE: "variantTypeList",
    ATTRIBUTE: "attributeList",
}

// Keys related to object and array updates
const itemNumberKey = "item_number"
const titleKey = "title"
const secondaryLanguageTitleKey = "secondary_language_title"
const descriptionKey = "description"
const secondaryLanguageDescriptionKey = "secondary_language_description"

const barcodeKey = "barcodes"
const activeVariantItemNumberKey = "active_variant_item_numbers"
const inactiveVariantItemNumberKey = "inactive_variant_item_numbers"
const activeVariantBarcodeKey = "active_variant_barcodes"
const inactiveVariantBarcodeKey = "inactive_variant_barcodes"

const searchIdentifierKey = "search_identifiers"

const searchIdentifierKeys = [
    activeVariantItemNumber<PERSON>ey,
    inactiveVariantItemNumberKey,
    activeVariantBarcodeKey,
    inactiveVariantBarcodeKey
]

const variantsKey = "variants"
const groupsKey = "groups"
const valuesKey = "values"
const typeKey = "type"
const variantValueKey = `${variantsKey}.${valuesKey}`
const groupValueKey = `${groupsKey}.${valuesKey}`
const variantTypeKey = `${variantsKey}.${typeKey}`
const groupTypeKey = `${groupsKey}.${typeKey}`

const inventoryMapKey = "inventory_mappings"
const priceMapKey = "price_mappings"
const packagingMapKey = "packaging_map"
const tagsKey = "tags"
const attributesKey = "attributes"

class MaterializedProductUpdateHelper {

    updateProductDoc = (productDoc, updatedDocument, mappingData, productType) => {
        const {
            updatedFields: {
                item_number,

                title,
                secondary_language_title,
                description,
                secondary_language_description,

                product_order,
                family_id,
                category_id,
                subcategory_id,
                brand_id,

                is_active,
                is_restocked,
                restocked_at,

                variant_count = 0,
                variant_order,
                variant_value_id,
                group_value_id,

                created_by,
                updated_by,
            },
        } = updatedDocument

        const removedFields = structuredClone(updatedDocument.removedFields)

        const catList = mappingData[mapKeys.CATEGORY]
        const brandList = mappingData[mapKeys.BRAND]
        const variantTypeList = mappingData[mapKeys.VARIANT_TYPE]

        const inUpdateProps = prop => Object.hasOwn(updatedDocument.updatedFields, prop)
        const isRemoved = prop => removedFields.includes(prop)

        const removeField = (field) => {
            const index = removedFields.indexOf(field)

            if (index !== -1) {
                removedFields.splice(index, 1)
            }
        }

        const setPropInDoc = (parentKey, childKey, value, defaultValue) => {
            this.setThisPropInDoc(
                productDoc,
                parentKey,
                childKey,
                value,
                defaultValue,
                isRemoved,
                inUpdateProps,
                removeField,
            )
        }

        const setCatPropInDoc = (key, actualKey, catId, catList, catPropName = "category_name") => {
            if (isRemoved(key)) {
                productDoc[actualKey] = null
                removeField(key)
            }
            else if (inUpdateProps(key)) {
                if (toObjectId(catId)) {
                    productDoc[actualKey] = {
                        "_id": toObjectId(catId),
                        "name": catList?.[stringifyObjectId(catId)]?.[catPropName] || ""
                    }
                }
                else {
                    productDoc[actualKey] = null
                }
            }
        }

        const setVariantPropInDoc = (key, actualKey, variantId) => {
            if (isRemoved(key)) {
                productDoc[actualKey] = null
                removeField(key)
            }
            else if (inUpdateProps(key)) {
                productDoc[actualKey] = variantTypeList[stringifyObjectId(variantId)] || null
            }
        }

        const setAmbiguousPropInDoc = (parentKey, childKey, value, ambiguousKey, defaultValue = [], nestedAmbiguousKey) => {
            this.setThisAmbiguousPropInDoc(
                productDoc,
                parentKey,
                childKey,
                value,
                ambiguousKey,
                defaultValue = [],
                nestedAmbiguousKey,
                isRemoved,
                inUpdateProps,
                removeField,
            )
        }

        const setSearchIdentifierInDoc = (parentKey, childKey, value, ambiguousKey, defaultValue = []) => {
            this.setThisSearchIdentifierInDoc(
                productDoc,
                parentKey,
                childKey,
                value,
                ambiguousKey,
                defaultValue = [],
                isRemoved,
                inUpdateProps,
                removeField,
            )
        }

        setPropInDoc(itemNumberKey, undefined, item_number)

        if (
            [
                NEW_PRODUCT_TYPE.SINGLE,
                NEW_PRODUCT_TYPE.PARENT,
            ].includes(productType)
        ) {
            setPropInDoc(titleKey, undefined, title, "")
            setPropInDoc(secondaryLanguageTitleKey, undefined, secondary_language_title, "")
            setPropInDoc(descriptionKey, undefined, description, "")
            setPropInDoc(secondaryLanguageDescriptionKey, undefined, secondary_language_description, "")

            setPropInDoc("product_order", undefined, product_order, 0)

            setCatPropInDoc("family_id", "family", family_id, catList)
            setCatPropInDoc("category_id", "category", category_id, catList)
            setCatPropInDoc("subcategory_id", "subcategory", subcategory_id, catList)
            setCatPropInDoc("brand_id", "brand", brand_id, brandList, "brand_name")

            if (productType === NEW_PRODUCT_TYPE.PARENT) {
                setPropInDoc("variant_count", undefined, variant_count, 0)
            }
        }

        setPropInDoc("is_active", undefined, is_active, false)
        setPropInDoc("is_restocked", undefined, is_restocked, false)
        setPropInDoc("restocked_at", undefined, restocked_at, null)

        if (productType === NEW_PRODUCT_TYPE.VARIANT) {
            setPropInDoc("variant_order", undefined, variant_order, 0)

            setVariantPropInDoc("variant_value_id", "variant", variant_value_id)
            setVariantPropInDoc("group_value_id", "group", group_value_id)
        }

        if (inUpdateProps("created_by")) {
            productDoc.created_by = toObjectId(created_by)
        }

        if (inUpdateProps("updated_by")) {
            productDoc.updated_by = toObjectId(updated_by)
        }

        for (const [key, value] of Object.entries(updatedDocument.updatedFields)) {

            if (key === barcodeKey) {
                setPropInDoc(barcodeKey, undefined, value, [])
            }
            else if (
                key.includes(barcodeKey) &&
                ![
                    activeVariantBarcodeKey,
                    inactiveVariantBarcodeKey
                ].includes(key)
            ) {
                setAmbiguousPropInDoc(barcodeKey, undefined, value, key)
            }

            if (productType === NEW_PRODUCT_TYPE.PARENT) {
                searchIdentifierKeys.forEach(siKey => {
                    if (key === siKey) {
                        setSearchIdentifierInDoc(searchIdentifierKey, siKey, value)
                    }
                    else if (key.includes(siKey) && !key === `in${siKey}`) {
                        setSearchIdentifierInDoc(searchIdentifierKey, siKey, value, key)
                    }
                })

                if (key.includes(variantsKey)) {
                    if (key === variantsKey) {
                        const {
                            type,
                            values
                        } = value

                        const variants = {
                            type: type,
                            values: this.getVariantValues(values, variantTypeList)
                        }
                        setPropInDoc(variantsKey, undefined, variants, {})
                    }
                    else {
                        if (key === variantTypeKey) {
                            setPropInDoc(variantsKey, typeKey, value, "")
                        }

                        if (key === variantValueKey) {
                            const values = this.getVariantValues(value, variantTypeList)
                            setPropInDoc(variantsKey, valuesKey, values, [])
                        }
                        else if (key.includes(variantValueKey)) {
                            const vtObj = variantTypeList[stringifyObjectId(value)]
                            const nestedAmbiguousKey = true

                            setAmbiguousPropInDoc(variantsKey, valuesKey, vtObj, key, undefined, nestedAmbiguousKey)
                        }
                    }
                }

                if (key.includes(groupsKey)) {
                    if (key === groupsKey) {
                        const {
                            type,
                            values
                        } = value

                        const groups = {
                            type: type,
                            values: this.getVariantValues(values, variantTypeList)
                        }
                        setPropInDoc(groupsKey, undefined, groups, {})
                    }
                    else {
                        if (key === groupTypeKey) {
                            setPropInDoc(groupsKey, typeKey, value, "")
                        }

                        if (key === groupValueKey) {
                            const values = this.getVariantValues(value, variantTypeList)
                            setPropInDoc(groupsKey, valuesKey, values, [])
                        }
                        else if (key.includes(groupValueKey)) {
                            const vtObj = variantTypeList[stringifyObjectId(value)]
                            const nestedAmbiguousKey = true

                            setAmbiguousPropInDoc(groupsKey, valuesKey, vtObj, key, undefined, nestedAmbiguousKey)
                        }
                    }
                }
            }

            if (key.includes(inventoryMapKey)) {
                if (key === inventoryMapKey) {
                    setPropInDoc(inventoryMapKey, undefined, value, [])
                }
                else {
                    setAmbiguousPropInDoc(inventoryMapKey, undefined, value, key)
                }
            }

            if (key.includes(packagingMapKey)) {
                const uomList = mappingData[mapKeys.UOM]
                const getUomName = uomId => uomList?.[stringifyObjectId(uomId)]?.unit_name || ""

                if (key === packagingMapKey) {
                    const obj = {
                        uom_id: toObjectId(value.uom_id),
                        uom_name: getUomName(value.uom_id),
                        min_qty: value.min_qty,
                        qty_ctn: value.qty_ctn,
                    }
                    setPropInDoc(packagingMapKey, undefined, obj, {})
                }
                else {
                    let newValue = value
                    const [actualKey, subKey] = key.split(".")

                    if (subKey === "uom_id") {
                        newValue = toObjectId(newValue)
                        setPropInDoc(packagingMapKey, "uom_name", getUomName(newValue), "")
                    }
                    else {
                        setPropInDoc(packagingMapKey, subKey, newValue)
                    }
                }
            }

            if (key.includes(priceMapKey)) {
                if (key === priceMapKey) {
                    value.forEach(price => {
                        price.master_price_id = toObjectId(price.master_price_id)

                        if (price.product_variant_id) {
                            price.product_variant_id = toObjectId(price.product_variant_id)
                        }
                    })
                    setPropInDoc(priceMapKey, undefined, value, [])
                }
                else {
                    let newValue = value

                    if (newValue) {
                        if (typeof newValue === "object") {
                            newValue = structuredClone(value)

                            if (newValue.master_price_id) {
                                newValue.master_price_id = toObjectId(newValue.master_price_id)
                            }

                            if (newValue.product_variant_id) {
                                newValue.product_variant_id = toObjectId(newValue.product_variant_id)
                            }
                        }
                        else {
                            const [actualKey, index, subKey] = key.split(".")

                            if (["master_price_id", "product_variant_id"].includes(subKey)) {
                                newValue = toObjectId(newValue)
                            }
                        }
                    }
                    setAmbiguousPropInDoc(priceMapKey, undefined, newValue, key)
                }
            }

            if (key.includes(tagsKey)) {
                if (key === tagsKey) {
                    let newValue = []

                    if (value.length) {
                        newValue = value.map(tag => toObjectId(tag))
                    }
                    setPropInDoc(tagsKey, undefined, newValue, [])
                }
                else {
                    setAmbiguousPropInDoc(tagsKey, undefined, toObjectId(value), key)
                }
            }

            if (key.includes(attributesKey)) {
                const attributeList = mappingData[mapKeys.ATTRIBUTE]

                if (key === attributesKey) {
                    const newValue = structuredClone(value)

                    newValue.forEach(att => {
                        att.attribute_id = toObjectId(att.attribute_id)
                        att.name = attributeList[stringifyObjectId(att.attribute_id)]?.attribute_name
                    })
                    setPropInDoc(attributesKey, undefined, newValue, [])
                }
                else {
                    let newValue = value
                    const getAttName = attId => attributeList[stringifyObjectId(attId)]?.attribute_name

                    if (newValue) {
                        if (typeof newValue === "object") {
                            newValue = structuredClone(value)

                            if (newValue.attribute_id) {
                                newValue.attribute_id = toObjectId(att.attribute_id)
                                newValue.name = getAttName(att.attribute_id)
                            }
                        }
                        else {
                            const [actualKey, index, subKey] = key.split(".")

                            if (subKey === "attribute_id") {
                                newValue = toObjectId(newValue)
                                setAmbiguousPropInDoc(attributesKey, undefined, getAttName(newValue), `${attributesKey}.${index}.name`)
                            }
                        }
                    }
                    setAmbiguousPropInDoc(attributesKey, undefined, newValue, key)
                }
            }
        }

        if (removedFields.length) {
            removedFields.forEach(fieldToRemove => {
                this.removePropFromDoc(productDoc, fieldToRemove, descriptionKey, undefined, "")
                this.removePropFromDoc(productDoc, fieldToRemove, secondaryLanguageDescriptionKey, undefined, "")

                this.removePropFromDoc(productDoc, fieldToRemove, "is_active", undefined, false)
                this.removePropFromDoc(productDoc, fieldToRemove, "is_restocked", undefined, false)
                this.removePropFromDoc(productDoc, fieldToRemove, "restocked_at", undefined, null)

                this.removeCatPropFromDoc(productDoc, fieldToRemove, "category_id", "category")
                this.removeCatPropFromDoc(productDoc, fieldToRemove, "subcategory_id", "subcategory")

                this.removeCatPropFromDoc(productDoc, fieldToRemove, "group_value_id", "group")

                if (fieldToRemove === barcodeKey) {
                    this.removePropFromDoc(productDoc, fieldToRemove, barcodeKey, undefined, [])
                }
                else if (
                    fieldToRemove.includes(barcodeKey) &&
                    ![
                        activeVariantBarcodeKey,
                        inactiveVariantBarcodeKey
                    ].includes(fieldToRemove)
                ) {
                    this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, barcodeKey)
                }

                if (productType === NEW_PRODUCT_TYPE.PARENT) {
                    searchIdentifierKeys.forEach(siKey => {
                        if (fieldToRemove.includes(siKey) && !fieldToRemove === `in${siKey}`) {
                            this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, searchIdentifierKey, siKey)
                        }
                    })

                    if (fieldToRemove.includes(variantsKey)) {
                        if (fieldToRemove === variantsKey) {
                            this.removePropFromDoc(productDoc, fieldToRemove, variantsKey, undefined, {})
                        }
                        else {
                            if (fieldToRemove === variantTypeKey) {
                                this.removePropFromDoc(productDoc, fieldToRemove, variantsKey, typeKey, "")
                            }

                            if (fieldToRemove === variantValueKey) {
                                this.removePropFromDoc(productDoc, fieldToRemove, variantsKey, valuesKey, [])
                            }
                            else if (fieldToRemove.includes(variantValueKey)) {
                                const nestedAmbiguousKey = true
                                this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, variantsKey, valuesKey, undefined, nestedAmbiguousKey)
                            }
                        }
                    }

                    if (fieldToRemove.includes(groupsKey)) {
                        if (fieldToRemove === groupsKey) {
                            this.removePropFromDoc(productDoc, fieldToRemove, groupsKey, undefined, {})

                        }
                        else {
                            if (fieldToRemove === groupTypeKey) {
                                this.removePropFromDoc(productDoc, fieldToRemove, groupsKey, typeKey, "")
                            }

                            if (fieldToRemove === groupValueKey) {
                                this.removePropFromDoc(productDoc, fieldToRemove, groupsKey, valuesKey, [])
                            }
                            else if (fieldToRemove.includes(groupValueKey)) {
                                const nestedAmbiguousKey = true
                                this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, groupsKey, valuesKey, undefined, nestedAmbiguousKey)
                            }
                        }
                    }
                }

                if (fieldToRemove.includes(inventoryMapKey)) {
                    if (fieldToRemove === inventoryMapKey) {
                        this.removePropFromDoc(productDoc, fieldToRemove, inventoryMapKey, undefined, [])
                    }
                    else {
                        this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, inventoryMapKey)
                    }
                }

                if (fieldToRemove.includes(packagingMapKey)) {
                    if (fieldToRemove === packagingMapKey) {
                        this.removePropFromDoc(productDoc, fieldToRemove, packagingMapKey, undefined, {})
                    }
                    else {
                        const [actualKey, subKey] = fieldToRemove.split(".")

                        if (subKey !== "uom_id") {
                            this.removePropFromDoc(productDoc, fieldToRemove, packagingMapKey, subKey, 0)
                        }
                    }
                }

                if (fieldToRemove.includes(priceMapKey)) {
                    if (fieldToRemove === priceMapKey) {
                        this.removePropFromDoc(productDoc, fieldToRemove, priceMapKey, undefined, [])
                    }
                    else {
                        this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, priceMapKey)
                    }
                }

                if (fieldToRemove.includes(tagsKey)) {
                    if (fieldToRemove === tagsKey) {
                        this.removePropFromDoc(productDoc, fieldToRemove, tagsKey, undefined, [])
                    }
                    else {
                        this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, tagsKey)
                    }
                }

                if (fieldToRemove.includes(attributesKey)) {
                    if (fieldToRemove === attributesKey) {
                        this.removePropFromDoc(productDoc, fieldToRemove, attributesKey, undefined, [])
                    }
                    else {
                        const [actualKey, index, subKey] = fieldToRemove.split(".")

                        if (subKey !== "attribute_id") {
                            this.removeAmbiguousPropFromDoc(productDoc, fieldToRemove, attributesKey, undefined, "")
                        }
                    }
                }
            })
        }
    }

    setThisPropInDoc = (
        productDoc,
        parentKey,
        childKey,
        value,
        defaultValue,
        isRemoved,
        inUpdateProps,
        removeField,
    ) => {
        const skipRemoveUpdate = [
            itemNumberKey,
            titleKey,
        ].some(key => parentKey.includes(key))

        if (childKey) {
            if (!productDoc[parentKey]) {
                productDoc[parentKey] = {}
            }
        }

        if (childKey) {
            const combinedKey = `${parentKey}.${childKey}`

            if (isRemoved(combinedKey)) {
                productDoc[parentKey][childKey] = defaultValue
                removeField(combinedKey)
            }
            else if (inUpdateProps(combinedKey)) {
                productDoc[parentKey][childKey] = value
            }
        }
        else {
            if (isRemoved(parentKey)) {
                if (!skipRemoveUpdate) {
                    productDoc[parentKey] = defaultValue
                    removeField(parentKey)
                }
            }
            else if (inUpdateProps(parentKey)) {
                productDoc[parentKey] = value
            }
        }
    }

    setThisAmbiguousPropInDoc = (
        productDoc,
        parentKey,
        childKey,
        value,
        ambiguousKey,
        defaultValue = [],
        nestedAmbiguousKey,
        isRemoved,
        inUpdateProps,
        removeField,
    ) => {
        if (!productDoc[parentKey]) {
            productDoc[parentKey] = childKey ? {} : defaultValue
        }

        if (childKey) {
            if (!productDoc[parentKey][childKey]) {
                productDoc[parentKey][childKey] = defaultValue
            }
        }

        if (ambiguousKey) {
            const splitKeys = ambiguousKey.split(".")

            let index = splitKeys[1]
            let subKey = splitKeys[2]

            if (nestedAmbiguousKey) {
                index = splitKeys[2]
                subKey = splitKeys[3]
            }

            if (subKey) {
                if (isRemoved(ambiguousKey)) {
                    if (childKey) {
                        productDoc[parentKey][childKey][index][subKey] = undefined
                    }
                    else {
                        const hasNumberKey = [
                            "price",
                            "quantity",
                            "_qty",
                        ].some(key => subKey.includes(key))

                        if (hasNumberKey) {
                            productDoc[parentKey][index][subKey] = 0
                        }
                        else {
                            const canSafelyDelete = [
                                "product_variant_id",
                                "value",
                            ].some(key => subKey.includes(key))

                            if (canSafelyDelete) {
                                productDoc[parentKey][index][subKey] = undefined
                            }
                        }
                    }
                    removeField(ambiguousKey)
                }
                else if (inUpdateProps(ambiguousKey)) {
                    if (isNullish(value)) {
                        if (childKey) {
                            productDoc[parentKey][childKey][index][subKey] = undefined
                        }
                        else {
                            const hasNumberKey = [
                                "price",
                                "quantity",
                                "_qty",
                            ].some(key => subKey.includes(key))

                            if (hasNumberKey) {
                                productDoc[parentKey][index][subKey] = 0
                            }
                            else {
                                const canSafelyDelete = [
                                    "product_variant_id",
                                    "value",
                                ].some(key => subKey.includes(key))

                                if (canSafelyDelete) {
                                    productDoc[parentKey][index][subKey] = undefined
                                }
                            }
                        }
                    }
                    else {
                        if (childKey) {
                            productDoc[parentKey][childKey][index][subKey] = value
                        }
                        else {
                            productDoc[parentKey][index][subKey] = value
                        }
                    }
                }
                else {
                    if (parentKey === attributesKey && subKey === "name") {
                        productDoc[parentKey][index][subKey] =
                            isNullish(value)
                                ? ""
                                : value
                    }
                }
            }
            else {
                if (isRemoved(ambiguousKey)) {
                    if (childKey) {
                        productDoc[parentKey][childKey].splice(index, 1)
                    }
                    else {
                        productDoc[parentKey].splice(index, 1)
                    }
                    removeField(ambiguousKey)
                }
                else if (inUpdateProps(ambiguousKey)) {
                    if (!isNullish(value)) {
                        if (childKey) {
                            productDoc[parentKey][childKey].splice(index, 0, value)
                        }
                        else {
                            productDoc[parentKey].splice(index, 0, value)
                        }
                    }
                }
            }
        }
        else {
            this.setThisPropInDoc(
                productDoc,
                parentKey,
                childKey,
                value,
                defaultValue,
                isRemoved,
                inUpdateProps,
                removeField,
            )
        }
    }

    setThisSearchIdentifierInDoc = (
        productDoc,
        parentKey,
        childKey,
        value,
        ambiguousKey,
        defaultValue = [],
        isRemoved,
        inUpdateProps,
        removeField,
    ) => {
        if (!productDoc[parentKey]) {
            productDoc[parentKey] = {}
        }

        if (!productDoc[parentKey][childKey]) {
            productDoc[parentKey][childKey] = defaultValue
        }

        if (ambiguousKey) {
            const [actualKey, index] = ambiguousKey.split(".")

            if (isRemoved(ambiguousKey)) {
                productDoc[parentKey][childKey].splice(index, 1)
                removeField(ambiguousKey)
            }
            else if (inUpdateProps(ambiguousKey)) {
                if (!isNullish(value)) {
                    productDoc[parentKey][childKey].splice(index, 0, value)
                }
            }
        }
        else {
            if (isRemoved(childKey)) {
                productDoc[parentKey][childKey] = defaultValue
                removeField(childKey)
            }
            else if (inUpdateProps(childKey)) {
                productDoc[parentKey][childKey] = value
            }
        }
    }

    getVariantValues = (values, variantTypeList) => {
        const variantValues = []

        values.forEach(vtValue => {
            if (!vtValue) {
                return
            }
            const vtObj = variantTypeList[stringifyObjectId(vtValue)]

            if (vtObj) {
                variantValues.push(vtObj)
            }
        })
        return variantValues
    }

    removePropFromDoc = (
        productDoc,
        fieldToRemove,
        parentKey,
        childKey,
        defaultValue,
    ) => {
        if (childKey) {
            const combinedKey = `${parentKey}.${childKey}`

            if (fieldToRemove === combinedKey) {
                productDoc[parentKey][childKey] = defaultValue
            }
        }
        else if (fieldToRemove === parentKey) {
            productDoc[parentKey] = defaultValue
        }
    }

    removeCatPropFromDoc = (
        productDoc,
        fieldToRemove,
        key,
        actualKey,
    ) => {
        if (fieldToRemove === key) {
            productDoc[actualKey] = null
        }
    }

    removeSearchIdentifierFromDoc = (
        productDoc,
        fieldToRemove,
        parentKey,
        childKey,
        defaultValue = [],
    ) => {
        const [actualKey, index, subKey] = fieldToRemove.split(".")

        if (childKey) {
            if (productDoc[parentKey]?.[childKey]) {
                if (index > -1) {
                    productDoc[parentKey][childKey].splice(index, 1)
                }
                else {
                    productDoc[parentKey][childKey] = defaultValue
                }
            }
        }
    }

    removeAmbiguousPropFromDoc = (
        productDoc,
        fieldToRemove,
        parentKey,
        childKey,
        defaultValue = [],
        nestedAmbiguousKey,
    ) => {
        const splitKeys = fieldToRemove.split(".")
        let actualKey = splitKeys[0]
        let index = splitKeys[1]
        let subKey = splitKeys[2]

        if (nestedAmbiguousKey) {
            index = splitKeys[2]
            subKey = splitKeys[3]
        }

        if (subKey) {
            if (childKey) {
                if (productDoc[parentKey]?.[childKey]) {
                    productDoc[parentKey][childKey][index][subKey] = undefined
                }
            }
            else if (productDoc[parentKey]) {
                const hasNumberKey = [
                    "price",
                    "quantity",
                    "_qty",
                ].some(key => subKey.includes(key))

                if (hasNumberKey) {
                    productDoc[parentKey][index][subKey] = 0
                }
                else {
                    const canSafelyDelete = [
                        "product_variant_id",
                        "value",
                    ].some(key => subKey.includes(key))

                    if (canSafelyDelete) {
                        productDoc[parentKey][index][subKey] = undefined
                    }
                }
            }
        }
        else {
            if (childKey) {
                if (productDoc[parentKey]?.[childKey]) {
                    if (index > -1) {
                        productDoc[parentKey][childKey].splice(index, 1)
                    }
                    else {
                        productDoc[parentKey][childKey] = defaultValue
                    }
                }
            }
            else if (productDoc[parentKey]) {
                productDoc[parentKey].splice(index, 1)
            }
        }
    }
}

module.exports = MaterializedProductUpdateHelper

const isEmpty = require("lodash.isempty")

const CategoryModel = new (require("../CategoryModel"))
const MasterDataModel = new (require("../MasterDataModel"))
const ProductModel = new (require("../ProductModel"))
const MaterializedProductModelMethods = new (require("./MaterializedProductModelMethods"))
const MaterializedProductUpdateHelper = new (require("./MaterializedProductUpdateHelper"))

const {
    NEW_PRODUCT_TYPE,
    PROMISE_STATES,
    MONGODB,
    ACTIONS,
    LISTING_PRODUCT_TYPE,
} = require("../../Configs/constants")

const {
    toLeanOption,
    addToSet,
    keyBy,
    toObjectId,
    stringifyObjectId,
    addToSetIfExists,
} = require("../../Utils/helpers")

const mapKeys = {
    CATEGORY: "categoryList",
    BRAND: "brandList",
    UOM: "uomList",
    VARIANT_TYPE: "variantTypeList",
    ATTRIBUTE: "attributeList",
}

class MaterializedProductHelper {

    prepareDataMapping = (args) => {
        const {
            messageBody: {
                operationType,
                fullDocument: {
                    family_id,
                    category_id,
                    subcategory_id,
                    brand_id,
                    packaging_map: {
                        uom_id,
                    } = {},
                    attributes = [],
                    variants,
                    groups,
                    variant_value_id,
                    group_value_id,
                } = {},
                updatedDocument: {
                    updatedFields = {},
                    removedFields = [],
                } = {}
            },
            categoryIdSet,
            brandIdSet,
            uomIdSet,
            variantTypesIdSet,
            attributeIdSet,
        } = args

        let familyId,
            categoryId,
            subcategoryId,
            brandId,
            uomId,
            attributeList,
            variantValues,
            groupValues,
            variantValueId,
            groupValueId

        if (operationType === MONGODB.OPERATION_TYPE.INSERT) {
            familyId = family_id
            categoryId = category_id
            subcategoryId = subcategory_id
            brandId = brand_id
            uomId = uom_id
            attributeList = attributes
            variantValues = variants?.values
            groupValues = groups?.values
            variantValueId = variant_value_id
            groupValueId = group_value_id
        }
        else if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
            familyId = updatedFields.family_id
            categoryId = updatedFields.category_id
            subcategoryId = updatedFields.subcategory_id
            brandId = updatedFields.brand_id
            uomId = updatedFields.packaging_map?.uom_id
            attributeList = updatedFields.attributes
            variantValues = updatedFields.variants?.values || []
            groupValues = updatedFields.groups?.values || []
            variantValueId = updatedFields.variant_value_id
            groupValueId = updatedFields.group_value_id

            const variantValueKey = "variants.values"
            const groupValueKey = "groups.values"
            const attributesKey = "attributes"

            for (const [key, value] of Object.entries(updatedFields)) {
                if (key === variantValueKey) {
                    variantValues = value
                }
                else if (key.includes(variantValueKey)) {
                    const [, , index] = key.split(".")
                    variantValues.splice(index, 0, value)
                }

                if (key === groupValueKey) {
                    groupValues = value
                }
                else if (key.includes(groupValueKey)) {
                    const [, , index] = key.split(".")
                    groupValues.splice(index, 0, value)
                }

                if (key.includes(attributesKey)) {
                    if (value) {
                        if (typeof value === "object") {
                            if (value.attribute_id) {
                                attributeIdSet.add(value.attribute_id)
                            }
                        }
                        else {
                            const [, , subKey] = key.split(".")

                            if (subKey === "attribute_id") {
                                attributeIdSet.add(value)
                            }
                        }
                    }
                }
            }
        }

        addToSet(categoryIdSet, familyId)
        addToSet(categoryIdSet, categoryId)
        addToSet(categoryIdSet, subcategoryId)

        addToSet(brandIdSet, brandId)
        addToSet(uomIdSet, uomId)

        addToSet(variantTypesIdSet, variantValues)
        addToSet(variantTypesIdSet, groupValues)
        addToSet(variantTypesIdSet, variantValueId)
        addToSet(variantTypesIdSet, groupValueId)

        if (attributeList?.length) {
            attributeList.forEach(attribute => {
                attributeIdSet.add(attribute.attribute_id)
            })
        }

        return {
            categoryIdSet,
            brandIdSet,
            uomIdSet,
            variantTypesIdSet,
            attributeIdSet,
        }
    }

    prepareNewDataMapping = (args) => {
        const {
            product: {
                family_id,
                category_id,
                subcategory_id,
                brand_id,
                packaging_map: {
                    uom_id,
                } = {},
                variants = {},
                groups = {},
                attributes = [],
            },
            mappingData,
            categoryIdSet,
            brandIdSet,
            uomIdSet,
            variantTypesIdSet,
            attributeIdSet,
        } = args

        const categoryList = mappingData[mapKeys.CATEGORY]
        const brandList = mappingData[mapKeys.BRAND]
        const uomList = mappingData[mapKeys.UOM]
        const variantList = mappingData[mapKeys.VARIANT_TYPE]

        addToSetIfExists(categoryIdSet, family_id, categoryList)
        addToSetIfExists(categoryIdSet, category_id, categoryList)
        addToSetIfExists(categoryIdSet, subcategory_id, categoryList)

        addToSetIfExists(brandIdSet, brand_id, brandList)
        addToSetIfExists(uomIdSet, uom_id, uomList)

        addToSetIfExists(variantTypesIdSet, variants.values, variantList)
        addToSetIfExists(variantTypesIdSet, groups.values, variantList)

        if (attributes.length) {
            const attributeList = mappingData[mapKeys.ATTRIBUTE]

            attributes.forEach(attribute => {
                const stringifiedAttId = stringifyObjectId(attribute.attribute_id)
                const hasAttValue = attributeList[stringifiedAttId]

                if (!hasAttValue) {
                    attributeIdSet.add(stringifiedAttId)
                }
            })
        }
    }

    getMappingData = async (args) => {
        const {
            categoryIdSet,
            brandIdSet,
            uomIdSet,
            variantTypesIdSet,
            attributeIdSet,
        } = args

        const promises = []
        const listKeys = {}
        let indexCounter = 0

        const responseObj = {
            categoryList: {},
            brandList: {},
            uomList: {},
            variantTypeList: {},
            attributeList: {},
        }

        if (categoryIdSet.size) {
            listKeys[indexCounter] = mapKeys.CATEGORY
            indexCounter++

            promises.push(
                CategoryModel.allCategory(
                    {
                        "_id": Array.from(categoryIdSet),
                    },
                    {
                        "category_name": 1
                    },
                    toLeanOption,
                )
            )
        }

        if (brandIdSet.size) {
            listKeys[indexCounter] = mapKeys.BRAND
            indexCounter++

            promises.push(
                MasterDataModel.findBrandList(
                    {
                        "_id": Array.from(brandIdSet),
                    },
                    {
                        "brand_name": 1
                    },
                    toLeanOption,
                )
            )
        }

        if (uomIdSet.size) {
            listKeys[indexCounter] = mapKeys.UOM
            indexCounter++

            promises.push(
                MasterDataModel.findUomList(
                    {
                        "_id": Array.from(uomIdSet),
                    },
                    {
                        "unit_name": 1
                    },
                    toLeanOption,
                )
            )
        }

        if (attributeIdSet.size) {
            listKeys[indexCounter] = mapKeys.ATTRIBUTE
            indexCounter++

            promises.push(
                MasterDataModel.findAttributeList(
                    {
                        "_id": Array.from(attributeIdSet),
                    },
                    {
                        "attribute_name": 1
                    },
                    toLeanOption,
                )
            )
        }

        if (variantTypesIdSet.size) {
            listKeys[indexCounter] = mapKeys.VARIANT_TYPE
            indexCounter++

            promises.push(
                ProductModel.getVariantTypes(
                    {
                        "_id": Array.from(variantTypesIdSet),
                    },
                    {
                        "name": 1,
                        "secondary_language_name": 1,
                        "order": 1,
                    },
                    toLeanOption,
                )
            )
        }

        if (promises.length) {
            const responses = await Promise.allSettled(promises)

            responses.forEach((res, index) => {
                const {
                    status,
                    value,
                    reason,
                } = res

                if (status === PROMISE_STATES.FULFILLED) {
                    const listKey = listKeys[index]
                    responseObj[listKey] = keyBy(value, "_id")
                }
                else if (status === PROMISE_STATES.REJECTED) {
                    const errorPath = "Error: (PROMISE_REJECTED) ProductSQS/MaterializedProductHelper/getMappingData"

                    if (reason instanceof Error) {
                        logger.error(reason, {
                            errorMessage: errorPath,
                        })
                    }
                    else {
                        logger.error(errorPath, {
                            errorMessage: reason,
                        })
                    }
                }
            })
        }
        return responseObj
    }

    mapApiData = (response, mappingData, listNameKey) => {
        const list = response[listNameKey]

        if (!isEmpty(list)) {
            Object.keys(list)
                .forEach(id => {
                    mappingData[listNameKey][id] = list[id]
                })
        }
    }

    generateSingleParentDoc = (message, mappingData) => {
        const {
            _id,
            tenant_id,
            item_number,
            type,
            barcodes,

            title,
            secondary_language_title,
            description,
            secondary_language_description,

            active_variant_item_numbers,
            inactive_variant_item_numbers,
            active_variant_barcodes,
            inactive_variant_barcodes,

            product_order,
            family_id,
            category_id,
            subcategory_id,
            brand_id,

            is_active,
            is_restocked,
            restocked_at,

            variants,
            groups,
            variant_count = 0,

            inventory_mappings,
            packaging_map = {},
            price_mappings,

            tags,
            attributes,
            created_by,
            updated_by,
        } = message

        /**
         * 🚩🚩🚩🚩🚩
         * Do not change the order of the fields set in the `doc` object,
         * as client wants data stored DB in specific order and
         * based on that `doc` object has been prepared.
         */
        const doc = {
            _id: toObjectId(_id),
            tenant_id,
            item_number,
            type,
            barcodes,

            title,
            secondary_language_title: secondary_language_title || title,
            description,
            secondary_language_description,
        }

        if (type === NEW_PRODUCT_TYPE.PARENT) {
            doc.search_identifiers = {
                active_variant_item_numbers,
                inactive_variant_item_numbers,
                active_variant_barcodes,
                inactive_variant_barcodes,
            }
        }

        doc.product_order = product_order
        doc.family = {
            _id: toObjectId(family_id),
            name: mappingData[mapKeys.CATEGORY]?.[stringifyObjectId(family_id)]?.category_name || ""
        }

        if (category_id) {
            const category = mappingData[mapKeys.CATEGORY]?.[stringifyObjectId(category_id)]

            doc.category = {
                _id: category._id,
                name: category.category_name || ""
            }
        }

        if (subcategory_id) {
            const subcategory = mappingData[mapKeys.CATEGORY]?.[stringifyObjectId(subcategory_id)]

            doc.subcategory = {
                _id: subcategory._id,
                name: subcategory.category_name || ""
            }
        }

        if (brand_id) {
            const brand = mappingData[mapKeys.BRAND]?.[stringifyObjectId(brand_id)]

            doc.brand = {
                _id: brand._id,
                name: brand.brand_name || ""
            }
        }

        doc.is_active = is_active
        doc.is_restocked = is_restocked
        doc.restocked_at = restocked_at

        const variantTypeList = mappingData[mapKeys.VARIANT_TYPE]
        if (type === NEW_PRODUCT_TYPE.PARENT) {

            if (variants?.values?.length) {
                doc.variants = structuredClone(variants)
                doc.variants.values = []

                variants.values.forEach(vtValue => {
                    const vtObj = variantTypeList[stringifyObjectId(vtValue)]

                    if (vtObj) {
                        doc.variants.values.push(vtObj)
                    }
                })
            }

            if (groups?.values?.length) {
                doc.groups = structuredClone(groups)
                doc.groups.values = []

                groups.values.forEach(vtValue => {
                    const vtObj = variantTypeList[stringifyObjectId(vtValue)]

                    if (vtObj) {
                        doc.groups.values.push(vtObj)
                    }
                })
            }
            else {
                doc.groups = null
            }

            doc.variant_count = variant_count
            doc.product_variants = []
        }

        doc.inventory_mappings = inventory_mappings

        if (packaging_map.uom_id) {
            doc.packaging_map = {
                uom_id: toObjectId(packaging_map.uom_id),
                uom_name: mappingData[mapKeys.UOM]?.[stringifyObjectId(packaging_map.uom_id)]?.unit_name || "",
                min_qty: packaging_map.min_qty,
                qty_ctn: packaging_map.qty_ctn,
            }
        }

        doc.price_mappings = price_mappings
        if (price_mappings?.length) {
            doc.price_mappings.forEach(price => {
                price.master_price_id = toObjectId(price.master_price_id)

                if (price.product_variant_id) {
                    price.product_variant_id = toObjectId(price.product_variant_id)
                }
            })
        }

        doc.tags = tags
        if (tags?.length) {
            doc.tags = doc.tags.map(tag => toObjectId(tag))
        }

        doc.attributes = attributes
        if (attributes?.length) {
            const attributeList = mappingData[mapKeys.ATTRIBUTE]

            doc.attributes.forEach(att => {
                att.attribute_id = toObjectId(att.attribute_id)
                att.name = attributeList[stringifyObjectId(att.attribute_id)]?.attribute_name
            })
        }

        doc.created_by = toObjectId(created_by)
        doc.updated_by = toObjectId(updated_by)

        return doc
    }

    generateParentVariantDoc = (productObj, mappingData, existingParent) => {
        let parentProductDoc

        if (existingParent) {
            parentProductDoc = existingParent
            let isAction

            if (Array.isArray(productObj.parent)) {
                const productType = LISTING_PRODUCT_TYPE.PARENT

                for (let index = 0; index < productObj.parent.length; index++) {
                    const {
                        operationType,
                        updatedDocument: {
                            updatedFields: {
                                is_deleted,
                            } = {},
                            removedFields = [],
                        } = {},
                    } = productObj.parent[index] // Whichever the value used from this obj, make sure its existence first from parent fn

                    if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
                        if (is_deleted) {
                            isAction = ACTIONS.DELETE
                            break
                        }

                        MaterializedProductUpdateHelper.updateProductDoc(
                            parentProductDoc,
                            productObj.parent[index].updatedDocument,
                            mappingData,
                            productType,
                        )
                    }
                }
            }

            if (isAction) {
                return isAction
            }
        }
        else if (productObj.parent) {
            const {
                operationType = MONGODB.OPERATION_TYPE.INSERT,
                fullDocument,
            } = productObj.parent // Whichever the value used from this obj, make sure its existence first from parent fn

            if (operationType === MONGODB.OPERATION_TYPE.INSERT) {
                // For a safer side only checked `operationType` condition here
                // as it'll always `INSERT` operationType

                parentProductDoc = this.generateSingleParentDoc(fullDocument, mappingData)
                parentProductDoc = MaterializedProductModelMethods.createDocument(parentProductDoc)
            }
        }

        const removeVariantIndexes = []

        productObj.variants?.forEach(messageBody => {
            const {
                operationType,
                documentId,
                fullDocument: {
                    parent_id,
                    type,
                } = {},
                updatedDocument: {
                    updatedFields: {
                        is_deleted,
                    } = {},
                    removedFields = [],
                } = {}
            } = messageBody

            if (operationType === MONGODB.OPERATION_TYPE.INSERT) {
                const doc = this.generateVariantDoc(messageBody.fullDocument, mappingData)
                parentProductDoc.product_variants.push(doc)
            }
            else if (operationType === MONGODB.OPERATION_TYPE.UPDATE) {
                const index = parentProductDoc.product_variants.findIndex(
                    pv => stringifyObjectId(pv._id) === documentId
                )

                if (index > -1) {
                    if (is_deleted) {
                        removeVariantIndexes.push(index)
                        return
                    }
                    const productType = NEW_PRODUCT_TYPE.VARIANT

                    MaterializedProductUpdateHelper.updateProductDoc(
                        parentProductDoc.product_variants[index],
                        messageBody.updatedDocument,
                        mappingData,
                        productType,
                    )
                }
                else {
                    logger.error(`Current update document(${documentId}) not found into 'parentProductDoc.product_variants'`, {
                        errorMessage: `Error: ProductSQS/MaterializedProductModel/addBulkVariantProduct/generateParentVariantDoc`,
                    })
                }
            }
        })

        // Remove deleted variants
        if (removeVariantIndexes.length) {
            const productVariants = parentProductDoc.product_variants.filter(
                (vp, index) => !removeVariantIndexes.includes(index)
            )
            parentProductDoc.product_variants = productVariants
        }

        if (productObj.variants?.length && Array.isArray(parentProductDoc.product_variants)) {
            parentProductDoc.product_variants.sort(
                (v1, v2) => v1.variant_order - v2.variant_order // sorts `variant order` in ascending order
            )
        }
        return parentProductDoc
    }

    generateVariantDoc = (message, mappingData) => {
        const {
            _id,
            item_number,
            barcodes,

            is_active,
            is_restocked,
            restocked_at,

            variant_order,
            variant_value_id,
            group_value_id,

            inventory_mappings,
            packaging_map,
            price_mappings,

            created_by,
            updated_by,
        } = message

        const variantTypeList = mappingData[mapKeys.VARIANT_TYPE]

        /**
         * 🚩🚩🚩🚩🚩
         * Do not change the order of the fields set in the `doc` object,
         * as client wants data stored DB in specific order and
         * based on that `doc` object has been prepared.
         */
        const doc = {
            _id: toObjectId(_id),
            item_number,
            barcodes,

            is_active,
            is_restocked,
            restocked_at,

            variant_order,
            variant: variantTypeList[stringifyObjectId(variant_value_id)],
        }

        if (group_value_id) {
            doc.group = variantTypeList[stringifyObjectId(group_value_id)]
        }

        doc.inventory_mappings = inventory_mappings
        doc.packaging_map = {
            uom_id: toObjectId(packaging_map.uom_id),
            uom_name: mappingData[mapKeys.UOM]?.[stringifyObjectId(packaging_map.uom_id)]?.unit_name || "",
            min_qty: packaging_map.min_qty,
            qty_ctn: packaging_map.qty_ctn,
        }

        doc.price_mappings = price_mappings
        if (price_mappings?.length) {
            doc.price_mappings.forEach(price => {
                price.master_price_id = toObjectId(price.master_price_id)

                if (price.product_variant_id) {
                    price.product_variant_id = toObjectId(price.product_variant_id)
                }
            })
        }

        return doc
    }

    handleDeleteMsgEntriesMap = (id, documentIdMap, deleteMsgEntriesMap) => {
        const docId = stringifyObjectId(id)
        const docMapData = documentIdMap.get(docId) || []

        docMapData.forEach(ele => {
            const {
                MessageId,
                ReceiptHandle,
            } = ele

            if (MessageId && ReceiptHandle) {
                deleteMsgEntriesMap.set(MessageId, {
                    "Id": MessageId,
                    "ReceiptHandle": ReceiptHandle
                })
            }
        })
    }

    deleteMessages = async (deleteMsgEntriesMap, deleteMessageBatch, filePath) => {
        if (!deleteMsgEntriesMap.size) {
            return
        }

        try {
            const res = await deleteMessageBatch(Array.from(deleteMsgEntriesMap.values()))

            if (res.Successful?.length) {
                logger.info(`${filePath}. Successfully deleted msgs of: ${res.Successful.map(item => item.Id).join(", ")}`)
            }

            if (res.Failed?.length) {
                logger.error(`Error: (FAILED_FROM_SQS) ${filePath} deleteMessageBatch`, {
                    failed: res.Failed
                })
            }
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Error: ${filePath} deleteMessageBatch`,
                messages
            })
        }
    }
}

module.exports = MaterializedProductHelper

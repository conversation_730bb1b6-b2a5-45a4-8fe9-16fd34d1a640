const DeletedImageSchema = require("../Database/Schemas/product/DeletedImageSchema")

class DeletedImageModel {

    bulkWrite(operations, options = {}) {
        return DeletedImageSchema.bulkWrite(operations, options)
    }

    getDeletedImageCount(filter) {
        return DeletedImageSchema.countDocuments(filter)
    }

    findDeletedImages(filter, projection, options) {
        return DeletedImageSchema.find(filter, projection, options)
    }

    deleteMany(filter) {
        return DeletedImageSchema.deleteMany(filter)
    }
}

module.exports = new DeletedImageModel() 

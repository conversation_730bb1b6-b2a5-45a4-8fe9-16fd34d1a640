const ImageSchema = require("../Database/Schemas/product/ImageSchema");
const ProductSchema = require("../Database/Schemas/product/ProductSchema");
const ImageCopyProcessSchema = require("../Database/Schemas/imagesCopyProcess");

const {
    NEW_PRODUCT_TYPE,
    VARIANT_TYPE_ENUM,
    DATA_SHEET,
} = require("../Configs/constants");

class ImageModel {

    async addUpdateImage(body, header) {
        const { tenantId, imageName, imageSize, s3Url, productVariantId, imageNumber, groupId } = body;

        const setFields = {
            image_name: imageName,
            image_size: imageSize,
            image_number: imageNumber,
            s3_url: s3Url,
            tenant_id: tenantId,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id,
            updated_at: new Date(),
        }
        setFields["product_variant_id"] = productVariantId;

        // if ("productId" in body) {
        //     setFields["product_id"] = productId
        // }

        // if ("variantId" in body) {
        //     setFields["product_variant_id"] = variantId
        // }

        if ("groupId" in body) {
            setFields["group_id"] = groupId;
            // setFields["product_variant_id"] = undefined;
        }

        return await ImageSchema.findOneAndUpdate(
            {
                "tenant_id": tenantId,
                "image_name": imageName,
            },
            {
                "$set": setFields
            },
            {
                new: true,
                upsert: true
            }
        )
    }

    async getImage(filter, projection, options) {
        return ImageSchema.findOne(filter, projection, options);
    }

    async getImages(filter, projection, options) {
        return ImageSchema.find(filter, projection, options);
    }

    getImageCount(filter) {
        return ImageSchema.countDocuments(filter);
    }

    async aggregateImages(pipeline = []) {
        return ImageSchema.aggregate(pipeline);
    }

    async deleteImages(filter, options) {
        return ImageSchema.deleteMany(filter, options);
    }

    async linkedTenantProductImage(body) {
        let { tenantId, linkedTenantId, searchKey = "", page = 0, perPage = 10, type } = body;
        page = parseInt(page);
        perPage = parseInt(perPage);
        const offset = (page - 1) * perPage;

        const pipeline = [];

        const $match = {
            tenant_id: tenantId,
            is_deleted: false,
            type: {
                $in: [
                    NEW_PRODUCT_TYPE.SINGLE,
                    NEW_PRODUCT_TYPE.PARENT,
                ]
            }
        }

        if (searchKey) {
            $match["$or"] = [
                { title: { $regex: searchKey, $options: "i" } },
                { item_number: { $regex: searchKey, $options: "i" } },
            ]
        }

        pipeline.push(
            {
                $match
            },
            {
                $sort: { "created_at": -1 }
            },
            {
                $lookup: {
                    from: 'products_2.0',
                    localField: '_id',
                    foreignField: 'parent_id',
                    as: 'product_variants',
                    pipeline: [
                        {
                            $match: {
                                is_deleted: false
                            }
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'variant_value_id',
                                foreignField: '_id',
                                as: 'variant_value_id',
                                pipeline: [
                                    {
                                        $project: { name: 1 }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'group_value_id',
                                foreignField: '_id',
                                as: 'group_value_id',
                                pipeline: [
                                    {
                                        $project: { name: 1 }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'images_2.0',
                                localField: '_id',
                                foreignField: 'product_variant_id',
                                as: 'variantImages',
                                pipeline: [
                                    {
                                        $match: {
                                            group_id: null
                                        }
                                    },
                                    {
                                        $count: 'count'
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                variantImages: { $first: '$variantImages' },
                                variant_name: { $first: '$variant_value_id.name' },
                                group_name: { $first: '$group_value_id.name' }
                            }
                        },
                        {
                            $match: {
                                variantImages: null
                            }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    product_variant_count: { $size: '$product_variants' }
                }
            },
            {
                $lookup: {
                    from: 'images_2.0',
                    localField: '_id',
                    foreignField: 'product_variant_id',
                    as: 'images',
                    pipeline: [
                        {
                            $match: {
                                group_id: null
                            }
                        },
                        {
                            $count: 'count'
                        },
                    ]
                }
            },
            {
                $addFields: {
                    images: { $first: '$images' },
                }
            },
            {
                $unwind: {
                    path: '$product_variants',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    $or: [
                        { type: NEW_PRODUCT_TYPE.SINGLE, 'images.count': null },
                        {
                            type: NEW_PRODUCT_TYPE.PARENT,
                            $or: [
                                { 'variants.type': VARIANT_TYPE_ENUM.SIZE, 'images.count': null },
                                {
                                    'variants.type': VARIANT_TYPE_ENUM.COLOR,
                                    product_variant_count: { $gt: 0 },
                                },
                                {
                                    'variants.type': VARIANT_TYPE_ENUM.TYPE,
                                    product_variant_count: { $gt: 0 },
                                }
                            ]
                        }
                    ]
                }
            },
            {
                $project: {
                    title: 1,
                    item_number: 1,
                    type: 1,
                    variants: 1,
                    'product_variants._id': 1,
                    'product_variants.product_id': 1,
                    'product_variants.item_number': 1,
                    'product_variants.variant_name': 1,
                    'product_variants.group_name': 1,
                }
            },
            {
                $lookup: {
                    from: 'products_2.0',
                    localField: 'item_number',
                    foreignField: 'item_number',
                    as: 'link_single_product',
                    pipeline: [
                        {
                            $match: {
                                tenant_id: linkedTenantId,
                                is_deleted: false,
                                type: NEW_PRODUCT_TYPE.SINGLE
                            }
                        }, {
                            $lookup: {
                                from: 'images_2.0',
                                localField: '_id',
                                foreignField: 'product_variant_id',
                                as: 'images',
                                pipeline: [
                                    {
                                        $match: {
                                            group_id: null
                                        }
                                    }, {
                                        $project: {
                                            image_number: 1,
                                            image_name: 1,
                                            s3_url: 1
                                        }
                                    }, {
                                        $sort: {
                                            image_number: 1
                                        }
                                    }
                                ]
                            }
                        }, {
                            $addFields: {
                                images_length: { $size: '$images' }
                            }
                        }, {
                            $project: {
                                tenant_id: 1,
                                type: 1,
                                item_number: 1,
                                title: 1,
                                images: 1,
                                images_length: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'products_2.0',
                    let: {
                        'groupName': '$product_variants.group_name',
                        'variantsType': '$variants.type'
                    },
                    localField: 'product_variants.item_number',
                    foreignField: 'item_number',
                    as: 'link_variant_product',
                    pipeline: [
                        {
                            $match: {
                                tenant_id: linkedTenantId,
                                is_deleted: false,
                                type: NEW_PRODUCT_TYPE.VARIANT
                            }
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'variant_value_id',
                                foreignField: '_id',
                                as: 'variant_value_id',
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'variant_types_2.0',
                                localField: 'group_value_id',
                                foreignField: '_id',
                                as: 'group_value_id',
                                pipeline: [
                                    {
                                        $addFields: {
                                            match_group_name: {
                                                $eq: ['$name', '$$groupName']
                                            }
                                        }
                                    },
                                    {
                                        $project: {
                                            name: 1,
                                            match_group_name: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'products_2.0',
                                localField: 'parent_id',
                                foreignField: '_id',
                                as: 'parent_product',
                                pipeline: [
                                    {
                                        $project: {
                                            title: 1,
                                            item_number: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'images_2.0',
                                localField: '_id',
                                foreignField: 'product_variant_id',
                                as: 'images',
                                pipeline: [
                                    {
                                        $match: {
                                            group_id: null
                                        }
                                    }, {
                                        $project: {
                                            image_number: 1,
                                            image_name: 1,
                                            s3_url: 1,
                                        }
                                    }, {
                                        $sort: {
                                            image_number: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: 'images_2.0',
                                localField: 'parent_product._id',
                                foreignField: 'product_variant_id',
                                as: 'parent_images',
                                pipeline: [
                                    {
                                        $match: {
                                            group_id: null
                                        }
                                    }, {
                                        $project: {
                                            image_number: 1,
                                            image_name: 1,
                                            s3_url: 1
                                        }
                                    }, {
                                        $sort: {
                                            image_number: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                variant_name: {
                                    $first: '$variant_value_id.name'
                                },
                                group_name: {
                                    $first: '$group_value_id.name'
                                },
                                match_group_name: {
                                    $first: "$group_value_id.match_group_name",
                                },
                                parent_product: {
                                    $first: '$parent_product'
                                }
                            }
                        },
                        {
                            $project: {
                                tenant_id: 1,
                                type: 1,
                                item_number: 1,
                                title: 1,
                                images: {
                                    $cond: [{ $eq: ['$$variantsType', VARIANT_TYPE_ENUM.SIZE] }, '$parent_images', '$images']
                                },
                                images_length: 1,
                                variant_name: 1,
                                group_name: 1,
                                match_group_name: 1,
                                parent_product: 1,
                            }
                        },
                        {
                            $addFields: {
                                images_length: {
                                    $size: '$images'
                                }
                            }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    link_single_product: {
                        $first: '$link_single_product'
                    },
                    link_variant_product: {
                        $first: '$link_variant_product'
                    }
                }
            },
            {
                $match: {
                    $or: [
                        {
                            type: NEW_PRODUCT_TYPE.SINGLE,
                            'link_single_product.images_length': {
                                $gt: 0
                            }
                        }, {
                            type: NEW_PRODUCT_TYPE.PARENT,
                            $and: [
                                { 'link_variant_product.images_length': { $gt: 0 } },
                                { 'link_variant_product.match_group_name': { $ne: false } }
                            ]
                        }
                    ]
                }
            },
        );

        if (type === DATA_SHEET.APPROVE_TYPE.ALL) {
            pipeline.push(
                {
                    $project: {
                        _id: 0,
                        productId: {
                            $cond: [{ $eq: ['$type', NEW_PRODUCT_TYPE.SINGLE] }, '$_id', null]
                        },
                        linkedTenantProductId: '$link_single_product._id',
                        variantId: '$product_variants._id',
                        parentId: {
                            $cond: [
                                { $eq: ["$type", NEW_PRODUCT_TYPE.PARENT] },
                                "$_id",
                                null,
                            ],
                        },
                        linkedTenantVariantId: '$link_variant_product._id',
                        linkedParentId: {
                            $cond: [
                                { $eq: ["$type", NEW_PRODUCT_TYPE.PARENT] },
                                "$link_variant_product.parent_product._id",
                                null,
                            ],
                        }
                    }
                },
                {
                    $facet: { 'product': [] }
                }
            )

            const data = await ProductSchema.aggregate(pipeline);
            return data.length ? data[0].product : [];
        }
        else {
            pipeline.push(
                {
                    $facet: {
                        list: [{ $skip: offset }, { $limit: perPage }],
                        count: [{ $count: 'count' }]
                    }
                },
                {
                    $unwind: { path: '$count' }
                }
            )

            const data = await ProductSchema.aggregate(pipeline);

            const list = data.length ? data[0].list : [];
            const count = data.length ? data[0].count.count : 0;

            return { list, count };
        }
    }

    async existProcess(tenantId, linkedTenantId) {
        return await ImageCopyProcessSchema.findOne({ tenant_id: tenantId, linked_tenant_id: linkedTenantId })
    }

    async addImageProcess(tenantId, linkedTenantId) {
        return ImageCopyProcessSchema({
            tenant_id: tenantId,
            linked_tenant_id: linkedTenantId,
        })
    }

    async linkedProductImages(filter, projection = {}, options = {}) {
        return await ImageSchema.find(filter, projection, options)
    }
}

module.exports = ImageModel;


class AuthModel {

    async generateTokenFromRefreshToken(refreshToken) {
        return new Promise((resolve, reject) => {
            const parameters = {
                UserPoolId: process.env.AWS_USER_POOL_ID,
                AuthFlow: "REFRESH_TOKEN",
                ClientId: process.env.AWS_USER_POOL_CLIENT_ID,
                AuthParameters: {
                    REFRESH_TOKEN: refreshToken
                }
            };
            
            AWSAdminCognito.adminInitiateAuth(parameters, function (err, result) {
                if(err) {
                    reject(err)
                }
                resolve(result);
            });
        });
    }

}

module.exports = AuthModel
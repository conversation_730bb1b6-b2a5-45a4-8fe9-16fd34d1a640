const uniq = require("lodash.uniq")
const startCase = require('lodash.startcase')
const difference = require('lodash.difference')

const ProductModel = new (require("./ProductModel"))()
const DataSheetModel = new (require("./DataSheetModel"))()
const CategoryModel = new (require("./CategoryModel"))()
const MasterDataModel = new (require("./MasterDataModel"))()
const Api = new (require("../Utils/Api"))()

const { MASTER_DATA } = require("../Configs/ApiType")
const {
    DATA_SHEET,
    VALUES,
    ENTITY_STATUS,
    INCREMENT_PRODUCT_NUMBER_BY,
    PRODUCT_TYPE,
    NEW_PRODUCT_TYPE,
    STATUS_CODES,
} = require("../Configs/constants")

const {
    generateExcelColumnCellName,
    toLeanOption,
} = require("../Utils/helpers")

const COLUMNS_TYPE_MAPPING = {
    "item_number": String,
    "barcode": String,
    "product_title": String,
    "product_title_sl": String,
    "product_type": String,
    "parentage_type": String,
    "parent_sku": String,
    "variant_value": String,
    "group_name": String,
    "group_value": String,
    "family": String,
    "category": String,
    "subcategory": String,
    "brand": String,
    "qty_ctn": Number,
    "min_qty": Number,
    "uom": String,
}

class ProductDataSheetModel {

    checkAddProductLimit = (info, validations) => {
        const {
            checkAddLimit,
            canCreateNewProduct,
            addLimit,
        } = info || {}

        if (checkAddLimit && !canCreateNewProduct) {
            validations.push(`Max product limit (${addLimit}) is exceeded`)
        }
    }

    checkValidItemNumber = async (tenantId, excelData, currentIndex, product_type, group_name, item_number, validations, apiVersion) => {
        if (!item_number) {
            validations.push("Missing item number")
        }
        else {
            const response = await ProductModel.findProduct(
                {
                    "unique_item_number": tenantId + "_" + item_number,
                    "is_deleted": false,
                },
                "_id",
                { lean: true }
            )

            if (response) {
                validations.push("Item number already exists")
            }
            else {
                const haveSameItemNumbers = excelData.some((element, index) =>
                    index !== currentIndex &&
                    element.item_number === item_number
                )

                if (haveSameItemNumbers) {
                    validations.push("Same item number already exists in the sheet")
                }
            }

            if (product_type === DATA_SHEET.PRODUCT_TYPE.PARENT) {
                // Find variant products of this parent product. 

                const variantProducts = excelData.filter(element =>
                    element.product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT &&
                    element.parent_sku === item_number
                )

                if (!variantProducts.length) {
                    validations.push("Missing variant product(s) with same sku")
                }
                else {
                    const haveGroupValues = variantProducts.some(element => element.group_value)

                    if (haveGroupValues && !group_name) {
                        validations.push("Missing group name")
                    }
                }
            }
        }
    }

    checkValidBarcode = async (tenantId, barcode, validations, excelData, rowNumber) => {
        if (!barcode || !barcode.length) {
            return
        }

        const sanitizedBarCodes = new Set();
        const smallBarcode = [];
        const barcodeCount = new Map();
        const duplicateBarcode = new Set();

        let emptyCount = 0
        let totalBarcode = 0

        // Process barcode
        barcode.split(",").forEach(element => {
            totalBarcode++

            let normalizedBarcode = element.trim();
            if (!normalizedBarcode) {
                emptyCount++
                return
            }

            if (normalizedBarcode.length < 3) {
                smallBarcode.push(normalizedBarcode);
            }

            if (barcodeCount.has(normalizedBarcode)) {
                duplicateBarcode.add(normalizedBarcode);
            }
            else {
                barcodeCount.set(normalizedBarcode, 1);
                sanitizedBarCodes.add(`${tenantId}_${normalizedBarcode}`);
            }
        });

        if (emptyCount > 0 && emptyCount === totalBarcode) {
            validations.push("Missing barcode(s): received empty values in the comma-separated list")
        }

        // Validate short barcode
        if (smallBarcode.length) {
            validations.push(`Barcode(s) [${smallBarcode}] must be at least 3 characters long`);
        }

        // Check for duplicates in other rows
        excelData.forEach(element => {
            if (element.row_number !== rowNumber) {
                element.barcode?.split(",").forEach(e => {
                    let rowBarcode = e.trim();

                    if (rowBarcode && barcodeCount.has(rowBarcode)) {
                        duplicateBarcode.add(rowBarcode);
                        sanitizedBarCodes.delete(`${tenantId}_${rowBarcode}`);
                    }
                });
            }
        });

        // Add validation message if duplicates exist
        if (duplicateBarcode.size) {
            validations.push(`Barcode(s) [${[...duplicateBarcode]}] already exists in the sheet`);
        }

        const dbBarcode = await ProductModel.getAllBarCodes(
            {
                "_id": {
                    "$in": [...sanitizedBarCodes]
                },
                "tenant_id": tenantId,
                "is_active": true,
            },
            "-_id barcode"
            ,
            toLeanOption,
        )

        if (dbBarcode.length) {
            const barcode = dbBarcode.map(element => element.barcode)
            const uniqBarcode = [...new Set(barcode)]
            validations.push(`Barcode(s) [${uniqBarcode}] already exists`)
        }
    }

    checkValidProductTitle = (product_type, product_title, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT) {
            return
        }

        if (!product_title) {
            validations.push("Missing product title")
        }
    }

    checkValidSlProductTitle = (product_type, product_title_sl, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT) {
            return
        }

        if (!product_title_sl) {
            validations.push("Missing secondary language's product title")
        }
    }

    checkValidProductType = (product_type, validations) => {
        if (!product_type) {
            validations.push("Missing product type")
        }
        else if (!Object.values(DATA_SHEET.PRODUCT_TYPE).includes(product_type)) {
            validations.push(`${product_type} is invalid product type. It must be ${Object.values(DATA_SHEET.PRODUCT_TYPE).join(" or ")}`)
        }
    }

    checkValidParentageType = (product_type, parentage_type, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.PARENT) {
            if (!parentage_type) {
                validations.push("Missing parentage type")
            }
            else if (!Object.values(DATA_SHEET.PARENTAGE_TYPE).includes(parentage_type)) {
                validations.push(`${parentage_type} is invalid parentage type. It must be ${Object.values(DATA_SHEET.PARENTAGE_TYPE).join(" or ")}`)
            }
        }
    }

    checkValidParentSku = (product_type, excelData, parent_sku, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT) {
            if (!parent_sku) {
                validations.push("Missing parent sku")
            }
            else {
                const parentProducts = excelData.filter(element =>
                    element.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT &&
                    element.item_number === parent_sku
                )

                if (!parentProducts.length) {
                    validations.push("Missing parent product with same sku")
                }
                else if (parentProducts.length > 1) {
                    validations.push("Multiple parent products found with same sku")
                }
            }
        }
    }

    checkValidVariantValue = (product_type, variant_value, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT) {
            if (!variant_value) {
                validations.push("Missing variant value")
            }
        }
    }

    checkValidGroupName = (product_type, group_name, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.PARENT) {
            if (!group_name) {
                return
            }
        }
    }

    checkValidGroupValue = (product_type, group_value, validations) => {
        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT) {
            if (!group_value) {
                return
            }
        }
    }

    checkDuplicateVariantAndGroup = (product_type, excelData, currentIndex, variant_value, group_value, parent_sku, validations, warnings) => {

        if (product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT && parent_sku) {
            if (!variant_value) {
                return
            }

            if (group_value) {
                const haveSameProducts = excelData.some((element, index) =>
                    index !== currentIndex &&
                    element.product_type === product_type &&
                    element.parent_sku === parent_sku &&
                    element.variant_value === variant_value &&
                    element.group_value === group_value
                )

                if (haveSameProducts) {
                    validations.push("Multiple variant products found with same 'sku' 'variant value' & 'group value'")
                }
                else {
                    /**
                     *  If parent has more than one variants and,
                     *  variants have more than one different group values then,
                     *  display warning for that variant.
                     */

                    const haveSameProducts = excelData.some((element, index) =>
                        index !== currentIndex &&
                        element.product_type === product_type &&
                        element.parent_sku === parent_sku &&
                        element.group_value === group_value
                    )

                    if (!haveSameProducts) {
                        warnings.push("Group value exists only once")
                    }
                }
            }
            else if (!group_value) {

                const parentProducts = excelData.filter(element =>
                    element.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT &&
                    element.item_number === parent_sku
                )

                const haveSomeGroupName = parentProducts.some(element => element.group_name)

                if (haveSomeGroupName) {
                    validations.push("Missing group value")
                }
                else {
                    const sameVariantProducts = excelData.filter(element =>
                        element.product_type === product_type &&
                        element.parent_sku === parent_sku
                    )

                    const hasSomeGroupValue = sameVariantProducts.some(element => element.group_value)

                    if (hasSomeGroupValue) {
                        validations.push("Missing group value")
                    }
                    else {
                        const haveSameProducts = excelData.some((element, index) =>
                            index !== currentIndex &&
                            element.product_type === product_type &&
                            element.parent_sku === parent_sku &&
                            element.variant_value === variant_value &&
                            element.group_value === ""
                        )

                        if (haveSameProducts) {
                            validations.push("Multiple variant products found with same 'sku' & 'variant value'")
                        }
                    }
                }

            }
        }
    }

    checkValidCategories = (product_type, family, category, subcategory, list, validations) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.PARENT].includes(product_type)) {
            const {
                familyList,
                categoryList,
                subCategoryList,
            } = list

            if (!family) {
                validations.push("Missing family")
            }
            else {
                const familyInfo = familyList.find(element => element.category_name === family)

                if (!familyInfo) {
                    validations.push("Family not found")
                }
                else if (familyInfo) {
                    if (category) {
                        const categoryInfo = categoryList.find(element => element.category_name === category)

                        if (!categoryInfo) {
                            validations.push("Category not found")
                        }
                        else if (categoryInfo) {
                            if (categoryInfo.parent_id.toString() !== familyInfo._id.toString()) {
                                validations.push("Category isn't matching with family")
                            }
                            else if (subcategory) {
                                const subCategoryInfo = subCategoryList.find(element => element.category_name === subcategory)

                                if (!subCategoryInfo) {
                                    validations.push("Subcategory not found")
                                }
                                else if (subCategoryInfo && subCategoryInfo.parent_id.toString() !== categoryInfo._id.toString()) {
                                    validations.push("Subcategory isn't matching with category")
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    checkValidBrand = async (product_type, brand, list, validations) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.PARENT].includes(product_type)) {
            if (!brand) {
                validations.push("Missing brand")
                return
            }
            const { brandList } = list

            const brandInfo = brandList.find(element => element.brand_name === brand)

            if (!brandInfo) {
                validations.push("Brand not found")
            }
        }
    }

    checkValidQtnCtn = (product_type, qty_ctn, validations) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.VARIANT].includes(product_type)) {
            if (!qty_ctn) {
                validations.push("Missing qty ctn or it must be greater than zero")
            }
        }
    }

    checkValidMinQtn = (product_type, min_qty, validations) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.VARIANT].includes(product_type)) {
            if (!min_qty) {
                validations.push("Missing min qty or it must be greater than zero")
            }
        }
    }

    checkValidUom = async (product_type, uom, list, validations) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.VARIANT].includes(product_type)) {
            if (!uom) {
                validations.push("Missing uom")
                return
            }
            const { uomList } = list

            const uomInfo = uomList.find(element => element.unit_name === uom)

            if (!uomInfo) {
                validations.push("Uom not found")
            }
        }
    }

    checkProductImportValidations = async (tenantId, excelData, index, list, extraInfo) => {
        const validations = []
        const warnings = []

        let {
            item_number = "",
            barcode = "",
            product_title = "",
            product_title_sl = "",
            product_type = "",
            parentage_type = "",
            parent_sku = "",
            variant_value = "",
            group_name = "",
            group_value = "",
            family = "",
            category = "",
            subcategory = "",
            brand = "",
            qty_ctn = 0,
            min_qty = 0,
            uom = "",

            row_number,
        } = excelData[index]

        this.checkAddProductLimit(extraInfo, validations)

        await this.checkValidItemNumber(tenantId, excelData, index, product_type, group_name, item_number, validations, extraInfo?.apiVersion)

        await this.checkValidBarcode(tenantId, barcode, validations, excelData, row_number)

        this.checkValidProductTitle(product_type, product_title, validations)

        this.checkValidSlProductTitle(product_type, product_title_sl, validations)

        this.checkValidProductType(product_type, validations)

        this.checkValidParentageType(product_type, parentage_type, validations)

        this.checkValidParentSku(product_type, excelData, parent_sku, validations)

        this.checkValidVariantValue(product_type, variant_value, validations)

        this.checkValidGroupName(product_type, group_name, validations)

        this.checkValidGroupValue(product_type, group_value, validations)

        this.checkDuplicateVariantAndGroup(product_type, excelData, index, variant_value, group_value, parent_sku, validations, warnings)

        this.checkValidCategories(product_type, family, category, subcategory, list, validations)

        this.checkValidBrand(product_type, brand, list, validations)

        this.checkValidQtnCtn(product_type, qty_ctn, validations)

        this.checkValidMinQtn(product_type, min_qty, validations)

        this.checkValidUom(product_type, uom, list, validations)

        return { validations, warnings }
    }

    getCategoryListing = async (tenantId, categoryType) => {
        return CategoryModel.categoryList(
            tenantId,
            categoryType,
            ENTITY_STATUS.ACTIVE,
            null,
            null,
            false,
            {
                "category_name": 1,
                "parent_id": 1,
            }
        )
    }

    getMasterDataListing = async (tenantId, projectField, apiType) => {
        return MasterDataModel.getAttributeList(
            {
                query: {
                    tenantId,
                    status: ENTITY_STATUS.ACTIVE,
                    perPage: 0,
                    page: 0,
                    projection: {
                        [projectField]: 1
                    }
                }
            },
            apiType,
        )
    }

    addProduct = async (
        req,
        tenantId,
        excelData,
        index,
        list,
        validations,
        variantValidationsMap,
        extraInfo,
    ) => {
        const {
            apiVersion,
        } = req.body

        const {
            row_number = "",
            item_number = "",
            barcode = "",
            product_title = "",
            product_title_sl = "",
            type = "",
            product_type = "",
            parentage_type = "",
            parent_sku = "",
            variant_value = "",
            group_name = "",
            group_value = "",
            family = "",
            category = "",
            subcategory = "",
            brand = "",
            qty_ctn = 0,
            min_qty = 0,
            uom = "",
            variantProducts = [],
            variants_values = undefined,
            groups_values = undefined,
        } = excelData[index] || {}

        const isVariantProduct = type === PRODUCT_TYPE.VARIANT

        try {
            // Using transactions for better data consistency
            var session = await mongoose.startSession()
            session.startTransaction()

            const {
                brandList,
                familyList,
                categoryList,
                subCategoryList,
                uomList,
            } = list

            this.checkAddProductLimit(extraInfo, validations)

            if (validations.length) {
                if (isVariantProduct) {
                    variantValidationsMap.set(row_number, validations.toString())
                }
                await session.abortTransaction()
                await session.endSession({ forceClear: true })
                return
            }
            const product = await ProductModel.createProduct()

            const createdBy = req.headers.userDetails?._id
            const updatedBy = req.headers.userDetails?._id

            product.tenant_id = tenantId
            product.title = product_title
            product.secondary_language_title = product_title_sl
            product.item_number = item_number
            product.unique_item_number = `${tenantId}_${item_number}`
            product.is_deleted = false
            product.created_by = createdBy
            product.updated_by = updatedBy
            product.is_active = true
            product.barcodes = []

            const brandInfo = brandList.find(element => element.brand_name === brand)
            product.brand_id = brandInfo?._id

            const familyInfo = familyList.find(element => element.category_name === family)
            product.family_id = familyInfo._id

            if (category) {
                const categoryInfo = categoryList.find(element => element.category_name === category)
                product.category_id = categoryInfo?._id

                if (product.category_id && subcategory) {
                    const subCategoryInfo = subCategoryList.find(element => element.category_name === subcategory)
                    product.subcategory_id = subCategoryInfo?._id
                }
            }

            // Update 'product_counter' in respected category & 'product_order' in product.
            let categoryDoc

            if (product.family_id && product.category_id && product.subcategory_id) {
                categoryDoc = await ProductModel.findCategoryById(
                    {
                        _id: product.subcategory_id
                    },
                    {
                        _id: 1,
                        product_counter: 1,
                        is_active: 1
                    }
                )

                if (!categoryDoc) {
                    validations.push("Subcategory not found")
                }
            }
            else if (product.family_id && product.category_id) {
                categoryDoc = await ProductModel.findCategoryById(
                    {
                        _id: product.category_id
                    },
                    {
                        _id: 1,
                        product_counter: 1,
                        is_active: 1
                    }
                )

                if (!categoryDoc) {
                    validations.push("Category not found")
                }
            }
            else {
                categoryDoc = await ProductModel.findCategoryById(
                    {
                        _id: product.family_id
                    },
                    {
                        _id: 1,
                        product_counter: 1,
                        is_active: 1
                    }
                )

                if (!categoryDoc) {
                    validations.push("Family not found")
                }
            }

            if (!categoryDoc) {
                await session.abortTransaction()
                await session.endSession({ forceClear: true })
                return
            }

            categoryDoc.product_counter += 1
            product.product_order = categoryDoc.product_counter * INCREMENT_PRODUCT_NUMBER_BY;

            await categoryDoc.save({ session })

            if (!isVariantProduct) {
                product.type = NEW_PRODUCT_TYPE.SINGLE

                if (barcode) {
                    await this.checkValidBarcode(tenantId, barcode, validations, excelData, row_number)

                    if (validations.length) {
                        await session.abortTransaction()
                        await session.endSession({ forceClear: true })
                        return
                    }

                    const sanitizedBarcode = new Set()

                    barcode.split(",").forEach(element => {
                        const normalizedBarcode = element.trim()
                        if (normalizedBarcode) {
                            sanitizedBarcode.add(normalizedBarcode)
                        }
                    })
                    product.barcodes = Array.from(sanitizedBarcode)

                    await ProductModel.addUpdateBarcodes(
                        tenantId,
                        product.barcodes,
                        product._id,
                        { session }
                    )
                }

                const uomInfo = uomList.find(element => element.unit_name === uom)

                product.packaging_map = {
                    uom_id: uomInfo?._id,
                    min_qty,
                    qty_ctn,
                }
            }
            else {
                product.type = NEW_PRODUCT_TYPE.PARENT
                //add variant ids and groups ids for product document to create product_variant document's PK (_id)
                const [variantTypes, groupTypes] = await ProductModel.addVariantsAndGroups(
                    tenantId,
                    variants_values,
                    groups_values,
                    product._id,
                    {
                        session,
                        ordered: true,
                    }
                )

                product.variants = {
                    type: startCase(parentage_type),
                    values: variantTypes.map(vt => vt._id)
                }

                if (group_name) {
                    product.groups = {
                        type: group_name,
                        values: groupTypes.map(gt => gt._id)
                    }
                }
                let variantCount = 0

                for (let i = 0; i < variantProducts.length; i++) {
                    const vProd = variantProducts[i]
                    let isValidBarcode = false
                    const variantValidations = []

                    const existingVarItemNumber = await ProductModel.findProduct(
                        {
                            "unique_item_number": tenantId + "_" + vProd.item_number,
                            "is_deleted": false,
                        },
                        "_id",
                        { lean: true }
                    )

                    if (existingVarItemNumber) {
                        variantValidationsMap.set(vProd.row_number, "Item number already exists")
                        continue; // don't save this variant
                    }

                    if (vProd.barcode) {
                        await this.checkValidBarcode(tenantId, vProd.barcode, variantValidations, excelData, row_number)

                        if (variantValidations.length) {
                            variantValidationsMap.set(vProd.row_number, variantValidations.toString())
                            continue; // don't save this variant
                        }
                        isValidBarcode = true
                    }

                    // Set item number of the variant in DB
                    if (product.is_active) {
                        product.active_variant_item_numbers.push(vProd.item_number)
                    }
                    else {
                        product.inactive_variant_item_numbers.push(vProd.item_number)
                    }

                    const variantInfo = variantTypes.find(v => v.name === vProd.variant_value)
                    let groupInfo

                    if (vProd.group_value) {
                        groupInfo = groupTypes.find(v => v.name === vProd.group_value)
                    }
                    const uomInfo = uomList.find(element => element.unit_name === vProd.uom)

                    // Prepare variant info of the product
                    const variantProductInfo = {
                        "variant_id": `${product._id}_${variantInfo._id}${groupInfo?._id ? `_${groupInfo?._id}` : ""}`,
                        "tenant_id": tenantId,
                        "parent_id": product._id,
                        "variant_value_id": variantInfo._id,
                        "group_value_id": groupInfo?._id,
                        "item_number": vProd.item_number,
                        "unique_item_number": `${tenantId}_${vProd.item_number}`,
                        "type": NEW_PRODUCT_TYPE.VARIANT,
                        "packaging_map": {
                            "uom_id": uomInfo?._id,
                            "min_qty": vProd.min_qty,
                            "qty_ctn": vProd.qty_ctn,
                        },
                        "variant_order": i + 1,
                        "created_by": createdBy,
                        "updated_by": updatedBy,
                        "is_active": product.is_active,
                        "is_deleted": false,
                    }

                    if (isValidBarcode) {
                        const sanitizedBarcode = new Set()

                        vProd.barcode.split(",").forEach(element => {
                            const normalizedBarcode = element.trim()
                            if (normalizedBarcode) {
                                sanitizedBarcode.add(normalizedBarcode)
                            }
                        })
                        variantProductInfo.barcodes = Array.from(sanitizedBarcode)

                        if (variantProductInfo.is_active) {
                            product.active_variant_barcodes.push(...variantProductInfo.barcodes)
                        }
                        else {
                            product.inactive_variant_barcodes.push(...variantProductInfo.barcodes)
                        }
                    }

                    const varProduct = await ProductModel.createProduct(variantProductInfo)

                    try {
                        await varProduct.save({ session })
                    }
                    catch (error) {
                        let newError = error.message || error

                        if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                            if ("unique_item_number" in error.keyValue) {
                                newError = "Item number already exists"
                            }
                            else if ("barcodes" in error.keyValue) {
                                newError = "Barcode already exists"
                            }
                            else if ("variant_id" in error.keyValue) {
                                newError = "Variant id already exists"
                            }
                        }
                        variantValidationsMap.set(vProd.row_number, newError)
                        continue; // don't save this variant
                    }

                    if (isValidBarcode) {
                        await ProductModel.addUpdateBarcodes(
                            tenantId,
                            varProduct.barcodes,
                            varProduct._id,
                            { session },
                        )
                    }
                    variantCount++
                }

                if (!variantCount) {
                    variantValidationsMap.set(row_number, "Something went wrong with its variants")

                    if (variantProducts?.length) {
                        variantProducts.forEach(vp => {
                            if (!variantValidationsMap.has(vp.row_number)) {
                                variantValidationsMap.set(vp.row_number, "Unable to make variant of the parent product")
                            }
                        })
                    }
                    await session.abortTransaction()
                    await session.endSession({ forceClear: true })
                    return
                }
                product.variant_count = variantCount
            }

            try {
                await product.save({ session })

                await session.commitTransaction()
                await session.endSession()

                logger.info(`${tenantId} - Product ${product._id} added successfully :)`)
            }
            catch (error) {
                let newError = error.message || error

                if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                    if ("unique_item_number" in error.keyValue) {
                        newError = "Item number already exists"
                    }
                    else if ("barcodes" in error.keyValue) {
                        newError = "Barcode already exists"
                    }
                    else if ("variant_id" in error.keyValue) {
                        newError = "Variant id already exists"
                    }
                }

                if (isVariantProduct) {
                    if (!variantValidationsMap.has(row_number)) {
                        variantValidationsMap.set(row_number, newError)
                    }

                    if (variantProducts?.length) {
                        variantProducts.forEach(vp => {
                            if (!variantValidationsMap.has(vp.row_number)) {
                                variantValidationsMap.set(vp.row_number, newError)
                            }
                        })
                    }
                }
                else {
                    validations.push(newError)
                }
                await session.abortTransaction()
                await session.endSession({ forceClear: true })
            }
        }
        catch (error) {
            const newError = error.message || error

            if (isVariantProduct) {
                if (!variantValidationsMap.has(row_number)) {
                    variantValidationsMap.set(row_number, newError)
                }

                if (variantProducts?.length) {
                    variantProducts.forEach(vp => {
                        if (!variantValidationsMap.has(vp.row_number)) {
                            variantValidationsMap.set(vp.row_number, newError)
                        }
                    })
                }
            }
            else {
                validations.push(newError)
            }
            await session.abortTransaction()
            await session.endSession({ forceClear: true })

            logger.error(error)
        }
    }

    setWarningValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, warnings = []) => {
        if (!warnings.length) {
            return
        }

        worksheet
            .getCell(columnCells[actualColumnCount - 1] + (row_number))
            .value = warnings.toString()
    }

    setValidationValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, validations = []) => {
        worksheet
            .getCell(columnCells[actualColumnCount - 2] + (row_number))
            .value =
            validations.length
                ? validations.toString()
                : "Passed Validation"
    }

    setStatusValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, validations = []) => {
        worksheet
            .getCell(columnCells[actualColumnCount - 3] + (row_number))
            .value =
            validations.length
                ? DATA_SHEET.SHEET_STATUS.PENDING
                : DATA_SHEET.SHEET_STATUS.COMPLETE
    }

    importProducts = async (req, productWorkSheet, excelData, list, columnCells, actualColumnCount, extraInfo) => {
        const {
            tenantId,
            approveType,
            operationType,
            apiVersion,
        } = req.body

        const importDataArray = []

        for (let index = 0; index < excelData.length; index++) {
            const {
                status,
                row_number,
                product_type,
                item_number,
                group_name,
            } = excelData[index]

            if (status === DATA_SHEET.SHEET_STATUS.PENDING) {
                const { validations, warnings } = await this.checkProductImportValidations(tenantId, excelData, index, list, extraInfo)

                this.setValidationValueInColumn(
                    productWorkSheet,
                    columnCells,
                    actualColumnCount,
                    row_number,
                    validations,
                )

                this.setWarningValueInColumn(
                    productWorkSheet,
                    columnCells,
                    actualColumnCount,
                    row_number,
                    warnings,
                )

                if (!validations.length) {
                    // Prepare import data to make products in DB.
                    if (product_type !== DATA_SHEET.PRODUCT_TYPE.VARIANT) {
                        const data = {
                            ...excelData[index],
                            "type": PRODUCT_TYPE.SINGLE
                        }

                        if ([DATA_SHEET.PRODUCT_TYPE.PARENT].includes(product_type)) {
                            const variantProducts = excelData.filter(element =>
                                element.product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT &&
                                element.parent_sku === item_number
                            )

                            if (variantProducts.length) {
                                data["type"] = PRODUCT_TYPE.VARIANT
                                data["variants_values"] = uniq(variantProducts.map(vp => vp.variant_value))

                                if (group_name) {
                                    data["groups_values"] = uniq(variantProducts.map(vp => vp.group_value))
                                }
                                data["variantProducts"] = variantProducts
                            }
                        }
                        importDataArray.push(data)
                    }
                }
            }
        }

        if (operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            for (let index = 0; index < importDataArray.length; index++) {
                const {
                    type,
                    row_number,
                    variantProducts,
                } = importDataArray[index]

                const validations = []
                const variantValidationsMap = new Map()

                await this.addProduct(
                    req,
                    tenantId,
                    importDataArray,
                    index,
                    list,
                    validations,
                    variantValidationsMap,
                    extraInfo,
                )

                if (type === PRODUCT_TYPE.SINGLE) {
                    this.setValidationValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        row_number,
                        validations,
                    )

                    this.setStatusValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        row_number,
                        validations,
                    )
                }
                else if (type === PRODUCT_TYPE.VARIANT) {
                    const errorRowNumbers = []

                    if (variantValidationsMap.size) {
                        variantValidationsMap.forEach((value, key, map) => {
                            errorRowNumbers.push(key)

                            this.setValidationValueInColumn(
                                productWorkSheet,
                                columnCells,
                                actualColumnCount,
                                key,
                                [value],
                            )

                            this.setStatusValueInColumn(
                                productWorkSheet,
                                columnCells,
                                actualColumnCount,
                                key,
                                [value],
                            )
                        })

                        /**
                         *  If parent product does not have any error then,
                         *  moving ahead for variant product's check
                         */
                        if (!errorRowNumbers.includes(row_number)) {
                            const variantRowNumbers = variantProducts.map(vp => vp.row_number)

                            if (errorRowNumbers.length !== variantRowNumbers.length) {
                                this.setValidationValueInColumn(
                                    productWorkSheet,
                                    columnCells,
                                    actualColumnCount,
                                    row_number,
                                )

                                this.setStatusValueInColumn(
                                    productWorkSheet,
                                    columnCells,
                                    actualColumnCount,
                                    row_number,
                                )

                                const validRowNumbers = difference(variantRowNumbers, errorRowNumbers)

                                if (validRowNumbers.length) {
                                    validRowNumbers.forEach(vpRowNumber => {
                                        this.setValidationValueInColumn(
                                            productWorkSheet,
                                            columnCells,
                                            actualColumnCount,
                                            vpRowNumber,
                                        )

                                        this.setStatusValueInColumn(
                                            productWorkSheet,
                                            columnCells,
                                            actualColumnCount,
                                            vpRowNumber,
                                        )
                                    })
                                }
                            }
                        }
                    }
                    else {
                        this.setValidationValueInColumn(
                            productWorkSheet,
                            columnCells,
                            actualColumnCount,
                            row_number,
                        )

                        this.setStatusValueInColumn(
                            productWorkSheet,
                            columnCells,
                            actualColumnCount,
                            row_number,
                        )

                        variantProducts.forEach(vp => {
                            this.setValidationValueInColumn(
                                productWorkSheet,
                                columnCells,
                                actualColumnCount,
                                vp.row_number,
                            )

                            this.setStatusValueInColumn(
                                productWorkSheet,
                                columnCells,
                                actualColumnCount,
                                vp.row_number,
                            )
                        })
                    }
                }
            }
        }
    }

    getSanitizedValue = (element, property, convert) => {
        const value = element[property]
        const isString = convert.name === "String"

        return value
            ? isString
                ? convert(value).trim()
                : convert(value)
            : isString
                ? ""
                : 0
    }

    updateProductImportDataSheet = async (req, worksheet) => {
        const {
            tenantId,
            operationType,
            importData,
            approveType,
            fileId,
            selectRow,
            apiVersion,
        } = req.body

        const productWorkSheet = worksheet.getWorksheet("product")

        if (!productWorkSheet) {
            return false
        }
        const A1Cell = productWorkSheet.getCell('A1').value

        if (A1Cell !== "item_number") {
            return false
        }
        const columnCells = generateExcelColumnCellName()

        // A count of the number of columns that have values.
        const actualColumnCount = productWorkSheet.actualColumnCount

        if (!importData) {
            DataSheetModel.setStatusAndValidationColumn(productWorkSheet, columnCells, actualColumnCount)
        }

        const familyList = await this.getCategoryListing(tenantId, VALUES.category.FAMILY)
        const categoryList = await this.getCategoryListing(tenantId, VALUES.category.CATEGORY)
        const subCategoryList = await this.getCategoryListing(tenantId, VALUES.category.SUBCATEGORY)
        const brandList = await this.getMasterDataListing(tenantId, "brand_name", MASTER_DATA.BRAND.LIST)
        const uomList = await this.getMasterDataListing(tenantId, "unit_name", MASTER_DATA.UNIT.LIST)

        const list = {
            familyList,
            categoryList,
            subCategoryList,
            brandList: brandList.list,
            uomList: uomList.list,
        }
        const extraInfo = {
            apiVersion,
        }

        if (operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            const tenantInfo = await MasterDataModel.getTenantInfo(tenantId, "advance_limit", { lean: true })
            const advanceLimitInfo = tenantInfo?.advance_limit?.find(data => data.key === "NUMBER_OF_PRODUCTS")
            const activeProducts = await ProductModel.allowNewValidation(tenantId)

            extraInfo.checkAddLimit = true
            extraInfo.addLimit = advanceLimitInfo.allowance
            extraInfo.canCreateNewProduct = advanceLimitInfo.allowance > activeProducts
        }

        const addRowNumber = true
        let excelData = DataSheetModel.getExcelData(productWorkSheet, 2, addRowNumber, importData)

        excelData.forEach(element => {
            for (let property in COLUMNS_TYPE_MAPPING) {
                element[property] = this.getSanitizedValue(element, property, COLUMNS_TYPE_MAPPING[property])
            }
        })

        if (importData) {
            if (approveType === DATA_SHEET.APPROVE_TYPE.SELECTED) {
                const selectedRows = []

                for (let i = 0, rows = selectRow.length; i < rows; i++) {
                    const row = excelData.find(element => element.row_number === selectRow[i])

                    if (row) {
                        selectedRows.push(row)
                    }
                }
                excelData = selectedRows
            }

            await this.importProducts(
                req,
                productWorkSheet,
                excelData,
                list,
                columnCells,
                actualColumnCount,
                extraInfo,
            )

            // Set process_start_time & status of the file in DB.
            const columnStatus = productWorkSheet.getColumn(actualColumnCount - 2).values
            const isPending = columnStatus.some(column => column === DATA_SHEET.SHEET_STATUS.PENDING)

            const payload = {
                "filter": {
                    "_id": fileId,
                },
                "updateFields": {
                    "process_start_time": null,
                    "status":
                        isPending
                            ? DATA_SHEET.STATUS.FOR_REVIEW
                            : DATA_SHEET.STATUS.COMPLETE,
                }
            }
            await Api.put(req, "dataSheet", payload);
        }
        else {
            for (let index = 0; index < excelData.length; index++) {
                if (excelData[index].status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    const { validations, warnings } = await this.checkProductImportValidations(tenantId, excelData, index, list, extraInfo)

                    productWorkSheet
                        .getCell(columnCells[actualColumnCount + 1] + (excelData[index]["row_number"]))
                        .value =
                        validations.length
                            ? validations.toString()
                            : "Passed Validation"

                    if (warnings.length) {
                        productWorkSheet
                            .getCell(columnCells[actualColumnCount + 2] + (excelData[index]["row_number"]))
                            .value = warnings.toString()
                    }
                }

                this.setStatusValueInColumn(
                    productWorkSheet,
                    columnCells,
                    actualColumnCount + 3,
                    excelData[index]["row_number"],
                    [1], // Radom array's value to set status PENDING
                )
            }
        }
        return true
    }

    setProductsInSheet = async (tenantId, worksheet, apiVersion) => {
        const productWorkSheet = worksheet.getWorksheet("product")

        let totalProducts,
            barcodeProductKey = "product_id",
            barcodeVariantKey = "variant_id",
            variantTypeKey = NEW_PRODUCT_TYPE.PARENT

        totalProducts = await ProductModel.getAllProductCount(tenantId)
        barcodeProductKey = barcodeVariantKey = "product_variant_id"

        const limit = 50
        const totalPages = Math.ceil(totalProducts / limit)
        let currentRowNumber = productWorkSheet.actualRowCount + 1

        const setValuesInRow = (data) => {
            // Get a row object. If it doesn't already exist, a new empty one will be returned
            const row = productWorkSheet.getRow(currentRowNumber)

            // assign row values by contiguous array (where array element 0 has a value)
            row.values = Object.values(data)

            // Increment row count, so that we can always put values in new row
            currentRowNumber++
        }

        for (let i = 1; i <= totalPages; i++) {
            const list = await ProductModel.getProductListingForDataSheet(tenantId, i, limit, apiVersion)

            for (let j = 0, totalProducts = list.length; j < totalProducts; j++) {
                const {
                    _id,
                    title = "",
                    secondary_language_title = "",
                    item_number: parent_item_number = "",
                    type = "",
                    brand_id = {},
                    family_id = {},
                    category_id = {},
                    subcategory_id = {},
                    packaging_map = {},
                    product_variants = [],
                    groups = {},
                    variants = {},
                    barcodes = []
                } = list[j]

                const excelData = {
                    item_number: parent_item_number,

                    product_type:
                        type === variantTypeKey
                            ? DATA_SHEET.PRODUCT_TYPE.PARENT
                            : DATA_SHEET.PRODUCT_TYPE.SINGLE,

                    barcode: "",
                    product_title: title,
                    product_title_sl: secondary_language_title,

                    parentage_type:
                        type === variantTypeKey
                            ? DATA_SHEET.PARENTAGE_TYPE[variants.type?.toUpperCase()] || ""
                            : "",

                    parent_sku: "",
                    variant_value: "",
                    group_name: groups.type || "",
                    group_value: "",

                    family: family_id.category_name || "",
                    category: category_id.category_name || "",
                    subcategory: subcategory_id.category_name || "",

                    brand: brand_id.brand_name || "",

                    qty_ctn: packaging_map.qty_ctn || "",
                    min_qty: packaging_map.min_qty || "",
                    uom: packaging_map.uom_id?.unit_name || "",
                }

                if (type === PRODUCT_TYPE.SINGLE) {
                    excelData["barcode"] = barcodes.join(",")

                    // Set values row wise in sheet
                    setValuesInRow(excelData)
                }
                else if (type === variantTypeKey) {
                    // Set values row wise in sheet
                    setValuesInRow(excelData)

                    // Get product variants and put in the sheet
                    for (let k = 0, totalProductVariants = product_variants.length; k < totalProductVariants; k++) {
                        const {
                            _id,
                            item_number = "",
                            packaging_map = {},
                            group_value_id = {},
                            variant_value_id = {},
                            barcodes = []
                        } = product_variants[k]

                        const excelData = {
                            item_number,
                            product_type: DATA_SHEET.PRODUCT_TYPE.VARIANT,

                            barcode: barcodes.join(","),
                            product_title: "",
                            product_title_sl: "",
                            parentage_type: "",

                            parent_sku: parent_item_number,
                            variant_value: variant_value_id.name || "",
                            group_name: "",
                            group_value: group_value_id.name || "",

                            family: "",
                            category: "",
                            subcategory: "",
                            brand: "",

                            qty_ctn: packaging_map.qty_ctn || 0,
                            min_qty: packaging_map.min_qty || 0,
                            uom: packaging_map.uom_id?.unit_name || "",
                        }

                        // Set values row wise in sheet
                        setValuesInRow(excelData)
                    }
                }
            }
        }
    }
}

module.exports = ProductDataSheetModel

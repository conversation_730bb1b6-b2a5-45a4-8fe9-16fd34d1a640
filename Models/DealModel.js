const DealSchema = require("../Database/Schemas/deal/deals");
const DealProductSchema = require("../Database/Schemas/deal/deal_products");
const ProductSchema = require("../Database/Schemas/product/ProductSchema");
const DealClickStatisticsLookupSchema = require('../Database/Schemas/deal/deal_click_statistics');

const {
	NEW_PRODUCT_TYPE,
	DEAL_STATUS,
	DEAL_SORT_BY,
	PRODUCT_SORT_TYPE,
	DEAL_TYPE,
	DEAL_SORT_TYPE,
	DEAL_STATISTICS_EVENT_TYPE,
	ORDER_STATUS_TYPES,
} = require("../Configs/constants");

const { toLeanOption } = require("../Utils/helpers");

class DealModel {
	async createDeal(body, header) {
		return DealSchema({
			tenant_id: body.tenantId,
			price_id: body.priceId,
			deal_type: body.dealType,
			created_by: header.userDetails._id,
			updated_by: header.userDetails._id,
		});
	}

	async getDeal(filter, projection = {}, options = {}) {
		return DealSchema.findOne(filter, projection, options);
	}

	async getDeals(filter, projection = {}, options = {}) {
		return await DealSchema.find(filter, projection, options);
	}

	getDealCount(filter) {
		return DealSchema.countDocuments(filter);
	}

	async getDealProductsAndUpdate(dealId, dealFromDate, dealToDate) {
		return DealProductSchema.updateMany(
			{ deal_id: dealId },
			{ deal_from_date: dealFromDate, deal_to_date: dealToDate },
		);
	}

	async getDealList(body) {
		const {
			tenantId,
			page,
			perPage,
			searchKey,
			priceId,
			dealType,
			dealStatus,
		} = body;

		const $match = {
			tenant_id: tenantId,
			deal_status: {
				$nin: [
					DEAL_STATUS.PROGRESS
				],
			},
		}

		if (searchKey) {
			$match.$or = [
				{ [`deal_name`]: { $regex: searchKey, $options: 'i' } },
				{
					[`secondary_deal_name`]: {
						$regex: searchKey,
						$options: 'i',
					},
				},
			]
		}
		else {
			$match.deal_status.$nin.push(DEAL_STATUS.ARCHIVED);
		}

		if (priceId) {
			$match.price_id = new mongoose.Types.ObjectId(priceId)
		}

		if (dealType) {
			$match.deal_type = dealType
		}

		if (dealStatus) {
			const currentDate = new Date();

			switch (dealStatus) {
				case DEAL_STATUS.RUNNING: {
					$match.deal_from_date = {
						$lte: currentDate
					}
					$match.deal_to_date = {
						$gte: currentDate
					}
					$match.deal_status = DEAL_STATUS.SCHEDULED
					break;
				}
				case DEAL_STATUS.SCHEDULED: {
					$match.deal_from_date = {
						$gt: currentDate
					}
					$match.deal_status = DEAL_STATUS.SCHEDULED
					break;
				}
				case DEAL_STATUS.ENDED: {
					$match.deal_to_date = {
						$lt: currentDate
					}
					$match.deal_status = DEAL_STATUS.SCHEDULED
					break;
				}
				default: {
					$match.deal_status = dealStatus
					break;
				}
			}
		}

		const pipeline = [
			{
				$match,
			},
			{
				$lookup: {
					from: 'deal_products',
					localField: '_id',
					foreignField: 'deal_id',
					as: 'deal_Products',
				}
			},
			{
				$addFields: {
					products_count: { $size: '$deal_Products' }
				}
			},
			{
				$match: {
					products_count: { $gt: 0 }
				}
			},
			{
				$sort: {
					created_at: -1,
				},
			},
			{
				$project: {
					deal_Products: 0,
					products_count: 0,
					created_by: 0,
					updated_by: 0,
					created_at: 0,
					updated_at: 0,
					__v: 0,
				},
			},
			{
				$facet: {
					item: [
						{ $skip: (page - 1) * perPage },
						{ $limit: perPage }
					],
					count: [
						{ $count: 'count' }
					],
				},
			},
			{
				$unwind: '$count',
			},
		];

		const data = await DealSchema.aggregate(pipeline);
		return {
			list: data[0]?.item ?? [],
			count: data[0]?.count?.count ?? 0
		};
	}

	async productList(body) {
		let {
			tenantId,
			dealFromDate,
			dealToDate,
			priceId,
			dealId,
			searchKey = '',
			page = 0,
			perPage = 10,
		} = body;

		const pipeline = [];

		/**
		 * @caution 🚩🚩🚩🚩🚩🚩🚩🚩🚩🚩
		 * If you change anything in $match then,
		 * please update its corresponding compound index
		 * in product schema.
		 */
		const $match = {
			tenant_id: tenantId,
			is_deleted: false,
			is_active: true,
			type: {
				$in: [NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.VARIANT],
			},
			"price_mappings": {
				"$elemMatch": {
					"master_price_id": new mongoose.Types.ObjectId(priceId),
					"price": { "$gt": 0 }
				}
			}
		};

		const dealInfo = await this.getDeal(
			{ _id: new mongoose.Types.ObjectId(dealId) },
			"-_id deal_type",
			toLeanOption,
		)

		if (dealInfo?.deal_type === DEAL_TYPE.BULK_PRICING) {
			$match["packaging_map.qty_ctn"] = { $gt: 0 }
		}

		pipeline.push(
			{
				$match,
			},
			{
				$project: {
					title: 1,
					item_number: 1,
					parent_id: 1,
					group_value_id: 1,
					variant_value_id: 1,
					group_id: 1,
					type: 1,
					packaging_map: 1,
					created_at: 1,
					price_mappings: {
						$first: {
							$filter: {
								input: '$price_mappings',
								as: 'price_mappings',
								cond: {
									$eq: [
										'$$price_mappings.master_price_id',
										new mongoose.Types.ObjectId(priceId),
									],
								},
							}
						},
					},
					inventory: {
						$sum: '$inventory_mappings.quantity',
					},
				},
			},
			{
				$sort: { created_at: -1 },
			},
			{
				$lookup: {
					from: 'products_2.0',
					localField: 'parent_id',
					foreignField: '_id',
					as: 'parent_product',
					pipeline: [
						{
							$project: {
								item_number: 1,
								title: 1,
							},
						},
						{
							$lookup: {
								from: 'images_2.0',
								localField: '_id',
								foreignField: 'product_variant_id',
								as: 'images',
								pipeline: [
									{
										$match: { group_id: null },
									},
									{
										$project: {
											image_number: 1,
											image_name: 1,
											s3_url: 1,
										},
									},
									{
										$sort: {
											image_number: 1,
										},
									},
									{
										$limit: 1,
									},
								],
							},
						},
						{
							$project: {
								item_number: 1,
								title: 1,
								image: {
									$first: '$images',
								},
							},
						},
					],
				},
			},
		)

		if (searchKey) {
			pipeline.push(
				{
					$match: {
						$or: [
							{ item_number: { $regex: searchKey, $options: 'i' } },
							{ title: { $regex: searchKey, $options: 'i' } },
							{ 'parent_product.title': { $regex: searchKey, $options: 'i' } }
						]
					}
				}
			)
		}

		pipeline.push(
			{
				$lookup: {
					from: 'images_2.0',
					localField: '_id',
					foreignField: 'product_variant_id',
					as: 'images',
					pipeline: [
						{
							$match: {
								group_id: null,
							},
						},
						{
							$project: {
								image_number: 1,
								image_name: 1,
								s3_url: 1,
							},
						},
						{
							$sort: {
								image_number: 1,
							},
						},
						{
							$limit: 1,
						},
					],
				},
			},
			{
				$lookup: {
					from: 'images_2.0',
					let: { group_id: '$group_value_id' },
					localField: 'group_value_id',
					foreignField: 'group_id',
					as: 'group_cover_image',
					pipeline: [
						{
							$match: {
								group_id: { $ne: null },
								$expr: {
									$eq: ['$group_id', '$$group_id'],
								},
							},
						},
						{
							$project: {
								image_number: 1,
								image_name: 1,
								s3_url: 1,
							},
						},
						{
							$sort: {
								image_number: 1,
							},
						},
						{
							$limit: 1,
						},
					],
				},
			},
			{
				$lookup: {
					from: 'variant_types_2.0',
					localField: 'variant_value_id',
					foreignField: '_id',
					as: 'variant_value_id',
					pipeline: [
						{
							$project: {
								name: 1
							},
						},
					],
				},
			},
			{
				$lookup: {
					from: 'variant_types_2.0',
					localField: 'group_value_id',
					foreignField: '_id',
					as: 'group_value_id',
					pipeline: [
						{
							$project: {
								name: 1
							},
						},
					],
				},
			},

			/**
			 * --------------------------------------------------------------------------------
			 * Start: Check whether current product is in any running deal or not. If in a 
			 * running deal then neglect current product
			 * --------------------------------------------------------------------------------
			 */
			{
				$lookup: {
					from: 'deal_products',
					localField: '_id',
					foreignField: 'product_id',
					as: 'deal',
					pipeline: [
						{
							$match: {
								price_id: new mongoose.Types.ObjectId(priceId)
							}
						},
						{
							$project: {
								deal_id: 1,
							}
						},
						{
							$lookup: {
								from: 'deals',
								localField: 'deal_id',
								foreignField: '_id',
								as: 'deal_info',
								pipeline: [
									{
										$project: {
											deal_from_date: 1,
											deal_to_date: 1,
										}
									}
								]
							}
						},
						{
							$unwind: '$deal_info'
						},
						{
							$project: {
								// deal_id: 1,
								deal_info: 1,
							}
						},
						{
							$addFields: {
								in_deal: {
									$and: [
										/**
										 * @description Checks deal dates overlapping. If true then product is in deal else not
										 * @reference https://stackoverflow.com/questions/325933/determine-whether-two-date-ranges-overlap
										 */
										{ $lte: ['$deal_info.deal_from_date', new Date(dealToDate)] },   // Deal starts on or before 'toDate'
										{ $gte: ['$deal_info.deal_to_date', new Date(dealFromDate)] }    // Deal ends on or after 'fromDate'
									]
								}
							}
						},
						{
							$match: {
								in_deal: true   // Only keep products that are in a deal
							}
						},
					]
				}
			},
			{
				$match: {
					deal: { $size: 0 }    // Ensure no deals are present for the product
				}
			},
			/**
			 * --------------------------------------------------------------------------------
			 * End: Check whether current product is in any running deal or not. If in a 
			 * running deal then neglect current product
			 * --------------------------------------------------------------------------------
			 */

			{
				$project: {
					title: 1,
					item_number: 1,
					image: {
						$first: '$images',
					},
					group_cover_image: {
						$first: '$group_cover_image',
					},
					type: 1,
					parent_product: {
						$first: '$parent_product',
					},
					variant_name: {
						$first: "$variant_value_id.name",
					},
					group_name: {
						$first: "$group_value_id.name",
					},
					packaging_map: 1,
					price_mappings: 1,
					inventory: 1,
				},
			},
			{
				$facet: {
					list: [
						{
							$skip: (page - 1) * perPage
						},
						{
							$limit: perPage
						}
					],
					count: [{ $count: 'count' }],
				},
			},
			{
				$unwind: {
					path: '$count',
				},
			},
		);

		const data = await ProductSchema.aggregate(pipeline);

		return {
			count: data[0]?.count?.count || 0,
			list: data[0]?.list || [],
		};
	}

	async getCurrentDealProductList(body) {
		let {
			dealId,
			priceId,
			searchKey = '',
			page = 0,
			perPage = 10,
			sortBy,
			sortType,
			dealProductSequence,
			nextDealProductCount
		} = body;

		page = parseInt(page);
		perPage = parseInt(perPage);
		const offset = (page - 1) * perPage;
		dealProductSequence = parseInt(dealProductSequence);
		nextDealProductCount = parseInt(nextDealProductCount);

		const pipeline = [];
		const sort = {};

		const $match = {
			deal_id: new mongoose.Types.ObjectId(dealId),
		};

		if (sortBy && sortType) {
			if (sortBy === DEAL_SORT_BY.PRICE) {
				sortBy = 'product.price_mappings.price';
			} else if (sortBy === DEAL_SORT_BY.INVENTORY) {
				sortBy = 'product.inventory_mappings';
			} else {
				sortBy = 'deal_product_sequence';
			}

			sort[sortBy] = sortType === PRODUCT_SORT_TYPE.ASC ? 1 : -1;
		} else {
			sort['deal_product_sequence'] = 1;
		}

		pipeline.push(
			{
				$match,
			},
			{
				$lookup: {
					from: 'products_2.0',
					localField: 'product_id',
					foreignField: '_id',
					as: 'product',
					pipeline: [
						{
							$lookup: {
								from: 'products_2.0',
								localField: 'parent_id',
								foreignField: '_id',
								as: 'parent_product',
								pipeline: [
									{
										$lookup: {
											from: 'images_2.0',
											localField: '_id',
											foreignField: 'product_variant_id',
											as: 'images',
											pipeline: [
												{
													$match: { group_id: null },
												},
												{
													$project: {
														image_number: 1,
														image_name: 1,
														s3_url: 1,
													},
												},
												{
													$sort: {
														image_number: 1,
													},
												},
												{
													$limit: 1,
												},
											],
										},
									},
									{
										$addFields: {
											image: {
												$first: '$images',
											},
										},
									},
									{
										$project: {
											item_number: 1,
											title: 1,
											image: 1,
										},
									},
								],
							},
						},
						{
							$lookup: {
								from: 'variant_types_2.0',
								localField: 'variant_value_id',
								foreignField: '_id',
								as: 'variant_value_id',
								pipeline: [
									{
										$project: {
											name: 1,
										},
									},
								],
							},
						},
						{
							$lookup: {
								from: 'variant_types_2.0',
								localField: 'group_value_id',
								foreignField: '_id',
								as: 'group_name',
								pipeline: [
									{
										$project: {
											name: 1,
										},
									},
								],
							},
						},
						{
							$lookup: {
								from: 'images_2.0',
								let: { group_id: '$group_value_id' },
								localField: 'group_value_id',
								foreignField: 'group_id',
								as: 'group_cover_image',
								pipeline: [
									{
										$match: {
											group_id: { $ne: null },
											$expr: {
												$eq: ['$group_id', '$$group_id'],
											},
										},
									},
									{
										$project: {
											image_number: 1,
											image_name: 1,
											s3_url: 1,
										},
									},
									{
										$sort: {
											image_number: 1,
										},
									},
									{
										$limit: 1,
									},
								],
							},
						},
						{
							$lookup: {
								from: 'images_2.0',
								localField: '_id',
								foreignField: 'product_variant_id',
								as: 'product_images',
								pipeline: [
									{
										$match: {
											group_id: null,
										},
									},
									{
										$project: {
											image_number: 1,
											image_name: 1,
											s3_url: 1,
										},
									},
									{
										$sort: {
											image_number: 1,
										},
									},
									{
										$limit: 1,
									},
								],
							},
						},
						{
							$addFields: {
								parent_product: {
									$first: '$parent_product',
								},
								variant_name: {
									$first: '$variant_value_id.name',
								},
								group_name: {
									$first: '$group_name.name',
								},
								product_images: {
									$first: '$product_images',
								},
								group_cover_image: {
									$first: '$group_cover_image',
								},
							},
						},
						{
							$project: {
								item_number: 1,
								title: 1,
								type: 1,
								variant_name: 1,
								group_name: 1,
								parent_product: 1,
								product_images: 1,
								group_cover_image: 1,
								packaging_map: 1,
								price_mappings: 1,
								inventory_mappings: 1,
								price_mappings: {
									$filter: {
										input: '$price_mappings',
										as: 'price_mappings',
										cond: {
											$eq: [
												'$$price_mappings.master_price_id',
												new mongoose.Types.ObjectId(priceId),
											],
										},
									},
								},
								inventory_mappings: {
									$sum: '$inventory_mappings.quantity',
								},
							},
						},
					],
				},
			},
			{
				$addFields: {
					product: {
						$first: '$product',
					},
				},
			},
		);

		if (searchKey) {
			const searchMatch = {};
			searchMatch['$or'] = [
				{ 'product.item_number': { $regex: searchKey, $options: 'i' } },
				{ 'product.parent_product.title': { $regex: searchKey, $options: 'i' } },
				{ 'product.title': { $regex: searchKey, $options: 'i' } },
			];

			pipeline.push({
				$match: searchMatch,
			});
		}

		pipeline.push(
			{
				$addFields: {
					'product.price_mappings': {
						$first: '$product.price_mappings',
					},
				},
			},
			{
				$project: {
					created_by: 0,
					updated_by: 0,
					created_at: 0,
					updated_at: 0,
					__v: 0,
				},
			},
			{
				$sort: sort,
			},
		)

		if (dealProductSequence) {
			pipeline.push(
				{
					$match: {
						deal_product_sequence: { $gt: dealProductSequence }
					}
				},
				{
					$limit: nextDealProductCount
				}
			)

			return await DealProductSchema.aggregate(pipeline)
		} else {

			pipeline.push(
				{
					$facet: {
						list: [{ $skip: offset }, { $limit: perPage }],
						count: [{ $count: 'count' }],
					},
				},
				{
					$unwind: '$count',
				},
			);
			const data = await DealProductSchema.aggregate(pipeline);
			const list = data.length && data[0].list ? data[0].list : [];
			const count =
				data.length && data[0].count && data[0].count.count ? data[0].count.count : 0;

			return { list, count };
		}
	}

	async getCurrentDealAllProducts(dealId) {
		const $match = {
			deal_id: new mongoose.Types.ObjectId(dealId),
		};
		const pipeline = [];
		pipeline.push(
			{
				$match,
			},
			{
				$project: {
					deal_id: 1,
					_id: 1,
					product_id: 1,
					deal_product_sequence: 1,
					deal_from_date: 1,
					deal_to_date: 1,
				},
			},
			{
				$sort: {
					deal_product_sequence: 1,
				},
			},
		);
		const data = await DealProductSchema.aggregate(pipeline);

		const list = data.length && data ? data : [];
		return list;
	}

	async checkCurrentDealProducts(products, dealDetail) {
		const pipeline = [];

		const $match = {
			deal_id: {
				$ne: new mongoose.Types.ObjectId(dealDetail._id),
			},
			product_id: { $in: products },
			price_id: new mongoose.Types.ObjectId(dealDetail.price_id),
			$or: [ //TODO need to add other AND conditions in this OR condition from segregateDealProducts
				{
					$and: [
						{ deal_from_date: { $gte: dealDetail.deal_from_date } },
						{ deal_to_date: { $lte: dealDetail.deal_to_date } },
					],
				},
				{
					$and: [
						{ deal_from_date: { $lte: dealDetail.deal_from_date } },

						{ deal_to_date: { $gte: dealDetail.deal_from_date } },
					],
				},
			],
		};
		pipeline.push(
			{
				$match,
			},
			{
				$project: {
					deal_id: 1,
					_id: 1,
					product_id: 1,
					deal_product_sequence: 1,
					deal_from_date: 1,
					deal_to_date: 1,
				},
			},
		);
		const data = await DealProductSchema.aggregate(pipeline);
		return data.length ? false : true;
	}

	async checkDealProduct(productId, dealDetail, checkInDeal) {
		const pipeline = [];

		const match = {
			product_id: new mongoose.Types.ObjectId(productId),
		};

		if (checkInDeal) {
			match['deal_id'] = new mongoose.Types.ObjectId(dealDetail._id)
		} else {
			match['deal_id'] = { $ne: new mongoose.Types.ObjectId(dealDetail._id) };
			match['price_id'] = new mongoose.Types.ObjectId(dealDetail.price_id);
			match['$or'] = [{ //TODO need to add other AND conditions in this OR condition from segregateDealProducts
				$and: [
					{ deal_from_date: { $gte: dealDetail.deal_from_date } },
					{ deal_to_date: { $lte: dealDetail.deal_to_date } },
				],
			},
			{
				$and: [
					{ deal_from_date: { $lte: dealDetail.deal_from_date } },

					{ deal_to_date: { $gte: dealDetail.deal_from_date } },
				],
			}]
		}
		pipeline.push(
			{
				$match: match,
			},
			{
				$project: {
					deal_id: 1,
					_id: 1,
					product_id: 1,
					deal_product_sequence: 1,
					deal_from_date: 1,
					deal_to_date: 1,
				},
			},
		);
		const data = await DealProductSchema.aggregate(pipeline);
		return data.length ? true : false;
	}

	async addDealProduct(body, productId, priceId, header) {
		return DealProductSchema({
			tenant_id: body.tenantId,
			deal_id: body.dealId,
			price_id: priceId,
			product_id: productId,
			created_by: header.userDetails._id,
			updated_by: header.userDetails._id,
			is_active: true,
		});
	}

	async getDealProduct(dealProductId) {
		return await DealProductSchema.findById(dealProductId);
	}

	async getSingleDealProduct(filter, projection = {}, options = {}) {
		return await DealProductSchema.findOne(filter, projection, options);
	}

	async findDealProducts(filter, projection = {}, options = {}) {
		return DealProductSchema.find(filter, projection, options)
	}

	aggregateDealProducts(pipeline) {
		return DealProductSchema.aggregate(pipeline)
	}

	async getDealProductsCount(filter) {
		return DealProductSchema.countDocuments(filter)
	}

	async deletedDealProduct(id) {
		return await DealProductSchema.deleteOne({ _id: id });
	}

	async deleteMultipleDealProducts(ids) {
		return DealProductSchema.deleteMany({ _id: { $in: ids } });
	}

	updateDealProduct(filter, update = {}, options = {}) {
		return DealProductSchema.updateOne(filter, update, options)
	}

	async updateMultipleDealProducts(filter, projection = {}, options = {}) {
		return DealProductSchema.updateMany(filter, projection, options);
	}

	async deleteDealProducts(ids) {
		return DealProductSchema.deleteMany({ deal_id: { $in: ids } });
	}

	async getRunningDealsProductList(body) {
		let {
			tenantId,
			productLimit,
			salesPersonUserId,
			dealType,
			priceId,
			hideOutOfStock,
			branchId,
		} = body;

		productLimit = parseInt(productLimit);
		const date = moment().utc().toDate();

		const match = {
			tenant_id: tenantId,
			deal_status: {
				$nin: [
					DEAL_STATUS.PROGRESS,
					DEAL_STATUS.PAUSED,
					DEAL_STATUS.CANCELLED,
					DEAL_STATUS.ARCHIVED
				],
			},
		};

		if (priceId) {
			match['price_id'] = new mongoose.Types.ObjectId(priceId);
		}

		if (salesPersonUserId) {
			match['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserId);
		}

		if (dealType) {
			match['deal_type'] = dealType;
		}

		const dealProductFilter = {
			"is_deleted": false,
			"is_active": true,
			"price_mappings": {
				"$elemMatch": {
					"master_price_id": new mongoose.Types.ObjectId(priceId),
					"price": { "$gt": 0 }
				}
			}
		}
		let dealProductInventoryProject = 1

		if (branchId) {
			if (hideOutOfStock) {
				dealProductFilter["inventory_mappings"] = {
					"$elemMatch": {
						"branch_id": branchId,
						"quantity": { "$gt": 0 }
					}
				}
			}
			else {
				dealProductFilter["inventory_mappings.branch_id"] = branchId
			}

			dealProductInventoryProject = {
				"$let": {
					"vars": {
						"filteredMapping": {
							"$arrayElemAt": [
								{
									"$filter": {
										"input": "$inventory_mappings",
										"as": "inventory_mapping",
										"cond": {
											"$eq": ["$$inventory_mapping.branch_id", branchId]
										}
									}
								},
								0
							]
						}
					},
					"in": "$$filteredMapping.quantity"
				}
			}
		}

		const dealProductPipeline = [
			{
				$lookup: {
					from: 'products_2.0',
					localField: 'product_id',
					foreignField: '_id',
					as: 'product',
					pipeline: [
						{
							$match: dealProductFilter,
						},
						{
							$lookup: {
								from: 'products_2.0',
								localField: 'parent_id',
								foreignField: '_id',
								as: 'parent_product',
								pipeline: [
									{
										$lookup: {
											from: 'images_2.0',
											localField: '_id',
											foreignField: 'product_variant_id',
											as: 'image',
											pipeline: [
												{
													$match: { group_id: null },
												},
												{
													$project: {
														image_number: 1,
														image_name: 1,
														s3_url: 1,
													},
												},
												{
													$sort: {
														image_number: 1,
													},
												},
												{
													$limit: 1,
												},
											],
										},
									},
									{
										$addFields: {
											product_image: { $first: '$image' },
										},
									},
									{
										$project: {
											item_number: 1,
											title: 1,
											secondary_language_title: 1,
											product_image: 1,
										},
									},
								],
							},
						},
						{
							$lookup: {
								from: 'variant_types_2.0',
								localField: 'variant_value_id',
								foreignField: '_id',
								as: 'variant_value_id',
								pipeline: [
									{
										$project: { name: 1 },
									},
								],
							},
						},
						{
							$lookup: {
								from: 'variant_types_2.0',
								localField: 'group_value_id',
								foreignField: '_id',
								as: 'group_name',
								pipeline: [
									{
										$project: { name: 1 },
									},
								],
							},
						},
						{
							$lookup: {
								from: 'images_2.0',
								let: {
									group_id: '$group_value_id',
								},
								localField: 'group_value_id',
								foreignField: 'group_id',
								as: 'group_cover_image',
								pipeline: [
									{
										$match: {
											group_id: { $ne: null },
											$expr: {
												$eq: ['$group_id', '$$group_id'],
											},
										},
									},
									{
										$project: {
											image_number: 1,
											image_name: 1,
											s3_url: 1,
										},
									},
									{
										$sort: { image_number: 1 },
									},
									{
										$limit: 1,
									},
								],
							},
						},
						{
							$lookup: {
								from: 'images_2.0',
								localField: '_id',
								foreignField: 'product_variant_id',
								as: 'product_images',
								pipeline: [
									{
										$match: { group_id: null },
									},
									{
										$project: {
											image_number: 1,
											image_name: 1,
											s3_url: 1,
										},
									},
									{
										$sort: { image_number: 1 },
									},
									{
										$limit: 1,
									},
								],
							},
						},
						{
							$addFields: {
								parent_product: {
									$first: '$parent_product',
								},
								variant_name: {
									$first: '$variant_value_id.name',
								},
								group_name: {
									$first: '$group_name.name',
								},
								product_images: {
									$first: '$product_images',
								},
								group_cover_image: {
									$first: '$group_cover_image',
								},
							},
						},
						{
							$project: {
								item_number: 1,
								title: 1,
								secondary_language_title: 1,
								type: 1,
								packaging_map: 1,
								inventory_mappings: dealProductInventoryProject,
								parent_product: 1,
								variant_id: 1,
								group_name: 1,
								variant_name: 1,
								product_images: 1,
								group_cover_image: 1,
								price_mappings: {
									$filter: {
										input: '$price_mappings',
										as: 'price_mappings',
										cond: {
											$eq: [
												'$$price_mappings.master_price_id',
												new mongoose.Types.ObjectId(priceId),
											],
										},
									},
								},
							},
						},
					],
				},
			},
			{
				$addFields: {
					product: {
						$first: '$product',
					},
				},
			},
			{
				$addFields: {
					'product.price_mapping': {
						$first: '$product.price_mappings',
					},
				},
			},
			{
				$addFields: {
					sort_products: {
						$switch: {
							branches: [
								{
									case: { $eq: ['$$sortType', DEAL_SORT_TYPE.INVENTORY] }, then: '$product.inventory_mappings'
								},
								{
									case: { $eq: ['$$sortType', DEAL_SORT_TYPE.PRICE] }, then: '$product.price_mapping.price'
								},
								{
									case: { $eq: ['$$sortType', DEAL_SORT_TYPE.MANUAL] }, then: '$deal_product_sequence'
								},
							],
							default: '$deal_product_sequence'
						}
					},
				},
			},
			{
				$sort: {
					sort_products: 1,
				},
			},
			{
				$match: {
					product: { $ne: null },
				},
			},
			{
				$addFields: {
					'product.discount_type': '$discount_type',
					'product.percent': '$percent',
					'product.amount': '$amount',
					'product.discounted_price': '$discounted_price',
					'product.first_tier': '$first_tier',
					'product.second_tier': '$second_tier',
					'product.third_tier': '$third_tier',
					'product.buy_product': '$buy_product',
					'product.free_product': '$free_product',
				},
			},
			{
				$project: {
					product: 1,
				},
			},
			{
				$match: {
					'product.price_mappings.price': { $nin: [null, 0] },
					$or: [
						{
							'product.percent': { $ne: null },
						},
						{
							'product.first_tier': { $ne: null },
						},
						{
							'product.buy_product': { $ne: null },
						},
					],
				},
			},
		];

		if (productLimit) {
			dealProductPipeline.push({
				$limit: productLimit,
			});
		}

		const pipeline = [
			{
				$match: match,
			},
			{
				$lookup: {
					from: 'deal_products',
					let: {
						sortType: '$sort_type'
					},
					localField: '_id',
					foreignField: 'deal_id',
					as: 'deal_products',
					pipeline: dealProductPipeline,
				},
			},
			{
				$project: {
					price_id: 1,
					deal_type: 1,
					sort_type: 1,
					deal_product_count: {
						$size: '$deal_products',
					},
					deal_name: 1,
					secondary_deal_name: 1,
					deal_from_date: 1,
					deal_to_date: 1,
					deal_products: 1,
				},
			},
			{
				$match: {
					deal_product_count: { $ne: 0 },
					deal_from_date: { $lte: date },
					deal_to_date: { $gte: date },
				},
			},
		];
		return await DealSchema.aggregate(pipeline);
	}

	async deleteMultipleDeals(ids) {
		return DealSchema.deleteMany({ _id: { $in: ids } });
	}

	async updateMultipleDeals(filter, projection = {}, options = {}) {
		return DealSchema.updateMany(filter, projection, options);
	}

	async updateClickViewCountForDeal(messageBody) {
		const dealProductUpdateObject = { $inc: { view_count: 1 } };

		const {
			dealId,
			dealProductId,
			userRoleId,
			customerUserRoleId,
		} = messageBody

		let userRoleIdAndCustomerRoleId = userRoleId

		if (
			customerUserRoleId &&
			customerUserRoleId !== userRoleId
		) {
			userRoleIdAndCustomerRoleId = userRoleId + "_" + customerUserRoleId
		}

		const result = await DealClickStatisticsLookupSchema.updateOne(
			{
				_id: `${dealProductId}_${userRoleIdAndCustomerRoleId}`
			},
			{
				deal_product_id: dealProductId,
				deal_id: dealId
			},
			{
				upsert: true
			}
		);

		if (!(result.matchedCount > 0)) {
			dealProductUpdateObject['$inc']['click_count'] = 1
		}

		await Promise.all([
			DealProductSchema.updateOne(
				{ _id: dealProductId },
				dealProductUpdateObject
			),
			DealSchema.updateOne(
				{ _id: dealId },
				dealProductUpdateObject
			)
		])
	}

	async updateSalesCountForDeal(messageBody) {
		const { dealId, dealProductId, dealType, freeItemCount, orderStatus, quantity, basPrice, tax } = messageBody;

		let salesBaseAmount = 0;
		let salesTaxAmount = 0;
		let salesQuantity = 0;
		switch (dealType) {
			case DEAL_TYPE.BUY_X_AND_GET_Y:
				{
					salesQuantity = freeItemCount + quantity;
					salesBaseAmount = quantity * basPrice;
					salesTaxAmount = quantity * tax;
					break;
				}
			case DEAL_TYPE.BULK_PRICING:
				{
					salesQuantity = quantity;
					salesBaseAmount = quantity * basPrice;
					salesTaxAmount = quantity * tax;
					break;
				}

			case DEAL_TYPE.DISCOUNT:
				{
					salesQuantity = quantity;
					salesBaseAmount = quantity * basPrice;
					salesTaxAmount = quantity * tax;
					break;
				}
		}

		const dealProductFilter = { _id: dealProductId };
		const updateObj = { $inc: { sales_base_price: salesBaseAmount, sales_tax: salesTaxAmount, unit_sold_count: salesQuantity } };
		const dealFilter = { _id: dealId };
		if (orderStatus === ORDER_STATUS_TYPES.CANCELLED) {
			dealProductFilter['sales_base_price'] = { $gte: salesBaseAmount };
			dealProductFilter['sales_tax'] = { $gte: salesTaxAmount };
			dealProductFilter['unit_sold_count'] = { $gte: salesQuantity };

			dealFilter['sales_base_price'] = { $gte: salesBaseAmount };
			dealFilter['sales_tax'] = { $gte: salesTaxAmount };
			dealFilter['unit_sold_count'] = { $gte: salesQuantity };

			updateObj['$inc']['unit_sold_count'] *= (-1);
			updateObj['$inc']['sales_base_price'] *= (-1);
			updateObj['$inc']['sales_tax'] *= (-1);
		}

		await Promise.all([
			DealSchema.updateOne(dealFilter, updateObj),
			DealProductSchema.updateOne(dealProductFilter, updateObj),
		])

	}

	async updateCartCountForDeal({ dealId, dealProductId }) {
		const updateObject = { $inc: { add_to_cart_count: 1 } }
		await Promise.all([
			DealProductSchema.updateOne(
				{ _id: dealProductId },
				updateObject
			),
			DealSchema.updateOne({ _id: dealId }, updateObject)
		])
	}

	async updateDealStatistics(message) {
		const messageBody = JSON.parse(message.Body);
		switch (messageBody.dealStatisticsEventType) {
			case DEAL_STATISTICS_EVENT_TYPE.CLICK_VIEW_COUNT_UPDATE:
				{
					await this.updateClickViewCountForDeal(messageBody);
					break;
				}

			case DEAL_STATISTICS_EVENT_TYPE.SALES_COUNT_UPDATE:
				{
					await this.updateSalesCountForDeal(messageBody);
					break;
				}

			case DEAL_STATISTICS_EVENT_TYPE.ADD_TO_CART_COUNT_UPDATE:
				{
					await this.updateCartCountForDeal(messageBody);
					break;
				}
		}

		return message.MessageId;
	}
}

module.exports = DealModel;

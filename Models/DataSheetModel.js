const excelJS = require("exceljs");
const path = require("path")

const { FILE_PATH, BUCKET_TYPE, ENTITY_STATUS, DATA_SHEET } = require("../Configs/constants");
const { MASTER_DATA } = require("../Configs/ApiType");

const FileUpload = require('../Configs/awsUploader').S3Upload;
const MasterDataModel = new (require("../Models/MasterDataModel"))()
const CategoryModel = new (require("./CategoryModel"))()

class DataSheetModel {

    async priceExcel(tenantId, tenantPriceList, allProduct) {
        const fileName = `${tenantId}_price_${new Date().toISOString().split('T')[0]}_${new Date().getTime()}.csv`

        const sheetPath = path.join(__dirname, `/../Assets/DataSheets/${fileName}`)
        const workbook = new excelJS.Workbook()

        const worksheet = workbook.addWorksheet("PriceSheet")
        worksheet.properties.defaultRowHeight = 15
        worksheet.properties.defaultColWidth = 30

        const priceListOrder = []

        worksheet.columns = [
            {
                header: 'Item Number',
                key: 'itemNumber',
                width: 35
            },
            {
                header: 'Parent',
                key: 'parentId',
                width: 35,
                protection: {
                    locked: true,
                    lockText: true
                }
            },
            {
                header: 'Product/Variant Name',
                key: 'productName',
                width: 35
            },
            {
                header: "Active",
                key: "isActive",
                width: 35,
            },
            ...tenantPriceList.map(element => {
                priceListOrder.push(element.price_name)
                return ({
                    header: element.price_name,
                    key: element.price_id,
                    width: 35
                })
            })
        ]

        allProduct.forEach(product => {
            const rowObj = {
                itemNumber: product?.itemNumber,
                productName: product.name,
                isActive: product.isActive
                    ? 'TRUE'
                    : 'FALSE',
                parentId: product?.parentItemNumber,
                width: 35,
            }

            product.priceDetails.forEach((item) => {
                rowObj[item.priceId] = item.price
            })
            worksheet.addRow(rowObj)
        })

        await workbook.csv.writeFile(sheetPath)

        const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

        await fileUpload.uploadFiles(
            FILE_PATH.DATA_SHEET.EXPORT +
            `/${tenantId}/price/update/export`,
            'price.csv',
            sheetPath
        )

        const url =
            process.env.AWS_PUBLIC_BUCKET_BASE_URL +
            process.env.AWS_BUCKET_FOLDER +
            `dataSheet/${tenantId}/price/update/export/price.csv`
        return url
    }

    async inventoryExcel(tenantId, branches, allProduct) {
        const fileName = `${tenantId}_inventory_${new Date().toISOString().split('T')[0]}_${new Date().getTime()}.csv`

        const sheetPath = path.join(__dirname, `/../Assets/DataSheets/${fileName}`)
        let workbook = new excelJS.Workbook()

        const worksheet = workbook.addWorksheet("InventorySheet")
        worksheet.properties.defaultRowHeight = 15
        worksheet.properties.defaultColWidth = 35

        const branchListOrder = []

        worksheet.columns = [
            {
                header: 'Item Number',
                key: 'itemNumber',
                width: 35
            },
            {
                header: 'Parent',
                key: 'parentId',
                width: 35,
                protection: {
                    locked: true,
                    lockText: true
                }
            },
            {
                header: 'Product/Variant Name',
                key: 'productName',
                width: 35
            },
            {
                header: "Active",
                key: "isActive",
                width: 35,
            },
            ...branches.map(element => {
                branchListOrder.push(element.name)

                return ({
                    header: element.name,
                    key: element._id,
                    width: 35
                })
            })
        ]

        allProduct.forEach(product => {
            const rowObj = {
                itemNumber: product?.itemNumber,
                productName: product.name,
                isActive: product.isActive
                    ? 'TRUE'
                    : 'FALSE',
                parentId: product.parentItemNumber,
                width: 35,
            }

            product.inventoryDetails.forEach((item) => {
                rowObj[item.branch_id] = item.quantity
            })
            worksheet.addRow(rowObj)
        })

        await workbook.csv.writeFile(sheetPath)

        const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC)

        await fileUpload.uploadFiles(
            FILE_PATH.DATA_SHEET.EXPORT +
            `/${tenantId}/inventory/update/export`,
            'inventory.csv',
            sheetPath
        )

        const url =
            process.env.AWS_PUBLIC_BUCKET_BASE_URL +
            process.env.AWS_BUCKET_FOLDER +
            `dataSheet/${tenantId}/inventory/update/export/inventory.csv`

        return url
    }

    setColumnInProduct = async (tenantId, column, columnName, projectKey, apiType) => {
        const params = {
            req: {
                query: {
                    tenantId,
                    status: ENTITY_STATUS.ACTIVE,
                    perPage: 0,
                    page: 0,
                    projection: {
                        "_id": 0,
                        [projectKey]: 1
                    }
                }
            },
            apiType,
        }

        const response = await MasterDataModel.getAttributeList(params.req, params.apiType)
        const values = response.list.map(element => element[projectKey])

        column.values = [columnName, ...values]
    }

    setValuesSheetInProduct = async (tenantId, worksheet) => {
        const valuesWorksheet = worksheet.getWorksheet("values")

        // Set brand list in the sheet.
        const brandsColumn = valuesWorksheet.getColumn(3)
        const brandArgs = [tenantId, brandsColumn, "brands", "brand_name", MASTER_DATA.BRAND.LIST]
        await this.setColumnInProduct(...brandArgs)

        // Set units of measurement in the sheet.
        const uomColumn = valuesWorksheet.getColumn(4)
        const uomArgs = [tenantId, uomColumn, "uom", "unit_name", MASTER_DATA.UNIT.LIST]
        await this.setColumnInProduct(...uomArgs)
    }

    setCategorySheetInProduct = async (tenantId, worksheet, worksheetName, categoryType, subCategoryType, sortCategory) => {
        const categoryWorksheetName = worksheet.getWorksheet(worksheetName)

        const categoryListResponse = await CategoryModel.categoryList(
            tenantId,
            categoryType,
            ENTITY_STATUS.ACTIVE,
            null,
            null,
            sortCategory,
            {
                "category_name": 1,
            }
        )
        const categoryList = categoryListResponse.map(category => category.category_name)

        // Get a row object. If it doesn't already exist, a new empty one will be returned
        const row = categoryWorksheetName.getRow(1)

        // assign row values by contiguous array (where array element 0 has a value)
        row.values = categoryList

        // Set sub-category; category wise in the sheet.
        for (let i = 0; i < categoryListResponse.length; i++) {
            const category = categoryListResponse[i]

            const subcategoryListResponse = await CategoryModel.categoryList(
                tenantId,
                subCategoryType,
                ENTITY_STATUS.ACTIVE,
                category._id,
                null,
                true,
                {
                    "_id": 0,
                    "category_name": 1,
                }
            )
            const subCategoryList = subcategoryListResponse.map(subCategory => subCategory.category_name)

            const column = await categoryWorksheetName.getColumn(i + 1)
            column.values = [category.category_name, ...subCategoryList]
        }
    }

    setStatusAndValidationColumn = (worksheet, columnCells, actualColumnCount) => {
        const newColumns = [
            {
                name: "status",
                value: null, // Set its value from the getExcelData function
                // value: DATA_SHEET.SHEET_STATUS.PENDING,
            },
            {
                name: "validations",
                value: null,
            },
            {
                name: "warnings",
                value: null,
            },
        ]

        for (let i = 0; i < newColumns.length; i++) {
            const currentColumn = newColumns[i]
            const newCell = columnCells[actualColumnCount + i]

            // Get or create a new cell, if not exists.
            worksheet.getCell(newCell + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            }

            // Set currentColumn name
            worksheet.getCell(newCell + 1).value = currentColumn.name

            if (currentColumn.value) {
                for (let j = 3; j <= worksheet.actualRowCount; j++) {
                    worksheet.getCell(newCell + j).value = currentColumn.value
                }
            }
        }
    }

    getExcelData = (worksheet, skipRows = 0, addRowNumber, importData) => {
        const excelData = []
        let excelTitles = []

        // excel to json converter (only the first sheet)
        worksheet.eachRow((row, rowNumber) => {
            // get values from row
            let rowValues = row.values;

            // remove first element (extra without reason)
            rowValues.shift();

            if (rowNumber === 1) {
                excelTitles = rowValues
            }
            // Create a JSON data with skipped first {skipRows} numbers of columns.
            else if (rowNumber > skipRows) {
                // create object with the titles and the row values (if any)
                const rowObject = {}

                for (let i = 0; i < excelTitles.length; i++) {
                    const title = excelTitles[i].trim()
                    const value = rowValues[i] || ''

                    rowObject[title] =
                        !importData && title.toUpperCase() === "STATUS"
                            ? DATA_SHEET.SHEET_STATUS.PENDING
                            : value
                }

                if (addRowNumber) {
                    rowObject.row_number = rowNumber
                }
                excelData.push(rowObject)
            }
        })
        return excelData
    }
}

module.exports = DataSheetModel;

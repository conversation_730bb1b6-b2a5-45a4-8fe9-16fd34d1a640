const mongoose = require("mongoose");
const ProductSchema = require("../Database/Schemas/product/ProductSchema");

const CategorySchema = require("../Database/Schemas/CategorySchema");
const ProductBarcodeSchema = require("../Database/Schemas/product/product_barcodes");

const ProductTagSchema = require("../Database/Schemas/product/product_tags");
const VariantTypesSchema = require("../Database/Schemas/product/variant_types");
const FavoriteProductSchema = require("../Database/Schemas/product/favorite_products");

const DealProductSchema = require("../Database/Schemas/deal/deal_products");
const InternalServiceModel = new (require("./InternalServiceModel"));

const {
    PRODUCT_VARIANT_TYPES,
    PRODUCT_SEARCH_TYPES,
    NEW_PRODUCT_TYPE,
    PRODUCT_FILTER_TYPES,
    FILE_PATH,
    ACTIVE_STATUS_ENUM,
    DEAL_STATUS,
    DATA_SHEET,
    PRODUCT_TYPE,
    STATUS_CODES
} = require("../Configs/constants");

const FileUpload = require('../Configs/awsUploader').S3Upload;

const {
    httpService,
    toLeanOption,
} = require("../Utils/helpers");

class ProductModel {
    async createProduct(productDetails) {
        return new ProductSchema(productDetails);
    }

    async findProduct(filter, projection, options) {
        return ProductSchema.findOne(filter, projection, options);
    }

    async findProducts(filter, projection, options) {
        return ProductSchema.find(filter, projection, options);
    }

    async countProducts(filter) {
        return ProductSchema.countDocuments(filter);
    }

    async updateProduct(filter, updateObj, options) {
        return ProductSchema.updateOne(filter, updateObj, options);
    }

    async updateProducts(filter, updateObj, options) {
        return ProductSchema.updateMany(filter, updateObj, options)
    }

    async deleteProducts(filter, options) {
        return ProductSchema.deleteMany(filter, options);
    }

    async findCategoryById(filter, projection = {}, options = {}) {
        return CategorySchema.findOne(filter, projection, options);
    }

    async getAllProductCount(tenantId) {
        return ProductSchema.countDocuments({
            tenant_id: tenantId,
            is_deleted: false,
        })
    }

    async getAllBarCodes(filter, projection = "", options = {}) {
        return ProductBarcodeSchema.find(filter, projection, options)
    }

    async allowNewValidation(tenantId, type) {
        return ProductSchema.countDocuments({
            tenant_id: tenantId,
            is_deleted: false,
            is_active: true,
            type: {
                $in: [NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.PARENT]
            }
        })
    }

    async getBarcode(filter, projection, options) {
        return ProductBarcodeSchema.findOne(filter, projection, options);
    }

    async addUpdateBarcodes(tenant_id, barcodes = [], product_variant_id, options = {}, checkDuplicateBarcode = false) {
        const ids = []
        const barcodeObj = {}

        for (let i = 0; i < barcodes.length; i++) {
            const _id = `${tenant_id}_${barcodes[i]}`;
            ids.push(_id);
            barcodeObj[_id] = {
                tenant_id,
                barcode: barcodes[i],
                product_variant_id,
                is_active: true
            }
        }

        if (checkDuplicateBarcode) {    
            const existingBarcodes = await this.getAllBarCodes(
                {
                    "tenant_id": tenant_id,
                    "product_variant_id": {
                        $ne: product_variant_id,
                    },
                    "is_active": true,
                    "_id": {
                        "$in": ids
                    },
                },
                "_id",
                {
                    lean: true
                }
            )

            if (existingBarcodes.length) {
                const totalBarCodeIds = existingBarcodes.map(element => element._id)

                const error = new Error();
                error.code = STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE
                error.name = "barcode_already_exists";
                error.keyValue = { barcodes: totalBarCodeIds };
                throw error;
            }
        }

        return Promise.all(ids.map(_id => {
            return ProductBarcodeSchema.updateOne({ _id }, barcodeObj[_id], { ...options, upsert: true })
        }));
    }

    async deleteBarcodes(filter, options) {
        return ProductBarcodeSchema.deleteMany(filter, options);
    }

    async addOrUpdateTag(tenant_id, tag = "", options) {
        const filter = {
            tenant_id,
            name: tag.trim(),
        }
        return ProductTagSchema.findOneAndUpdate(filter, { tenant_id, name: tag.trim(), $inc: { attached_count: 1 } }, { upsert: true, returnOriginal: false, ...options });
    }

    async updateTags(filter, updateObject, options) {
        ProductTagSchema.updateMany(filter, updateObject, options);
    }

    async decreaseTagCount(tenant_id, tagIds = [], options) {
        const filter = {
            tenant_id,
            _id: { $in: tagIds },
            attached_count: { $gt: 0 }
        }
        return ProductTagSchema.updateMany(filter, { $inc: { attached_count: -1 } }, options);
    }

    async findTags(filter, projection, options) {
        return ProductTagSchema.find(filter, projection, options);
    }

    async addVariantsAndGroups(tenant_id, variants = [], groups = [], product_id, options = {}) {
        let varList = [];
        const variantTypes = [];
        const groupTypes = [];

        for (let i = 0; i < variants.length; i++) {
            const vr = variants[i];
            let name = vr
            let order = i + 1

            if (typeof vr === "object") {
                name = vr.name
                order = vr.order
            }

            variantTypes.push({
                _id: new mongoose.Types.ObjectId().toString(),
                name,
                product_id,
                type: PRODUCT_VARIANT_TYPES.VARIANT_VALUE,
                tenant_id,
                is_deleted: false,
                order,
            });
        }

        for (let i = 0; i < groups.length; i++) {
            const gr = groups[i];
            let name = gr
            let order = i + 1

            if (typeof gr === "object") {
                name = gr.name
                order = gr.order
            }

            groupTypes.push({
                _id: new mongoose.Types.ObjectId().toString(),
                name,
                product_id,
                type: PRODUCT_VARIANT_TYPES.GROUP_VALUE,
                tenant_id,
                is_deleted: false,
                order,
            })
        }
        varList = [...variantTypes, ...groupTypes];
        await VariantTypesSchema.create(varList, options);
        return [variantTypes, groupTypes];
    }

    async deleteVariantTypes(filter, options) {
        return VariantTypesSchema.deleteMany(filter, options)
    }

    async getVariantType(filter = {}, projection = {}, options = {}) {
        return VariantTypesSchema.findOne(filter, projection, options);
    }

    async getVariantTypes(filter = {}, projection = {}, options = {}) {
        return VariantTypesSchema.find(filter, projection, options)
    }

    async productAggregation(pipeline) {
        return ProductSchema.aggregate(pipeline)
    }

    async updateDealProducts(filter, updateObject, options) {
        return DealProductSchema.updateMany(filter, updateObject, options);
    }

    async searchProducts(req) {
        const { searchType } = req.query

        try {
            var searchStage = await this.getProductSearchStage(req)
        }
        catch (error) {
            throw error
        }

        if (searchType === PRODUCT_SEARCH_TYPES.AUTO_COMPLETE) {
            return await this.autoCompleteProducts(req, searchStage)
        }
        else if ([PRODUCT_SEARCH_TYPES.SEARCH, PRODUCT_SEARCH_TYPES.FILTER].includes(searchType)) {
            return await this.getSearchedProductList(req, searchStage)
        }
    }

    async rewardProductList(body, rewardProducts) {
        const {
            tenantId,
            searchKey,
            page,
            perPage,
        } = body;

        const pipeline = [];

        const $match = {
            tenant_id: tenantId,
            is_deleted: false,
            is_active: true,
            type: {
                $in: [NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.VARIANT],
            }
        };

        if (rewardProducts.length) {
            $match._id = {
                $nin: rewardProducts.map(product => {
                    return new mongoose.Types.ObjectId(product.product_variant_id)
                })
            }
        }

        pipeline.push(
            {
                $match,
            },
            {
                $sort: { created_at: -1 },
            },
            {
                $lookup: {
                    from: 'products_2.0',
                    localField: 'parent_id',
                    foreignField: '_id',
                    as: 'parent_product',
                    pipeline: [
                        {
                            $lookup: {
                                from: 'images_2.0',
                                localField: '_id',
                                foreignField: 'product_variant_id',
                                as: 'images',
                                pipeline: [
                                    {
                                        $match: { group_id: null },
                                    },
                                    {
                                        $project: {
                                            image_number: 1,
                                            image_name: 1,
                                            s3_url: 1,
                                        },
                                    },
                                    {
                                        $sort: {
                                            image_number: 1,
                                        },
                                    },
                                    {
                                        $limit: 1,
                                    },
                                ],
                            },
                        },
                        {
                            $addFields: {
                                image: {
                                    $first: '$images',
                                },
                            },
                        },
                        {
                            $project: {
                                item_number: 1,
                                title: 1,
                                image: 1,
                            },
                        },
                    ],
                },
            },
        )

        if (searchKey) {
            pipeline.push(
                {
                    $match: {
                        $or: [
                            { item_number: { $regex: searchKey, $options: 'i' } },
                            { title: { $regex: searchKey, $options: 'i' } },
                            { 'parent_product.title': { $regex: searchKey, $options: 'i' } }
                        ]
                    }
                }
            )
        }

        pipeline.push(
            {
                $lookup: {
                    from: 'images_2.0',
                    localField: '_id',
                    foreignField: 'product_variant_id',
                    as: 'images',
                    pipeline: [
                        {
                            $match: {
                                group_id: null,
                            },
                        },
                        {
                            $project: {
                                image_number: 1,
                                image_name: 1,
                                s3_url: 1,
                            },
                        },
                        {
                            $sort: {
                                image_number: 1,
                            },
                        },
                        {
                            $limit: 1,
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'images_2.0',
                    let: { group_id: '$group_value_id' },
                    localField: 'group_value_id',
                    foreignField: 'group_id',
                    as: 'group_cover_image',
                    pipeline: [
                        {
                            $match: {
                                group_id: { $ne: null },
                                $expr: {
                                    $eq: ['$group_id', '$$group_id'],
                                },
                            },
                        },
                        {
                            $project: {
                                image_number: 1,
                                image_name: 1,
                                s3_url: 1,
                            },
                        },
                        {
                            $sort: {
                                image_number: 1,
                            },
                        },
                        {
                            $limit: 1,
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'variant_types_2.0',
                    localField: 'variant_value_id',
                    foreignField: '_id',
                    as: 'variant_value_id',
                    pipeline: [
                        {
                            $project: {
                                name: 1
                            },
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'variant_types_2.0',
                    localField: 'group_value_id',
                    foreignField: '_id',
                    as: 'group_value_id',
                    pipeline: [
                        {
                            $project: {
                                name: 1
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    image: {
                        $first: '$images',
                    },
                    group_cover_image: {
                        $first: '$group_cover_image',
                    },
                    parent_product: {
                        $first: '$parent_product',
                    },
                    variant_name: {
                        $first: '$variant_value_id.name',
                    },
                    group_name: {
                        $first: '$group_value_id.name',
                    },
                },
            },
            {
                $project: {
                    title: 1,
                    item_number: 1,
                    image: 1,
                    group_cover_image: 1,
                    type: 1,
                    parent_product: 1,
                    variant_name: 1,
                    group_name: 1,
                    packaging_map: 1,
                    price_mappings: 1,
                    inventory: {
                        $sum: '$inventory_mappings.quantity',
                    },
                },
            },
            {
                $facet: {
                    list: [
                        { $skip: (page - 1) * perPage },
                        { $limit: perPage }
                    ],
                    count: [
                        { $count: 'count' }
                    ],
                },
            },
            {
                $unwind: {
                    path: '$count',
                },
            },
        );

        const data = await ProductSchema.aggregate(pipeline)
        return {
            list: data[0]?.list ?? [],
            count: data[0]?.count?.count ?? 0
        };
    }

    async getProductListingForDataSheet(tenantId, page = 1, perPage = 10) {
        const variantCollection = "products_2.0"
        const variantForeignKey = "parent_id"
        const variantTypesForeignKey = "variant_types_2.0"

        const pipeline = [
            {
                "$match": {
                    "tenant_id": tenantId,
                    "is_deleted": false,
                    "type": {
                        "$in": [NEW_PRODUCT_TYPE.SINGLE, NEW_PRODUCT_TYPE.PARENT]
                    },
                }
            },
            {
                "$project": {
                    "title": 1,
                    "secondary_language_title": 1,
                    "item_number": 1,
                    "type": 1,
                    "brand_id": 1,
                    "family_id": 1,
                    "category_id": 1,
                    "subcategory_id": 1,
                    "packaging_map": 1,
                    "groups": 1,
                    "variants": 1,
                    "barcodes": 1,
                }
            },
            {
                "$facet": {
                    "product": [
                        {
                            "$skip": perPage * (page - 1)
                        },
                        {
                            "$limit": perPage
                        }
                    ],
                }
            },
            {
                "$unwind": "$product"
            },
            {
                "$lookup": {
                    "from": "master_brands",
                    "localField": "product.brand_id",
                    "foreignField": "_id",
                    "as": "product.brand_id",
                    "pipeline": [
                        {
                            "$project": {
                                "brand_name": 1,
                            }
                        },
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "categories",
                    "localField": "product.family_id",
                    "foreignField": "_id",
                    "as": "product.family_id",
                    "pipeline": [
                        {
                            "$project": {
                                "category_name": 1,
                            }
                        },
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "categories",
                    "localField": "product.category_id",
                    "foreignField": "_id",
                    "as": "product.category_id",
                    "pipeline": [
                        {
                            "$project": {
                                "category_name": 1,
                            }
                        },
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "categories",
                    "localField": "product.subcategory_id",
                    "foreignField": "_id",
                    "as": "product.subcategory_id",
                    "pipeline": [
                        {
                            "$project": {
                                "category_name": 1,
                            }
                        },
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "master_units",
                    "localField": "product.packaging_map.uom_id",
                    "foreignField": "_id",
                    "as": "product.packaging_map.uom_id",
                    "pipeline": [
                        {
                            "$project": {
                                "unit_name": 1
                            }
                        }
                    ]
                }
            },
            {
                "$lookup": {
                    "from": variantCollection,
                    "as": "product.product_variants",
                    "foreignField": variantForeignKey,
                    "localField": "product._id",
                    "pipeline": [
                        {
                            "$match": {
                                "is_deleted": false,
                            }
                        },
                        {
                            "$project": {
                                [variantForeignKey]: 1,
                                "item_number": 1,
                                "packaging_map": 1,
                                "group_value_id": 1,
                                "variant_value_id": 1,
                                "variant_order": 1,
                                "barcodes": 1,
                            }
                        },
                        {
                            "$lookup": {
                                "from": variantTypesForeignKey,
                                "localField": "variant_value_id",
                                "foreignField": "_id",
                                "as": "variant_value_id",
                                "pipeline": [
                                    {
                                        "$project": {
                                            "name": 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "$lookup": {
                                "from": variantTypesForeignKey,
                                "localField": "group_value_id",
                                "foreignField": "_id",
                                "as": "group_value_id",
                                "pipeline": [
                                    {
                                        "$project": {
                                            "name": 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "$lookup": {
                                "from": "master_units",
                                "localField": "packaging_map.uom_id",
                                "foreignField": "_id",
                                "as": "packaging_map.uom_id",
                                "pipeline": [
                                    {
                                        "$project": {
                                            "unit_name": 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "$addFields": {
                                "group_value_id": {
                                    "$first": "$group_value_id"
                                },
                                "variant_value_id": {
                                    "$first": "$variant_value_id"
                                },
                                "packaging_map.uom_id": {
                                    "$first": "$packaging_map.uom_id"
                                },
                            }
                        },
                        {
                            "$sort": {
                                "variant_order": 1
                            }
                        }
                    ]
                }
            },
            {
                "$addFields": {
                    "product.family_id": {
                        "$first": "$product.family_id"
                    },
                    "product.category_id": {
                        "$first": "$product.category_id"
                    },
                    "product.subcategory_id": {
                        "$first": "$product.subcategory_id"
                    },
                    "product.brand_id": {
                        "$first": "$product.brand_id"
                    },
                    "product.packaging_map.uom_id": {
                        "$first": "$product.packaging_map.uom_id"
                    },
                }
            }
        ];


        const data = await ProductSchema.aggregate(pipeline)

        const list = data.map(d => d.product)
        return list
    }

    async findByItemNumber(
        isSingleProduct,
        type,
        itemNumber,
        mappingArr,
        updatedBy,
        parentProductItemNumber,
        branchWarehouseMapping,
        tenantId,
        apiVersion,
        // active,
    ) {
        let answer;

        /* let isActive = false

        if (active.toUpperCase() === BOOLEAN.TRUE) {
            isActive = true
        } */

        if (type === DATA_SHEET.DATA_TYPE.PRICE) {
            if (isSingleProduct) {
                answer = await ProductSchema.findOneAndUpdate(
                    {
                        unique_item_number: `${tenantId}_${itemNumber}`,
                        tenant_id: tenantId,
                        is_deleted: false,
                    },
                    {
                        price_mappings: mappingArr,
                        // is_active: isActive,
                        updated_by: updatedBy
                    },
                    {
                        lean: true,
                        projection: {
                            _id: 1,
                        },
                    }
                )
            }
            else {
                const filter = {
                    unique_item_number: `${tenantId}_${itemNumber}`,
                    tenant_id: tenantId,
                    is_deleted: false,
                    type: PRODUCT_TYPE.VARIANT
                }

                answer = await ProductSchema.findOneAndUpdate(
                    filter,
                    {
                        price_mappings: mappingArr,
                        // is_active: isActive,
                        updated_by: updatedBy
                    },
                    {
                        lean: true,
                        projection: {
                            _id: 1,
                        },
                    }
                );

                const parentProduct = await ProductSchema.findOne(
                    {
                        unique_item_number: `${tenantId}_${parentProductItemNumber}`,
                        tenant_id: tenantId,
                        is_deleted: false,
                    },
                    {
                        price_mappings: 1,
                    }
                );

                const allVariants = await ProductSchema.find(
                    {
                        "parent_id": parentProduct._id,
                        "is_deleted": false,
                        "is_active": true,
                    },
                    {
                        price_mappings: 1,
                        // is_active: 1
                    },
                    toLeanOption,
                );
                const productMasterPriceMap = {}
                // const allVariantsActiveStatus = []

                allVariants.forEach((variant) => {
                    for (let p = 0; p < variant.price_mappings?.length; p++) {
                        let varPriceMap = variant.price_mappings[p];

                        varPriceMap["product_variant_id"] = variant._id; // add the variant info for price-mapping

                        if (
                            varPriceMap.price > 0 && (
                                (!productMasterPriceMap[varPriceMap.master_price_id]) ||
                                (productMasterPriceMap[varPriceMap.master_price_id].price > varPriceMap.price)
                            )
                        ) {
                            productMasterPriceMap[varPriceMap.master_price_id] = varPriceMap;
                        }
                    }
                    // allVariantsActiveStatus.push(variant.is_active)
                })

                /* const areAllActive = allVariantsActiveStatus.every(status => status === true)
                const areAllIsActive = allVariantsActiveStatus.every(status => status === false)

                if (areAllActive) {
                    parentProduct.is_active = true
                }
                else if (areAllIsActive) {
                    parentProduct.is_active = false
                } */

                parentProduct.price_mappings = Object.values(productMasterPriceMap)
                await parentProduct.save()
            }
        }
        else {
            if (isSingleProduct) {
                answer = await ProductSchema.findOne(
                    {
                        unique_item_number: `${tenantId}_${itemNumber}`,
                        tenant_id: tenantId,
                        is_deleted: false,
                    },
                    {
                        inventory_mappings: 1,
                    },
                );

                const existingInventoryMappings = [...(answer.inventory_mappings || [])]

                mappingArr.forEach(element => {
                    const dataIndex = existingInventoryMappings.findIndex(inventory => inventory.branch_id === element.branch_id)

                    if (dataIndex > -1) {
                        existingInventoryMappings[dataIndex]["quantity"] = element.quantity || 0
                    }
                    else {
                        existingInventoryMappings.push({
                            ...element,
                            "warehouse_id": branchWarehouseMapping[element.branch_id],
                        })
                    }
                })

                answer.inventory_mappings = existingInventoryMappings
                // answer.is_active = isActive

                await answer.save()
            }
            else {
                const filter = {
                    unique_item_number: `${tenantId}_${itemNumber}`,
                    tenant_id: tenantId,
                    is_deleted: false,
                    type: PRODUCT_TYPE.VARIANT
                }

                answer = await ProductSchema.findOne(
                    filter,
                    {
                        inventory_mappings: 1,
                    },
                );

                const existingInventoryMappings = [...(answer.inventory_mappings || [])]

                mappingArr.forEach(element => {
                    const dataIndex = existingInventoryMappings.findIndex(inventory => inventory.branch_id === element.branch_id)

                    if (dataIndex > -1) {
                        existingInventoryMappings[dataIndex]["quantity"] = element.quantity || 0
                    }
                    else {
                        existingInventoryMappings.push({
                            ...element,
                            "warehouse_id": branchWarehouseMapping[element.branch_id],
                        })
                    }
                })

                answer.inventory_mappings = existingInventoryMappings
                // answer.is_active = isActive

                await answer.save()

                const parentProduct = await ProductSchema.findOne(
                    {
                        unique_item_number: `${tenantId}_${parentProductItemNumber}`,
                        tenant_id: tenantId,
                        is_deleted: false,
                    },
                    {
                        inventory_mappings: 1,
                    }
                );

                const allVariants = await ProductSchema.find(
                    {
                        "parent_id": parentProduct._id,
                        is_deleted: false,
                        "is_active": true,
                    },
                    {
                        inventory_mappings: 1,
                        // is_active: 1,
                    },
                    toLeanOption,
                );
                const idWithTotalValue = {}
                // const allVariantsActiveStatus = []

                allVariants.forEach((variant) => {
                    variant.inventory_mappings.forEach((inventory) => {
                        if (idWithTotalValue[inventory.branch_id]) {
                            idWithTotalValue[inventory.branch_id].quantity += inventory.quantity
                        } else {
                            idWithTotalValue[inventory.branch_id] = inventory
                        }
                    })
                    // allVariantsActiveStatus.push(variant.is_active)
                })

                parentProduct.inventory_mappings = Object.values(idWithTotalValue)

                /* const areAllActive = allVariantsActiveStatus.every(status => status === true)
                const areAllIsActive = allVariantsActiveStatus.every(status => status === false)

                if (areAllActive) {
                    parentProduct.is_active = true
                }
                else if (areAllIsActive) {
                    parentProduct.is_active = false
                } */

                await parentProduct.save()
            }
        }
        if (answer) {
            return true;
        }
        return false;
    }

    async getProductSearchStage(req) {
        const {
            tenantId,
            searchKey,
            searchType,
            priceListId,
            filters,
            hideOutOfStock,
            branchId
        } = req.query

        const searchStage = {
            "$search": {
                "index": "product_v2_search",
                "returnStoredSource": true,
                "compound": {
                    "filter": [
                        {
                            "range": {
                                "path": "tenant_id",
                                "gte": tenantId,
                                "lte": tenantId,
                            }
                        },
                        {
                            "queryString": {
                                "defaultPath": "type",
                                "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                            }
                        },
                        {
                            "equals": {
                                "path": "is_active",
                                "value": true,
                            }
                        },
                        {
                            "equals": {
                                "path": "is_deleted",
                                "value": false,
                            }
                        },
                        {
                            "embeddedDocument": {
                                "path": "price_mappings",
                                "operator": {
                                    "compound": {
                                        "filter": [
                                            {
                                                "equals": {
                                                    "path": "price_mappings.master_price_id",
                                                    "value": new mongoose.Types.ObjectId(priceListId),
                                                }
                                            },
                                            {
                                                "range": {
                                                    "path": "price_mappings.price",
                                                    "gt": 0,
                                                }
                                            },
                                        ],
                                    },
                                }
                            }
                        }
                    ],
                },
                // "highlight": {
                //     "path": titleKey
                // }
            },
        }

        if (
            [
                PRODUCT_SEARCH_TYPES.AUTO_COMPLETE,
                PRODUCT_SEARCH_TYPES.SEARCH
            ].includes(searchType)
        ) {
            const shouldMatches = [
                {
                    "autocomplete": {
                        "query": searchKey,
                        // "path": titleKey,
                        "path": "title",
                        "fuzzy": {
                            "maxEdits": 1
                        },
                    },
                },
                {
                    "autocomplete": {
                        "query": searchKey,
                        // "path": titleKey,
                        "path": "secondary_language_title",
                        "fuzzy": {
                            "maxEdits": 1
                        },
                    },
                },
                {
                    "autocomplete": {
                        "query": searchKey,
                        "path": "item_number",
                    },
                },
                {
                    "autocomplete": {
                        "query": searchKey,
                        "path": "active_variant_item_numbers",
                    },
                },
                {
                    "text": {
                        "query": searchKey,
                        "path": ["title", "secondary_language_title"],
                        "fuzzy": {
                            "maxEdits": 1
                        },
                    }
                },
                {
                    "text": {
                        "query": searchKey,
                        "path": [
                            "item_number",
                            "active_variant_item_numbers",
                        ],
                    }
                },
            ]

            searchStage["$search"]["compound"]["should"] = shouldMatches
            searchStage["$search"]["compound"]["minimumShouldMatch"] = 1
        }
        else if (searchType == PRODUCT_SEARCH_TYPES.FILTER) {
            const mustMatches = []

            const {
                productType = [],
                brands = [],
                priceRange = {},
                tags = [],
            } = filters

            if (productType.length) {
                for await (let type of productType) {

                    if (type === PRODUCT_FILTER_TYPES.NEW_PRODUCTS) {
                        const settings = await InternalServiceModel.tenantAppSettings(tenantId, "consider_new_item")

                        const days = "consider_new_item" in settings
                            ? settings.consider_new_item
                            : 30

                        mustMatches.push(
                            {
                                "range": {
                                    "path": "created_at",
                                    "gte": moment().subtract(days, "days").toDate()
                                }
                            }
                        )
                    }
                    else if (type === PRODUCT_FILTER_TYPES.RESTOCKED_PRODUCTS) {
                        mustMatches.push(
                            {
                                "equals": {
                                    "path": "is_restocked",
                                    "value": true,
                                }
                            }
                        )
                    }
                }
            }

            if (brands.length) {
                const shouldMatches = []

                brands.forEach(brandId => {
                    shouldMatches.push(
                        {
                            "equals": {
                                "path": "brand_id",
                                "value": new mongoose.Types.ObjectId(brandId),
                            }
                        }
                    )
                })

                mustMatches.push(
                    {
                        "compound": {
                            "should": shouldMatches,
                            "minimumShouldMatch": 1,
                        }
                    }
                )
            }

            if ("from" in priceRange && "to" in priceRange) {

                const existingFilterIndex = searchStage["$search"]["compound"]["filter"].findIndex(
                    query => query.embeddedDocument?.path === "price_mappings"
                )

                if (existingFilterIndex > -1) {
                    const filterArray = searchStage["$search"]["compound"]["filter"][existingFilterIndex]["embeddedDocument"]["operator"]["compound"]["filter"]
                    const rangeIndex = filterArray.findIndex(query => query.range)

                    if (rangeIndex > -1) {
                        filterArray[rangeIndex]["range"] = {
                            "path": "price_mappings.price",
                            "gte": +(priceRange.from || 0),
                            "lte": +(priceRange.to || 0),
                        }
                    }
                }
                else {
                    searchStage["$search"]["compound"]["filter"].push({
                        "embeddedDocument": {
                            "path": "price_mappings",
                            "operator": {
                                "compound": {
                                    "filter": [
                                        {
                                            "equals": {
                                                "path": "price_mappings.master_price_id",
                                                "value": new mongoose.Types.ObjectId(priceListId),
                                            }
                                        },
                                        {
                                            "range": {
                                                "path": "price_mappings.price",
                                                "gte": +(priceRange.from || 0),
                                                "lte": +(priceRange.to || 0),
                                            }
                                        },
                                    ]
                                }
                            }
                        }
                    })
                }
            }

            if (tags.length) {
                const shouldMatches = []

                tags.forEach(tagId => {
                    shouldMatches.push(
                        {
                            "equals": {
                                "path": "tags",
                                "value": new mongoose.Types.ObjectId(tagId),
                            }
                        }
                    )
                })

                mustMatches.push(
                    {
                        "compound": {
                            "should": shouldMatches,
                            "minimumShouldMatch": 1,
                        }
                    }
                )
            }

            if (!mustMatches.length) {
                return Promise.reject()
            }
            searchStage["$search"]["compound"]["must"] = mustMatches
        }

        if (hideOutOfStock && branchId) {
            searchStage["$search"]["compound"]["filter"].push({
                "embeddedDocument": {
                    "path": "inventory_mappings",
                    "operator": {
                        "compound": {
                            "filter": [
                                {
                                    "text": {
                                        "query": branchId,
                                        "path": "inventory_mappings.branch_id",
                                    }
                                },
                                {
                                    "range": {
                                        "path": "inventory_mappings.quantity",
                                        "gt": 0,
                                    }
                                },
                            ]
                        }
                    }
                }
            })
        }

        return searchStage
    }

    async autoCompleteProducts(req, searchStage) {
        const {
            perPage = 10,
            salesPersonUserRoleId,
            priceListId,
            tenantId,
        } = req.query

        const date = moment().utc().toDate();

        const dealLookUpMatchObj = {
            deal_from_date: { $lte: date },
            deal_to_date: { $gte: date },
        }

        const dealMatch = {
            tenant_id: tenantId,
            deal_status: {
                $nin: [DEAL_STATUS.PROGRESS, DEAL_STATUS.PAUSED, DEAL_STATUS.CANCELLED],
            }
        }

        if (salesPersonUserRoleId) {
            dealMatch['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserRoleId)
        }

        const dealPipeline = [
            {
                $match: dealMatch
            },
            {
                $project: {
                    deal_id: 1,
                    deal_type: 1,
                    deal_name: 1,
                    secondary_deal_name: 1,
                    deal_from_date: 1,
                    deal_to_date: 1,
                }
            }
        ]

        if (priceListId) {
            dealLookUpMatchObj['price_id'] = new mongoose.Types.ObjectId(priceListId);
        }

        const pipeline = [
            searchStage,
            {
                "$limit": perPage
            },
            {
                "$lookup": {
                    "from": "products_2.0",
                    "localField": "_id",
                    "foreignField": "_id",
                    "as": "product",
                    "pipeline": [
                        {
                            $lookup: {
                                from: "deal_products",
                                localField: "_id",
                                foreignField: "product_id",
                                as: "product_deal_info",
                                pipeline: [
                                    {
                                        $match: dealLookUpMatchObj
                                    },
                                    {
                                        $lookup: {
                                            from: "deals",
                                            localField: "deal_id",
                                            foreignField: "_id",
                                            as: "deal_id",
                                            pipeline: dealPipeline
                                        }
                                    },
                                    {
                                        $addFields: {
                                            "deal_id": { $first: "$deal_id" },
                                        }
                                    },
                                    {
                                        $match: { deal_id: { $ne: null } }
                                    },
                                    {
                                        $project: {
                                            deal_id: 1,
                                            discount_type: 1,
                                            percent: 1,
                                            amount: 1,
                                            discounted_price: 1,
                                            first_tier: 1,
                                            second_tier: 1,
                                            third_tier: 1,
                                            buy_product: 1,
                                            free_product: 1,
                                            deal_from_date: 1,
                                            deal_to_date: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "deal_products",
                                localField: "_id",
                                foreignField: "parent_id",
                                as: "parent_deals",
                                pipeline: [
                                    {
                                        $match: dealLookUpMatchObj
                                    },
                                    {
                                        $lookup: {
                                            from: "deals",
                                            localField: "deal_id",
                                            foreignField: "_id",
                                            as: "deal_id",
                                            pipeline: dealPipeline
                                        }
                                    },
                                    {
                                        $addFields: {
                                            "deal_id": { $first: "$deal_id" },
                                        }
                                    },
                                    {
                                        $match: { deal_id: { $ne: null } }
                                    },
                                    {
                                        $project: {
                                            deal_id: 1,
                                            discount_type: 1,
                                            percent: 1,
                                            amount: 1,
                                            discounted_price: 1,
                                            first_tier: 1,
                                            second_tier: 1,
                                            third_tier: 1,
                                            buy_product: 1,
                                            free_product: 1,
                                            deal_from_date: 1,
                                            deal_to_date: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "$addFields": {
                                "product_deal_info": {
                                    "$first": "$product_deal_info",
                                },
                                "parent_deal_size": {
                                    "$size": "$parent_deals"
                                },
                                "parent_deals": {
                                    "$first": "$parent_deals",
                                },
                            }
                        },
                        {
                            "$project": {
                                "title": 1,
                                "secondary_language_title": 1,
                                "tenant_id": 1,
                                "item_number": 1,
                                "active_variant_item_numbers": 1,
                                "price_mappings": 1,
                                "inventory_mappings": 1,
                                "is_active": 1,
                                "is_deleted": 1,
                                "created_at": 1,
                                "product_deal_info": 1,
                                "variant_deal_info": {
                                    $cond: [{ $eq: ['$parent_deal_size', 1] }, '$parent_deals', null]
                                },
                                "product_deal_info": 1,
                                "multipleDeal": {
                                    $cond: [{ $gt: ['$parent_deal_size', 1] }, true, false
                                    ]
                                },
                            }
                        },
                    ]
                }
            },
            {
                "$addFields": {
                    "product": {
                        "$first": "$product",
                    },
                }
            },
        ]

        const data = await ProductSchema.aggregate(pipeline)
        const list = data.map(d => d.product)

        return list
    }

    async getSearchedProductList(req, searchStage) {
        const {
            tenantId,
            perPage = 10,
            page = 1,
            salesPersonUserRoleId,
            priceListId,
            searchType,
            filters,
            hideOutOfStock,
            branchId,
            isProductSplitting,
        } = req.query

        const userRoleId = req.headers.userroleid;

        const offset = perPage * (page - 1)

        const date = moment().utc().toDate();

        const dealLookUpMatchObj = {
            deal_from_date: { $lte: date },
            deal_to_date: { $gte: date },
        }

        if (priceListId) {
            dealLookUpMatchObj['price_id'] = new mongoose.Types.ObjectId(priceListId);
        }

        const dealMatch = {
            tenant_id: tenantId,
            deal_status: {
                $nin: [DEAL_STATUS.PROGRESS, DEAL_STATUS.PAUSED, DEAL_STATUS.CANCELLED],
            }
        }

        if (salesPersonUserRoleId) {
            dealMatch['sales_persons'] = new mongoose.Types.ObjectId(salesPersonUserRoleId)
        }

        const dealPipeline = [
            {
                $match: dealMatch
            },
            {
                $project: {
                    deal_id: 1,
                    deal_type: 1,
                    deal_name: 1,
                    secondary_deal_name: 1,
                    deal_from_date: 1,
                    deal_to_date: 1,
                }
            }
        ]

        const pipeline = [
            searchStage,
        ]

        let variantProductPipeline = []

        if (isProductSplitting) {
            const variantProductFilter = {
                "is_deleted": false,
                "is_active": true,
                "price_mappings": {
                    "$elemMatch": {
                        "master_price_id": new mongoose.Types.ObjectId(priceListId),
                        "price": { "$gt": 0 }
                    }
                },
                "group_value_id": {
                    "$ne": null,
                },
            }

            if (hideOutOfStock && branchId) {
                variantProductFilter["inventory_mappings"] = {
                    "$elemMatch": {
                        "branch_id": branchId,
                        "quantity": { "$gt": 0 }
                    }
                }
            }

            variantProductPipeline = [
                {
                    "$lookup": {
                        "from": "products_2.0",
                        "localField": "_id",
                        "foreignField": "parent_id",
                        "as": "variant_products",
                        "pipeline": [
                            {
                                "$match": variantProductFilter,
                            },
                            {
                                "$project": {
                                    "price_mappings": {
                                        "$filter": {
                                            "input": "$price_mappings",
                                            "as": "item",
                                            "cond": {
                                                "$eq": ["$$item.master_price_id", new mongoose.Types.ObjectId(priceListId)]
                                            }
                                        }
                                    },
                                    // "inventory_mappings": 1,
                                    "parent_id": 1,
                                    "variant_value_id": 1,
                                    "group_value_id": 1,
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "variant_types_2.0",
                                    "localField": "group_value_id",
                                    "foreignField": "_id",
                                    "as": "group_value_id",
                                    "pipeline": [
                                        {
                                            "$project": {
                                                "name": 1,
                                                "product_id": 1,
                                            }
                                        },
                                        {
                                            "$lookup": {
                                                "from": "images_2.0",
                                                "localField": "product_id",
                                                "foreignField": "product_variant_id",
                                                "as": "group_cover_image",
                                                "let": {
                                                    "groupId": "$_id"
                                                },
                                                "pipeline": [
                                                    {
                                                        "$match": {
                                                            "$expr": {
                                                                "$eq": ["$group_id", "$$groupId"],
                                                            },
                                                            "group_id": { "$ne": null },
                                                        },
                                                    },
                                                    {
                                                        "$project": {
                                                            "image_name": 1,
                                                            "image_number": 1,
                                                            'product_variant_id': 1,
                                                            "s3_url": 1,
                                                            "updated_at": 1,
                                                        },
                                                    },
                                                    {
                                                        "$sort": { "image_number": 1 },
                                                    },
                                                    {
                                                        "$limit": 1,
                                                    },
                                                ],
                                            },
                                        },
                                        {
                                            "$project": {
                                                "name": 1,
                                                "group_cover_image": {
                                                    "$first": "$group_cover_image"
                                                }
                                            }
                                        },
                                    ]
                                }
                            },
                            {
                                "$addFields": {
                                    "group_value_id": {
                                        "$first": "$group_value_id"
                                    }
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "images_2.0",
                                    "localField": "_id",
                                    "foreignField": "product_variant_id",
                                    "as": "variant_cover_image",
                                    "pipeline": [
                                        {
                                            "$match": {
                                                "group_id": null
                                            },
                                        },
                                        {
                                            "$project": {
                                                "image_name": 1,
                                                "image_number": 1,
                                                'product_variant_id': 1,
                                                "s3_url": 1,
                                                "updated_at": 1,
                                            },
                                        },
                                        {
                                            "$sort": { "image_number": 1 },
                                        },
                                        {
                                            "$limit": 1,
                                        },
                                    ],
                                },
                            },
                            {
                                "$addFields": {
                                    "variant_cover_image": {
                                        "$first": "$variant_cover_image"
                                    }
                                }
                            }
                        ]
                    }
                },
            ]
        }

        if (searchType === PRODUCT_SEARCH_TYPES.FILTER) {
            const {
                productType = [],
            } = filters

            productType.forEach(type => {
                if (type === PRODUCT_FILTER_TYPES.NEW_PRODUCTS) {
                    pipeline.push({
                        "$sort": {
                            "created_at": -1
                        }
                    })
                }
                else if (type === PRODUCT_FILTER_TYPES.RESTOCKED_PRODUCTS) {
                    pipeline.push({
                        "$sort": {
                            "restocked_at": -1
                        }
                    })
                }
            })
        }

        pipeline.push(
            {
                "$facet": {
                    "list": [
                        {
                            "$skip": offset
                        },
                        {
                            "$limit": perPage
                        },
                    ],
                    "count": [
                        {
                            "$count": "count"
                        }
                    ]
                }
            },
            {
                "$unwind": "$list"
            },
            {
                "$lookup": {
                    "from": "products_2.0",
                    "localField": "list._id",
                    "foreignField": "_id",
                    "as": "product",
                    "pipeline": [
                        {
                            $lookup: {
                                from: "deal_products",
                                localField: "_id",
                                foreignField: "product_id",
                                as: "product_deal_info",
                                pipeline: [
                                    {
                                        $match: dealLookUpMatchObj
                                    },
                                    {
                                        $lookup: {
                                            from: "deals",
                                            localField: "deal_id",
                                            foreignField: "_id",
                                            as: "deal_id",
                                            pipeline: dealPipeline
                                        }
                                    },
                                    {
                                        $addFields: {
                                            "deal_id": { $first: "$deal_id" },
                                        }
                                    },
                                    {
                                        $match: { deal_id: { $ne: null } }
                                    },
                                    {
                                        $project: {
                                            deal_id: 1,
                                            discount_type: 1,
                                            percent: 1,
                                            amount: 1,
                                            discounted_price: 1,
                                            first_tier: 1,
                                            second_tier: 1,
                                            third_tier: 1,
                                            buy_product: 1,
                                            free_product: 1,
                                            deal_from_date: 1,
                                            deal_to_date: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "deal_products",
                                localField: "_id",
                                foreignField: "parent_id",
                                as: "parent_deals",
                                pipeline: [
                                    {
                                        $match: dealLookUpMatchObj
                                    },
                                    {
                                        $lookup: {
                                            from: "deals",
                                            localField: "deal_id",
                                            foreignField: "_id",
                                            as: "deal_id",
                                            pipeline: dealPipeline
                                        }
                                    },
                                    {
                                        $addFields: {
                                            "deal_id": { $first: "$deal_id" },
                                        }
                                    },
                                    {
                                        $match: { deal_id: { $ne: null } }
                                    },
                                    {
                                        $project: {
                                            deal_id: 1,
                                            discount_type: 1,
                                            percent: 1,
                                            amount: 1,
                                            discounted_price: 1,
                                            first_tier: 1,
                                            second_tier: 1,
                                            third_tier: 1,
                                            buy_product: 1,
                                            free_product: 1,
                                            deal_from_date: 1,
                                            deal_to_date: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "$addFields": {
                                "product_deal_info": {
                                    "$first": "$product_deal_info",
                                },
                                "parent_deal_size": {
                                    "$size": "$parent_deals"
                                },
                                "parent_deals": {
                                    "$first": "$parent_deals",
                                },
                            }
                        },
                        {
                            "$project": {
                                "is_active": 1,
                                "is_deleted": 1,
                                "title": 1,
                                "secondary_language_title": 1,
                                "tenant_id": 1,
                                "item_number": 1,
                                "active_variant_item_numbers": 1,
                                "price_mappings": 1,
                                "inventory_mappings": 1,
                                "variant_count": 1,
                                "variants": 1,
                                "group_value_id": 1,
                                "type": 1,
                                "created_at": 1,
                                "brand_id": 1,
                                "tags": 1,
                                "product_deal_info": 1,
                                "variant_deal_info": {
                                    $cond: [{ $eq: ['$parent_deal_size', 1] }, '$parent_deals', null]
                                },
                                "product_deal_info": 1,
                                "multipleDeal": {
                                    $cond: [{ $gt: ['$parent_deal_size', 1] }, true, false
                                    ]
                                },
                            }
                        },
                        ...variantProductPipeline,
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "images_2.0",
                    "localField": "list._id",
                    "foreignField": "product_variant_id",
                    "as": "cover_image",
                    "pipeline": [
                        {
                            $match: {
                                group_id: null
                            }
                        },
                        {
                            "$project": {
                                "image_name": 1,
                                "s3_url": 1,
                                "image_number": 1,
                                "updated_at": 1
                            }
                        },
                        {
                            "$sort": {
                                "image_number": 1
                            }
                        },
                        {
                            "$limit": 1
                        }
                    ]
                }
            },
            {
                "$addFields": {
                    "product": {
                        "$first": "$product",
                    },
                    "count": {
                        "$first": "$count",
                    },
                }
            },
            {
                '$lookup': {
                    'from': 'favorite_products_2.0',
                    'localField': 'product._id',
                    'foreignField': 'product_variant_id',
                    'as': 'isFavorite',
                    'pipeline': [
                        {
                            '$match': {
                                'user_role_id': new mongoose.Types.ObjectId(userRoleId),
                                'tenant_id': tenantId
                            }
                        }
                    ]
                }
            },
            {
                "$addFields": {
                    "product.cover_image": {
                        "$first": "$cover_image",
                    },
                    'product.is_favorite': {
                        "$toBool": {
                            "$size": '$isFavorite'
                        }
                    }
                }
            },
            {
                "$unwind": "$count"
            }
        )

        const data = await ProductSchema.aggregate(pipeline)
        const list = data.map(d => d.product)
        const count = data.length ? data[0].count.count : 0

        return {
            list,
            count
        }
    }

    async getProductsWithPopulation(filter, projection, options, populationObjects = []) {
        return ProductSchema.find(filter, projection, options);
    }

    deleteS3ImageList = (tenantId, imageDocs, publicS3) => {
        const deleteObjects = []
        const productImagePath = Object.values(FILE_PATH.PRODUCT_IMAGE)

        imageDocs.forEach(image => {
            productImagePath.forEach(path => {
                deleteObjects.push({
                    "Key": `${path}/${tenantId}/${image.image_name}`
                })
            })
        })

        if (deleteObjects.length) {
            return publicS3.deleteFiles(deleteObjects)
        }
    }

    deleteS3Images = async (tenantId, imageDocs = [], imgPromisesArr, publicS3) => {
        if (!Array.isArray(imgPromisesArr)) {
            throw new Error("Provide promise array as reference");
        }

        if (!(publicS3 instanceof FileUpload)) {
            throw new Error("Provide public s3 object reference");
        }

        if (imageDocs.length) {
            imgPromisesArr.push(
                this.deleteS3ImageList(tenantId, imageDocs, publicS3)
            )
        }
    }

    async generateMappingForParent(product, existingAllVariants) {
        if (!product instanceof mongoose.Model || (product.type !== NEW_PRODUCT_TYPE.PARENT)) {

            const error = new Error("invalid product parameter found");
            error.name = "invalid_product_parameter";
            throw error;
        }

        const productMasterPriceMap = {};
        const variantInventoryMap = new Map();

        const activeVariantItemNumbers = [];
        const inactiveVariantItemNumbers = [];

        const activeVariantBarcodes = []
        const inactiveVariantBarcodes = []

        let isRestocked = false
        let allVariantProducts = []

        if (Array.isArray(existingAllVariants)) {
            allVariantProducts = existingAllVariants
        }
        else {
            allVariantProducts = await this.findProducts(
                {
                    "parent_id": product._id,
                    "is_deleted": false,
                },
                {
                    "price_mappings": 1,
                    "inventory_mappings": 1,
                    "item_number": 1,
                    "is_active": 1,
                    "is_restocked": 1,
                    "barcodes": 1
                }
            )
        }

        if (allVariantProducts.length) {
            allVariantProducts.forEach(variantProd => {
                if (variantProd.is_active === true) {
                    if (variantProd.is_restocked === true) {
                        isRestocked = true
                    }

                    activeVariantItemNumbers.push(variantProd.item_number)
                    activeVariantBarcodes.push(...variantProd.barcodes)

                    for (let p = 0; p < variantProd.price_mappings?.length; p++) {
                        let varPriceMap = variantProd.price_mappings[p];
                        varPriceMap["product_variant_id"] = variantProd._id; // add the variant info for price-mapping

                        if (
                            varPriceMap.price > 0 && (
                                (!productMasterPriceMap[varPriceMap.master_price_id]) ||
                                (productMasterPriceMap[varPriceMap.master_price_id].price > varPriceMap.price)
                            )
                        ) {
                            productMasterPriceMap[varPriceMap.master_price_id] = varPriceMap;
                        }
                    }

                    // generating inventory mapping object for parent
                    for (let i = 0; i < variantProd.inventory_mappings.length; i++) {
                        const iMap = variantProd.inventory_mappings[i];

                        if (variantInventoryMap.has(iMap.branch_id)) {
                            const inventoryObj = variantInventoryMap.get(iMap.branch_id);
                            inventoryObj.quantity += iMap.quantity;
                            variantInventoryMap.set(iMap.branch_id, inventoryObj);
                        }
                        else {
                            variantInventoryMap.set(iMap.branch_id, iMap);
                        }
                    }
                }
                else if (variantProd.is_active === false) {
                    inactiveVariantItemNumbers.push(variantProd.item_number)
                    inactiveVariantBarcodes.push(...variantProd.barcodes)
                }
            })
        }

        product.active_variant_barcodes = activeVariantBarcodes
        product.inactive_variant_barcodes = inactiveVariantBarcodes
        product.price_mappings = Object.values(productMasterPriceMap)
        product.inventory_mappings = Array.from(variantInventoryMap.values());

        product.active_variant_item_numbers = activeVariantItemNumbers;
        product.inactive_variant_item_numbers = inactiveVariantItemNumbers;
        product.variant_count = allVariantProducts.length || 0

        if (
            !product.is_restocked &&
            isRestocked
        ) {
            product.restocked_at = new Date()
        }
        product.is_restocked = isRestocked
    }

    async countProductOfTenant(tenantId, statusType) {
        const searchFilters = [
            {
                "range": {
                    "path": "tenant_id",
                    "gte": tenantId,
                    "lte": tenantId,
                }
            },
            {
                "equals": {
                    "path": "is_deleted",
                    "value": false,
                }
            },
            {
                "queryString": {
                    "defaultPath": "type",
                    "query": `${NEW_PRODUCT_TYPE.SINGLE} OR ${NEW_PRODUCT_TYPE.PARENT}`
                }
            }
        ];

        if (statusType) {
            searchFilters.push({
                "equals": {
                    "path": "is_active",
                    "value": statusType === ACTIVE_STATUS_ENUM.ACTIVE ? true : false,
                }
            })
        }

        const searchStage = {
            "$search": {
                "index": "product_v2_search",
                "returnStoredSource": false,
                "compound": {
                    "filter": searchFilters,
                },
            },
        }

        const pipeline = [
            searchStage,
            {
                $count: "count"
            }
        ];

        const data = await this.productAggregation(pipeline);

        return data[0]?.count || 0;
    }

    async getTenantInfo(_id, projection, options, populate, req) {
        const params = {
            _id,
            projection,
            options,
            populate
        }
        // const url = VALUES.internalServiceBaseURL + "tenant"
        const details = await httpService(req).get("tenant", { params })
        // const authResponse = await axios({
        //     url,
        //     params,
        // })
        // const details = authResponse?.["data"]?.["data"]
        return details.data.data
    }

    async upsertFavoriteProduct(user_role_id, product_variant_id, tenant_id) {
        if (!tenant_id || !product_variant_id || !user_role_id) {
            throw new Error("please provide all parameters to upsertFavoriteProduct");
        }
        return FavoriteProductSchema.updateOne({ user_role_id, product_variant_id, tenant_id }, { user_role_id, product_variant_id, tenant_id }, { upsert: true, runValidators: true });
    }

    async findFavoriteProducts(filter, projection, options) {
        return FavoriteProductSchema.find(filter, projection, options);
    }

    async findFavoriteProduct(filter, projection, options) {
        return FavoriteProductSchema.findOne(filter, projection, options);
    }

    getFavoriteProductCount(filter) {
        return FavoriteProductSchema.countDocuments(filter);
    }

    async aggregateFavoriteProduct(pipeline) {
        return FavoriteProductSchema.aggregate(pipeline);
    }

    async deleteFavoriteProducts(filter, options) {
        return FavoriteProductSchema.deleteMany(filter, options);
    }


    async MigrationAddVariantsAndGroups(tenant_id, variants, groups, product_id, options) {
        let varList = [];
        const variantTypes = [];
        const groupTypes = [];
        for (let i = 0; i < variants?.values?.length; i++) {
            const vr = variants?.values[i];
            variantTypes.push({
                _id: vr._id,
                name: vr.name,
                product_id,
                type: PRODUCT_VARIANT_TYPES.VARIANT_VALUE,
                tenant_id,
                is_deleted: false,
                order: i + 1
            });
        }

        for (let i = 0; i < groups?.values?.length; i++) {
            const gr = groups?.values[i];
            groupTypes.push({
                _id: gr._id,
                name: gr.name,
                product_id,
                type: PRODUCT_VARIANT_TYPES.GROUP_VALUE,
                tenant_id,
                is_deleted: false,
                order: i + 1
            })
        }
        varList = [...variantTypes, ...groupTypes];
        await VariantTypesSchema.create(varList, options);
        return [variantTypes, groupTypes];

    }
};

module.exports = ProductModel;

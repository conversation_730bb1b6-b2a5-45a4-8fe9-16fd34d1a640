const CartItemSchema = require("../Database/Schemas/order/cart_items");

const DraftSchema = require("../Database/Schemas/order/drafts");
const DraftItemSchema = require("../Database/Schemas/order/draft_items");
const DraftCounterSchema = require("../Database/Schemas/order/draft_counter");
const DealProductSchema = require("../Database/Schemas/deal/deal_products");

const OrderSchema = require("../Database/Schemas/order/OrderSchema")
const OrderItemSchema = require("../Database/Schemas/order/OrderItemSchema")
const OrderReportSchema = require("../Database/Schemas/order/OrderReportSchema");

const DealModel = new (require("./DealModel"))();
const ConfigurationModel = new (require('./ConfigurationModel'))();

const OrderSalesStatSchema = require("../Database/Schemas/order/order_sales_statistics");
const SaleStatisticSchema = require("../Database/Schemas/order/sales_statistics_per_day");

const {
    roundOf,
    excludeProjections
} = require("../Utils/helpers");

const {
    sendDealStatMessageToSQS
} = require("../SQS/DealStatisticsQueue");

const {
    VALUES,
    DEAL_TYPE,
    ENTITY_STATUS,
    DEAL_STATUS,
    DISCOUNT_TYPE,
    DEAL_STATISTICS_EVENT_TYPE,
    ORDER_STATUS_TYPES,
    ORDER_ITEM_LISTING_TYPES,
} = require("../Configs/constants");

class OrderModel {

    async findCartItems(filter, projection, options) {
        return CartItemSchema.find(filter, projection, options);
    }

    async aggregateCartItems(pipeline) {
        return CartItemSchema.aggregate(pipeline)
    }

    async findCartItem(filter, projection, options) {
        return CartItemSchema.findOne(filter, projection, options);
    }

    async upsertCartItem(filter, updateObject, options) {
        return CartItemSchema.findOneAndUpdate(filter, updateObject, options);
    }

    async createCartItems(items, options) {
        return CartItemSchema.create(items, options)
    }

    async deleteCartItems(filter, options) {
        return CartItemSchema.deleteMany(filter, options);
    }

    async deleteDealProductItems(filter, options) {
        return DealProductSchema.deleteMany(filter, options);
    }

    async cartItemCount(filter, options) {
        return CartItemSchema.countDocuments(filter, options)
    }

    async createDraft(object) {
        return new DraftSchema(object);
    }

    async aggregateDraft(pipeline) {
        return DraftSchema.aggregate(pipeline)
    }

    async findDrafts(filter, projection, options) {
        return DraftSchema.find(filter, projection, options);
    }

    async findDraft(filter, projection, options) {
        return DraftSchema.findOne(filter, projection, options);
    }

    /**
     *
     * @param {Object} counterObject need to maintain _id manually
     * @returns {DraftCounterSchema}
     */
    async addDraftCounter(counterObject = {}) {
        return new DraftCounterSchema(counterObject);
    }

    async deleteDrafts(filter, options) {
        return DraftSchema.deleteMany(filter, options);
    }

    async createDraftItem(object) {
        return new DraftItemSchema(object);
    }

    async findDraftItems(filter, projection, options) {
        return DraftItemSchema.find(filter, projection, options);
    }

    async deleteDraftItems(filter, options) {
        return DraftItemSchema.deleteMany(filter, options);
    }

    async getDraftCounter(filter, projection, options) {
        return DraftCounterSchema.findOne(filter, projection, options);
    }

    async addDraftCounter(object = {}) {
        return new DraftCounterSchema(object);
    }
    async addOrder(orderObject = {}) {
        return new OrderSchema(orderObject)
    }

    async upsertOrder(filter, object, options) {
        return OrderSchema.updateOne(filter, object, { ...options, upsert: true });
    }

    async updateOrder(filter, object, options) {
        return OrderSchema.updateOne(filter, object, options);
    }

    async addOrderItem(orderItemObject = {}) {
        return new OrderItemSchema(orderItemObject)
    }

    async addOrderItems(orderItemObjects) {
        return OrderItemSchema.create(orderItemObjects)
    }

    async aggregateOrderItems(pipeline) {
        return OrderItemSchema.aggregate(pipeline);
    }

    async upsertOrderItem(filter, object, options) {
        return OrderItemSchema.updateOne(filter, object, { ...options, upsert: true, setDefaultsOnInsert: false });
    }

    updateOrderItems = (filter, update, options) => {
        return OrderItemSchema.updateMany(filter, update, options)
    }

    async aggregateOrders(pipeline = []) {
        return OrderSchema.aggregate(pipeline)
    }

    async countOrders(filter) {
        return OrderSchema.countDocuments(filter);
    }

    async findOrder(filter, projection = "", option = {}) {
        return OrderSchema.findOne(filter, projection, option)
    }

    async findOrders(filter, projection = "", option = {}) {
        return OrderSchema.find(filter, projection, option)
    }

    async findOrderItems(filter, projection = "", option = {}) {
        return OrderItemSchema.find(filter, projection, option)
    }

    async updateOrderStatistic(filter, updateObject = {}, options = { upsert: false }) {
        return OrderSalesStatSchema.updateOne(filter, updateObject, options);
    }

    async getSalesStatistic(filter = {}, projection = {}, options = {}) {
        return OrderSalesStatSchema.findOne(filter, projection, options);
    }

    async getSalesStatistics(filter = {}, projection = {}, options = {}) {
        return OrderSalesStatSchema.find(filter, projection, options);
    }

    async salesStatistics(branchId, date, salesType) {
        const pipeline = [
            {
                '$match': {
                    'branch_id': new mongoose.Types.ObjectId(branchId),
                    'date': { $gte: date }
                }
            }, {
                '$group': {
                    '_id': salesType ? '$sales_user_role_id' : '$branch_id',
                    'total_amount': {
                        '$sum': {
                            '$add': [
                                '$day_sales_amount', '$day_sales_tax'
                            ]
                        }
                    }
                }
            }
        ]
        return SaleStatisticSchema.aggregate(pipeline)
    }

    getOrder = async (req) => {
        const {
            tenantId,
            orderId,
            perPage = 10,
            page = 1,
            searchKey,
            orderItemListingType,
            orderItemProjection,
        } = req.query

        let listOptions = [
            {
                '$skip': perPage * (page - 1)
            },
            {
                '$limit': perPage
            }
        ]

        if (orderItemListingType === ORDER_ITEM_LISTING_TYPES.ALL) {
            listOptions = []
        }
        const orderItemsKey = "order_items"

        const pipeline = [
            {
                '$match': {
                    '_id': new mongoose.Types.ObjectId(orderId),
                    'tenant_id': tenantId
                }
            },
            {
                '$lookup': {
                    'from': orderItemsKey,
                    'localField': '_id',
                    'foreignField': 'order_id',
                    'as': orderItemsKey,
                    'pipeline': [
                        {
                            '$sort': {
                                'sort_order': 1,
                            }
                        }
                    ],
                }
            },
            {
                '$unwind': {
                    'path': `$${orderItemsKey}`,
                }
            }
        ];

        if (orderItemProjection) {
            const project = orderItemProjection
                .reduce((accumulator, currentValue) => {
                    return {
                        ...accumulator,
                        [`${orderItemsKey}.${currentValue}`]: 1,
                    }
                },
                    {}
                )

            pipeline.push({ "$project": project })
        }

        if (searchKey) {
            pipeline.push({
                $match: {
                    $or: [
                        { [`${orderItemsKey}.product_name`]: { $regex: searchKey, $options: "i" } },
                        { [`${orderItemsKey}.variant_name`]: { $regex: searchKey, $options: "i" } },
                        { [`${orderItemsKey}.item_number`]: { $regex: searchKey, $options: "i" } },
                        { [`${orderItemsKey}.parent_item_number`]: { $regex: searchKey, $options: "i" } },
                    ]
                }
            });
        }

        pipeline.push(
            {
                '$facet': {
                    'item': listOptions,
                    'count': [{ '$count': 'count' }]
                }
            },
            {
                '$unwind': {
                    'path': '$item',
                }
            },
            {
                '$lookup': {
                    'from': 'images_2.0',
                    'localField': `item.${orderItemsKey}.product_variant_id`,
                    'foreignField': 'product_variant_id',
                    'as': 'item.cover_image',
                    'pipeline': [
                        {
                            '$match': {
                                'group_id': null,
                            }
                        }, {
                            '$project': {
                                'image_name': 1,
                                's3_url': 1,
                                'image_number': 1,
                                'product_variant_id': 1,
                                'updated_at': 1,
                            }
                        }, {
                            '$sort': {
                                'image_number': 1
                            }
                        }, {
                            '$limit': 1
                        }
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "products_2.0",
                    "localField": `item.${orderItemsKey}.product_variant_id`,
                    "foreignField": "_id",
                    "as": "product",
                    "pipeline": [
                        {
                            "$project": {
                                "parent_id": 1,
                                "group_value_id": 1,
                            },
                        },
                    ],
                },
            },
            {
                "$addFields": {
                    "product": {
                        "$first": "$product",
                    },
                },
            },
            {
                "$lookup": {
                    "from": "images_2.0",
                    "localField": "product.parent_id",
                    "foreignField": "product_variant_id",
                    "as": "item.group_cover_image",
                    "let": {
                        "groupId": "$product.group_value_id"
                    },
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$eq": ["$group_id", "$$groupId"],
                                },
                                "group_id": { "$ne": null },
                            },
                        },
                        {
                            "$project": {
                                "image_name": 1,
                                "image_number": 1,
                                'product_variant_id': 1,
                                "s3_url": 1,
                                "updated_at": 1,
                            },
                        },
                        {
                            "$sort": { "image_number": 1 },
                        },
                        {
                            "$limit": 1,
                        },
                    ],
                },
            },
            {
                "$lookup": {
                    "from": "images_2.0",
                    "localField": "product.parent_id",
                    "foreignField": "product_variant_id",
                    "as": "item.parent_cover_image",
                    "pipeline": [
                        {
                            "$match": {
                                "group_id": null
                            }
                        },
                        {
                            "$project": {
                                "image_name": 1,
                                "image_number": 1,
                                'product_variant_id': 1,
                                "s3_url": 1,
                                "updated_at": 1,
                            },
                        },
                        {
                            "$sort": { "image_number": 1 },
                        },
                        {
                            "$limit": 1,
                        },
                    ],
                },
            },
            {
                "$addFields": {
                    'item.cover_image': {
                        '$first': '$item.cover_image'
                    },
                    "item.group_cover_image": {
                        "$first": "$item.group_cover_image"
                    },
                    "item.parent_cover_image": {
                        "$first": "$item.parent_cover_image"
                    },
                    'count': { '$first': '$count.count' },
                    [`item.${orderItemsKey}.parent_id`]: "$product.parent_id",
                }
            },
            {
                '$project': {
                    'count': 1,
                    'item._id': 1,
                    [`item.${orderItemsKey}`]: 1,
                    'item.cover_image': 1,
                    'item.group_cover_image': 1,
                    'item.parent_cover_image': 1,
                }
            }
        )

        let list = [];
        const data = await OrderSchema.aggregate(pipeline);

        if (!data.length) {
            list = []
        }
        else if (data.length === 1) {
            if (data[0].item.hasOwnProperty("_id")) {
                list = data.map(d => d.item);
            }
            else {
                list = [];
            }
        }
        else {
            list = data.map(d => d.item);
        }

        const count =
            data.length && (data[0].count !== undefined)
                ? data[0].count
                : 0;

        return { list, count };
    }

    async updateOrderStatus(orderIds, orderStatus, headers) {
        const update = {
            "$push": {
                "order_status_track": {
                    "order_status": orderStatus,
                    "time": new Date(),
                    "user_info": {
                        "user_role_id": headers.userroleid,
                        "name": `${headers.userDetails.first_name} ${headers.userDetails.last_name}`,
                    },
                }
            },
            "order_status": orderStatus,
            "updated_by": headers.userDetails._id
        }

        if (orderStatus === ORDER_STATUS_TYPES.RELEASED) {
            update.order_hold_reason_id = undefined;
            update.whatsapp_message_sent = undefined;
        }

        return OrderSchema.updateMany(
            {
                "_id": { "$in": orderIds }
            },
            update
        )
    }

    async addOrderReport(data) {
        return OrderReportSchema({ ...data })
    }

    async updateBulkOrders(filter, updateFields, headers) {
        let $set = updateFields
        if (headers.userDetails?._id) {
            $set["updated_by"] = headers.userDetails._id
        }

        return OrderSchema.updateMany(
            filter,
            {
                $set
            }
        )
    }

    /**
     * for creating tax_calculation object
     * @param {Number} productPrice // this can not be zero
     * @param {{ taxDetail: {}, groupTax: [] }} taxInfo
     * @returns {{
    *  calculatedTax: Number,
    *  tax_details: Object
    * }}
    */
    async calculateTaxFromBasePrice(productPrice, taxInfo, priceType) {

        const taxCalculation = {
            calculatedTax: 0,
            tax_details: {
                tax_id: "",
                type: "", // GROUP, SINGLE
                tax_name: "",
                tax_rate: 0, // !GROUP
                tax_calculation: "", // PERCENTAGE, FLAT_VALUE // !GROUP
                calculated_tax: 0,
                price: priceType,
                group_taxes: new Map(),
                // {
                //     "tax_object_id": {
                //         tax_id: "",
                //         tax_name: "",
                //         tax_rate: 0,
                //         tax_calculation: "", // PERCENTAGE, FLAT_VALUE
                //         calculated_tax: 0,
                //     }
                // }
            }
        }
        if (!taxInfo?.taxDetail) {
            delete taxCalculation.tax_details;
            return taxCalculation;
        }

        taxCalculation.tax_details.tax_id = taxInfo.taxDetail._id;
        taxCalculation.tax_details.type = taxInfo.taxDetail.type;
        taxCalculation.tax_details.tax_name = taxInfo.taxDetail.tax_name;

        switch (taxInfo.taxDetail.type) {
            case VALUES.TAX_TYPE.GROUP:
                {
                    let calculatedTax = 0;
                    // const groupTaxMap = new Map();
                    if (Array.isArray(taxInfo.groupTax)) {
                        taxInfo.groupTax.forEach(gt => {
                            const taxRate = Number(gt.tax_rate);
                            let taxValue = 0;
                            if (gt.tax_calculation === VALUES.TAX_CALCULATION.PERCENTAGE) {
                                const taxPercentValue = taxRate / 100;
                                taxValue =
                                    VALUES.MASTER_PRICE.EXCLUDE === priceType ?
                                        ((productPrice * taxPercentValue)) :
                                        (productPrice * (taxPercentValue / (1 + taxPercentValue)));
                                calculatedTax += taxValue
                            } else if (gt.tax_calculation === VALUES.TAX_CALCULATION.FLAT_VALUE) {
                                taxValue = taxRate;
                                calculatedTax += taxValue
                            }
                            const groupTax = {
                                tax_id: gt._id,
                                tax_name: gt.tax_name,
                                tax_rate: taxRate,
                                tax_calculation: gt.tax_calculation, // PERCENTAGE, FLAT_VALUE
                                calculated_tax: roundOf(taxValue),
                            };

                            taxCalculation.tax_details.group_taxes.set(groupTax.tax_id.toString(), groupTax);
                        });
                    }

                    taxCalculation.calculatedTax = calculatedTax;
                    taxCalculation.tax_details.calculated_tax = roundOf(taxCalculation.calculatedTax);

                    delete taxCalculation.tax_details.tax_rate;
                    delete taxCalculation.tax_details.tax_calculation;

                    break;
                }
            case VALUES.TAX_TYPE.SINGLE:
                {
                    const taxDetail = taxInfo.taxDetail;
                    const taxRate = Number(taxDetail.tax_rate);
                    if (taxDetail.tax_calculation === VALUES.TAX_CALCULATION.PERCENTAGE) {
                        const taxPercentValue = taxRate / 100;
                        taxCalculation.calculatedTax +=
                            VALUES.MASTER_PRICE.EXCLUDE === priceType ?
                                ((productPrice * taxPercentValue)) :
                                (productPrice * (taxPercentValue / (1 + taxPercentValue)));
                    } else if (taxDetail.tax_calculation === VALUES.TAX_CALCULATION.FLAT_VALUE) {
                        taxCalculation.calculatedTax = taxRate;
                    }

                    taxCalculation.tax_details.tax_calculation = taxDetail.tax_calculation;
                    taxCalculation.tax_details.calculated_tax = roundOf(taxCalculation.calculatedTax);
                    taxCalculation.tax_details.tax_rate = taxRate;

                    delete taxCalculation.tax_details.group_taxes
                    break;
                }
        }
        return taxCalculation;
    }

    /**
     *
     * @param {Object} item CartItem, draftItem
     * @param {Object} tax_details Return type of calculateTaxFromBasePrice
     * @param {Map} taxCalculationMap this parameter will be passed as reference, and update the information of the map after calculating the map
     * @param {Number} quantity CartItem, draftItem
     */
    updateTaxCalculationMap(tax_details, taxCalculationMap, quantity) {

        if (tax_details && tax_details?.tax_id && quantity) {
            if (!(taxCalculationMap instanceof Map)) {
                throw new Error("taxCalculationMap needs to be instance of Map");
            }
            const taxId = tax_details.tax_id.toString();

            switch (tax_details?.type) {
                case VALUES.TAX_TYPE.SINGLE:
                    {
                        if (taxCalculationMap.has(taxId)) {
                            const existingTaxInfo = taxCalculationMap.get(taxId);
                            existingTaxInfo.calculated_tax = roundOf((existingTaxInfo.calculated_tax) + (tax_details.calculated_tax * quantity));
                            taxCalculationMap.set(taxId, existingTaxInfo);
                        } else {
                            tax_details.calculated_tax = roundOf(tax_details.calculated_tax * quantity);
                            taxCalculationMap.set(taxId, tax_details);
                        }
                        break;
                    }

                case VALUES.TAX_TYPE.GROUP:
                    {
                        if (taxCalculationMap.has(taxId)) {
                            const existingTaxInfo = taxCalculationMap.get(taxId);
                            existingTaxInfo.calculated_tax = roundOf((existingTaxInfo.calculated_tax) + (tax_details.calculated_tax * quantity));

                            const group_taxes = tax_details.group_taxes;
                            if ((group_taxes instanceof Map) && (existingTaxInfo.group_taxes instanceof Map)) {
                                for (const txEntrySet of group_taxes.entries()) {
                                    const gtTaxId = txEntrySet[0].toString();
                                    const gtTaxInfo = txEntrySet[1];
                                    if (existingTaxInfo.group_taxes.has(gtTaxId)) {
                                        const existTaxObj = existingTaxInfo.group_taxes.get(gtTaxId);
                                        existTaxObj.calculated_tax += (gtTaxInfo.calculated_tax * quantity);
                                        existTaxObj.calculated_tax = roundOf(existTaxObj.calculated_tax);
                                        existingTaxInfo.group_taxes.set(gtTaxId, existTaxObj);
                                    } else {
                                        gtTaxInfo.calculated_tax = roundOf(gtTaxInfo.calculated_tax * quantity);
                                        existingTaxInfo.group_taxes.set(gtTaxId, gtTaxInfo);
                                    }
                                }
                                taxCalculationMap.set(taxId, existingTaxInfo);
                            }
                        } else {
                            tax_details.calculated_tax = roundOf(tax_details.calculated_tax * quantity);

                            const group_taxes = tax_details.group_taxes;
                            if ((group_taxes instanceof Map)) {
                                for (const txEntrySet of group_taxes.entries()) {
                                    const gtTaxId = txEntrySet[0].toString();
                                    const gtTaxInfo = txEntrySet[1];
                                    gtTaxInfo.calculated_tax = roundOf((gtTaxInfo.calculated_tax * quantity));
                                    tax_details.group_taxes.set(gtTaxId, gtTaxInfo);
                                }
                                taxCalculationMap.set(taxId, tax_details);
                            }
                        }
                        break;
                    }
            }
        }

    }

    async generateTaxDetailsForCart(productDetails, dealPrice = 0, original_price) {
        let taxInfo = { taxDetail: null, groupTax: [] };
        let taxCalculations = {
            basePrice: 0,
            calculatedTax: 0
        };
        let taxValues = {
            calculatedTax: 0,
            tax_details: undefined
        }

        const taxSettings = await ConfigurationModel.masterTaxInfo(productDetails.tenant_id, { enable_tax: 1, price: 1, default_tax: 1, universal_tax: 1 });
        if (!taxSettings) {
            return { taxInfo, taxCalculations, taxValues };
        }

        if (taxSettings.universal_tax && taxSettings.default_tax) {
            taxInfo["taxDetail"] = await ConfigurationModel.taxById(taxSettings.default_tax);
            if (taxInfo["taxDetail"]?.type === VALUES.TAX_TYPE.GROUP) {
                taxInfo["groupTax"] = await ConfigurationModel.taxList(productDetails.tenant_id, ENTITY_STATUS.ALL, VALUES.TAX_TYPE.GROUP, taxSettings.default_tax);
            }
        }

        let taxId = productDetails.tax_id;
        taxCalculations.basePrice = dealPrice || original_price;

        if (taxSettings.universal_tax) {
            taxValues = await this.calculateTaxFromBasePrice(taxCalculations.basePrice, taxInfo, taxSettings.price);
        } else {
            if (taxId) {
                taxInfo["taxDetail"] = await ConfigurationModel.taxById(taxId);
                if (taxInfo["taxDetail"]?.type === VALUES.TAX_TYPE.GROUP) {
                    taxInfo["groupTax"] = await ConfigurationModel.taxList(productDetails.tenant_id, ENTITY_STATUS.ALL, VALUES.TAX_TYPE.GROUP, taxId)
                }
            }
            taxValues = await this.calculateTaxFromBasePrice(taxCalculations.basePrice, taxInfo, taxSettings.price);
        }

        if (taxSettings.price === VALUES.MASTER_PRICE.INCLUDE) {
            taxCalculations.basePrice = taxCalculations.basePrice - taxValues?.calculatedTax || 0;
        }
        taxCalculations.basePrice = roundOf(taxCalculations.basePrice, 3);
        taxCalculations.calculatedTax = roundOf(taxValues?.calculatedTax || 0, 3);

        return { taxInfo, taxCalculations, taxValues };
    }

    async getActiveDealInfo(productDetails, masterPriceId, salesPersonRoleId) {

        const result = { activeProductDealDetails: null };
        const today = new Date();
        const activeProductDealDetails = await DealModel.getSingleDealProduct(
            {
                product_id: productDetails._id,
                deal_from_date: { $lte: today },
                deal_to_date: { $gte: today },
                price_id: masterPriceId
            },
            excludeProjections,
            {
                lean: true,
                populate: { path: 'deal_id', select: excludeProjections, lean: true }
            }
        );

        if (activeProductDealDetails
            && !([DEAL_STATUS.CANCELLED, DEAL_STATUS.PAUSED].includes(activeProductDealDetails.deal_id.deal_status)) // TODO: need to allow deal with specific status
            && Array.isArray(activeProductDealDetails.deal_id?.sales_persons)
            && activeProductDealDetails.deal_id?.sales_persons.find(s => s.toString() === salesPersonRoleId.toString())
        ) {
            result.activeProductDealDetails = activeProductDealDetails;
            return result
        } else {
            return result;
        }

    }

    async calculateDealPrice(activeProductDealDetails, original_price, quantity, qtyPerCtn) {

        let isDealApplied = false;
        let dealPrice = 0;
        let invalidDealConfiguration = false;

        const cartItemDealInfo = {};
        if (activeProductDealDetails) {

            cartItemDealInfo.deal_id = activeProductDealDetails.deal_id._id;
            cartItemDealInfo.deal_name = activeProductDealDetails.deal_id.deal_name;
            cartItemDealInfo.secondary_deal_name = activeProductDealDetails.deal_id.secondary_deal_name;
            cartItemDealInfo.deal_number = activeProductDealDetails.deal_id.deal_id;
            cartItemDealInfo.deal_type = activeProductDealDetails.deal_id.deal_type;

            cartItemDealInfo.deal_product_id = activeProductDealDetails._id;
            cartItemDealInfo.deal_from_date = activeProductDealDetails.deal_from_date;
            cartItemDealInfo.deal_to_date = activeProductDealDetails.deal_to_date;

            switch (activeProductDealDetails.deal_id.deal_type) {
                case DEAL_TYPE.BUY_X_AND_GET_Y:// calculates free items
                    {
                        cartItemDealInfo.buy_product = activeProductDealDetails.buy_product;
                        cartItemDealInfo.free_product = activeProductDealDetails.free_product;

                        cartItemDealInfo.free_item_count = Math.floor(quantity / cartItemDealInfo.buy_product) * cartItemDealInfo.free_product;
                        isDealApplied = cartItemDealInfo.free_item_count > 0;
                        dealPrice = undefined;
                    }
                    break;

                case DEAL_TYPE.BULK_PRICING: // update the pricing of the product based on applied
                    {
                        if (!qtyPerCtn) {
                            invalidDealConfiguration = true;
                            return { isDealApplied, dealPrice, cartItemDealInfo, invalidDealConfiguration };
                        }

                        cartItemDealInfo.first_tier = activeProductDealDetails.first_tier;
                        cartItemDealInfo.second_tier = activeProductDealDetails.second_tier;
                        cartItemDealInfo.third_tier = activeProductDealDetails.third_tier;

                        if ((activeProductDealDetails.third_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = activeProductDealDetails.third_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        } else if ((activeProductDealDetails.second_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = activeProductDealDetails.second_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        } else if ((activeProductDealDetails.first_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = activeProductDealDetails.first_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        }
                    }
                    break;

                case DEAL_TYPE.DISCOUNT:
                    {
                        cartItemDealInfo.discount_type = activeProductDealDetails.discount_type
                        cartItemDealInfo.discounted_price = activeProductDealDetails.discounted_price
                        cartItemDealInfo.amount = original_price - cartItemDealInfo.discounted_price

                        const percent = (cartItemDealInfo.amount * 100) / original_price
                        cartItemDealInfo.percent = roundOf(percent)

                        if (cartItemDealInfo.amount > 0) {
                            isDealApplied = true;
                            dealPrice = cartItemDealInfo.discounted_price;
                        }
                    }
                    break;
            }
        }

        return { isDealApplied, dealPrice, cartItemDealInfo, invalidDealConfiguration };

    }

    async calculateDealPriceFromCartItem(cartItemDealInfo, quantity, qtyPerCtn) {
        let isDealApplied = false;
        let dealPrice = 0;
        let invalidDealConfiguration = false;

        if (cartItemDealInfo) {

            switch (cartItemDealInfo.deal_type) { // Only need to check for deals which are depended upon item quantity
                case DEAL_TYPE.BUY_X_AND_GET_Y:// calculates free items
                    {
                        cartItemDealInfo.free_item_count = Math.floor(quantity / cartItemDealInfo.buy_product) * cartItemDealInfo.free_product;
                        isDealApplied = cartItemDealInfo.free_item_count > 0;
                        dealPrice = undefined;
                    }
                    break;

                case DEAL_TYPE.BULK_PRICING: // update the pricing of the product based on applied
                    {
                        if (!qtyPerCtn) {
                            invalidDealConfiguration = true
                            return { isDealApplied, dealPrice, cartItemDealInfo, invalidDealConfiguration };
                        }

                        if ((cartItemDealInfo.third_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = cartItemDealInfo.third_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        } else if ((cartItemDealInfo.second_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = cartItemDealInfo.second_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        } else if ((cartItemDealInfo.first_tier.product_qty * qtyPerCtn) <= quantity) {
                            cartItemDealInfo.bulk_price = cartItemDealInfo.first_tier.price;
                            dealPrice = cartItemDealInfo.bulk_price;
                            isDealApplied = true;
                        }
                    }
                    break;
            }
        }

        return { isDealApplied, dealPrice, cartItemDealInfo };
    }

    async dealAppliedInCartMessageToSQS(cartItemDealInfo, tenantId, cartId) {
        const messageBody = {
            dealId: cartItemDealInfo.deal_id,
            dealProductId: cartItemDealInfo.deal_product_id,
            ts: new Date().toISOString(),
            cartId,
            dealStatisticsEventType: DEAL_STATISTICS_EVENT_TYPE.ADD_TO_CART_COUNT_UPDATE
        }
        await sendDealStatMessageToSQS(messageBody, tenantId);
    }

    /**
     * this function generates mongoDB aggregation pipeline staging for fetching cover_image, group_cover_image and parent_cover_image
     * @returns
     */
    async getImgsPipelineStagesFromProductInfo() {
        return [
            {
                $lookup: {
                    from: "images_2.0",
                    localField: '_id',
                    foreignField: 'product_variant_id',
                    as: "cover_image",
                    pipeline: [
                        {
                            $match: {
                                group_id: null
                            }
                        },
                        {
                            $project: {
                                image_name: 1,
                                image_number: 1,
                                s3_url: 1,
                                updated_at: 1,
                            }
                        },
                        {
                            $sort: { image_number: 1 }
                        },
                        {
                            $limit: 1,
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: "images_2.0",
                    localField: 'parent_id',
                    foreignField: 'product_variant_id',
                    as: "parent_cover_image",
                    pipeline: [
                        {
                            $match: {
                                group_id: null
                            }
                        },
                        {
                            $project: {
                                image_name: 1,
                                image_number: 1,
                                s3_url: 1,
                                updated_at: 1,
                            }
                        },
                        {
                            $sort: { image_number: 1 }
                        },
                        {
                            $limit: 1,
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: "images_2.0",
                    localField: 'parent_id',
                    foreignField: 'product_variant_id',
                    as: "group_cover_image",
                    let: { group_id: "$group_value_id" },
                    pipeline: [
                        {
                            $match: {
                                group_id: { $ne: null },
                                $expr: {
                                    $eq: ["$group_id", "$$group_id"]
                                }
                            }
                        },
                        {
                            $project: {
                                image_name: 1,
                                image_number: 1,
                                s3_url: 1,
                                updated_at: 1,
                            }
                        },
                        {
                            $sort: { image_number: 1 }
                        },
                        {
                            $limit: 1,
                        }
                    ]
                }
            },
            {
                $addFields: {
                    cover_image: { $first: "$cover_image" },
                    parent_cover_image: { $first: "$parent_cover_image" },
                    group_cover_image: { $first: "$group_cover_image" },
                }
            }
        ]
    }
    
}

module.exports = OrderModel;

const MasterDataModel = new (require("./MasterDataModel"))()
const ProductModel = new (require("./ProductModel"))()
const Product2Model = new (require("./ProductModel"))()
const Api = new (require("../Utils/Api"))()

const {
    generateExcelColumnCellName,
    httpService,
} = require("../Utils/helpers")

const {
    DATA_SHEET,
    NEW_PRODUCT_TYPE,
    BOOLEAN,
} = require("../Configs/constants")

class PriceDatasheetModel {
    // Getting a dataSheetController instance from the base class to this derived class.
    constructor(modelInstance) {
        this.dataSheetController = modelInstance
    }

    checkActivateProduct = async (
        sheetData,
        excelData,
        parentItemNumberMapping,
        parentProductIdMapping,
        isActiveItemNumberMapping,
        activateProductInfo,
        tenantId,
        validations,
    ) => {
        const setLimitValidation = () => {
            if (activateProductInfo.activateLimit < 1) {
                validations.push(`Max product limit (${activateProductInfo.allowedLimit}) is exceeded`)
            }
            else {
                activateProductInfo.activateLimit--
            }
        }

        const itemNumber = excelData["Item Number"]
        const parentItemNumber = excelData["Parent"]
        const active = String(excelData.Active)?.toUpperCase()

        if (
            !active ||
            !Object.values(BOOLEAN).includes(active)
        ) {
            validations.push("Please provide 'Active' value (case insensitive) 'TRUE' or 'FALSE'")
        }

        /**
         *  Check current product's active/inactive status,
         *  and compare it to the excel's active/inactive status.
         * 
         *  If user is requesting to activate product,
         *  then check its validation over here.
         */

        if (
            active === BOOLEAN.TRUE &&
            !parentItemNumber &&
            isActiveItemNumberMapping[itemNumber] === false
        ) {
            setLimitValidation()
        }
        else if (
            active === BOOLEAN.TRUE &&
            parentItemNumber &&
            isActiveItemNumberMapping[itemNumber] === false &&
            isActiveItemNumberMapping[parentItemNumber] === false
        ) {
            /**
             *  Check other variants of the same parent, 
             *  if they're also activated then need to activate, 
             *  parent product also.
             */
            const filter = {
                "tenant_id": tenantId,
                "parent_id": new mongoose.Types.ObjectId(parentProductIdMapping[parentItemNumber]),
                "unique_item_number": { $ne: tenantId + "_" + itemNumber },
            }
            const projection = "item_number parent_id is_active"
            const options = { "lean": true }

            const otherVariants = await Product2Model.findProducts(filter, projection, options)

            if (otherVariants?.length) {
                const variantMapping = {
                    [itemNumber]: true
                }

                otherVariants.forEach(oVp => {
                    variantMapping[oVp.item_number] = oVp.is_active
                })

                for (const key in parentItemNumberMapping) {
                    if (Object.hasOwnProperty.call(parentItemNumberMapping, key)) {
                        const element = parentItemNumberMapping[key]

                        if (element === parentItemNumber) {
                            const variant = sheetData.find(data => data["Item Number"] === key)

                            if (variant) {
                                const active = String(variant["Active"])?.toUpperCase()

                                const hasValidActiveValue = Object.values(BOOLEAN).includes(active)

                                if (hasValidActiveValue) {
                                    variantMapping[variant["Item Number"]] =
                                        active === BOOLEAN.TRUE
                                            ? true
                                            : false
                                }
                            }
                        }
                    }
                }
                const allVariantsActive = Object.values(variantMapping).every(vp => vp === true)

                if (
                    allVariantsActive &&
                    !activateProductInfo.parentItemNumbers[parentItemNumber]
                ) {
                    setLimitValidation()
                    activateProductInfo.parentItemNumbers[parentItemNumber] = true
                }
            }
            else {
                setLimitValidation()
            }
        }
    }

    checkValidation = async (
        sheetData,
        excelData,
        mappingObj,
        itemNumberMapping,
        parentItemNumberMapping,
        parentProductIdMapping,
        isActiveItemNumberMapping,
        activateProductInfo,
        tenantId,
    ) => {
        const validations = []
        const itemNumber = excelData["Item Number"]

        const validItemNumber = itemNumberMapping[itemNumber]

        if (!validItemNumber) {
            validations.push("Product not found")
        }
        else {
            excelData["Parent"] = parentItemNumberMapping[itemNumber]
        }

        // Check valid price/inventory value
        const keys = Object.keys(excelData)

        keys.forEach((key) => {
            if (mappingObj[key]) {
                if (isNaN(excelData[key])) {
                    validations.push(`${key} has invalid value '${excelData[key]}'`)
                }
            }
        })

        /* await this.checkActivateProduct(
            sheetData,
            excelData,
            parentItemNumberMapping,
            parentProductIdMapping,
            isActiveItemNumberMapping,
            activateProductInfo,
            tenantId,
            validations,
        ) */
        return validations
    }

    getMappingObj = async (req, tenantId, dataType) => {
        let mappingObj = {}
        let warehouses = {}

        if (dataType === DATA_SHEET.DATA_TYPE.PRICE) {
            const priceList = await MasterDataModel.masterPriceList(
                tenantId,
                undefined,
                "price_name",
                { lean: true }
            )

            priceList.forEach(price => {
                mappingObj[price.price_name] = price._id
            })
        }
        else {
            const branches = await httpService(req)
                .get(
                    "tenantBranches",
                    {
                        "params": {
                            "tenantId": tenantId
                        }
                    }
            )
            const branchData = branches?.data?.data || []

            branchData.forEach((item) => {
                mappingObj[item.name] = item._id
                warehouses[item._id] = item.warehouses[0]._id
            })
        }
        return { mappingObj, warehouses }
    }

    getProductsMapping = async (tenantId, itemNumbers) => {
        const itemNumberMapping = {}
        const parentItemNumberMapping = {}
        const parentProductIdMapping = {}
        const isActiveItemNumberMapping = {}

        const uniqueItemNumbers = itemNumbers.map(itemNum => `${tenantId}_${itemNum}`)

        const filter = {
            "tenant_id": tenantId,
            "unique_item_number": { $in: uniqueItemNumbers },
            "is_deleted": false,
            "type": {
                $in: [
                    NEW_PRODUCT_TYPE.SINGLE,
                    NEW_PRODUCT_TYPE.VARIANT
                ]
            }
        }

        const projection = "item_number parent_id"

        const options = {
            "lean": true,
            "populate": [
                {
                    "path": "parent_id",
                    "select": {
                        "item_number": 1,
                        // "is_active": 1,
                    }
                },
            ]
        }

        const products = await Product2Model.findProducts(filter, projection, options)

        products.forEach(product => {
            const parentItemNumber = product.parent_id?.item_number || ""

            if (parentItemNumber) {
                parentItemNumberMapping[product.item_number] = parentItemNumber
                // parentProductIdMapping[parentItemNumber] = product.parent_id._id
                // isActiveItemNumberMapping[parentItemNumber] = product.parent_id.is_active
            }
            // isActiveItemNumberMapping[product.item_number] = product.is_active
            itemNumberMapping[product.item_number] = true
        })

        return {
            itemNumberMapping,
            parentItemNumberMapping,
            parentProductIdMapping,
            isActiveItemNumberMapping,
        }
    }

    updatePriceImportDataSheet = async (tenantId, operationType, dataType, worksheet, req) => {
        const {
            importData,
            fileId,
            approveType,
            selectRow,
            apiVersion,
        } = req.body

        if (!worksheet) {
            return false
        }
        const columnCells = generateExcelColumnCellName()

        if (!importData) {
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            };
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).value = "Status";

            for (let i = 2; i <= worksheet.actualRowCount; i++) {
                worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + i).value = DATA_SHEET.SHEET_STATUS.PENDING;
            }

            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            };
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).value = "Validations";
        }

        let excelData = []
        let excelTitles = [];
        let itemNumbers = []

        worksheet.eachRow((row, rowNumber) => {
            // rowNumber 0 is empty
            if (rowNumber > 0) {
                // get values from row
                let rowValues = row.values;
                // remove first element (extra without reason)
                rowValues.shift();
                // titles row
                if (rowNumber === 1) excelTitles = rowValues;
                // table data
                else {
                    // create object with the titles and the row values (if any)
                    let rowObject = {}

                    for (let i = 0; i < excelTitles.length; i++) {
                        let title = excelTitles[i];
                        let value = rowValues[i] ?? '';

                        if (title === "Parent") {
                            /**
                             *  Client Requirement:
                             * 
                             *  Skipping parent column, as it may have wrong data.
                             */
                            continue
                        }

                        rowObject[title] = value;
                        rowObject.row_number = rowNumber

                        if (title === "Item Number") {
                            const itemNumber = String(value).trim()

                            rowObject[title] = itemNumber
                            itemNumbers.push(itemNumber)
                        }
                    }
                    excelData.push(rowObject);
                }
            }
        })

        const { mappingObj, warehouses } = await this.getMappingObj(req, tenantId, dataType)

        /** 
         *  Check add product limit for the tenant,
         *  activate product only if limit is not exceeded.
         */
        /* const tenantInfo = await MasterDataModel.getTenantInfo(tenantId, "advance_limit", { lean: true })
        const advanceLimitInfo = tenantInfo?.advance_limit?.find(data => data.key === "NUMBER_OF_PRODUCTS") */

        if (importData) {
            if (approveType === DATA_SHEET.APPROVE_TYPE.SELECTED) {
                const selectedRows = []
                const selectedItemNumbers = []

                for (let i = 0, rows = selectRow.length; i < rows; i++) {
                    const row = excelData.find(element => element.row_number === selectRow[i])

                    if (row) {
                        selectedRows.push(row)
                        selectedItemNumbers.push(row["Item Number"])
                    }
                }
                excelData = selectedRows
                itemNumbers = selectedItemNumbers
            }

            const {
                itemNumberMapping,
                parentItemNumberMapping,
                parentProductIdMapping,
                isActiveItemNumberMapping,
            } = await this.getProductsMapping(tenantId, itemNumbers)

            for (let index = 0; index < excelData.length; index++) {
                const {
                    Status,
                    row_number,
                } = excelData[index]

                if (Status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    /* const activeProducts = await Product2Model.allowNewValidation(tenantId)

                    const activateProductInfo = {
                        "allowedLimit": advanceLimitInfo.allowance,
                        "activateLimit": advanceLimitInfo.allowance - activeProducts,
                        "parentItemNumbers": {},
                    } */

                    const validations = await this.checkValidation(
                        excelData,
                        excelData[index],
                        mappingObj,
                        itemNumberMapping,
                        parentItemNumberMapping,
                        parentProductIdMapping,
                        isActiveItemNumberMapping,
                        // activateProductInfo,
                        tenantId,
                    )

                    if (validations.length) {
                        worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = validations.toString()
                    }
                    else {
                        const products = {
                            "single": [],
                            "variant": []
                        }
                        const rowObject = {}

                        Object
                            .keys(excelData[index])
                            .map(key => {
                                if (mappingObj[key]) {
                                    rowObject[mappingObj[key]] = excelData[index][key]
                                }
                                else {
                                    rowObject[key] = excelData[index][key]
                                }
                            })

                        if (rowObject["Parent"]) {
                            products.variant.push(rowObject)
                        }
                        else {
                            products.single.push(rowObject)
                        }

                        req.body = {
                            "arrayOfProducts": products,
                            "listOfPriceOrInventory": mappingObj,
                            "type": dataType,
                            tenantId,
                            apiVersion,
                        }

                        if (dataType === DATA_SHEET.DATA_TYPE.INVENTORY) {
                            req.body.branchWarehouseMapping = warehouses
                        }

                        const validation = await this.updateProductsForPriceAndInventory(req)

                        let isUpdated = validation[0].value

                        if (validation.length === 2 && isUpdated) {
                            isUpdated = validation[1].value
                        }

                        if (!isUpdated) {
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + (row_number)).value =
                                validation[0]?.reason ||
                                validation[1]?.reason ||
                                "Unable to Update"
                        }
                        else {
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 2] + (row_number)).value = DATA_SHEET.SHEET_STATUS.COMPLETE;
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + (row_number)).value = "Passed Validation";
                        }
                    }
                }
            }

            const columnStatus = worksheet.getColumn(worksheet.actualColumnCount - 1).values;
            const isPending = columnStatus.some(column => column === DATA_SHEET.SHEET_STATUS.PENDING)

            const payload = {
                "filter": {
                    "_id": fileId,
                },
                "updateFields": {
                    "process_start_time": null,
                    "status":
                        isPending
                            ? DATA_SHEET.STATUS.FOR_REVIEW
                            : DATA_SHEET.STATUS.COMPLETE,
                }
            }
            await Api.put(req, "dataSheet", payload);
        }
        else {
            const {
                itemNumberMapping,
                parentItemNumberMapping,
                parentProductIdMapping,
                isActiveItemNumberMapping,
            } = await this.getProductsMapping(tenantId, itemNumbers)

            /* const activeProducts = await Product2Model.allowNewValidation(tenantId)

            const activateProductInfo = {
                "allowedLimit": advanceLimitInfo.allowance,
                "activateLimit": advanceLimitInfo.allowance - activeProducts,
                "parentItemNumbers": {},
            } */

            for (let index = 0; index < excelData.length; index++) {
                const {
                    Status,
                    row_number,
                } = excelData[index]

                if (Status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    const validations = await this.checkValidation(
                        excelData,
                        excelData[index],
                        mappingObj,
                        itemNumberMapping,
                        parentItemNumberMapping,
                        parentProductIdMapping,
                        isActiveItemNumberMapping,
                        // activateProductInfo,
                        tenantId,
                    )

                    worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value =
                        validations.length
                            ? validations.toString()
                            : "Passed Validation"
                }
            }
        }
        return true;
    }

    async updateProductsForPriceAndInventory(req) {
        try {
            const {
                arrayOfProducts,
                listOfPriceOrInventory,
                type,
                branchWarehouseMapping,
                tenantId,
                apiVersion,
            } = req.body

            const promises = []
            const idOfAllPriceOrInventory = []
            for (const [key, value] of Object.entries(listOfPriceOrInventory)) {
                idOfAllPriceOrInventory.push({ key, value })
            }
            if (type == "PRICE") {
                arrayOfProducts.single.forEach((item) => {
                    const priceArr = []
                    idOfAllPriceOrInventory.forEach((price) => {
                        if (item[price.value]) {
                            priceArr.push({
                                "master_price_id": new mongoose.Types.ObjectId(price.value),
                                "price": item[price.value]
                            })
                        }
                    })

                    promises.push(
                        Product2Model.findByItemNumber(
                            true,
                            "PRICE",
                            item["Item Number"],
                            priceArr,
                            JSON.parse(req.headers.userdetails)["_id"],
                            null,
                            null,
                            tenantId,
                            apiVersion,
                            // item["Active"],
                        )
                    )
                })

                arrayOfProducts.variant.forEach((item) => {
                    const priceArr = []
                    idOfAllPriceOrInventory.forEach((price) => {
                        if (item[price.value]) {
                            priceArr.push({
                                "master_price_id": new mongoose.Types.ObjectId(price.value),
                                "price": item[price.value],
                            })
                        }
                    })

                    promises.push(
                        Product2Model.findByItemNumber(
                            false,
                            "PRICE",
                            item["Item Number"],
                            priceArr,
                            JSON.parse(req.headers.userdetails)["_id"],
                            item["Parent"],
                            null,
                            tenantId,
                            apiVersion,
                            // item["Active"],
                        )
                    )
                })
            }
            else if (type == "INVENTORY") {
                arrayOfProducts.single.forEach((item) => {
                    const priceArr = []
                    idOfAllPriceOrInventory.forEach((inventory) => {
                        priceArr.push({
                            "branch_id": inventory.value,
                            "quantity": item[inventory.value] || 0
                        })
                    })

                    promises.push(
                        Product2Model.findByItemNumber(
                            true,
                            "INVENTORY",
                            item["Item Number"],
                            priceArr,
                            JSON.parse(req.headers.userdetails)["_id"],
                            null,
                            branchWarehouseMapping,
                            tenantId,
                            apiVersion,
                            // item["Active"],
                        )
                    )
                })

                arrayOfProducts.variant.forEach((item) => {
                    const priceArr = []
                    idOfAllPriceOrInventory.forEach((inventory) => {
                        priceArr.push({
                            "branch_id": inventory.value,
                            "quantity": item[inventory.value] || 0,
                        })
                    })

                    promises.push(
                        Product2Model.findByItemNumber(
                            false,
                            "INVENTORY",
                            item["Item Number"],
                            priceArr,
                            JSON.parse(req.headers.userdetails)["_id"],
                            item["Parent"],
                            branchWarehouseMapping,
                            tenantId,
                            apiVersion,
                            // item["Active"],
                        )
                    )
                })
            }
            const responceOfPromis = await Promise.allSettled(promises)
            return responceOfPromis;
        }
        catch (error) {
            logger.error(error)
            return error
        }
    }
}

module.exports = PriceDatasheetModel

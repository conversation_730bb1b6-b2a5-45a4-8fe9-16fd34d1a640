const axios = require("axios")
const querystring = require("querystring")

const { VALUES } = require("../Configs/constants")

class InternalServiceModel {

    async getShippingLabel(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "shippingLabel"

            const authResponse = await axios({
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getCustomer(data) {
        try {
            const url = VALUES.internalServiceBaseURL + "customer"

            const authResponse = await axios({
                method: "post",
                url,
                data,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getTenants(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "tenants"

            const response = await axios({
                url,
                params,
            })
            const details = response?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getCountries(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "countries"

            const response = await axios({
                url,
                params,
            })
            const details = response?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getSalesPersons(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "salespersons"

            const response = await axios({
                url,
                params,
            })
            const details = response?.["data"]?.["data"]
            return details
        }
        catch (error) {
            logError(error)
            throw new Error(error)
        }
    }

    async getUsers(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "users"

            const authResponse = await axios({
                method: "get",
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async updateCustomer(data) {
        try {
            const url = VALUES.internalServiceBaseURL + "customer"

            const response = await axios({
                method: "put",
                url,
                data,
            })

            return response?.["data"]?.["data"]
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getCustomers(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "customers"

            const authResponse = await axios({
                method: "get",
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getCustomersCount(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "customersCount"

            const response = await axios({
                method: "get",
                url,
                params,
            })

            return response?.["data"]?.["data"] ?? 0
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getUserRoleSettings(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "userRoleSettings"

            const authResponse = await axios({
                method: "get",
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getUserRoleSettingsCount(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "userRoleSettingsCount"

            const response = await axios({
                method: "get",
                url,
                params,
            })

            return response?.["data"]?.["data"] ?? 0
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async getTenantsAppSettings(params) {
        try {
            const url = VALUES.internalServiceBaseURL + "tenantsAppSettings"

            const response = await axios({
                method: "get",
                url,
                params,
            })

            return response?.["data"]?.["data"]
        }
        catch (error) {
            throw new Error(error)
        }
    }

    async tenantAppSettings(_id, projection) {
        const data = {
            _id,
            projection
        }

        const url = VALUES.internalServiceBaseURL + "tenantAppSettings?" + querystring.stringify(data)
        const response = await axios.get(url)
        return response?.["data"]?.["data"]
    }

    async tenantBranches(params) {
        const response = await axios({
            method: "patch",
            url: VALUES.internalServiceBaseURL + "tenantBranches",
            params
        })

        return response?.["data"]?.["data"]
    }

    getReason = async (params) => {
        try {
            const url = VALUES.internalServiceBaseURL + 'holdReasonTemplate';

            const response = await axios({
                method: "get",
                url,
                params
            })

            const details = response?.["data"]?.["data"]
            return details;

        } catch (error) {
            throw new Error(error)
        }
    }

    sentNotification = async (body) => {
        try {
            const url = VALUES.internalServiceBaseURL + "notification"
            const response = await axios({
                method: "POST",
                url,
                data: body,
            })

            return response?.["data"]?.["data"];
        }
        catch (error) {
            throw new Error(error)
        }
    }

    getRewardProgramProducts = async (params) => {
        try {
            const url = VALUES.internalServiceBaseURL + "rewardProgramProducts"

            const response = await axios({
                method: "get",
                url,
                params,
            })

            return response?.["data"]?.["data"]
        }
        catch (error) {
            throw new Error(error)
        }
    }

    checkCustomersPreApprovedStatus = async (
        tenantId,
        externalIds
    ) => {
        let statuscode
        let data
        let error

        try {
            const url = VALUES.internalServiceBaseURL + "customers/checkPreApproved"

            const params = {
                tenantId,
                externalIds
            }

            const response = await axios({
                method: "get",
                url,
                params,
            })

            statuscode = response.status
            data = response.data?.data
        }
        catch (err) {
            statuscode = err.response?.status ?? 500
            data = err.response?.data
            error = data?.error ?? err
        }
        finally {
            return {
                statuscode,
                data,
                error
            }
        }
    } 
    
    getIntegrationCredentials = async (params) => {
        try {
            const url = VALUES.internalServiceBaseURL + "integrationCredentials"

            const authResponse = await axios({
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

    getRecentlyRestockedItems = async (params) => {
        try {
            const url = VALUES.internalServiceBaseURL + "recentlyRestockedItems"

            const authResponse = await axios({
                url,
                params,
            })
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        catch (error) {
            throw new Error(error)
        }
    }

}

module.exports = InternalServiceModel

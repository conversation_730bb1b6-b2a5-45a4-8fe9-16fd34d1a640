const OrderReportSchema = require("../Database/Schemas/order/OrderReportSchema")
const OrderSchema = require("../Database/Schemas/order/OrderSchema")

const InternalServiceModel = new (require("../Models/InternalServiceModel"))()

const { stringifyObjectId } = require("../Utils/helpers")

class OrderReportModel {

    async addOrderReport(data) {
        return OrderReportSchema({ ...data })
    }

    async getOrderReportList(data) {
        const {
            tenantId,
            startDate,
            endDate,
            userRoleIds,
            page = 0,
            perPage = 10,
            timezone,
        } = data

        const userRoleIdsArray = userRoleIds.map(_id => new mongoose.Types.ObjectId(_id))
        const newStartDate = new Date(momentTimezone.tz(startDate, timezone).startOf("day"))
        const newEndDate = new Date(momentTimezone.tz(endDate, timezone).endOf("day"))

        const pipeline = [
            {
                $match: {
                    tenant_id: tenantId,
                    user_role_id: { $in: userRoleIdsArray },
                    date: {
                        $gte: newStartDate,
                        $lte: newEndDate,
                    }
                }
            },
            {
                $group: {
                    _id: "$user_role_id",
                    total_orders: {
                        $sum: "$total_orders",
                    },
                    total_amount: {
                        $sum: { $add: ["$total_sales_amount", "$total_tax"] },
                    },
                },
            },
            {
                $sort: {
                    _id: 1
                }
            },
            {
                $facet: {
                    list: [
                        {
                            $skip: (page - 1) * perPage
                        },
                        {
                            $limit: perPage
                        }
                    ],
                    count: [{ $count: "count" }],
                },
            },
            {
                $unwind: "$count",
            },
        ]

        const aggregatedData = await OrderReportSchema.aggregate(pipeline)

        const list = aggregatedData[0]?.list || []
        const count = aggregatedData[0]?.count?.count || 0

        return {
            list,
            count
        }
    }

    getTenantList = async () => {
        const params = {
            "filter": {
                "is_active": true,
                "is_deleted": false,
            },
            "projection": "country",
            "options": {
                "lean": true,
                "populate": [
                    {
                        "path": "country",
                        "select": "-_id timezone"
                    }
                ]
            }
        }
        return await InternalServiceModel.getTenants(params)
    }

    getSalesPersonList = async (tenantId) => {
        const params = {
            "filter": {
                "tenant_id": tenantId,
                "is_deleted": false,
            },
            "projection": {
                "tenant_id": 1,
                /* "user_id": 1,
                "collection_name": 1, */
            },
            "options": {
                "lean": true,
                /* "populate": [
                    {
                        "path": "user_id",
                        "select": "first_name last_name"
                    }
                ] */
            }
        }
        return await InternalServiceModel.getSalesPersons(params)
    }

    generateMapping = (userRoleMap, userRoleId, totalAmount, totalTax) => {
        const _id = stringifyObjectId(userRoleId)

        const data = userRoleMap.get(_id)

        userRoleMap.set(
            _id,
            {
                "total_orders": (data?.total_orders || 0) + 1,
                "total_sales_amount": (data?.total_sales_amount || 0) + totalAmount,
                "total_tax": (data?.total_tax || 0) + totalTax,
            }
        )
    }

    prepareReportData = (userRoleMap, tenantId, todayDate, timezone, promises) => {

        if (userRoleMap.size) {
            for (const [key, value] of userRoleMap) {
                const orderReportData = {
                    "tenant_id": tenantId,
                    "user_role_id": new mongoose.Types.ObjectId(key),
                    "date": todayDate,
                    "timezone": timezone,
                    "total_orders": value.total_orders,
                    "total_sales_amount": value.total_sales_amount,
                    "total_tax": value.total_tax,
                }

                promises.push(
                    this
                        .addOrderReport(orderReportData)
                        .then(doc => doc.save())
                )
            }
        }
    }

    generatePastOrderMapping = (params) => {
        const {
            dateRangeMap,
            dateRange,
            userRoleId,
            startDate,
            endDate,
            orderDate,
            totalAmount,
            totalTax,
        } = params

        const existingData = dateRangeMap.get(dateRange) ||
        {
            "user_role_id": userRoleId,
            "start_date": startDate,
            "end_date": endDate,
            "total_orders": 0,
            "total_sales_amount": 0,
            "total_tax": 0,
        }

        const isInDateRange =
            moment(orderDate)
                .isBetween(existingData.start_date, existingData.end_date, null, '[]')

        dateRangeMap.set(
            dateRange,
            {
                ...existingData,
                "total_orders":
                    existingData.total_orders +
                    (isInDateRange ? 1 : 0),

                "total_sales_amount":
                    existingData.total_sales_amount +
                    (isInDateRange ? totalAmount : 0),

                "total_tax":
                    existingData.total_tax +
                    (isInDateRange ? totalTax : 0)
            }
        )
    }

    preparePastReportData = (params) => {

        const {
            dateRangeMap,
            tenantId,
            timezone,
            promises,
        } = params

        if (dateRangeMap.size) {
            for (const [key, value] of dateRangeMap) {
                const orderReportData = {
                    "tenant_id": tenantId,
                    "user_role_id": new mongoose.Types.ObjectId(value.user_role_id),
                    "date": value.start_date,
                    "timezone": timezone,
                    "total_orders": value.total_orders,
                    "total_sales_amount": value.total_sales_amount,
                    "total_tax": value.total_tax,
                }

                promises.push(
                    this
                        .addOrderReport(orderReportData)
                        .then(doc => doc.save())
                )
            }
        }
    }

    /**
     * 
     * @param {*} req 
     * @param {*} res 
     * @description A script to getCustomerPunchedOrders  
     */
    getCustomerPunchedOrders = async (req, res) => {
        try {
            const data = await OrderSchema.aggregate([
                {
                    '$match': {
                        'tenant_id': 1001,
                        'order_app_type': 'CUSTOMER_APP',
                        'created_at': {
                            '$gte': new Date('Sun, 01 Jan 2023 00:00:00 GMT'),
                            '$lte': new Date('Sun, 31 Dec 2023 23:59:59 GMT')
                        }
                    }
                },
                {
                    '$project': {
                        'tenant_id': 1,
                        'order_app_type': 1,
                        'sales_user_role_id': 1,
                        'customer_user_role_id': 1,
                        'order_number': 1,
                        'total_sales_amount': {
                            '$sum': [
                                '$total_amount', '$total_tax'
                            ]
                        },
                        'total_tax': 1,
                        'total_amount': 1,
                        'master_price_ids': 1,
                        'order_status': 1,
                        'created_at': 1
                    }
                },
                {
                    '$lookup': {
                        'from': 'master_prices',
                        'localField': 'master_price_ids',
                        'foreignField': '_id',
                        'as': 'price_list',
                        'pipeline': [
                            {
                                '$project': {
                                    'price_name': 1,
                                    '_id': 0
                                }
                            }
                        ]
                    }
                }
            ])
            const userRoleIds = []

            data.forEach(order => {
                userRoleIds.push(
                    stringifyObjectId(order['customer_user_role_id']),
                    stringifyObjectId(order['sales_user_role_id'])
                )
            })

            const uniqueUserRoleIds = [...new Set(userRoleIds)]
            const users = []
            const limit = 50
            const totalPages = Math.ceil(uniqueUserRoleIds.length / limit)

            for (let i = 1; i <= totalPages; i++) {
                const skip = (i - 1) * limit
                const processingIds = uniqueUserRoleIds.slice(skip, skip + limit)

                const userApiParams = {
                    filter: {
                        _id: {
                            $in: processingIds
                        },
                    },
                    projection: "user_id collection_name customer_legal_name",
                    options: {
                        lean: true,
                        populate: [
                            {
                                path: "user_id",
                                select: "_id first_name last_name"
                            },
                        ]
                    },
                };

                const receivers = await InternalServiceModel.getUsers(
                    userApiParams
                );
                users.push(...receivers)
            }

            data.forEach(order => {
                const customerData = users.find(user => {
                    const cond = stringifyObjectId(order.customer_user_role_id) === stringifyObjectId(user._id)
                    return cond
                })
                order.customer_legal_name = customerData.customer_legal_name || "NA"

                const spData = users.find(user => {
                    const cond = stringifyObjectId(order.sales_user_role_id) === stringifyObjectId(user._id)
                    return cond
                })

                order.salesperson_name =
                    spData
                        ? spData?.user_id?.first_name + " " + spData?.user_id?.last_name
                        : "NA"

                order.price_list = order.price_list.map(order => order.price_name).join(",")
            })
            return res.handler.success(null, { data, users })
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = OrderReportModel

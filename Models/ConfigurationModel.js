const TaxConfigurationSchema = require("../Database/Schemas/configuration/tax_configurations")
const MasterTaxSchema = require("../Database/Schemas/configuration/master_tax")

const { VALUES, ENTITY_STATUS } = require('../Configs/constants')

class CategoryModel {

    async taxById(taxId) {
        return TaxConfigurationSchema.findById(taxId, { created_by: 0, updated_by: 0, created_at: 0, updated_at: 0 });
    }

    async addTax(body, header) {

        if (body.group_value_type) {
            return TaxConfigurationSchema({
                tenant_id: body.tenant_id,
                type: body.group_value_type,
                group_id: body.group_id,
                tax_name: body.group_value_tax_name,
                tax_calculation: body.group_value_tax_calculation,
                tax_rate: body.group_value_tax_rate,
                created_by: header.userDetails._id,
                updated_by: header.userDetails._id
            })
        }
        return TaxConfigurationSchema({
            tenant_id: body.tenant_id,
            type: body.type,
            group_id: body.group_id,
            tax_name: body.tax_name,
            tax_calculation: body.tax_calculation,
            tax_rate: body.tax_rate,
            status: body.status,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id
        })
    }

    async groupValue(taxId) {
        return TaxConfigurationSchema.deleteMany({ group_id: taxId });
    }

    async updateTaxStatus(taxId, status = ENTITY_STATUS.ACTIVE, header) {
        return TaxConfigurationSchema.updateOne({ _id: taxId },
            {
                status: status === ENTITY_STATUS.ACTIVE ? true : false, updated_by: header.userDetails._id
            });
    }

    async taxList(tenantId, status, type, groupId) {

        const pipeline = [];

        const match = {
            'tenant_id': tenantId,
            type: { $in: [VALUES.TAX_TYPE.GROUP, VALUES.TAX_TYPE.SINGLE] }
        }

        if (type === VALUES.TAX_TYPE.GROUP) {
            pipeline.push([
                {
                    '$match': {
                        'group_id': new mongoose.Types.ObjectId(groupId)
                    }
                },
                {
                    '$project': {
                        'created_by': 0,
                        'updated_by': 0,
                        'created_at': 0,
                        'updated_at': 0
                    }
                }
            ]);
        } else {

            if (status !== ENTITY_STATUS.ALL) {
                match['status'] = status === ENTITY_STATUS.ACTIVE ? true : false
            }
            pipeline.push([
                {
                    '$match': match
                }, {
                    '$lookup': {
                        'from': 'tax_configurations',
                        'localField': '_id',
                        'foreignField': 'group_id',
                        'as': 'group_tax'
                    }
                }, {
                    '$project': {
                        'created_by': 0,
                        'updated_by': 0,
                        'created_at': 0,
                        'updated_at': 0,
                        'group_tax.created_by': 0,
                        'group_tax.updated_by': 0,
                        'group_tax.created_at': 0,
                        'group_tax.updated_at': 0
                    }
                }
            ]);
        }
        return TaxConfigurationSchema.aggregate(pipeline);
    }

    findTaxes(filter, projection, options) {
        return TaxConfigurationSchema.find(filter, projection, options)
    }

    async masterTaxInfo(tenantId, projection, options) {
        return MasterTaxSchema.findOne({ tenant_id: tenantId }, projection, options)
    }

    findMasterTaxInfo(filter, projection, options) {
        return MasterTaxSchema.findOne(filter, projection, options)
    }

    async addMasterTax(body, header) {
        const userDetails = header.userDetails;
        return MasterTaxSchema({
            tenant_id: body.tenant_id,
            enable_tax: true,
            price: VALUES.MASTER_PRICE.INCLUDE,
            universal_tax: true,
            created_by: userDetails._id, // header.userDetails._id,
            updated_by: userDetails._id //header.userDetails._id
        })
    }

}

module.exports = CategoryModel;

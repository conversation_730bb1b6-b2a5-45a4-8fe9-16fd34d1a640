const difference = require('lodash.difference')

const DataSheetModel = new (require("../DataSheetModel"))()
const UtilityModel = new (require("./ProductDataSheetUtilityModel"))()
const ValidationModel = new (require("./ProductUpdateValidationModel"))()
const Api = new (require("../../Utils/Api"))()
const ProductModel = new (require("../ProductModel"))()

const {
    DATA_SHEET,
    VALUES,
    NEW_PRODUCT_TYPE,
    STATUS_CODES,
    INCREMENT_PRODUCT_NUMBER_BY,
} = require("../../Configs/constants")

const { MASTER_DATA } = require("../../Configs/ApiType")

const {
    generateExcelColumnCellName,
    stringifyObjectId,
} = require("../../Utils/helpers")

class ProductUpdateDataSheetModel {

    updateProduct = async (
        req,
        currentData,
        list,
        excelData,
        validationMap,
    ) => {
        const {
            tenantId,
        } = req.body

        const {
            row_number = "",
            item_number = "",
            barcode = "",
            product_title = "",
            product_title_sl = "",
            type = "",
            product_type = "",
            parentage_type = "",
            parent_sku = "",
            variant_value = "",
            group_name = "",
            group_value = "",
            family = "",
            category = "",
            subcategory = "",
            brand = "",
            qty_ctn = 0,
            min_qty = 0,
            uom = "",
            variantProducts = [],
            variants_values = undefined,
            groups_values = undefined,
        } = currentData

        const setValidation = (errorMsg, rowNumber) => {
            UtilityModel.setValidationInMap(
                validationMap,
                rowNumber ?? row_number,
                errorMsg
            )
        }

        const setValidationInVariant = (errorMsg) => {
            if (variantProducts.length) {
                variantProducts.forEach(vProduct => {
                    setValidation(errorMsg, vProduct.row_number)
                })
            }
        }

        try {
            // Using transactions for better data consistency
            var session = await mongoose.startSession()
            session.startTransaction()

            const {
                brandList,
                familyList,
                categoryList,
                subCategoryList,
                uomList,
            } = list

            const product = await ProductModel.findProduct(
                {
                    "tenant_id": tenantId,
                    "is_deleted": false,
                    "unique_item_number": tenantId + "_" + item_number,
                },
                null,
                {
                    "populate": [
                        {
                            "path": "variants.values",
                            "select": { "name": 1 }
                        },
                        {
                            "path": "groups.values",
                            "select": { "name": 1 }
                        },
                    ],
                    session,
                })

            if (!product) {
                setValidation("Product not found")
                setValidationInVariant("Parent product not found")

                await session.abortTransaction()
                await session.endSession({ forceClear: true })
                return
            }

            if (
                (product.type !== type) ||
                (product.type === NEW_PRODUCT_TYPE.VARIANT)
            ) {
                setValidation(`${type} is invalid product type. It must be ${product.type}`)

                await session.abortTransaction()
                await session.endSession({ forceClear: true })
                return
            }
            const updatedBy = req.headers.userDetails?._id

            product.title = product_title
            product.secondary_language_title = product_title_sl
            product.updated_by = updatedBy

            const brandInfo = brandList.find(element => element.brand_name === brand)
            product.brand_id = brandInfo?._id

            const familyInfo = familyList.find(element => element.category_name === family)

            let familyId = familyInfo?._id
            let categoryId = null
            let subcategoryId = null

            if (category) {
                const categoryInfo = categoryList.find(element => element.category_name === category)
                categoryId = categoryInfo?._id

                if (categoryId && subcategory) {
                    const subCategoryInfo = subCategoryList.find(element => element.category_name === subcategory)
                    subcategoryId = subCategoryInfo?._id
                }
            }
            let hasCatChanged = false

            if (stringifyObjectId(product.family_id) !== stringifyObjectId(familyId) ||
                (stringifyObjectId(product.category_id) !== stringifyObjectId(categoryId)) ||
                (stringifyObjectId(product.subcategory_id) !== stringifyObjectId(subcategoryId))
            ) {
                hasCatChanged = true
            }

            product.family_id = familyId
            product.category_id = categoryId
            product.subcategory_id = subcategoryId

            if (hasCatChanged) {
                // Update 'product_counter' in respected category & 'product_order' in product.
                let categoryDoc

                if (product.family_id && product.category_id && product.subcategory_id) {
                    categoryDoc = await ProductModel.findCategoryById(
                        {
                            "_id": product.subcategory_id
                        },
                        {
                            "_id": 1,
                            "product_counter": 1,
                            "is_active": 1
                        }
                    )

                    if (!categoryDoc) {
                        setValidation("Subcategory not found")
                        return
                    }
                }
                else if (product.family_id && product.category_id) {
                    categoryDoc = await ProductModel.findCategoryById(
                        {
                            "_id": product.category_id
                        },
                        {
                            "_id": 1,
                            "product_counter": 1,
                            "is_active": 1
                        }
                    )

                    if (!categoryDoc) {
                        setValidation("Category not found")
                        return
                    }
                }
                else {
                    categoryDoc = await ProductModel.findCategoryById(
                        {
                            "_id": product.family_id
                        },
                        {
                            "_id": 1,
                            "product_counter": 1,
                            "is_active": 1
                        }
                    )

                    if (!categoryDoc) {
                        setValidation("Family not found")
                        return
                    }
                }
                categoryDoc.product_counter += 1
                product.product_order = categoryDoc.product_counter * INCREMENT_PRODUCT_NUMBER_BY

                await categoryDoc.save({ session })
            }

            switch (product.type) {
                case NEW_PRODUCT_TYPE.SINGLE: {
                    const uomInfo = uomList.find(element => element.unit_name === uom)

                    product.packaging_map = {
                        uom_id: uomInfo?._id,
                        min_qty,
                        qty_ctn,
                    }

                    if (barcode) {
                        /** Check barcode validation */
                        await ValidationModel.checkValidBarcode(
                            tenantId,
                            currentData,
                            product,
                            excelData,
                            validationMap,
                        )

                        if (validationMap.has(row_number)) {
                            await session.abortTransaction()
                            await session.endSession({ forceClear: true })
                            return
                        }

                        /** If barcodes are valid then create in DB */
                        const sanitizedBarcode = new Set()

                        barcode.split(",").forEach(element => {
                            const normalizedBarcode = element.trim()
                            if (normalizedBarcode) {
                                sanitizedBarcode.add(normalizedBarcode)
                            }
                        })

                        const uniqBarcode = Array.from(sanitizedBarcode)
                        const newBarCodes = []

                        uniqBarcode.forEach(uniqBarCode => {
                            if (!product.barcodes?.find(barcode => barcode === uniqBarCode)) {
                                newBarCodes.push(uniqBarCode)
                            }
                        })

                        if (newBarCodes.length) {
                            await ProductModel.addUpdateBarcodes(
                                tenantId,
                                newBarCodes,
                                product._id,
                                { session }
                            )

                            product.barcodes = (product.barcodes ?? []).concat(newBarCodes)
                        }
                    }
                    break
                }
                /** Update parent product with its variants */
                case NEW_PRODUCT_TYPE.PARENT: {
                    let updatedVariantCount = 0

                    for (let i = 0; i < variantProducts.length; i++) {
                        const vProd = variantProducts[i]
                        const checkSingleValidation = true
                        let isValidBarcode = false

                        const existingVariant = await ProductModel.findProduct(
                            {
                                "parent_id": product._id,
                                "tenant_id": tenantId,
                                "is_deleted": false,
                                "unique_item_number": tenantId + "_" + vProd.item_number,
                            },
                            null,
                            {
                                session,
                            }
                        )

                        if (existingVariant) {
                            const variantInfo = product.variants?.values?.find(v => v?.name === vProd.variant_value)
                            let groupInfo

                            if (vProd.group_value) {
                                groupInfo = product.groups?.values?.find(v => v?.name === vProd.group_value)
                            }
                            const variant_id = `${product._id}_${variantInfo._id}${groupInfo?._id ? `_${groupInfo?._id}` : ""}`

                            if (existingVariant.variant_id !== variant_id) {
                                setValidation(
                                    `Variant id '${variant_id}' is mismatched. It must be '${existingVariant.variant_id}'`,
                                    vProd.row_number
                                )
                                continue; // don't update this variant
                            }
                            const uomInfo = uomList.find(element => element.unit_name === vProd.uom)

                            existingVariant.packaging_map = {
                                "uom_id": uomInfo?._id,
                                "min_qty": vProd.min_qty,
                                "qty_ctn": vProd.qty_ctn
                            }
                            existingVariant.updated_by = updatedBy

                            let newBarCodes = []

                            if (vProd.barcode) {
                                /** Check barcode validation */
                                await ValidationModel.checkValidBarcode(
                                    tenantId,
                                    vProd,
                                    existingVariant,
                                    excelData,
                                    validationMap,
                                    checkSingleValidation,
                                )

                                if (validationMap.has(vProd.row_number)) {
                                    continue; // don't update this variant
                                }
                                isValidBarcode = true
                                const sanitizedBarcode = new Set()

                                vProd.barcode.split(",").forEach(element => {
                                    const normalizedBarcode = element.trim()
                                    if (normalizedBarcode) {
                                        sanitizedBarcode.add(normalizedBarcode)
                                    }
                                })
                                const uniqBarcode = Array.from(sanitizedBarcode)

                                uniqBarcode.forEach(uniqBarCode => {
                                    if (!existingVariant.barcodes?.find(barcode => barcode === uniqBarCode)) {
                                        newBarCodes.push(uniqBarCode)
                                    }
                                })

                                existingVariant.barcodes = (existingVariant.barcodes ?? []).concat(newBarCodes)
                                if (existingVariant.is_active) {
                                    product.active_variant_barcodes = (product.active_variant_barcodes ?? []).concat(newBarCodes)
                                }
                                else {
                                    product.inactive_variant_barcodes = (product.inactive_variant_barcodes ?? []).concat(newBarCodes)
                                }
                            }

                            try {
                                await existingVariant.save({ session })
                            }
                            catch (error) {
                                let newError = error.message || error

                                if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                                    if ("unique_item_number" in error.keyValue) {
                                        newError = "Item number already exists"
                                    }
                                    else if ("barcodes" in error.keyValue) {
                                        newError = "Barcode already exists"
                                    }
                                    else if ("variant_id" in error.keyValue) {
                                        newError = "Variant id already exists"
                                    }
                                }
                                setValidation(newError, vProd.row_number)
                                continue; // don't update this variant
                            }

                            if (isValidBarcode) {
                                /** If barcodes are valid then create in DB */

                                if (newBarCodes.length) {
                                    await ProductModel.addUpdateBarcodes(
                                        tenantId,
                                        newBarCodes,
                                        existingVariant._id,
                                        { session }
                                    )
                                }
                            }
                            updatedVariantCount++
                        }
                        else {
                            setValidation("Product not found", vProd.row_number)
                        }
                    }

                    if (!updatedVariantCount) {
                        if (!validationMap.has(row_number)) {
                            setValidation("Something went wrong with its variants")
                        }

                        if (variantProducts?.length) {
                            variantProducts.forEach(vp => {
                                if (!validationMap.has(vp.row_number)) {
                                    setValidation("Unable to update variant of the parent product", vp.row_number)
                                }
                            })
                        }
                        await session.abortTransaction()
                        await session.endSession({ forceClear: true })
                        return
                    }
                }
                default:
                    break;
            }

            try {
                await product.save({ session })
                await session.commitTransaction()
                await session.endSession()

                logger.info(`${tenantId} - Product ${product._id} updated successfully :)`)
            }
            catch (error) {
                let newError = error.message || error

                if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                    if ("unique_item_number" in error.keyValue) {
                        newError = "Item number already exists"
                    }
                    else if ("barcodes" in error.keyValue) {
                        newError = "Barcode already exists"
                    }
                    else if ("variant_id" in error.keyValue) {
                        newError = "Variant id already exists"
                    }
                }
                setValidation(newError)

                if (variantProducts.length) {
                    variantProducts.forEach(vp => {
                        setValidation(newError, vp.row_number)
                    })
                }
                await session.abortTransaction()
                await session.endSession({ forceClear: true })
            }
        }
        catch (error) {
            const newError = error.message || error

            setValidation(newError)

            if (variantProducts.length) {
                variantProducts.forEach(vp => {
                    setValidation(newError, vp.row_number)
                })
            }
            await session.abortTransaction()
            await session.endSession({ forceClear: true })

            logger.error(error)
        }
    }

    importProducts = async (
        req,
        productWorkSheet,
        mappedData,
        excelData,
        list,
        columnCells,
        actualColumnCount,
        extraInfo,
    ) => {
        const importDataArray = []

        /**
         * Check validation on mappedData and 
         * if error then set it in column
         * else add row in importDataArray.
         */
        for (let index = 0; index < mappedData.length; index++) {

            const currentData = mappedData[index]
            const validationMap = new Map()

            await ValidationModel.checkValidations(
                req.body.tenantId,
                excelData,
                currentData,
                list,
                extraInfo,
                validationMap,
            )

            if (!validationMap.size) {
                importDataArray.push(currentData)
            }
            else {
                validationMap.forEach((value, key, map) => {

                    UtilityModel.setValidationValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        key,
                        [...new Set(value)]
                    )
                })
            }
        }

        /**
         * If importDataArray.length > 0 then update data in DB
         * else if error then set it in column and
         * finally for success rows set success message
         */
        for (let index = 0; index < importDataArray.length; index++) {
            const validationMap = new Map()
            const errorRowNumbers = []
            const currentData = importDataArray[index]

            await this.updateProduct(
                req,
                currentData,
                list,
                excelData,
                validationMap,
            )

            if (validationMap.size) {
                validationMap.forEach((value, key, map) => {
                    const validations = [...new Set(value)]
                    errorRowNumbers.push(key)

                    UtilityModel.setValidationValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        key,
                        validations
                    )
                })
            }
            else {
                const allRowNumbers = [currentData.row_number]

                if (currentData.variantProducts?.length) {
                    currentData.variantProducts.forEach(vProduct => {
                        allRowNumbers.push(vProduct.row_number)
                    })
                }
                const validRowNumbers = difference(allRowNumbers, errorRowNumbers)

                if (validRowNumbers.length) {
                    validRowNumbers.forEach(rowNumber => {
                        UtilityModel.setValidationValueInColumn(
                            productWorkSheet,
                            columnCells,
                            actualColumnCount,
                            rowNumber,
                        )

                        UtilityModel.setStatusValueInColumn(
                            productWorkSheet,
                            columnCells,
                            actualColumnCount,
                            rowNumber,
                        )
                    })
                }
            }
        }
    }

    updateProductImportDataSheet = async (req, worksheet) => {
        const {
            tenantId,
            operationType,
            importData,
            approveType,
            fileId,
            selectRow,
        } = req.body

        const productWorkSheet = worksheet.getWorksheet("product")

        if (!productWorkSheet) {
            return false
        }
        const A1Cell = productWorkSheet.getCell('A1').value

        if (A1Cell !== "item_number") {
            return false
        }
        const columnCells = generateExcelColumnCellName()

        // A count of the number of columns that have values.
        const actualColumnCount = productWorkSheet.actualColumnCount

        if (!importData) {
            DataSheetModel.setStatusAndValidationColumn(productWorkSheet, columnCells, actualColumnCount)
        }

        const familyList = await UtilityModel.getCategoryListing(tenantId, VALUES.category.FAMILY)
        const categoryList = await UtilityModel.getCategoryListing(tenantId, VALUES.category.CATEGORY)
        const subCategoryList = await UtilityModel.getCategoryListing(tenantId, VALUES.category.SUBCATEGORY)
        const brandList = await UtilityModel.getMasterDataListing(tenantId, "brand_name", MASTER_DATA.BRAND.LIST)
        const uomList = await UtilityModel.getMasterDataListing(tenantId, "unit_name", MASTER_DATA.UNIT.LIST)

        const list = {
            familyList,
            categoryList,
            subCategoryList,
            brandList: brandList.list,
            uomList: uomList.list,
        }
        const extraInfo = {}

        const addRowNumber = true
        let excelData = DataSheetModel.getExcelData(productWorkSheet, 2, addRowNumber, importData)

        UtilityModel.sanitizeData(excelData)

        if (importData) {
            if (approveType === DATA_SHEET.APPROVE_TYPE.SELECTED) {
                const selectedRows = []

                for (let i = 0, rows = selectRow.length; i < rows; i++) {
                    const row = excelData.find(element => element.row_number === selectRow[i])

                    if (row) {
                        selectedRows.push(row)
                    }
                }
                excelData = selectedRows
            }

            const mappedData = UtilityModel.generateMappedData(excelData)

            await this.importProducts(
                req,
                productWorkSheet,
                mappedData,
                excelData,
                list,
                columnCells,
                actualColumnCount,
                extraInfo,
            )

            // Set process_start_time & status of the file in DB.
            const columnStatus = productWorkSheet.getColumn(actualColumnCount - 2).values
            const isPending = columnStatus.some(column => column === DATA_SHEET.SHEET_STATUS.PENDING)

            const payload = {
                "filter": {
                    "_id": fileId,
                },
                "updateFields": {
                    "process_start_time": null,
                    "status":
                        isPending
                            ? DATA_SHEET.STATUS.FOR_REVIEW
                            : DATA_SHEET.STATUS.COMPLETE,
                }
            }
            await Api.put(req, "dataSheet", payload);
        }
        else {
            const mappedData = UtilityModel.generateMappedData(excelData)
            const validationMap = new Map()

            for (let index = 0; index < mappedData.length; index++) {

                await ValidationModel.checkValidations(
                    tenantId,
                    excelData,
                    mappedData[index],
                    list,
                    extraInfo,
                    validationMap,
                )
            }

            const errorRowNumbers = []
            const allRowNumbers = excelData.map(data => data.row_number)

            if (validationMap.size) {
                validationMap.forEach((value, key, map) => {
                    errorRowNumbers.push(key)

                    UtilityModel.setValidationInSheet(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        key,
                        [...new Set(value)]
                    )

                    UtilityModel.setStatusValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount + 3,
                        key,
                        [1], // Radom array's value to set status PENDING
                    )
                })
            }

            const validRowNumbers = difference(allRowNumbers, errorRowNumbers)

            if (validRowNumbers.length) {
                validRowNumbers.forEach(rowNumber => {
                    UtilityModel.setValidationInSheet(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        rowNumber,
                    )

                    UtilityModel.setStatusValueInColumn(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount + 3,
                        rowNumber,
                        [1], // Radom array's value to set status PENDING
                    )
                })
            }

            const orphanVariantSet = UtilityModel.getOrphanVariantSet(excelData)

            if (orphanVariantSet.size) {
                orphanVariantSet.forEach(rowNumber => {
                    UtilityModel.setValidationInSheet(
                        productWorkSheet,
                        columnCells,
                        actualColumnCount,
                        rowNumber,
                        ["Missing parent product"]
                    )
                })
            }
        }
        return true
    }
}

module.exports = ProductUpdateDataSheetModel

/**
 * ProductUpdateValidationModel will have methods,
 * which will validate product update dataSheet's data.
 */

const ProductModel = new (require("../ProductModel"))()
const UtilityModel = new (require("./ProductDataSheetUtilityModel"))()

const {
    DATA_SHEET,
    NEW_PRODUCT_TYPE,
} = require("../../Configs/constants")

const {
    stringifyObjectId,
    toLeanOption,
} = require("../../Utils/helpers")

class ProductUpdateValidationModel {

    checkValidItemNumber = (
        excelData,
        currentData,
        productData,
        validationMap,
    ) => {
        const {
            row_number,
            item_number,
            product_type,
            group_name,
        } = currentData

        let message = "Missing item number"

        const setValidation = (rowNumber) => {
            UtilityModel.setValidationInMap(
                validationMap,
                rowNumber ?? row_number,
                message
            )
        }

        const setValidationInVariant = () => {
            if (currentData.variantProducts?.length) {
                currentData.variantProducts.forEach(vProduct => {
                    setValidation(vProduct.row_number)
                })
            }
        }

        if (!item_number) {
            setValidation()

            message = "Missing parent item number"
            setValidationInVariant()
        }
        else if (!productData.item_number) {
            message = "Product not found"
            setValidation()

            message = "Parent product not found"
            setValidationInVariant()
        }
        else {
            const sameItemNumbersData = excelData.filter((element, index) =>
                element.item_number === item_number &&
                element.row_number !== row_number
            )

            if (sameItemNumbersData.length) {
                message = "Same item number already exists in the sheet"
                setValidation()

                sameItemNumbersData.forEach(element => {
                    setValidation(element.row_number)
                })
            }

            if (product_type === DATA_SHEET.PRODUCT_TYPE.PARENT) {
                // Find variant products of this parent product. 

                const variantProducts = excelData.filter(element =>
                    element.product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT &&
                    element.parent_sku === item_number
                )

                if (!variantProducts.length) {
                    message = "Missing variant product(s) with same sku"
                    setValidation()
                }
                else {
                    if (!productData.variantProducts?.length) {
                        message = "Variant product(s) not found"
                        setValidation()
                    }
                    else {
                        variantProducts.forEach(element => {
                            const vProduct = productData.variantProducts.find(vp => vp.item_number === element.item_number)

                            if (!vProduct) {
                                message = "Product not found"
                                setValidation(element.row_number)
                            }
                        })
                    }

                    const haveGroupValues = variantProducts.some(element => element.group_value)

                    if (haveGroupValues && !group_name) {
                        message = "Missing group name"
                        setValidation()
                    }
                    else if (group_name) {
                        if (productData.groups?.type) {
                            if (group_name !== productData.groups.type) {
                                message = `Group name '${group_name}' is mismatched. It must be '${productData.groups.type}'`
                                setValidation()
                            }
                        }
                        else {
                            message = `Product not found with group name '${group_name}'`
                            setValidation()
                        }
                    }
                }
            }
        }
    }

    checkValidBarcode = async (tenantId, currentData, productData, excelData, validationMap, checkSingleValidation) => {
        const isSingleProduct = currentData.product_type === DATA_SHEET.PRODUCT_TYPE.SINGLE

        if ((!currentData.barcode && isSingleProduct) || !productData.item_number) {
            return
        }

        const checkBarcode = async (barcode, rowNumber, productVariantId) => {
            if (!barcode || !barcode.length) {
                return
            }

            const sanitizedBarCodes = new Set()
            const smallBarcode = [];
            const barcodeCount = new Map();
            const duplicateBarcode = new Set();
            let emptyCount = 0
            let totalBarcode = 0

            // Process barcode
            barcode.split(",").forEach(element => {
                totalBarcode++

                let normalizedBarcode = element.trim();
                if (!normalizedBarcode) {
                    emptyCount++
                    return
                }

                if (normalizedBarcode.length < 3)
                    smallBarcode.push(normalizedBarcode);

                if (barcodeCount.has(normalizedBarcode)) {
                    duplicateBarcode.add(normalizedBarcode);
                }
                else {
                    barcodeCount.set(normalizedBarcode, 1);
                    sanitizedBarCodes.add(`${tenantId}_${normalizedBarcode}`);
                }
            });

            if (emptyCount > 0 && emptyCount === totalBarcode) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    rowNumber,
                    `Missing barcode(s): received empty values in the comma-separated list`
                )
            }

            // Validate short barcode
            if (smallBarcode.length) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    rowNumber,
                    `Barcode(s) [${smallBarcode}] must be at least 3 characters long`
                )
            }

            // Check for duplicates in other rows
            excelData.forEach(element => {
                if (element.row_number !== rowNumber) {

                    element.barcode?.split(",").forEach(e => {
                        const rowBarcode = e.trim();

                        if (rowBarcode && barcodeCount.has(rowBarcode)) {
                            duplicateBarcode.add(rowBarcode);
                            sanitizedBarCodes.delete(`${tenantId}_${rowBarcode}`)
                        }
                    });
                }
            });

            // Add validation message if duplicates exist
            if (duplicateBarcode.size) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    rowNumber,
                    `Barcode(s) [${[...duplicateBarcode]}] already exists in the sheet`
                )
            }

            const dbBarcode = await ProductModel.getAllBarCodes(
                {
                    "_id": {
                        "$in": [...sanitizedBarCodes]
                    },
                    "tenant_id": tenantId,
                    "product_variant_id": {
                        "$ne": new mongoose.Types.ObjectId(productVariantId),
                    },
                    "is_active": true,
                },
                "-_id barcode",
                toLeanOption,
            )

            if (dbBarcode.length) {
                const barcode = dbBarcode.map(element => element.barcode)
                const uniqBarcode = [...new Set(barcode)]

                UtilityModel.setValidationInMap(
                    validationMap,
                    rowNumber,
                    `Barcode(s) [${uniqBarcode}] already exists`
                )
            }
        }

        if (isSingleProduct || checkSingleValidation) {
            await checkBarcode(currentData.barcode, currentData.row_number, productData._id)
        }

        if (productData.variantProducts?.length) {
            for (let i = 0; i < currentData.variantProducts.length; i++) {
                const data = currentData.variantProducts[i]

                const product = productData.variantProducts.find(element => element.item_number === data.item_number)

                if (product) {
                    await checkBarcode(data.barcode, data.row_number, product._id)
                }
            }
        }
    }

    checkValidProductTitle = (currentData, validationMap) => {
        if (
            [
                DATA_SHEET.PRODUCT_TYPE.SINGLE,
                DATA_SHEET.PRODUCT_TYPE.PARENT
            ].includes(currentData.product_type)
        ) {
            if (!currentData.product_title) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Missing product title"
                )
            }
        }
    }

    checkValidSlProductTitle = (currentData, validationMap) => {
        if (
            [
                DATA_SHEET.PRODUCT_TYPE.SINGLE,
                DATA_SHEET.PRODUCT_TYPE.PARENT
            ].includes(currentData.product_type)
        ) {
            if (!currentData.product_title_sl) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Missing secondary language's product title"
                )
            }
        }
    }

    checkValidProductType = (currentData, productData, validationMap) => {

        const setMissingValidation = (data) => {
            if (!data.product_type) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    data.row_number,
                    "Missing product type"
                )
            }
        }

        const setInvalidTypeValidation = (data, product) => {
            const productType = DATA_SHEET.PRODUCT_TYPE[product.type]

            if (data.product_type !== productType) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    data.row_number,
                    `${data.product_type} is invalid product type. It must be ${productType}`
                )
            }
        }

        /** Check valid product type for SINGLE and PARENT */
        setMissingValidation(currentData)

        if (productData._id) {
            setInvalidTypeValidation(currentData, productData)
        }

        /** Check valid product type for VARIANT */
        if (currentData.variantProducts?.length) {
            currentData.variantProducts.forEach(element => {
                setMissingValidation(element)

                if (productData._id) {
                    const vProduct = productData.variantProducts.find(vp => vp.item_number === element.item_number)

                    if (vProduct) {
                        setInvalidTypeValidation(element, vProduct)
                    }
                }
            })
        }
    }

    checkValidParentageType = (currentData, productData, validationMap) => {

        if (currentData.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT) {
            if (!currentData.parentage_type) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Missing parentage type"
                )
            }
            else if (productData._id) {
                const productParentageType = productData.variants?.type?.toLowerCase()

                if (currentData.parentage_type !== productParentageType) {
                    UtilityModel.setValidationInMap(
                        validationMap,
                        currentData.row_number,
                        `${currentData.parentage_type} is invalid parentage type. It must be ${productParentageType}`
                    )
                }
            }
        }
    }

    checkValidVariantValue = (currentData, productData, validationMap) => {
        if (
            currentData.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT &&
            currentData.variantProducts?.length
        ) {
            currentData.variantProducts.forEach(vProduct => {
                if (!vProduct.variant_value) {
                    UtilityModel.setValidationInMap(
                        validationMap,
                        vProduct.row_number,
                        "Missing variant value"
                    )
                }
                else if (productData.variantProducts?.length) {
                    const product = productData.variantProducts.find(element => element.item_number === vProduct.item_number)

                    if (product) {
                        const variantInfo = productData.variants?.values?.find(
                            value => stringifyObjectId(value?._id) === stringifyObjectId(product.variant_value_id)
                        )

                        if (variantInfo) {
                            if (variantInfo.name !== vProduct.variant_value) {
                                UtilityModel.setValidationInMap(
                                    validationMap,
                                    vProduct.row_number,
                                    `Variant name '${vProduct.variant_value}' is mismatched. It must be '${variantInfo.name}'`
                                )
                            }
                        }
                        else {
                            UtilityModel.setValidationInMap(
                                validationMap,
                                vProduct.row_number,
                                `Product not found with variant name '${vProduct.variant_value}'`
                            )
                        }
                    }
                }
            })
        }
    }

    checkDuplicateVariantAndGroup = (currentData, productData, excelData, validationMap) => {

        if (
            currentData.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT &&
            currentData.variantProducts?.length
        ) {
            currentData.variantProducts.forEach(vProduct => {
                if (vProduct.parent_sku && vProduct.variant_value) {

                    if (vProduct.group_value) {
                        const haveSameProducts = excelData.some((element, index) =>
                            element.row_number !== vProduct.row_number &&
                            element.product_type === vProduct.product_type &&
                            element.parent_sku === vProduct.parent_sku &&
                            element.variant_value === vProduct.variant_value &&
                            element.group_value === vProduct.group_value
                        )

                        if (haveSameProducts) {
                            UtilityModel.setValidationInMap(
                                validationMap,
                                vProduct.row_number,
                                "Multiple variant products found with same 'sku' 'variant value' & 'group value'"
                            )
                        }
                        else {
                            const product = productData.variantProducts?.find(element => element?.item_number === vProduct.item_number)

                            if (product) {
                                const groupInfo = productData.groups?.values?.find(
                                    value => stringifyObjectId(value?._id) === stringifyObjectId(product.group_value_id)
                                )

                                if (groupInfo) {
                                    if (groupInfo.name !== vProduct.group_value) {
                                        UtilityModel.setValidationInMap(
                                            validationMap,
                                            vProduct.row_number,
                                            `Group value '${vProduct.group_value}' is mismatched. It must be '${groupInfo.name}'`
                                        )
                                    }
                                }
                                else {
                                    UtilityModel.setValidationInMap(
                                        validationMap,
                                        vProduct.row_number,
                                        `Product not found with group value '${vProduct.group_value}'`
                                    )
                                }
                            }
                        }
                    }
                    else if (!vProduct.group_value) {

                        const parentProducts = excelData.filter(element =>
                            element.product_type === DATA_SHEET.PRODUCT_TYPE.PARENT &&
                            element.item_number === vProduct.parent_sku
                        )

                        const haveSomeGroupName = parentProducts.some(element => element.group_name)

                        if (haveSomeGroupName) {
                            UtilityModel.setValidationInMap(
                                validationMap,
                                vProduct.row_number,
                                "Missing group value"
                            )
                        }
                        else {
                            const sameVariantProducts = excelData.filter(element =>
                                element.product_type === vProduct.product_type &&
                                element.parent_sku === vProduct.parent_sku
                            )

                            const hasSomeGroupValue = sameVariantProducts.some(element => element.group_value)

                            if (hasSomeGroupValue) {
                                UtilityModel.setValidationInMap(
                                    validationMap,
                                    vProduct.row_number,
                                    "Missing group value"
                                )
                            }
                            else {
                                const haveSameProducts = excelData.some((element, index) =>
                                    element.row_number !== vProduct.row_number &&
                                    element.product_type === vProduct.product_type &&
                                    element.parent_sku === vProduct.parent_sku &&
                                    element.variant_value === vProduct.variant_value &&
                                    element.group_value === ""
                                )

                                if (haveSameProducts) {
                                    UtilityModel.setValidationInMap(
                                        validationMap,
                                        vProduct.row_number,
                                        "Multiple variant products found with same 'sku' & 'variant value'"
                                    )
                                }
                            }
                        }
                    }
                }
            })
        }
    }

    checkValidCategories = (currentData, list, validationMap) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.PARENT].includes(currentData.product_type)) {
            const {
                familyList,
                categoryList,
                subCategoryList,
            } = list

            if (!currentData.family) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Missing family"
                )
            }
            else {
                const familyInfo = familyList.find(element => element.category_name === currentData.family)

                if (!familyInfo) {
                    UtilityModel.setValidationInMap(
                        validationMap,
                        currentData.row_number,
                        "Family not found"
                    )
                }
                else if (familyInfo) {
                    if (currentData.category) {
                        const categoryInfo = categoryList.find(element => element.category_name === currentData.category)

                        if (!categoryInfo) {
                            UtilityModel.setValidationInMap(
                                validationMap,
                                currentData.row_number,
                                "Category not found"
                            )
                        }
                        else if (categoryInfo) {
                            if (categoryInfo.parent_id.toString() !== familyInfo._id.toString()) {
                                UtilityModel.setValidationInMap(
                                    validationMap,
                                    currentData.row_number,
                                    "Category isn't matching with family"
                                )
                            }
                            else if (currentData.subcategory) {
                                const subCategoryInfo = subCategoryList.find(element => element.category_name === currentData.subcategory)

                                if (!subCategoryInfo) {
                                    UtilityModel.setValidationInMap(
                                        validationMap,
                                        currentData.row_number,
                                        "Subcategory not found"
                                    )
                                }
                                else if (subCategoryInfo && subCategoryInfo.parent_id.toString() !== categoryInfo._id.toString()) {
                                    UtilityModel.setValidationInMap(
                                        validationMap,
                                        currentData.row_number,
                                        "Subcategory isn't matching with category"
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    checkValidBrand = async (currentData, list, validationMap) => {

        if ([DATA_SHEET.PRODUCT_TYPE.SINGLE, DATA_SHEET.PRODUCT_TYPE.PARENT].includes(currentData.product_type)) {
            if (!currentData.brand) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Missing brand"
                )
                return
            }
            const { brandList } = list

            const brandInfo = brandList.find(element => element.brand_name === currentData.brand)

            if (!brandInfo) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    currentData.row_number,
                    "Brand not found"
                )
            }
        }
    }

    checkValidQtnCtn = (currentData, validationMap) => {

        const checkValidation = (data) => {
            if (!data.qty_ctn) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    data.row_number,
                    "Missing qty ctn or it must be greater than zero"
                )
            }
        }

        if (currentData.product_type === DATA_SHEET.PRODUCT_TYPE.SINGLE) {
            checkValidation(currentData)
        }
        else if (currentData.variantProducts?.length) {
            currentData.variantProducts.forEach(vProduct => {
                checkValidation(vProduct)
            })
        }
    }

    checkValidMinQtn = (currentData, validationMap) => {

        const checkValidation = (data) => {
            if (!data.min_qty) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    data.row_number,
                    "Missing min qty or it must be greater than zero"
                )
            }
        }

        if (currentData.product_type === DATA_SHEET.PRODUCT_TYPE.SINGLE) {
            checkValidation(currentData)
        }
        else if (currentData.variantProducts?.length) {
            currentData.variantProducts.forEach(vProduct => {
                checkValidation(vProduct)
            })
        }
    }

    checkValidUom = async (currentData, list, validationMap) => {
        const { uomList } = list

        const checkValidation = (data) => {
            if (!data.uom) {
                UtilityModel.setValidationInMap(
                    validationMap,
                    data.row_number,
                    "Missing uom"
                )
            }
            else {
                const uomInfo = uomList.find(element => element.unit_name === data.uom)

                if (!uomInfo) {
                    UtilityModel.setValidationInMap(
                        validationMap,
                        data.row_number,
                        "Uom not found"
                    )
                }
            }
        }

        if (currentData.product_type === DATA_SHEET.PRODUCT_TYPE.SINGLE) {
            checkValidation(currentData)
        }
        else if (currentData.variantProducts?.length) {
            currentData.variantProducts.forEach(vProduct => {
                checkValidation(vProduct)
            })
        }
    }

    checkValidations = async (
        tenantId,
        excelData,
        currentData,
        list,
        extraInfo,
        validationMap,
    ) => {

        let {
            item_number = "",
            type = "",
        } = currentData

        const projection = `
            -price_mappings
            -inventory_mappings
            -tags
            -product_order
            -variant_order
            -variant_count
            -tax_id
            -attributes
            -attribute_set
            -description
            -secondary_language_description
            -created_at
            -updated_at
            -created_by
            -updated_by
            -__v
        `

        const productInfo = await ProductModel.findProduct(
            {
                "tenant_id": tenantId,
                "is_deleted": false,
                "unique_item_number": tenantId + "_" + item_number,
            },
            projection,
            {
                populate: [
                    { path: "variants.values", select: { name: 1 } },
                    { path: "groups.values", select: { name: 1 } },
                ],
            }
        )
        let variantInfo = []

        if (productInfo && type === NEW_PRODUCT_TYPE.PARENT) {
            variantInfo = await ProductModel.findProducts(
                {
                    "tenant_id": tenantId,
                    "is_deleted": false,
                    "parent_id": productInfo._id,
                },
                projection,
            )
        }

        const productData = {
            ...productInfo?.toJSON(),
            variantProducts: variantInfo,
        }

        this.checkValidItemNumber(
            excelData,
            currentData,
            productData,
            validationMap,
        )

        await this.checkValidBarcode(
            tenantId,
            currentData,
            productData,
            excelData,
            validationMap,
        )

        this.checkValidProductTitle(currentData, validationMap)

        this.checkValidSlProductTitle(currentData, validationMap)

        this.checkValidProductType(currentData, productData, validationMap)

        this.checkValidParentageType(currentData, productData, validationMap)

        this.checkValidVariantValue(currentData, productData, validationMap)

        this.checkDuplicateVariantAndGroup(currentData, productData, excelData, validationMap)

        this.checkValidCategories(currentData, list, validationMap)

        this.checkValidBrand(currentData, list, validationMap)

        this.checkValidQtnCtn(currentData, validationMap)

        this.checkValidMinQtn(currentData, validationMap)

        this.checkValidUom(currentData, list, validationMap)
    }
}

module.exports = ProductUpdateValidationModel

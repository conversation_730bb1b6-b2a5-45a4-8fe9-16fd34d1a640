const uniq = require("lodash.uniq")

const CategoryModel = new (require("../CategoryModel"))()
const MasterDataModel = new (require("../MasterDataModel"))()

const {
    DATA_SHEET,
    ENTITY_STATUS,
    PRODUCT_TYPE,
    NEW_PRODUCT_TYPE,
} = require("../../Configs/constants")

const COLUMNS_TYPE_MAPPING = {
    "item_number": String,
    "barcode": String,
    "product_title": String,
    "product_title_sl": String,
    "product_type": String,
    "parentage_type": String,
    "parent_sku": String,
    "variant_value": String,
    "group_name": String,
    "group_value": String,
    "family": String,
    "category": String,
    "subcategory": String,
    "brand": String,
    "qty_ctn": Number,
    "min_qty": Number,
    "uom": String,
}

class ProductDataSheetUtilityModel {

    getCategoryListing = async (tenantId, categoryType) => {
        return CategoryModel.categoryList(
            tenantId,
            categoryType,
            ENTITY_STATUS.ACTIVE,
            null,
            null,
            false,
            {
                "category_name": 1,
                "parent_id": 1,
            }
        )
    }

    getMasterDataListing = async (tenantId, projectField, apiType) => {
        return MasterDataModel.getAttributeList(
            {
                query: {
                    tenantId,
                    status: ENTITY_STATUS.ACTIVE,
                    perPage: 0,
                    page: 0,
                    projection: {
                        [projectField]: 1
                    }
                }
            },
            apiType,
        )
    }

    getSanitizedValue = (element, property, convert) => {
        const value = element[property]
        const isString = convert.name === "String"

        return value
            ? isString
                ? convert(value).trim()
                : convert(value)
            : isString
                ? ""
                : 0
    }

    sanitizeData = (excelData) => {
        excelData.forEach(element => {
            for (let property in COLUMNS_TYPE_MAPPING) {
                element[property] = this.getSanitizedValue(element, property, COLUMNS_TYPE_MAPPING[property])
            }
        })
    }

    setWarningValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, warnings = []) => {
        if (!warnings.length) {
            return
        }

        worksheet
            .getCell(columnCells[actualColumnCount - 1] + (row_number))
            .value = warnings.toString()
    }

    setValidationValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, validations = []) => {
        worksheet
            .getCell(columnCells[actualColumnCount - 2] + (row_number))
            .value =
            validations.length
                ? validations.toString()
                : "Passed Validation"
    }

    setStatusValueInColumn = (worksheet, columnCells, actualColumnCount, row_number, validations = []) => {
        worksheet
            .getCell(columnCells[actualColumnCount - 3] + (row_number))
            .value =
            validations.length
                ? DATA_SHEET.SHEET_STATUS.PENDING
                : DATA_SHEET.SHEET_STATUS.COMPLETE
    }

    setValidationInSheet = (
        worksheet,
        columnCells,
        actualColumnCount,
        rowNumber,
        validations = [],
    ) => {
        worksheet
            .getCell(columnCells[actualColumnCount + 1] + rowNumber)
            .value =
            validations.length
                ? validations.toString()
                : "Passed Validation"
    }

    generateMappedData = (excelData) => {
        const mappedData = []

        for (let index = 0; index < excelData.length; index++) {
            const {
                status,
                product_type,
                item_number,
                group_name,
            } = excelData[index]

            if (
                status === DATA_SHEET.SHEET_STATUS.PENDING &&
                product_type !== DATA_SHEET.PRODUCT_TYPE.VARIANT
            ) {
                const data = {
                    ...excelData[index],
                    "type": PRODUCT_TYPE.SINGLE
                }

                if ([DATA_SHEET.PRODUCT_TYPE.PARENT].includes(product_type)) {
                    const variantProducts = excelData.filter(element =>
                        element.product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT &&
                        element.parent_sku === item_number
                    )

                    if (variantProducts.length) {
                        data["type"] = NEW_PRODUCT_TYPE.PARENT
                        data["variants_values"] = uniq(variantProducts.map(vp => vp.variant_value))

                        if (group_name) {
                            data["groups_values"] = uniq(variantProducts.map(vp => vp.group_value))
                        }
                        data["variantProducts"] = variantProducts
                    }
                }
                mappedData.push(data)
            }
        }
        return mappedData
    }

    getOrphanVariantSet = (excelData) => {
        let orphanVariantSet = new Set()

        for (let index = 0; index < excelData.length; index++) {
            const {
                status,
                product_type,
                parent_sku,
                row_number,
            } = excelData[index]

            if (
                status === DATA_SHEET.SHEET_STATUS.PENDING &&
                product_type === DATA_SHEET.PRODUCT_TYPE.VARIANT
            ) {
                const parentProduct = excelData.find(element => element.item_number === parent_sku)
                if (!parentProduct) {
                    orphanVariantSet.add(row_number)
                }
            }
        }
        return orphanVariantSet
    }

    setValidationInMap = (validationMap, rowNumber, message) => {

        if (validationMap && rowNumber && message) {
            let validationArray = []

            if (validationMap.has(rowNumber)) {
                validationArray = validationMap.get(rowNumber)
            }

            validationArray.push(message)
            validationMap.set(rowNumber, validationArray)
        }
    }
}

module.exports = ProductDataSheetUtilityModel

const ProductModel = new (require("../ProductModel"))()

const {
    NEW_PRODUCT_TYPE,
    BOOLEAN,
} = require("../../Configs/constants")

class ProductStatusUpdateValidationModel {

    getProductsMapping = async (tenantId, itemNumbers) => {
        const itemNumberMapping = {}
        const parentItemNumberMapping = {}
        const parentProductIdMapping = {}
        const isActiveItemNumberMapping = {}

        const uniqueItemNumbers = itemNumbers.map(itemNum => `${tenantId}_${itemNum}`)

        const filter = {
            "tenant_id": tenantId,
            "unique_item_number": { $in: uniqueItemNumbers },
            "is_deleted": false,
            "type": {
                $in: [
                    NEW_PRODUCT_TYPE.SINGLE,
                    NEW_PRODUCT_TYPE.VARIANT
                ]
            }
        }

        const projection = "item_number parent_id is_active"

        const options = {
            "lean": true,
            "populate": [
                {
                    "path": "parent_id",
                    "select": {
                        "item_number": 1,
                        "is_active": 1,
                    }
                },
            ]
        }
        const products = await ProductModel.findProducts(filter, projection, options)

        products.forEach(product => {
            const parentItemNumber = product.parent_id?.item_number || ""

            if (parentItemNumber) {
                parentItemNumberMapping[product.item_number] = parentItemNumber
                parentProductIdMapping[parentItemNumber] = product.parent_id._id
                isActiveItemNumberMapping[parentItemNumber] = product.parent_id.is_active
            }
            isActiveItemNumberMapping[product.item_number] = product.is_active
            itemNumberMapping[product.item_number] = true
        })

        return {
            itemNumberMapping,
            parentItemNumberMapping,
            parentProductIdMapping,
            isActiveItemNumberMapping,
        }
    }

    checkActivateProduct = async (
        excelData,
        currentData,
        parentItemNumberMapping,
        parentProductIdMapping,
        isActiveItemNumberMapping,
        activateProductInfo,
        scannedInfo,
        tenantId,
        validations,
    ) => {

        const setLimitValidation = () => {
            if (activateProductInfo.activateLimit < 1) {
                validations.push(`Max product limit (${activateProductInfo.allowedLimit}) is exceeded`)
            }
            else {
                activateProductInfo.activateLimit--
            }
        }

        const itemNumber = currentData.itemNumber
        const parentItemNumber = currentData.parentItemNumber
        const active = String(currentData.active)?.toUpperCase()

        if (
            !active ||
            !Object.values(BOOLEAN).includes(active)
        ) {
            validations.push("Please provide 'Active' value (case insensitive) 'TRUE' or 'FALSE'")
        }

        /**
         *  Check current product's active/inactive status,
         *  and compare it to the excel's active/inactive status.
         * 
         *  If user is requesting to activate product,
         *  then check its validation over here.
         */

        if (
            active === BOOLEAN.TRUE &&
            !parentItemNumber &&
            isActiveItemNumberMapping[itemNumber] === false
        ) {
            setLimitValidation()
        }
        else if (
            active === BOOLEAN.TRUE &&
            parentItemNumber &&
            isActiveItemNumberMapping[itemNumber] === false &&
            isActiveItemNumberMapping[parentItemNumber] === false &&
            !scannedInfo.parentItemNumbers[parentItemNumber]
        ) {
            /**
             *  Check other variants of the same parent, 
             *  if they're also activated then need to activate, 
             *  parent product also.
             */
            const filter = {
                "tenant_id": tenantId,
                "parent_id": new mongoose.Types.ObjectId(parentProductIdMapping[parentItemNumber]),
                "unique_item_number": { $ne: tenantId + "_" + itemNumber },
            }
            const projection = "item_number parent_id is_active"
            const options = { "lean": true }

            const otherVariants = await ProductModel.findProducts(filter, projection, options)

            if (otherVariants?.length) {
                const variantMapping = {
                    [itemNumber]: true
                }

                otherVariants.forEach(oVp => {
                    variantMapping[oVp.item_number] = oVp.is_active
                })

                for (const key in parentItemNumberMapping) {
                    if (Object.hasOwnProperty.call(parentItemNumberMapping, key)) {
                        const element = parentItemNumberMapping[key]

                        if (element === parentItemNumber) {
                            const variant = excelData.find(data => data.itemNumber === key)

                            if (variant) {
                                const active = String(variant.active)?.toUpperCase()

                                const hasValidActiveValue = Object.values(BOOLEAN).includes(active)

                                if (hasValidActiveValue) {
                                    variantMapping[variant.itemNumber] =
                                        active === BOOLEAN.TRUE
                                            ? true
                                            : false
                                }
                            }
                        }
                    }
                }
                const allVariantsActive = Object.values(variantMapping).every(vp => vp === true)

                if (
                    allVariantsActive &&
                    !scannedInfo.parentItemNumbers[parentItemNumber]
                ) {
                    setLimitValidation()
                    scannedInfo.parentItemNumbers[parentItemNumber] = true
                }
            }
            else {
                setLimitValidation()
            }
        }
    }

    checkValidation = async (
        excelData,
        currentData,
        itemNumberMapping,
        parentItemNumberMapping,
        parentProductIdMapping,
        isActiveItemNumberMapping,
        activateProductInfo,
        scannedInfo,
        tenantId,
    ) => {
        const validations = []
        const itemNumber = currentData.itemNumber

        const validItemNumber = itemNumberMapping[itemNumber]

        if (!validItemNumber) {
            validations.push("Product not found")
        }
        else {
            currentData.parentItemNumber = parentItemNumberMapping[itemNumber]
        }

        await this.checkActivateProduct(
            excelData,
            currentData,
            parentItemNumberMapping,
            parentProductIdMapping,
            isActiveItemNumberMapping,
            activateProductInfo,
            scannedInfo,
            tenantId,
            validations,
        )
        return validations
    }
}

module.exports = ProductStatusUpdateValidationModel

const MasterDataModel = new (require("../MasterDataModel"))()
const ProductModel = new (require("../ProductModel"))()
const ValidationModel = new (require("./ProductStatusUpdateValidationModel"))()
const Api = new (require("../../Utils/Api"))()

const {
    generateExcelColumnCellName,
} = require("../../Utils/helpers")

const {
    DATA_SHEET, BOOLEAN,
} = require("../../Configs/constants")


const COLUMN_NAME_MAPPING = {
    // "Parent": "parentItemNumber",
    "Item Number": "itemNumber",
    "Active": "active",
    "Status": "status",
}

class ProductStatusUpdateDataSheetModel {

    updateProductImportDataSheet = async (req, worksheet) => {
        const {
            tenantId,
            operationType,
            importData,
            approveType,
            fileId,
            selectRow,
        } = req.body

        if (!worksheet) {
            return false
        }
        const columnCells = generateExcelColumnCellName()

        if (!importData) {
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            };
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).value = "Status";

            for (let i = 2; i <= worksheet.actualRowCount; i++) {
                worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + i).value = DATA_SHEET.SHEET_STATUS.PENDING;
            }

            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            };
            worksheet.getCell(columnCells[worksheet.actualColumnCount] + 1).value = "Validations";
        }

        let excelData = []
        let excelTitles = [];
        let itemNumbers = []

        worksheet.eachRow((row, rowNumber) => {
            // rowNumber 0 is empty
            if (rowNumber > 0) {
                // get values from row
                let rowValues = row.values;
                // remove first element (extra without reason)
                rowValues.shift();
                // titles row
                if (rowNumber === 1) {
                    excelTitles = rowValues
                }
                // table data
                else {
                    // create object with the titles and the row values (if any)
                    let rowObject = {}

                    for (let i = 0; i < excelTitles.length; i++) {
                        const title = excelTitles[i];
                        const value = rowValues[i] ?? '';

                        rowObject.row_number = rowNumber

                        if (["Item Number", "Active", "Status"].includes(title)) {
                            const rowValue = String(value).trim()
                            const columnName = COLUMN_NAME_MAPPING[title]

                            rowObject[columnName] = rowValue

                            if (columnName === "itemNumber") {
                                itemNumbers.push(rowValue)
                            }
                        }
                    }
                    excelData.push(rowObject);
                }
            }
        })

        /**
         *  Check add product limit for the tenant,
         *  activate product only if limit is not exceeded.
         */
        const tenantInfo = await MasterDataModel.getTenantInfo(tenantId, "advance_limit", { lean: true })
        const advanceLimitInfo = tenantInfo?.advance_limit?.find(data => data.key === "NUMBER_OF_PRODUCTS")

        if (importData) {
            if (approveType === DATA_SHEET.APPROVE_TYPE.SELECTED) {
                const selectedRows = []
                const selectedItemNumbers = []

                for (let i = 0, rows = selectRow.length; i < rows; i++) {
                    const row = excelData.find(element => element.row_number === selectRow[i])

                    if (row) {
                        selectedRows.push(row)
                        selectedItemNumbers.push(row.itemNumber)
                    }
                }
                excelData = selectedRows
                itemNumbers = selectedItemNumbers
            }

            const {
                itemNumberMapping,
                parentItemNumberMapping,
                parentProductIdMapping,
                isActiveItemNumberMapping,
            } = await ValidationModel.getProductsMapping(tenantId, itemNumbers)

            const scannedInfo = {
                "parentItemNumbers": {},
                "updatedParentItemNumbers": {},
            }

            for (let index = 0; index < excelData.length; index++) {
                const {
                    status,
                    active,
                    itemNumber,
                    row_number,
                } = excelData[index]

                if (status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    const activeProducts = await ProductModel.allowNewValidation(tenantId)

                    const activateProductInfo = {
                        "allowedLimit": advanceLimitInfo.allowance,
                        "activateLimit": advanceLimitInfo.allowance - activeProducts,
                    }

                    const validations = await ValidationModel.checkValidation(
                        excelData,
                        excelData[index],
                        itemNumberMapping,
                        parentItemNumberMapping,
                        parentProductIdMapping,
                        isActiveItemNumberMapping,
                        activateProductInfo,
                        scannedInfo,
                        tenantId,
                    )

                    if (validations.length) {
                        worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = validations.toString()
                    }
                    else {
                        /** Update product in database */
                        try {
                            let isActive = false
                            const parentItemNumber = excelData[index].parentItemNumber

                            if (active.toUpperCase() === BOOLEAN.TRUE) {
                                isActive = true
                            }

                            /** Update single/variant product */
                            const product = await ProductModel.findProduct(
                                {
                                    "unique_item_number": tenantId + "_" + itemNumber,
                                    "tenant_id": tenantId,
                                },
                                "_id"
                            )

                            if (!product) {
                                worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = "Product not found"
                                continue;
                            }
                            product.is_active = isActive
                            await product.save()

                            if (parentItemNumber) {
                                /** Update parent product */

                                const parentProduct = await ProductModel.findProduct(
                                    {
                                        unique_item_number: tenantId + "_" + parentItemNumber,
                                        tenant_id: tenantId,
                                    },
                                    "_id type"
                                );

                                if (!parentProduct) {
                                    worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = "Parent product not found"
                                    continue;
                                }

                                const allVariants = await ProductModel.findProducts(
                                    {
                                        "parent_id": parentProduct._id,
                                        "is_deleted": false,
                                    },
                                    "is_active price_mappings inventory_mappings item_number is_restocked barcodes",
                                );

                                if (!allVariants?.length) {
                                    worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = "Product variants not found"
                                    continue;
                                }
                                const areSomeActive = allVariants.some(variant => variant.is_active === true)
                                const areAllInActive = allVariants.every(variant => variant.is_active === false)

                                if (areSomeActive) {
                                    parentProduct.is_active = true
                                }
                                else if (areAllInActive) {
                                    parentProduct.is_active = false
                                }

                                await ProductModel.generateMappingForParent(parentProduct, allVariants);
                                await parentProduct.save()
                            }

                            /** On success update Status and Validation message in the sheet */
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 2] + (row_number)).value = DATA_SHEET.SHEET_STATUS.COMPLETE;
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + (row_number)).value = "Passed Validation";
                        }
                        catch (error) {
                            logger.error(error)
                            worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value = error.message
                        }
                    }
                }
            }

            const columnStatus = worksheet.getColumn(worksheet.actualColumnCount - 1).values;
            const isPending = columnStatus.some(column => column === DATA_SHEET.SHEET_STATUS.PENDING)

            const payload = {
                "filter": {
                    "_id": fileId,
                },
                "updateFields": {
                    "process_start_time": null,
                    "status":
                        isPending
                            ? DATA_SHEET.STATUS.FOR_REVIEW
                            : DATA_SHEET.STATUS.COMPLETE,
                }
            }
            await Api.put(req, "dataSheet", payload)
        }
        else {
            const {
                itemNumberMapping,
                parentItemNumberMapping,
                parentProductIdMapping,
                isActiveItemNumberMapping,
            } = await ValidationModel.getProductsMapping(tenantId, itemNumbers)

            const activeProducts = await ProductModel.allowNewValidation(tenantId)

            const scannedInfo = {
                "parentItemNumbers": {},
                "updatedParentItemNumbers": {},
            }

            const activateProductInfo = {
                "allowedLimit": advanceLimitInfo.allowance,
                "activateLimit": advanceLimitInfo.allowance - activeProducts,
            }

            for (let index = 0; index < excelData.length; index++) {
                const {
                    status,
                    row_number,
                } = excelData[index]

                if (status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    const validations = await ValidationModel.checkValidation(
                        excelData,
                        excelData[index],
                        itemNumberMapping,
                        parentItemNumberMapping,
                        parentProductIdMapping,
                        isActiveItemNumberMapping,
                        activateProductInfo,
                        scannedInfo,
                        tenantId,
                    )

                    worksheet.getCell(columnCells[worksheet.actualColumnCount - 1] + row_number).value =
                        validations.length
                            ? validations.toString()
                            : "Passed Validation"
                }
            }
        }
        return true;
    }
}

module.exports = ProductStatusUpdateDataSheetModel
